{"name": "global-esim", "version": "1.4.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"airtrip": "turbo run dev --scope=airtrip", "analyze": "turbo run analyze", "build": "turbo run build", "compress:image": "node scripts/imagemin.mjs", "copy:translations-airtrip": "rm -rf apps/airtrip/src/locales/* && cp -r apps/translation-sheet/lang/i18n/jp apps/airtrip/src/i18n/locales", "copy:translations-web": "rm -rf apps/web/src/locales/* && cp -r apps/translation-sheet/lang/i18n/ apps/web/src/i18n/locales &&  cp -r apps/translation-sheet/lang/i18n/ apps/web/public/locales", "copy:translations-webjp": "rm -rf apps/webjp/src/locales/* && cp -r apps/translation-sheet/lang/i18n/jp apps/webjp/src/i18n/locales", "dev": "turbo run dev --parallel", "download:translations": "node apps/translation-sheet/lang/index.mjs", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "migrate": "turbo run migrate --scope suchana", "prisma": "turbo run prisma --filter api", "prisma:migrate-prod": "turbo run prisma:migrate-prod --filter api", "prod": "turbo run prod", "release": "standard-version", "server": "turbo run dev --scope api", "stripe:listen": "stripe listen --forward-to localhost:3004/api/v1/payment/webhook/global-esim", "stripe:listen-airtrip": "stripe listen --forward-to localhost:3004/api/v1/payment/webhook/airtrip", "stripe:listen-global-esim-jp": "stripe listen --forward-to localhost:3004/api/v1/payment/webhook/global-esim-jp", "web": "turbo run dev --scope web", "webjp": "turbo run dev --scope=webjp"}, "dependencies": {"@adminjs/import-export": "^3.0.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/cron": "^2.4.0", "pm2": "5.4.2"}, "devDependencies": {"@repo/eslint-config": "*", "@turbo/gen": "^1.9.7", "eslint": "^7.32.0", "eslint-config-prettier": "^10.0.1", "prettier": "3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "standard-version": "^9.5.0", "turbo": "^1.9.7"}, "packageManager": "yarn@1.22.19", "engines": {"node": "^20.18.1 || 20.2.0 || ^22.x || ^20.x"}, "nohoist": ["**/web", "**/api/**", "**/web/**"]}