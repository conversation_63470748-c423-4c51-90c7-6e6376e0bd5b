"use client";

import { useSearchParams } from "next/navigation";

import { Image, ImageProps, Text } from "@mantine/core";

import { IconHeartHandshake, IconMoodSmileBeam } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import {
  countryNameException,
  getCDNUrl,
  getCountryImage,
} from "@gmesim/fe-utils/src";

const countryWithSellingPoints = ["macau", "hongkong"];

export const CoverImage = ({
  isLGU,
  fitfAffilate,
  isUnlimitedPlan,
  ...props
}: ImageProps & {
  isLGU?: boolean;
  fitfAffilate?: string[];
  isUnlimitedPlan: boolean;
}) => {
  const searchParams = useSearchParams();
  const { i18n } = useTranslation("home");
  const agent =
    searchParams?.has("via") && searchParams?.get("via")
      ? searchParams?.get("via")
      : null;

  const getCoverImage = () => {
    if (typeof isUnlimitedPlan !== "undefined" && !isUnlimitedPlan) {
      return "/assets/banner/korea-banner.webp";
    }

    if (fitfAffilate && fitfAffilate.includes(agent as string)) {
      return `/assets/banner/korea-lgu-banner-${agent}.webp`;
    } else {
      return "/assets/banner/korea-lgu-banner.webp";
    }
  };

  return (
    <div className="relative">
      {countryWithSellingPoints.includes(props.src) &&
      i18n.language === "jp" ? (
        <div className="absolute bottom-4 left-4">
          <Text className="text-2xl font-bold text-white">
            <IconMoodSmileBeam stroke={2} className="inline h-auto w-7" /> 5G
            無制限プラン
          </Text>
          <Text className="text-2xl font-bold text-white">
            <IconHeartHandshake stroke={2} className="inline h-auto w-7" />{" "}
            安心のお客様サポート
          </Text>
        </div>
      ) : null}
      <Image
        maw={"auto"}
        mx="auto"
        {...props}
        className="rounded-lg"
        src={getCDNUrl(
          isLGU
            ? getCoverImage()
            : getCountryImage(
                countryNameException(props.src?.toLowerCase()) || ""
              )
        )}
      />
    </div>
  );
};
export default CoverImage;
