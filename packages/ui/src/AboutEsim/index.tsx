import { Group, Stack, Text } from "@mantine/core";

import {
  IconClockHour10,
  IconRecharging,
  IconSettings,
} from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

const AboutEsim = ({
  isLGU,
  isNeedRegistration,
}: {
  isLGU?: boolean;
  isNeedRegistration?: boolean;
}) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language.startsWith("en");

  return (
    <Stack className="my-5">
      <Stack className="gap-2">
        <Group className="flex-nowrap">
          <IconClockHour10 className="text-primary shrink-0" />
          <Text className="text-sm">
            {isEnglish
              ? "Order after eSIM is issued immediately"
              : "注文後、eSIM即時発行"}
          </Text>
        </Group>
        <Group className="flex-nowrap">
          <IconSettings className="text-primary shrink-0" />
          <Text className="text-sm">
            {isEnglish
              ? "Easy setup, use it right away"
              : "かんたん設定ですぐに使える"}
          </Text>
        </Group>
        <Group className="flex-nowrap">
          <IconRecharging className="text-primary shrink-0" />
          <Text className="text-sm">
            {isEnglish
              ? "Add purchase when gig is used up"
              : "ギガが無くなった追加購入できる"}
          </Text>
        </Group>
      </Stack>
      {isNeedRegistration && (
        <Text className="text-sm">
          <span
            dangerouslySetInnerHTML={{
              __html: t("home:note.caution.hongkongtaiwancontent")
                .replaceAll("li>", "span>")
                .replaceAll("<br/>", "") as string,
            }}
          />{" "}
          <br />
        </Text>
      )}
      {isLGU && (
        <Text className="text-sm">
          {isEnglish
            ? "LGU⁺'s Korean eSIM can be used unlimitedly anytime, anywhere, for both travel and business trips in Korea."
            : "LGU⁺の韓国eSIMは、韓国旅行にも出張にも、いつでもどこでも無制限にデータを使用できます。"}
        </Text>
      )}
    </Stack>
  );
};

export default AboutEsim;
