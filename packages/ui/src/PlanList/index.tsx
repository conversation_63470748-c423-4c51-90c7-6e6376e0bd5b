"use client";

import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";

import { Box, SegmentedControl, Title } from "@mantine/core";

import { Trans, useTranslation } from "react-i18next";

import { ApiService } from "@gmesim/fe-apis/src";
import type { INetwork } from "@gmesim/fe-interfaces/src/INetwork";
import { IProfile } from "@gmesim/fe-interfaces/src/IProfile";
import {
  countryAlias,
  getCDNUrl,
  reverseKebabCase,
} from "@gmesim/fe-utils/src";
import { Currency } from "@gmesim/fe-utils/src/currency";

import PlanForm from "@repo/ui/src/PlanForm";
import TrafficData from "@repo/ui/src/TrafficData";
import { DataType, NetworkGenerationType } from "@repo/ui/src/constants/plans";
import type { IDiscount } from "@repo/ui/src/interfaces/ICoupon";
import type { IPlan, IPlans } from "@repo/ui/src/interfaces/IPlan";

import { generateUrlWithVia } from "../utils";

async function getQuotationAPI(planIds: any[], coupon: string) {
  try {
    const response = await ApiService.getQuotationNew(
      {
        products: planIds,
        couponId: coupon,
      },
      {
        requestOriginServiceName: coupon,
      }
    );

    return {
      data: response.data?.data,
    };
  } catch (err) {
    console.log(err);
    return {
      discountedPrice: null,
    };
  }
}

export const separateDataPlans = (plans: any, dataType: string) => {
  try {
    let organizedPlans: IPlans[] = [];
    const originalPlans = plans?.[dataType]?.filter((plan: any) => {
      if (
        plan?.country?.name === "japan" &&
        plan?.network?.type === "ROAMING"
      ) {
        return false;
      }
      return true;
    });

    for (let x = 0; x < originalPlans?.length || 0; x += 1) {
      const itemIndex = organizedPlans.findIndex(
        (item: IPlans) =>
          item.dataId === originalPlans[x].dataId ||
          (originalPlans[x].name === "unlimited" &&
            item.dataId === originalPlans[x].name)
      );

      if (itemIndex > -1) {
        organizedPlans[itemIndex].plans.push(originalPlans[x]);
      } else {
        organizedPlans.push({
          dataId:
            originalPlans[x].name === "unlimited"
              ? originalPlans[x].name
              : originalPlans[x].dataId,
          validityDays: originalPlans[x].validityDays,
          plans: [originalPlans[x]],
        });
      }
    }

    return organizedPlans;
  } catch (err) {
    console.log(err);
    return [];
  }
};
export default function PlanList({
  props,
  profile,
  isNeedRegistration,
  isLGU,
  country,
  setSelectedPlan,
  setSelectedPlanType,
  dark,
  onAddToCartClick,
  defaultAgent,
  setIsUnlimitedPlan,
  service,
}: {
  props: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  };
  defaultAgent?: string;
  dark?: React.ComponentProps<typeof PlanForm>["dark"];
  onAddToCartClick?: React.ComponentProps<typeof PlanForm>["onAddToCartClick"];
  profile?: IProfile;
  isNeedRegistration?: boolean;
  isLGU?: boolean;
  country?: string;
  setSelectedPlan: (planType: IPlan) => string;
  setSelectedPlanType: (planType: string) => string;
  setIsUnlimitedPlan: (isUnlimied: boolean) => void;
  service?: "AIRTRIP" | "GM_JP" | "GM_EN";
}) {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  // TODO: remove AIRTRIP20 default after campaign
  const agent =
    searchParams?.has("via") && searchParams.get("via")
      ? searchParams.get("via")
      : defaultAgent;
  const [selectedData, setSelectedData] = useState<string>();
  const [discount, setDiscount] = useState<IDiscount>();
  const [activePlanList, setActivePlanList] = useState(
    props.plans?.[DataType.PER_DAY] ? DataType.PER_DAY : DataType.FIXED_DAY
  );
  const separatePlansBy5G = (plans: IPlans[]) => {
    const [firstPlan] = plans;
    try {
      const separatedPlansBy5G = firstPlan?.plans.filter(
        (plan) =>
          plan.network?.networkGeneration === NetworkGenerationType.FIVE_G
      );
      return [
        {
          ...firstPlan,
          dataId: "unlimited_5G",
          plans: separatedPlansBy5G,
        },
      ];
    } catch (err) {
      console.log(err);
      return [];
    }
  };

  const separatePlansBy4G = (plans: IPlans[]) => {
    const [firstPlan] = plans;
    try {
      const separatedPlansBy4G = firstPlan?.plans.filter(
        (plan) =>
          plan.network?.networkGeneration !== NetworkGenerationType.FIVE_G
      );
      return [
        {
          ...firstPlan,
          plans: separatedPlansBy4G,
        },
      ];
    } catch (err) {
      console.log(err);
      return [];
    }
  };

  const fixedDayPlansMemo = useMemo(() => {
    return separateDataPlans(props.plans, DataType.FIXED_DAY);
  }, [props.plans]);

  const perDayPlansMemo = useMemo(() => {
    const separatePlans = separateDataPlans(props.plans, DataType.PER_DAY);
    return separatePlansBy4G(separatePlans);
  }, [props.plans]);

  const perDayPlans5GMemo = useMemo(() => {
    const separatePlans = separateDataPlans(props.plans, DataType.PER_DAY);

    return separatePlansBy5G(separatePlans);
  }, [props.plans]);

  const selectedPlanList =
    activePlanList === DataType.PER_DAY
      ? perDayPlans5GMemo.concat(perDayPlansMemo)
      : fixedDayPlansMemo;

  const countryName = (
    <Trans i18nKey={`countries:${country}`}>
      {countryAlias(reverseKebabCase(country))}
    </Trans>
  );

  const plans = useMemo(() => {
    const nextPerDayPlans5GMemo = perDayPlans5GMemo.filter(
      (item) => item.plans.length > 0
    );
    return activePlanList === DataType.PER_DAY
      ? (nextPerDayPlans5GMemo || []).concat(perDayPlansMemo)
      : fixedDayPlansMemo;
  }, [activePlanList, fixedDayPlansMemo, perDayPlansMemo, perDayPlans5GMemo]);

  const planForm = useCallback(
    (service?: "AIRTRIP" | "GM_JP" | "GM_EN") => (
      <PlanForm
        dark={dark}
        onAddToCartClick={onAddToCartClick}
        plans={plans}
        setSelectedData={setSelectedData}
        selectPlanEvent={selectPlanEvent}
        activePlanList={activePlanList}
        formatPrice={Currency.formatToSelectedNoCode}
        selectedCode={Currency.getSelectedCurrency()}
        discount={discount}
        country={countryName}
        service={service}
      />
    ),
    [activePlanList, fixedDayPlansMemo, perDayPlansMemo, discount]
  );

  const trafficeData = useCallback(
    () => (
      <Box className="traffic-card-container mt-4">
        <TrafficData
          dataLimit={(selectedData as string).replaceAll(" ", "")}
          getCDNUrl={getCDNUrl}
        />
      </Box>
    ),
    [selectedData]
  );

  const selectPlanEvent = async (planId: string, getQuotation?: boolean) => {
    let selectedPlan: IPlan | undefined = undefined;

    selectedPlan = props.plans?.[activePlanList.toUpperCase()]?.find(
      (plan) => plan.planId === planId
    );

    if (selectedPlan) setSelectedPlan(selectedPlan);
    if (getQuotation) {
      if (selectedPlan?.id && agent) {
        const res = await getQuotationAPI(
          [
            {
              optionId: `${selectedPlan?.id}`,
            },
          ],
          agent as string
        );
        setDiscount(res?.data);
      }
    } else {
      if (selectedPlan) {
        if (
          typeof window !== "undefined" &&
          typeof window?.gtag === "function"
        ) {
          window.gtag("event", "begin_checkout", {
            item_list_id: "global-mobile-esim",
            item_list_name: "Global Mobile eSIM",
            items: {
              item_id: selectedPlan.id,
              item_name:
                selectedPlan.name === "unlimited"
                  ? `Unlimited - ${selectedPlan.validityDays} days`
                  : `Fixed ${selectedPlan.dataId} - ${selectedPlan.validityDays} days`,
              index: selectedPlan.id,
              item_brand: "Global Mobile eSIM",
              item_category: "esim",
              item_category2: country,
              item_list_id: "global-mobile-esim",
              item_list_name: "Global Mobile eSIM",
              item_variant: selectedPlan.packageType,
              price: selectedPlan.xe["JPY"],
              quantity: 1,
            },
          });
        }
      }

      if (
        isLGU &&
        !profile?.email &&
        searchParams?.get("via") === "KOREAFREECAMPAIGN"
      ) {
        router.push("/campaign/airtrip/form?via=KOREAFREECAMPAIGN");
        sessionStorage.setItem(
          "returnUrl",
          `/checkout/plan/${selectedPlan?.id}`
        );
      } else {
        router.push(
          generateUrlWithVia(
            `/checkout/plan/${selectedPlan?.id}`,
            //@ts-ignore
            searchParams,
            { defaultVia: defaultAgent }
          )
        );
      }
    }
  };

  useEffect(() => {
    setSelectedPlanType?.(activePlanList);
  }, [activePlanList]);

  useEffect(() => {
    if (
      typeof window?.gtag !== "function" ||
      !selectedPlanList[0]?.plans.length ||
      !selectedPlanList.length
    )
      return;
    window.gtag("event", "view_item_list", {
      item_list_id: "global-mobile-esim",
      item_list_name: "Global Mobile eSIM",
      items:
        activePlanList === DataType.FIXED_DAY
          ? selectedPlanList
          : selectedPlanList[0]?.plans.map((plan) => {
              return {
                item_id: plan.id,
                item_name: `Unlimited - ${plan.validityDays} days`,
                index: plan.id,
                item_brand: "Global Mobile eSIM",
                item_category: "esim",
                item_list_id: "global-mobile-esim",
                item_list_name: "Global Mobile eSIM",
                item_variant: plan.packageType,
                price: plan.xe["JPY"],
                quantity: 1,
              };
            }),
    });
  }, [activePlanList]);

  useEffect(() => {
    if (
      typeof window?.gtag !== "function" &&
      (!props.plans.length ||
        !props.plans[DataType.FIXED_DAY].length ||
        !props.plans[DataType.PER_DAY].length)
    )
      return;
    window.gtag("event", "view_item", {
      item_list_id: "global-mobile-esim",
      item_list_name: "Global Mobile eSIM",
      items: [
        {
          [DataType.FIXED_DAY]: props.plans[DataType.FIXED_DAY].map((plan) => {
            return {
              item_id: plan.id,
              item_name: `Fixed ${plan.dataId} - ${plan.validityDays} days`,
              index: plan.id,
              item_brand: "Global Mobile eSIM",
              item_category: "esim",
              item_list_id: "Global Mobile eSIM",
              item_list_name: "Global Mobile eSIM",
              item_variant: plan.packageType,
              price: plan.xe["JPY"],
              quantity: 1,
            };
          }),
        },
        {
          [DataType.PER_DAY]: props.plans[DataType.PER_DAY].map((plan) => {
            return {
              item_id: plan.id,
              item_name: `Unlimited -  ${plan.validityDays} days`,
              index: plan.id,
              item_brand: "Global Mobile eSIM",
              item_category: "esim",
              item_list_id: "Global Mobile eSIM",
              item_list_name: "Global Mobile eSIM",
              item_variant: plan.packageType,
              price: plan.xe["JPY"],
              quantity: 1,
            };
          }),
        },
      ],
    });
  }, []);

  return (
    <Box className="relative">
      {/* {country === "japan" && perDayPlans5GMemo?.length !== 0 && (
        <SegmentedControl
          w={"100%"}
          radius="lg"
          defaultValue={activePlanList}
          value={activePlanList}
          onChange={setActivePlanList}
          color="app-pink.4"
          classNames={{
            root: `p-0 my-1 bg-white `,
            control: `rounded border border-gray-200 ${styles["mantine-SegmentedControl-control"]}`,
            indicator: "rounded-none",
            label:
              "text-primary data-[active=true]:text-white data-[disabled=true]:text-white data-[disabled=true]:bg-gray-300",
          }}
          data={[
            {
              value: PER_DAY_5G,
              label: (
                <Title order={2} className="text-base">
                  無制限プラン 5G
                </Title>
              ),
            },
            {
              disabled: true,
              value: "",
              label: (
                <Title order={2} className="text-base">
                  データプラン[近日公開!]
                </Title>
              ),
            },
          ]}
        />
      )} */}
      {fixedDayPlansMemo?.length !== 0 && perDayPlansMemo?.length !== 0 && (
        <SegmentedControl
          w={"100%"}
          radius="lg"
          defaultValue={activePlanList}
          value={activePlanList}
          onChange={(h) => {
            if (h === "PER_DAY") {
              setIsUnlimitedPlan(true);
            } else {
              setIsUnlimitedPlan(false);
            }
            setActivePlanList(h);
          }}
          color="app-pink.4"
          classNames={{
            root: "p-0 my-1 bg-white",
            control: "rounded border border-gray-200",
            indicator: "rounded",
            label: "text-primary data-[active=true]:text-white",
          }}
          data={[
            {
              value: DataType.PER_DAY,
              label: (
                <Title order={2} className="text-base">
                  {t("chooseplan:unlimited.plan", "無制限プラン")}
                </Title>
              ),
            },
            {
              value: DataType.FIXED_DAY,
              label: (
                <Title order={2} className="text-base">
                  {t("chooseplan:siminfo-card.fixedday.type", "データプラン")}
                </Title>
              ),
            },
          ]}
        />
      )}

      {planForm(service)}
      {selectedData &&
        activePlanList === DataType.FIXED_DAY &&
        selectedData !== "unlimited" &&
        (i18n.language === "ja" || i18n.language === "jp") &&
        trafficeData()}
    </Box>
  );
}
