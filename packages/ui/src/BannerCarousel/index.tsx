"use client";

/*
 * import components
 */
import { useRef } from "react";

import { Carousel } from "@mantine/carousel";
import { Anchor, Image } from "@mantine/core";

/*
 * import modules and libraries
 */
import Autoplay from "embla-carousel-autoplay";

export default function BannerCarousel({
  slides,
  linkComponent,
}: {
  slides: { link: string; src: string }[];
  linkComponent?: any;
}) {
  const autoplay = useRef(Autoplay({ delay: 3000 }));
  return (
    <Carousel
      plugins={[autoplay.current]}
      onMouseLeave={autoplay.current.reset}
      loop
      withIndicators
      containScroll="trimSnaps"
      slideGap="md"
      classNames={{
        root: "flex justify-center drop-shadow-lg",
        viewport: "w-[calc(100%-70px)] md:w-[calc(100%-85px)] rounded",
        slide: "max-w-md",
        controls: "p-1",
        control:
          "bg-neutral text-white md:size-8 data-[inactive=true]:bg-[#F1F3F5] data-[inactive=true]:text-[#ADB5BD] data-[inactive=true]:cursor-default",
      }}
    >
      {slides.map((slide, index) => (
        <Carousel.Slide key={index}>
          {slide.link ? (
            <Anchor
              className="block max-w-md"
              component={linkComponent}
              href={slide.link}
            >
              <Image className="rounded" src={slide.src} />
            </Anchor>
          ) : (
            <Image className="rounded" src={slide.src} />
          )}
        </Carousel.Slide>
      ))}
    </Carousel>
  );
}
