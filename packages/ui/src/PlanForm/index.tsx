"use client";

import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useState,
} from "react";

import {
  Box,
  Button,
  Combobox,
  ComboboxChevron,
  ComboboxDropdown,
  ComboboxEmpty,
  ComboboxOption,
  ComboboxOptions,
  ComboboxTarget,
  Drawer,
  Flex,
  Group,
  InputPlaceholder,
  ScrollAreaAutosize,
  Stack,
  Text,
  TextInput,
  em,
  useCombobox,
} from "@mantine/core";
import {
  useDisclosure,
  useMediaQuery,
  useScrollIntoView,
} from "@mantine/hooks";

import { useTranslation } from "react-i18next";

import type { IDiscount } from "../interfaces/ICoupon";
import type { IPlan, IPlans } from "../interfaces/IPlan";

const PlanForm = ({
  gtag,
  plans,
  formatPrice,
  setSelectedData,
  selectPlanEvent,
  activePlanList,
  dark,
  country,
  selectedCode,
  discount,
  onAddToCartClick,
  service,
  ...props
}: {
  noShadow?: boolean;
  gtag?: (...args: any[]) => void | undefined;
  plans: IPlans[];
  hideCheckoutButtons?: boolean;
  setSelectedData: Dispatch<SetStateAction<string | undefined>>;
  // eslint-disable-next-line no-unused-vars
  selectPlanEvent: (planId: string, getQuotation?: boolean) => void;
  activePlanList: string;
  // eslint-disable-next-line no-unused-vars
  formatPrice: (plan: Pick<IPlan, "xe" | "price">) => string;
  dark?: boolean;
  country?: JSX.Element;
  selectedCode: {
    code: string;
    name: string;
    sign: string;
  };
  discount?: IDiscount;
  onAddToCartClick?: (d: any) => void;
  service?: "AIRTRIP" | "GM_JP" | "GM_EN";
}) => {
  const { t } = useTranslation();
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [selectedDataOption, setSelectedDataOption] = useState<IPlans>();
  const [selectedDataDays, setSelectedDataDays] = useState<IPlan>();
  const [isDrawerDataOpen, setIsDataDrawer] = useDisclosure(false);
  const [isDrawerDaysOpen, setIsDaysDrawer] = useDisclosure(false);
  const { scrollIntoView, targetRef } = useScrollIntoView<HTMLDivElement>({
    offset: 32,
    duration: 500,
  });
  const dataCombobox = useCombobox();
  const daysCombobox = useCombobox();

  // TODO: can be move to utils
  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });

  useEffect(() => {
    setSelectedDataOption(undefined);
    setSelectedDataDays(undefined);
    let defaultPlan = plans[0];

    if (plans && plans.length > 0) {
      if (activePlanList === "FIXED_DAY") {
        for (let x = 0; x < plans.length; x += 1) {
          if (defaultPlan.plans.length < plans[x].plans.length) {
            defaultPlan = plans[x];
          }
        }
        setSelectedDataOption(defaultPlan);
      } else {
        setSelectedDataOption(defaultPlan);
      }
      setSelectedData(defaultPlan.dataId);

      if (defaultPlan.plans && defaultPlan.plans.length > 0) {
        setSelectedDataDays(defaultPlan.plans[0]);
      } else {
        setSelectedDataDays(undefined);
      }
    }
  }, [plans]);

  useEffect(() => {
    const sameDaysPlan = selectedDataOption?.plans.find(
      (item: IPlan) => item.validityDays === selectedDataDays?.validityDays
    );

    if (sameDaysPlan) {
      setSelectedDataDays(sameDaysPlan);
    } else {
      if (selectedDataOption?.plans && selectedDataOption?.plans.length > 0) {
        setSelectedDataDays(selectedDataOption?.plans[0]);
      } else {
        setSelectedDataDays(undefined);
      }
    }
  }, [selectedDataOption]);

  useEffect(() => {
    if (isMobileSize && activePlanList === "FIXED_DAY") {
      scrollIntoView();
    }
  }, [activePlanList]);

  useEffect(() => {
    if (!selectedDataDays) return;
    selectPlanEvent(selectedDataDays?.planId as string, true);
  }, [selectedDataDays]);

  useEffect(() => {
    if (!gtag || !selectedDataDays) return;

    gtag("event", "select_item", {
      item_list_id: "global-mobile-esim",
      item_list_name: "Global Mobile eSIM",
      items: {
        item_id: selectedDataDays?.id,
        item_name:
          selectedDataDays?.name === "unlimited"
            ? `Unlimited - ${selectedDataDays?.validityDays} days`
            : `Fixed ${selectedDataDays?.dataId} - ${selectedDataDays?.validityDays} days`,
        index: selectedDataDays?.id,
        item_brand: "Global Mobile eSIM",
        item_category: "esim",
        item_category2: country,
        item_list_id: "global-mobile-esim",
        item_list_name: "Global Mobile eSIM",
        item_variant: selectedDataDays?.packageType,
        price: selectedDataDays?.xe["JPY"],
        quantity: 1,
      },
    });
  }, [selectedDataDays]);

  const dataOptions = useCallback(
    () =>
      plans && plans.length !== 0 ? (
        <ComboboxOptions>
          <ScrollAreaAutosize mah={isMobileSize ? 300 : 200} type="scroll">
            {plans.map((item, index) => (
              <ComboboxOption
                value={
                  item.dataId === "unlimited_5G"
                    ? `5G ${t("chooseplan:unlimited.plan")}`
                    : item.dataId === "unlimited"
                      ? `4G ${t("chooseplan:unlimited.plan")}`
                      : item.dataId
                }
                key={item.dataId + index}
                active={item === selectedDataOption}
                className="border-t py-4 text-center last:mb-10 last:border-b last:border-b-0 md:border-t-0 md:py-2 md:text-left last:md:mb-0"
                onClick={() => {
                  setSelectedData(item.dataId);
                  setSelectedDataOption(item);
                  setIsDataDrawer.close();
                }}
              >
                <Text
                  className={`${
                    item === selectedDataOption ? "text-primary font-bold" : ""
                  } text-sm`}
                >
                  {item.dataId === "unlimited_5G"
                    ? `5G ${t("chooseplan:unlimited.plan")}`
                    : item.dataId === "unlimited"
                      ? `4G ${t("chooseplan:unlimited.plan")}`
                      : item.dataId}
                </Text>
              </ComboboxOption>
            ))}
          </ScrollAreaAutosize>
        </ComboboxOptions>
      ) : (
        <ComboboxEmpty className="mt-6 md:mt-0">
          {t("chooseplan:data.notfound", "利用日数が見つかりません。")}
        </ComboboxEmpty>
      ),
    [plans, selectedDataOption]
  );
  const daysOptions = useCallback(
    () =>
      selectedDataOption && selectedDataOption.plans.length !== 0 ? (
        <ComboboxOptions>
          <ScrollAreaAutosize mah={isMobileSize ? 300 : 200} type="scroll">
            {selectedDataOption.plans.map((item) => {
              const price = formatPrice({
                xe: item.xe,
                price: item.price,
              });

              return (
                <ComboboxOption
                  value={item.planId}
                  key={item.planId}
                  active={item === selectedDataDays}
                  classNames={{
                    option: "flex justify-between",
                  }}
                  className="border-t py-4 text-center last:mb-10 last:border-b last:border-b-0 md:border-t-0 md:py-2 md:text-left last:md:mb-0"
                  onClick={() => {
                    setSelectedDataDays(item);
                    setIsDaysDrawer.close();
                  }}
                >
                  <>
                    <Text
                      className={`${
                        item === selectedDataDays
                          ? "text-primary font-bold"
                          : ""
                      } text-sm`}
                    >
                      {t("common:siminfo-card.validity.unit", {
                        count: item.validityDays, // Get the usage plan days without unit
                        defaultValue: "{{ count }}day",
                        defaultValue_other: "{{ count }}days",
                      })}
                    </Text>
                    <div>
                      <Text span className={`text-sm`}>
                        {selectedCode?.code !== "JPY" ? selectedCode?.sign : ""}
                        {price}
                        {selectedCode?.code === "JPY" ? "円" : ""}
                      </Text>
                    </div>
                  </>
                </ComboboxOption>
              );
            })}
          </ScrollAreaAutosize>
        </ComboboxOptions>
      ) : (
        <ComboboxEmpty className="mt-6 md:mt-0">
          {t("chooseplan:usagedays.notfound", "利用日数が見つかりません。")}
        </ComboboxEmpty>
      ),
    [selectedDataOption, selectedDataDays]
  );
  return (
    <Box
      className={`card-bg my-4 rounded-lg p-4 ${props.noShadow ? "" : "shadow-lg"} `}
      ref={targetRef}
    >
      <Stack>
        <Text className="text-lg font-bold">
          {country || ""}
          {t("chooseplan:countryselection", ": choose your plan")}
        </Text>
        <Flex className="flex-col gap-4 md:flex-row">
          <Combobox
            onOptionSubmit={() => {
              dataCombobox.closeDropdown();
            }}
            store={dataCombobox}
          >
            <ComboboxTarget>
              <TextInput
                size="md"
                component="button"
                label={t("chooseplan:data.type")}
                placeholder={t(
                  "chooseplan:data.placeholder",
                  "データ容量を選択する"
                )}
                value={
                  selectedDataOption?.dataId === "unlimited_5G"
                    ? `5G ${t("chooseplan:unlimited.plan")}`
                    : selectedDataOption?.dataId === "unlimited"
                      ? `4G ${t("chooseplan:unlimited.plan")}`
                      : selectedDataOption?.dataId
                }
                onChange={() => {
                  dataCombobox.openDropdown();
                  dataCombobox.updateSelectedOptionIndex();
                }}
                onClick={() =>
                  isMobileSize
                    ? setIsDataDrawer.open()
                    : dataCombobox.openDropdown()
                }
                onFocus={() =>
                  isMobileSize ? {} : dataCombobox.openDropdown()
                }
                onBlur={() => dataCombobox.closeDropdown()}
                rightSection={
                  <ComboboxChevron
                    onClick={() =>
                      selectedDataOption?.dataId !== "unlimited"
                        ? isMobileSize
                          ? setIsDataDrawer.open()
                          : dataCombobox.openDropdown()
                        : {}
                    }
                  />
                }
                classNames={{
                  root: "md:flex-1",
                  label: "text-sm font-bold",
                  input: "rounded-lg border-black",
                }}
              >
                {selectedDataOption ? (
                  selectedDataOption?.dataId === "unlimited_5G" ? (
                    `5G ${t("chooseplan:unlimited.plan")}`
                  ) : selectedDataOption?.dataId === "unlimited" ? (
                    `4G ${t("chooseplan:unlimited.plan")}`
                  ) : (
                    selectedDataOption?.dataId
                  )
                ) : (
                  <InputPlaceholder>
                    {t("chooseplan:data.placeholder", "データ容量を選択する")}
                  </InputPlaceholder>
                )}
              </TextInput>
            </ComboboxTarget>
            <ComboboxDropdown
              visibleFrom="md"
              className="z-10 rounded-lg shadow-lg"
            >
              {dataOptions()}
            </ComboboxDropdown>
            <Drawer
              radius="md"
              size="360px"
              opened={isDrawerDataOpen}
              onClose={setIsDataDrawer.close}
              title={t("chooseplan:data.type", "データ容量")}
              hiddenFrom="md"
              position="bottom"
              classNames={{
                header: "justify-center",
                title: "text-base font-bold",
                close: "absolute right-3",
                body: "p-0",
              }}
            >
              {dataOptions()}
            </Drawer>
          </Combobox>

          <Combobox
            classNames={{
              option: "px-8 md:px-4",
            }}
            onOptionSubmit={() => {
              daysCombobox.closeDropdown();
            }}
            store={daysCombobox}
          >
            <ComboboxTarget>
              <TextInput
                size="md"
                component="button"
                label={t("chooseplan:usagedays.title")}
                placeholder={t(
                  "chooseplan:usagedays.placeholder",
                  "利用日数を選択する"
                )}
                value={selectedDataDays?.dataId}
                onChange={() => {
                  daysCombobox.openDropdown();
                  daysCombobox.updateSelectedOptionIndex();
                }}
                onClick={() =>
                  isMobileSize
                    ? setIsDaysDrawer.open()
                    : daysCombobox.openDropdown()
                }
                onFocus={() => daysCombobox.openDropdown()}
                onBlur={() => daysCombobox.closeDropdown()}
                rightSection={
                  <ComboboxChevron
                    onClick={() =>
                      isMobileSize
                        ? setIsDaysDrawer.open()
                        : daysCombobox.openDropdown()
                    }
                  />
                }
                classNames={{
                  root: "md:flex-1",
                  label: "text-sm font-bold",
                  input: "rounded-lg border-black",
                }}
              >
                {selectedDataDays ? (
                  <>
                    {t("common:siminfo-card.validity.unit", {
                      count: selectedDataDays.validityDays, // Get the usage plan days without unit
                      defaultValue: "{{ count }}day",
                      defaultValue_other: "{{ count }}days",
                    })}
                  </>
                ) : (
                  <InputPlaceholder>
                    {t(
                      "chooseplan:usagedays.placeholder",
                      "利用日数を選択する"
                    )}
                  </InputPlaceholder>
                )}
              </TextInput>
            </ComboboxTarget>
            <ComboboxDropdown
              visibleFrom="md"
              className="z-10 rounded-lg shadow-lg"
            >
              {daysOptions()}
            </ComboboxDropdown>
            <Drawer
              radius="md"
              size="360px"
              opened={isDrawerDaysOpen}
              onClose={setIsDaysDrawer.close}
              title={t("chooseplan:usagedays.title")}
              hiddenFrom="md"
              position="bottom"
              classNames={{
                header: "justify-center",
                title: "text-base font-bold",
                close: "absolute right-3",
                body: "p-0",
              }}
            >
              {daysOptions()}
            </Drawer>
          </Combobox>
        </Flex>
        <Group className="justify-between">
          <Text className="text-sm font-bold">{t("chooseplan:total")}</Text>

          <Text className="text-sm font-bold">
            {selectedCode?.code === "JPY" ? (
              <>
                <span
                  className={
                    discount?.discount
                      ? "mr-2 font-normal text-gray-400 line-through"
                      : ""
                  }
                >
                  <span className={discount?.discount ? "" : "text-xl"}>
                    {selectedDataDays
                      ? formatPrice({
                          xe: selectedDataDays?.xe,
                          price: selectedDataDays?.price,
                        })
                      : "--"}
                  </span>
                  円
                </span>
                {discount?.discount && (
                  <>
                    <span className="text-xl">
                      {selectedDataDays
                        ? formatPrice({
                            xe: discount?.xe,
                            price: discount?.total as number,
                          })
                        : "--"}
                    </span>
                    円
                  </>
                )}
              </>
            ) : (
              <>
                {selectedCode?.code}
                <span
                  className={
                    discount
                      ? "mx-2 font-normal text-gray-400 line-through"
                      : ""
                  }
                >
                  {selectedCode?.sign}
                  <span className={discount ? "" : "text-xl"}>
                    {selectedDataDays
                      ? formatPrice({
                          xe: selectedDataDays?.xe,
                          price: selectedDataDays?.price,
                        })
                      : "--"}
                  </span>
                </span>
                {discount && (
                  <>
                    {selectedCode?.sign}
                    <span className="text-xl">
                      {selectedDataDays
                        ? formatPrice({
                            xe: discount?.xe,
                            price: discount?.total as number,
                          })
                        : "--"}
                    </span>
                  </>
                )}
              </>
            )}
          </Text>
        </Group>
      </Stack>

      {props.hideCheckoutButtons ? null : (
        <Stack className="mt-4 text-center" gap={5}>
          <Button
            size="md"
            color="app-action.4"
            classNames={{
              root: "rounded-lg max-w-sm mx-auto w-full",
              label: `${dark ? "text-black" : ""}`,
            }}
            disabled={!selectedDataDays}
            loading={isSubmitted}
            onClick={() => {
              setIsSubmitted(true);
              selectPlanEvent(selectedDataDays?.planId as string);
            }}
          >
            {t("chooseplan:cta.button")}
          </Button>
          {onAddToCartClick && (
            <Button
              size="md"
              classNames={{
                root: "rounded-lg max-w-sm mx-auto w-full",
                label: `${dark ? "text-white" : ""}`,
              }}
              disabled={!selectedDataDays}
              loading={isSubmitted}
              onClick={() => onAddToCartClick(selectedDataDays)}
            >
              {t("common:cart.menu.addtocart")}
            </Button>
          )}
        </Stack>
      )}
    </Box>
  );
};
export default PlanForm;
