"use client";

import dynamic from "next/dynamic";
import { useState } from "react";

import {
  <PERSON><PERSON>,
  <PERSON>,
  Collapse,
  ScrollAreaAutosize,
  Stack,
  Text,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";

import { IconMinus, IconPlus } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import type { INetwork } from "@gmesim/fe-interfaces/src/INetwork";

import AboutEsim from "@repo/ui/src/AboutEsim";

import SpecsList from "../DynamicSpecsList";
import PlanForm from "../PlanForm";
import PlanList from "../PlanList";
import { IPlan } from "../interfaces/IPlan";
import { IProfile } from "../interfaces/IProfile";

const DynamicRegionalCountries = dynamic(
  () => import("@repo/ui/src/modals/RegionalCountries")
);

const Actions = ({
  dark,
  isLGU,
  profile,
  isRegional,
  country,
  network,
  subcountries,
  regionData,
  isNeedRegistration,
  setSelectedPlan,
  onAddToCartClick,
  defaultAgent,
  setSelectedPlanType,
  setIsUnlimitedPlan,
  service,
}: {
  dark?: React.ComponentProps<typeof PlanForm>["dark"];
  onAddToCartClick?: React.ComponentProps<typeof PlanForm>["onAddToCartClick"];
  profile?: IProfile;
  isLGU?: boolean;
  isRegional?: boolean;
  country: string;
  network: INetwork[];
  subcountries: string;
  // TO DO: add regionData Interface
  regionData: any | null;
  isNeedRegistration?: boolean;
  setSelectedPlan: (planType: IPlan) => string;
  defaultAgent?: string;
  setSelectedPlanType: (planType: string) => string;
  setIsUnlimitedPlan: (isUnlimited: boolean) => void;
  service?: "AIRTRIP" | "GM_JP" | "GM_EN";
}) => {
  const { t } = useTranslation();
  const [specs, setSpecs] = useDisclosure(true);
  const [plan, setPlan] = useState<IPlan | null>(null);
  const [planType, setPlanType] = useState<string>("");
  const [planValidityDay, setPlanValidityDay] = useState<number>(1);
  const [regionalCountriesModal, setRegionalCountriesModalModal] =
    useDisclosure();
  const handleSelectedPlanType = (event: string) => {
    setPlanType(event);
    setSelectedPlanType(event);
  };

  const handleSelectedPlan = (event: IPlan) => {
    setSelectedPlan(event);
    setPlan(event);
    setPlanValidityDay(event?.validityDays);
  };

  const region = country ? t(`countries:${country}`) : "";

  return (
    <>
      <Stack>
        <PlanList
          dark={dark}
          profile={profile}
          //@ts-ignore
          setSelectedPlan={handleSelectedPlan}
          defaultAgent={defaultAgent}
          //@ts-ignore
          setSelectedPlanType={handleSelectedPlanType}
          props={regionData}
          isNeedRegistration={isNeedRegistration}
          isLGU={isLGU}
          country={country}
          onAddToCartClick={onAddToCartClick}
          setIsUnlimitedPlan={setIsUnlimitedPlan}
          service={service}
        />
        <Button
          fullWidth
          size="xs"
          radius="md"
          variant="outline"
          color="app-pink.4"
          bg="white"
          className=""
          classNames={{
            root: "shadow hover:bg-white relative",
            section: "absolute right-2",
          }}
          rightSection={
            specs ? (
              <IconMinus
                size={20}
                className="bg-primary rounded-full text-white"
              />
            ) : (
              <IconPlus
                size={20}
                className="bg-primary rounded-full text-white"
              />
            )
          }
          onClick={setSpecs.toggle}
        >
          <Text className="text-sm font-bold text-black">
            {t("region:about-esim", { region })}
          </Text>
        </Button>
      </Stack>
      <Collapse in={specs} className="mb-4">
        <AboutEsim isLGU={isLGU} isNeedRegistration={isNeedRegistration} />
        <Card className="rounded-lg p-4 shadow-lg">
          <ScrollAreaAutosize type="scroll" className="max-h-fit">
            <SpecsList
              // @ts-expect-error
              key={plan?.serviceProvider?.name}
              // @ts-expect-error
              plan={plan}
              selectedValidityDay={planValidityDay}
              selectedPlanType={planType}
              onClick={(options) => {
                if (options.type === "specs") {
                  setRegionalCountriesModalModal.open();
                }
              }}
              isLGU={isLGU}
              country={country}
              network={network}
              isModal
            />
          </ScrollAreaAutosize>
        </Card>
      </Collapse>
      {isRegional && (
        <DynamicRegionalCountries
          title={t("region:coverage")}
          opened={regionalCountriesModal}
          onClose={setRegionalCountriesModalModal.close}
          subcountries={subcountries}
        />
      )}
    </>
  );
};
export default Actions;
