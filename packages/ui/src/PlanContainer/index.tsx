"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

import { Image, Stack, Text, em } from "@mantine/core";
import { useMediaQuery, useScrollIntoView } from "@mantine/hooks";

import type { INetwork } from "@gmesim/fe-interfaces/src/INetwork";
import {
  bannerSlides,
  getCDNUrl,
  regionalOrCountry,
} from "@gmesim/fe-utils/src";
import {
  AGENT,
  checkIsValidAirTripAgent,
} from "@gmesim/fe-utils/src/agent-handler";

import BannerCarousel from "@repo/ui/src/BannerCarousel";
import SectionContent from "@repo/ui/src/common/SectionContent";
import type { IPlan } from "@repo/ui/src/interfaces/IPlan";

import CountryHeader from "../CountryHeader";
import CoverImage from "../CoverImage";
import PlanForm from "../PlanForm";
import Actions from "../PlanSelectActions";
import { IProfile } from "../interfaces/IProfile";

export default function PlanContainer({
  dark,
  agent,
  service,
  isNeedRegistration,
  isLGU,
  isJapan,
  country,
  regionData,
  setSelectedPlan,
  setSelectedPlanType,
  checkExistAgentCode,
  profile,
  onAddToCartClick,
  defaultAgent,
}: {
  dark: React.ComponentProps<typeof PlanForm>["dark"];
  defaultAgent?: string;
  service: "AIRTRIP" | "GM_JP" | "GM_EN";
  onAddToCartClick?: React.ComponentProps<typeof PlanForm>["onAddToCartClick"];
  profile?: IProfile;
  checkExistAgentCode?: (params: {
    agent_code: string;
  }) => Promise<{ is_exists: boolean }>;
  setSelectedPlan: (planType: IPlan) => string;
  setSelectedPlanType: (planType: string) => string;
  agent?: string;
  isNeedRegistration?: boolean;
  isLGU?: boolean;
  isJapan?: boolean;
  country: string;
  regionData: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  } | null;
}) {
  const [isUnlimitedPlan, setIsUnlimitedPlan] = useState(true);

  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });
  const { scrollIntoView, targetRef } = useScrollIntoView<HTMLDivElement>({
    offset: isMobileSize ? 70 : 250,
    duration: 250,
  });
  const AIRTRIP_AGENT = checkIsValidAirTripAgent(agent);

  const checkIsExistAgentCode = async (agentCode: string | undefined) => {
    if (!checkExistAgentCode) return;
    try {
      let agent: string | undefined = agentCode || AIRTRIP_AGENT;
      const listAgentsStorage: string[] = localStorage.getItem("agents")
        ? JSON.parse(localStorage.getItem("agents") || "")
        : [];
      const result = await checkExistAgentCode?.({ agent_code: agent || "" });
      if (!result.is_exists) {
        localStorage.removeItem("agent");
      } else {
        if (
          agent &&
          listAgentsStorage[listAgentsStorage.length - 1] !== agent
        ) {
          listAgentsStorage.push(agent);
        }

        if (listAgentsStorage.length > 10) {
          listAgentsStorage.shift();
        }

        localStorage.setItem("agents", JSON.stringify(listAgentsStorage));

        if (agent) localStorage.setItem("agent", agent);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const searchParams = useSearchParams();
  const via = searchParams?.get("via");
  const scroll = searchParams?.get("scroll");

  useEffect(() => {
    if (scroll) {
      scrollIntoView({
        alignment: "start",
      });
    }
  }, [country]);

  useEffect(() => {
    checkIsExistAgentCode(agent).catch((err) => {
      console.log(err);
    });
  }, [agent]);

  const bannerSlidesMemo = useMemo(() => {
    if (service === "AIRTRIP") {
      return bannerSlides.concat([
        {
          link: "",
          src: getCDNUrl("/assets/gm-airtrip-paypay.png"),
        },
      ]);
    }
    if (service === "GM_JP") {
      return bannerSlides.concat([
        {
          link: "",
          src: getCDNUrl("/assets/gm-webjp-paypay.png"),
        },
      ]);
    }
    return bannerSlides;
  }, [service]);

  return (
    <>
      <div ref={targetRef}></div>
      {regionData ? (
        <>
          <SectionContent noHeader noFooter small>
            {(!isJapan || service !== "AIRTRIP") && (
              <Stack>
                <CoverImage
                  maw={"auto"}
                  h={isLGU ? undefined : "13rem"}
                  mx="auto"
                  src={regionData.countryProfile?.name}
                  isLGU={isLGU}
                  isUnlimitedPlan={
                    service === "GM_EN" ? false : isUnlimitedPlan
                  }
                />
                {isLGU && via === AGENT.AIRTRIP_KOREA && (
                  <Image
                    className="max-w-auto max-h-auto self-center"
                    src={getCDNUrl("/assets/campaign/free-esim-campaign.png")}
                  />
                )}
              </Stack>
            )}
            {service !== "GM_EN" && (
              <div className="py-3">
                <BannerCarousel
                  slides={bannerSlidesMemo}
                  linkComponent={Link}
                />
              </div>
            )}
            <CountryHeader
              isLGU={isLGU}
              isRegional={
                regionalOrCountry(regionData.countryProfile?.name) !== "Country"
              }
              country={country}
              subcountries={regionData.countryProfile?.subCountries || "[]"}
            />
          </SectionContent>
          <SectionContent noHeader small>
            <Stack className="gap-2">
              <Actions
                dark={dark}
                defaultAgent={defaultAgent}
                onAddToCartClick={onAddToCartClick}
                profile={profile}
                setSelectedPlan={setSelectedPlan}
                setSelectedPlanType={setSelectedPlanType}
                isLGU={isLGU}
                isRegional={
                  regionalOrCountry(regionData.countryProfile?.name) !==
                  "Country"
                }
                network={regionData.network}
                country={country}
                subcountries={regionData.countryProfile?.subCountries || "[]"}
                regionData={regionData}
                isNeedRegistration={isNeedRegistration}
                setIsUnlimitedPlan={setIsUnlimitedPlan}
                service={service}
              />
            </Stack>
          </SectionContent>
        </>
      ) : (
        <SectionContent small noHeader>
          <Text className="pt-8 text-center text-base">
            {service === "GM_EN"
              ? "No data plan found."
              : "データプランが見つかりませんでした。"}
          </Text>
        </SectionContent>
      )}
    </>
  );
}
