"use client";

import dynamic from "next/dynamic";
import { use<PERSON>ara<PERSON>, useSearchParams } from "next/navigation";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import {
  Box,
  Button,
  Center,
  Flex,
  Image,
  Skeleton,
  Stack,
  Title,
} from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import { useInterval, useTimeout } from "@mantine/hooks";

import { Elements, PaymentRequestButtonElement } from "@stripe/react-stripe-js";
import { Stripe, loadStripe } from "@stripe/stripe-js";
import isEmpty from "lodash/isEmpty";
import { GoogleReCaptcha } from "react-google-recaptcha-v3";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

import { ApiService } from "@gmesim/fe-apis/src";
import {
  ICouponDiscounted,
  IPlan,
  IProfile,
  IQuotation,
} from "@gmesim/fe-interfaces/src";
import { getCDNUrl } from "@gmesim/fe-utils/src";
import { Currency } from "@gmesim/fe-utils/src/currency";
import Storage from "@gmesim/fe-utils/src/storage";

import SectionContent from "@repo/ui/src/common/SectionContent";

import CheckoutPageSkeleton from "../CheckoutPageSkeleton";
import CouponContainer from "../CouponContainer";
import StickyContainer from "../StickyContainer";
import TopupImporantInfo from "../TopupContainer/TopupImportantInfo";
import PaymentCheckoutBox from "../checkout/PaymentCheckoutBox";
import { ERR_GENERIC_REQUIRED } from "../constants";
import { useTopup } from "../hooks/useTopup";
import { z } from "../i18n/ja-zod";

const DynamicStepper = dynamic(() => import("@repo/ui/src/checkout/Stepper"));
const DynamicUserInfo = dynamic(() => import("@repo/ui/src/checkout/UserInfo"));

const schema = z.object({
  email: z.string().email("メールアドレスが無効です"),
  firstName: z
    .string()
    .min(1, ERR_GENERIC_REQUIRED)
    .regex(/^[a-zA-Z. ]*$/, "ローマ字で記入してください"),
  lastName: z
    .string()
    .min(1, ERR_GENERIC_REQUIRED)
    .regex(/^[a-zA-Z. ]*$/, "ローマ字で記入してください"),
});

interface IProps {
  isLoading?: boolean;
  profile?: IProfile;
  discount?: ICouponDiscounted | null;
  renderCheckoutButton?: () => JSX.Element;
  isMobile?: boolean;
  quotation?: IQuotation;
  source: "single" | "multiple";
  plan?: IPlan;
  toggleLoading: (loading?: boolean) => void;
  stripePublishableKey: string;
  renderBasketCheckout?: (props: IProps) => JSX.Element;
  onCouponChange?: (coupon?: string | null) => void;
  topupId?: string;
  origin?: "airtrip" | "gmesim";
}

const currentStep = 1;

const StickyPaymentButton = ({
  paymentMethod,
  isLoading,
  isCompatibilityChecked,
  isTermsChecked,
  isUserInfoValid,
  isCardDataValid,
  isDataDeleteChecked,
  isNeedRegistrationChecked,
  isNeedRegistration,
  isTopup,
  isLGUTopup,
  handleCheckout,
  handlePayPayPayment,
  recaptcha,
  paymentRequest,
  stripe,
  setIsErrorOnAgreement,
}: {
  paymentMethod: string;
  isLoading?: boolean;
  isCompatibilityChecked: boolean;
  isTermsChecked: boolean;
  isUserInfoValid: boolean;
  isCardDataValid: boolean;
  isDataDeleteChecked: boolean;
  isNeedRegistrationChecked: boolean;
  isNeedRegistration: boolean;
  isTopup: boolean;
  isLGUTopup: boolean;
  handleCheckout: () => void;
  handlePayPayPayment: () => void;
  recaptcha: string | null;
  paymentRequest: any;
  stripe: any;
  setIsErrorOnAgreement: (isError: boolean) => void;
}) => {
  const { t } = useTranslation();

  return (
    <>
      {paymentMethod === "invoice" && (
        <Box className="sticky bottom-0 w-full bg-white py-2">
          <Button
            loading={isLoading}
            size="lg"
            onClick={handleCheckout}
            disabled={
              !isCompatibilityChecked || !isTermsChecked || !isUserInfoValid
            }
            fullWidth
          >
            購入する
          </Button>
        </Box>
      )}
      {paymentMethod === "credit" && (
        <Box className="sticky bottom-0 w-full bg-white py-2">
          <Button
            loading={isLoading}
            size="lg"
            onClick={handleCheckout}
            disabled={
              !recaptcha?.length ||
              !isCompatibilityChecked ||
              !isTermsChecked ||
              !isUserInfoValid ||
              !isCardDataValid ||
              (isTopup && !isDataDeleteChecked && !isLGUTopup) ||
              (isNeedRegistration && !isNeedRegistrationChecked)
            }
            fullWidth
            color="app-action.4"
            classNames={{
              label: "text-black",
            }}
          >
            {t("payment:pay-now")}
          </Button>
        </Box>
      )}
      {paymentMethod === "paypay" && (
        <Box className="sticky bottom-0 w-full bg-white py-2">
          <Button
            loading={isLoading}
            size="lg"
            onClick={handlePayPayPayment}
            disabled={
              !isCompatibilityChecked || !isTermsChecked || !isUserInfoValid
            }
            fullWidth
            color="app-action.4"
            classNames={{ label: "text-black" }}
          >
            {t("payment:pay-now")}
          </Button>
        </Box>
      )}
      {paymentRequest &&
        !["credit", "invoice", "paypay"].includes(paymentMethod) && (
          <StickyContainer>
            <PaymentRequestButtonElement
              onClick={(e) => {
                if (
                  !isCompatibilityChecked ||
                  (isNeedRegistration && !isNeedRegistrationChecked)
                ) {
                  setIsErrorOnAgreement(true);
                  e.preventDefault();
                  return;
                }
                sessionStorage.setItem(
                  "isNeedRegistrationChecked",
                  isNeedRegistrationChecked.toString()
                );
                setIsErrorOnAgreement(false);
              }}
              options={{
                paymentRequest,
                style: {
                  paymentRequestButton: {
                    height: "50px", // same as "Pay Now" button
                  },
                },
              }}
            />
          </StickyContainer>
        )}
    </>
  );
};

const CheckoutContainer = (props: IProps) => {
  const {
    toggleLoading,
    renderBasketCheckout,
    isLoading,
    profile,
    stripePublishableKey,
  } = props;
  const { isTopup, topupOrderId, isLGU: isLGUTopup } = useTopup();
  const { planId } = useParams() || {};
  const stripeRef = useRef<Promise<Stripe | null>>();
  const formRef = useRef<HTMLFormElement>(null);
  const [recaptcha, setRecaptcha] = useState<string | null>("");
  const [captchaResetKey, setCaptchaResetKey] = useState(Date.now());
  const [paymentMethod, setPaymentMethod] = useState("credit");
  const [paymentRequest, setPaymentRequest] = useState();
  const [isErrorOnAgreement, setIsErrorOnAgreement] = useState(false);
  const [isCheckoutDisabled, setIsCheckoutDisabled] = useState({
    number: false,
    expiry: false,
    cvc: false,
  });
  const searchParams = useSearchParams();
  const affiliate =
    Storage.getInstance("cookie").getFromCookie("affiliate") || undefined;

  const isLGU = useMemo(
    () =>
      props.plan?.country?.name.toLowerCase() === "korea" ||
      props.quotation?.orders?.some(
        (order) => order?.plan?.country.name?.toLowerCase?.() === "korea"
      ),
    [props.plan, props.quotation]
  );
  const isNeedRegistration = useMemo(
    () =>
      ["taiwan", "hongkong"].includes(props.plan?.country?.name as string) ||
      props.quotation?.orders?.some((order) =>
        ["taiwan", "hongkong"].includes(order?.plan?.country.name)
      ),
    [props.plan, props.quotation]
  );

  const form = useForm({
    initialValues: {
      email: "",
      firstName: "",
      lastName: "",
    },
    validate: zodResolver(schema),
    validateInputOnBlur: true,
  });

  const [isCompatibilityChecked, setIsCompatibilityChecked] =
    useState<boolean>(false);
  const [isTermsChecked, setIsTermsChecked] = useState<boolean>(false);
  const [isDataDeleteChecked, setIsDataDeleteChecked] =
    useState<boolean>(false);
  const [isUserInfoValid, setIsUserInfoValid] = useState<boolean>(
    profile ? true : false
  );
  const [isCardDataValid, setIsCardDataValid] = useState<boolean>(
    Object.values(isCheckoutDisabled).every((value) => value === true)
      ? true
      : false
  );
  const [isNeedRegistrationChecked, setIsNeedRegistrationChecked] =
    useState<boolean>(false);
  const { t, i18n } = useTranslation();

  const handlePayPayPayment = useCallback(async () => {
    if (!isCompatibilityChecked || !isTermsChecked || !isUserInfoValid) return;
    toggleLoading(true);

    let result: any;
    const orderPayload = {
      recaptha: recaptcha || "",
      firstName: form?.values?.firstName,
      lastName: form?.values?.lastName,
      email: form?.values?.email,
      locale: i18n.language || "jp",
      currency:
        i18n.language === "jp" ? "JPY" : Currency.getSelectedCurrency()?.code,
      couponId: props.quotation?.coupon?.code,
      products: props.quotation?.orders
        ? props.quotation.orders.map((order: any) => ({
            optionId: order.plan.id.toString(),
            insurance: order.insured ? "insured" : undefined,
          }))
        : [],
      source: props.origin !== "gmesim" ? props.origin : "global-esim-jp",
      affiliate,
      paymentMethod: "paypay",
    };

    const postOrderApiResponse = profile
      ? await ApiService.postOrder(orderPayload)
      : await ApiService.postOrderAsGuest(orderPayload);

    const orderResult = postOrderApiResponse.data?.data;

    const paypayPayload = {
      orderId: orderResult.orderId,
      amount: orderResult.payment.amount,
      customerId: profile?.id || orderResult.userId,
      customerEmail: orderPayload.email || profile?.email,
      customerName:
        constructFullName(orderPayload.firstName, orderPayload.lastName) ||
        constructFullName(profile?.firstName, profile?.lastName),
      planIds:
        orderPayload.products.length > 0
          ? orderPayload.products.map((product) => product.optionId)
          : [],
      source: props.origin !== "gmesim" ? props.origin : "global-esim-jp",
      requestOriginUrl: `${window.location.protocol}//${window.location.host}`,
    };

    try {
      const paymentResult = profile
        ? await ApiService.initiatePayPayPayment(paypayPayload)
        : await ApiService.initiatePayPayPaymentAsGuest(paypayPayload);

      result = paymentResult.data?.data;

      if (result?.action && result?.fields) {
        // Create and submit the form to redirect to the SBPS payment page
        const form = document.createElement("form");
        form.method = result.method || "POST";
        form.action = result.action; // The SBPS payment URL (e.g., https://stbfep.sps-system.com/xxxx/xxxxxxxx)
        form.acceptCharset = "shift_jis";

        Object.keys(result.fields).forEach((key) => {
          const input = document.createElement("input");
          input.type = "hidden";
          input.name = key;
          input.value = result.fields[key];
          form.appendChild(input);
        });

        document.body.appendChild(form);

        form.submit();
      }
    } catch (err: any) {
      alert(err);
      console.log(err);
      toggleLoading(false);
      return;
    }
  }, [
    isCompatibilityChecked,
    isTermsChecked,
    isUserInfoValid,
    recaptcha,
    form?.values,
    i18n.language,
    props.quotation,
    profile,
    toggleLoading,
  ]);

  const constructFullName = (first?: string, last?: string): string => {
    return [first, last].filter(Boolean).join(" ");
  };

  const handleCheckout = useCallback(() => {
    if (!recaptcha) {
      alert(`Please complete reCAPTCHA before proceeding`);
      return;
    }

    formRef.current?.dispatchEvent(
      new Event("submit", {
        bubbles: true,
        cancelable: true,
      })
    );
  }, [formRef, recaptcha]);

  const onVerify = useCallback((token: string) => {
    setRecaptcha(token);
  }, []);

  const { data: setUpIntentQuery, isLoading: isSetupIntentLoading } = useQuery(
    "setupIntent",
    () => {
      return ApiService.getClientSecret({
        planId: "",
        source: process.env.NEXT_PUBLIC_SOURCE,
        userPool: process.env.NEXT_PUBLIC_USER_POOL,
        corporateId: profile?.corporate?.id,
      });
    }
  );
  const interval = useInterval(() => setCaptchaResetKey(Date.now()), 30000);

  useEffect(() => {
    interval.start();
    return interval.stop;
  }, []);

  useEffect(() => {
    if (
      profile ||
      (isEmpty(form.errors) &&
        form.values.email &&
        form.values.firstName &&
        form.values.lastName)
    ) {
      setIsUserInfoValid(true);
    } else {
      setIsUserInfoValid(false);
    }
  }, [form, profile]);

  useEffect(() => {
    setIsCardDataValid(
      Object.values(isCheckoutDisabled).every((value) => value === true)
        ? true
        : false
    );
  }, [isCheckoutDisabled]);

  const finalPriceJPY = useMemo(() => {
    return props.quotation?.xe["JPY"] as number;
  }, [props.quotation]);

  const finalPrice = useMemo(() => {
    return props.quotation?.xe["USD"] as number;
  }, [props.quotation]);

  useEffect(() => {
    stripeRef.current = loadStripe(stripePublishableKey as string);
  }, [stripePublishableKey]);

  const couponFromLocalStorage = useMemo(() => {
    return Storage.getInstance().get("coupon");
  }, []);

  const handleCouponApplied = useCallback((coupon: string | null) => {
    props?.onCouponChange?.(coupon);
  }, []);

  const isFREEeSIM = useMemo(() => {
    return finalPriceJPY < 1;
  }, [finalPriceJPY]);

  // Sometimes quotation data might not available and at that time we just reload.
  const { start, clear } = useTimeout(([quotation]) => {
    if (!quotation) {
      window.location.reload();
    }
  }, 10000);

  useEffect(() => {
    start(props.quotation);
    return clear;
  }, [props.quotation]);

  return (
    <>
      <Center>
        <GoogleReCaptcha
          onVerify={onVerify}
          refreshReCaptcha={captchaResetKey}
        />
      </Center>

      {setUpIntentQuery?.data?.data && stripeRef?.current ? (
        <>
          <Elements
            stripe={stripeRef?.current}
            options={{
              clientSecret: setUpIntentQuery?.data?.data.client_secret,
              appearance: {
                theme: "flat",
                variables: {
                  fontFamily: ' "Gill Sans", sans-serif',
                  fontLineHeight: "1.5",
                  borderRadius: "10px",
                  colorBackground: "#F6F8FA",
                  accessibleColorOnColorPrimary: "#262626",
                },
                rules: {
                  ".Block": {
                    backgroundColor: "var(--colorBackground)",
                    boxShadow: "none",
                    padding: "12px",
                  },
                  ".Input": {
                    padding: "12px",
                  },
                  ".Input:disabled, .Input--invalid:disabled": {
                    color: "lightgray",
                  },
                  ".Tab": {
                    padding: "10px 12px 8px 12px",
                    border: "none",
                  },
                  ".Tab:hover": {
                    border: "none",
                    boxShadow:
                      "0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 7px rgba(18, 42, 66, 0.04)",
                  },
                  ".Tab--selected, .Tab--selected:focus, .Tab--selected:hover":
                    {
                      border: "none",
                      backgroundColor: "#fff",
                      boxShadow:
                        "0 0 0 1.5px var(--colorPrimaryText), 0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 7px rgba(18, 42, 66, 0.04)",
                    },
                  ".Label": {
                    fontWeight: "500",
                  },
                },
              },
            }}
          >
            <DynamicStepper currentStep={currentStep} />
            <div className="mb-[10px]" />
            <SectionContent small noHeader isWhiteBg noFooter>
              <Stack className="gap-6 pt-6">
                {props.quotation ? (
                  renderBasketCheckout?.(props)
                ) : (
                  <Stack className="pt-6">
                    <Skeleton height={25} className="shadow-xs rounded-md" />
                    <Skeleton height={200} className="shadow-xs rounded-md" />
                  </Stack>
                )}
                {!profile && (
                  <Box mih={"250px"}>
                    <React.Suspense
                      fallback={
                        <Stack className="pt-6">
                          <Skeleton
                            height={25}
                            className="shadow-xs rounded-md"
                          />
                          <Skeleton
                            height={200}
                            className="shadow-xs rounded-md"
                          />
                        </Stack>
                      }
                    >
                      <DynamicUserInfo form={form} origin={props?.origin} />
                    </React.Suspense>
                  </Box>
                )}
                {props.source === "single" &&
                  props.quotation?.orders?.[0].plan &&
                  props.quotation?.orders?.[0].plan.topupEnabled &&
                  props.quotation?.orders?.[0].plan.packageType ===
                    "FIXED_DAY" &&
                  isTopup && (
                    <TopupImporantInfo
                      dataId={props.quotation?.orders[0].plan.dataId}
                      validityDays={
                        props.quotation?.orders[0].plan.validityDays
                      }
                    />
                  )}
                <CouponContainer
                  defaultCouponCode={
                    (searchParams.get("coupon") ||
                      couponFromLocalStorage) as string
                  }
                  defaultCoupon={props.quotation?.coupon}
                  onValidCoupon={handleCouponApplied}
                  planId={searchParams.get("planId") + ""}
                />
                <Stack className="gap-4" mt={"-30px"}>
                  {isFREEeSIM ? null : (
                    <>
                      <Title order={4}>{t("payment:method.title")}</Title>
                    </>
                  )}{" "}
                  <Box>
                    {((props.plan || props.quotation) && (
                      <PaymentCheckoutBox
                        topupOrderId={topupOrderId}
                        isFREEeSIM={isFREEeSIM}
                        shouldSkipPayment={
                          isFREEeSIM || !!profile?.corporate?.id
                        }
                        agent={null}
                        quotation={props.quotation}
                        isAgreementError={isErrorOnAgreement}
                        clientSecret={
                          setUpIntentQuery?.data?.data.client_secret
                        }
                        setPaymentMethod={setPaymentMethod}
                        setPaymentRequest={setPaymentRequest}
                        price={finalPriceJPY + ""}
                        planId={planId as string}
                        ref={formRef}
                        // TODO: should move this to state management or refactor checkout components
                        isCompatibilityChecked={isCompatibilityChecked}
                        setIsCompatibilityChecked={setIsCompatibilityChecked}
                        isTermsChecked={isTermsChecked}
                        setIsTermsChecked={setIsTermsChecked}
                        isCheckoutDisabled={isCheckoutDisabled}
                        setIsCheckoutDisabled={setIsCheckoutDisabled}
                        isNeedRegistrationChecked={isNeedRegistrationChecked}
                        setIsNeedRegistrationChecked={
                          setIsNeedRegistrationChecked
                        }
                        //
                        isLGU={isLGU}
                        form={form}
                        isNeedRegistration={isNeedRegistration}
                        recaptcha={recaptcha ? recaptcha : undefined}
                        setCaptchaResetKey={setCaptchaResetKey}
                        profile={profile}
                        toggleLoading={toggleLoading}
                        isDataDeleteChecked={isDataDeleteChecked}
                        setIsDataDeleteChecked={setIsDataDeleteChecked}
                      ></PaymentCheckoutBox>
                    )) || (
                      <Stack className="pt-6">
                        <Skeleton
                          height={25}
                          className="shadow-xs rounded-md"
                        />
                        <Skeleton
                          height={200}
                          className="shadow-xs rounded-md"
                        />
                      </Stack>
                    )}
                  </Box>
                  <Flex h={{ md: "60px", sm: "100%" }} justify={"center"}>
                    <Image
                      width={"100%"}
                      h={"100%"}
                      src={getCDNUrl("/assets/secure-payment-stripe.png")}
                      alt="payment image."
                    />
                  </Flex>{" "}
                  {(props.plan || props.quotation) && !isFREEeSIM && (
                    <StickyPaymentButton
                      isCompatibilityChecked={isCompatibilityChecked}
                      isTermsChecked={isTermsChecked}
                      isUserInfoValid={isUserInfoValid}
                      isDataDeleteChecked={isDataDeleteChecked}
                      isNeedRegistrationChecked={isNeedRegistrationChecked}
                      isCardDataValid={isCardDataValid}
                      isLoading={isLoading}
                      handleCheckout={handleCheckout}
                      setIsErrorOnAgreement={setIsErrorOnAgreement}
                      paymentMethod={paymentMethod}
                      paymentRequest={paymentRequest}
                      isNeedRegistration={isNeedRegistration ?? false}
                      isTopup={!!isTopup}
                      isLGUTopup={!!isLGUTopup}
                      handlePayPayPayment={handlePayPayPayment}
                      recaptcha={recaptcha}
                      stripe={stripeRef?.current}
                    />
                  )}
                </Stack>
              </Stack>
              {isFREEeSIM && (
                <Box className="sticky bottom-0 w-full bg-white py-2">
                  <Button
                    loading={isLoading}
                    size="lg"
                    onClick={handleCheckout}
                    disabled={
                      !recaptcha?.length ||
                      !isCompatibilityChecked ||
                      !isTermsChecked ||
                      !isUserInfoValid ||
                      (isTopup && !isDataDeleteChecked) ||
                      (isNeedRegistration && !isNeedRegistrationChecked)
                    }
                    fullWidth
                    color="app-action.4"
                    classNames={{
                      root: "disabled:bg-gray-300",
                    }}
                  >
                    eSIMをGET！
                  </Button>
                </Box>
              )}
            </SectionContent>
          </Elements>
        </>
      ) : (
        <CheckoutPageSkeleton name="When stripe and setup query are not loaded" />
      )}
    </>
  );
};
export default CheckoutContainer;
