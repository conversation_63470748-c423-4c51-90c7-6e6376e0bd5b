"use client";

import { useCallback, useMemo } from "react";

import { Anchor, Flex, Image, List, Text, ThemeIcon } from "@mantine/core";

import { Trans, useTranslation } from "react-i18next";

import {
  capitalizeFirstLetter,
  countryAlias,
  getCDNUrl,
  regionalOrCountry,
} from "@gmesim/fe-utils/src";

import { DataType } from "@repo/ui/src/constants/plans";
import { INetwork } from "@repo/ui/src/interfaces/INetwork";
import type { IPlan } from "@repo/ui/src/interfaces/IPlan";

//provide the proper translation key based on country
const countryFupMap: Record<string, string> = {
  korea: "korea",
  japan: "japan",
  guam: "guam",
  hongkong: "multi1",
  macau: "multi1",
  china: "multi2",
  cambodia: "multi2",
  france: "multi2",
  germany: "multi2",
  indonesia: "multi2",
  italy: "multi2",
  laos: "multi2",
  malaysia: "multi2",
  singapore: "multi2",
  taiwan: "multi2",
  uk: "multi2",
  vietnam: "vietnam",
  thailand: "thailand",
};
const verificationRequiredCountrys: string[] = ["hongkong", "taiwan"];
const SpecsList = (props: {
  plan: IPlan;
  selectedValidityDay: number;
  isModal?: boolean;
  isLGU?: boolean;
  country: string;
  network: INetwork[];
  onClick?: (option: { type: string; value: Array<string> }) => void;
  selectedPlanType: string;
}) => {
  const selectedPlanType = props.selectedPlanType;
  const selectedValidityDay = props.selectedValidityDay;
  const { t, i18n } = useTranslation();
  const regexForKorea = props.country === "korea" ? /^\-|\b3G\b\/?/g : /^\-/;
  const specNetwork =
    props.country === "japan"
      ? [props.network[0]]
      : selectedPlanType === DataType.FIXED_DAY && props.isLGU
        ? [props.network[1]]
        : selectedPlanType !== DataType.FIXED_DAY && props.isLGU
          ? [props.network[0]]
          : props.network;
  const isVietnamLinkKorea =
    props.country === "vietnam" && selectedValidityDay >= 4;
  const isThaiLinkKorea =
    props.country === "thailand" &&
    selectedValidityDay >= 4 &&
    selectedValidityDay <= 10;

  // @ts-expect-error
  const isKDDI = props.plan?.serviceProvider?.name === "KDDI";

  const provideCountryFupDetails = useCallback(
    (country: string) => {
      const normalizedCountry = country.toLowerCase();
      const key = countryFupMap[normalizedCountry] ?? "other";

      const isVietnam = normalizedCountry === "vietnam";
      const isThailand = normalizedCountry === "thailand";
      const isJapan = normalizedCountry === "japan";
      const useOther =
        (isVietnam && selectedValidityDay <= 3) ||
        (isThailand &&
          (selectedValidityDay <= 3 ||
            (selectedValidityDay >= 10 && selectedValidityDay <= 30)));

      const valueKey = useOther ? "other" : key;

      const baseItem = {
        name: t("region:fup.title", "Usage days count"),
        value: t(`region:fup.${valueKey}.desc1`),
        iconURL: "specs-other.png",
      };

      // --- HOTFIX: Japan/KDDI Fair Usage Policy ---
      const lang = i18n.language;
      const isJapanese = lang.startsWith("jp");
      const fairUsageJP =
        "通信会社の公平利用のポリシーに基づき速度が低下する場合があります。";
      const fairUsageEN =
        "Your speed may be reduced according to the carrier's fair usage policy.";
      const unlimitedItem =
        isJapan && isKDDI
          ? {
              name: t("region:fup.title2", "Reduced Speed < Unlimited Plan >"),
              value: isJapanese ? fairUsageJP : fairUsageEN,
              iconURL: "specs-other.png",
            }
          : {
              name: t("region:fup.title2", "Reduced Speed < Unlimited Plan >"),
              value: t(`region:fup.${valueKey}.desc2`),
              iconURL: "specs-other.png",
            };
      // --- END HOTFIX ---

      if (selectedPlanType === DataType.FIXED_DAY) {
        return [baseItem];
      } else {
        return [baseItem, unlimitedItem];
      }
    },
    [selectedPlanType, selectedValidityDay, t, props.plan]
  );

  const specs = useMemo(
    () => [
      {
        name: t("region:coverage"),
        value:
          regionalOrCountry(props.country) === "Country" ? (
            t(
              `countries:${props.country}`,
              capitalizeFirstLetter(countryAlias(props.country || ""))
            )
          ) : (
            <Anchor
              td={"underline"}
              onClick={() =>
                props.onClick?.({
                  type: "specs",
                  value: [],
                })
              }
            >
              {t("region:availablecountries")}
            </Anchor>
          ),
        iconURL: "coverage.png",
        invert: true as const,
      },
      {
        name: t("region:network", "NETWORK"),
        value: `${
          isKDDI
            ? "KDDI 5G"
            : isThaiLinkKorea
              ? "AIS 5G/4G"
              : isVietnamLinkKorea
                ? "Vinafone 4G/LTE"
                : specNetwork
                    ?.map?.((item) =>
                      `${item.name} ${item.networkGeneration}`?.replace?.(
                        regexForKorea,
                        ""
                      )
                    )
                    .join(" - ")
        }`,
        iconURL: "specs-network.png",
      },
      {
        name: t("region:plantype", "PLAN TYPE"),
        value:
          isVietnamLinkKorea || isThaiLinkKorea
            ? t("region:datawithcalls", "Data and can make and receive calls")
            : props.isLGU && selectedPlanType !== DataType.FIXED_DAY
              ? t("region:datawithcalls", "Data and can make and receive calls")
              : t("region:dataonlynocalls", "Data only (No Calls)"),
        iconURL: "specs-type.png",
      },
      {
        name: t("region:tethering", "TETHERING"),
        value: t("region:tetheringpossible", "Tethering possible"),
        iconURL: "specs-tethering.svg",
      },
      {
        name: t("region:ekyc", "eKYC (IDENTITY VERIFICATION)"),
        value: !verificationRequiredCountrys.includes(props.country)
          ? t("region:notrequired", "Not required")
          : t("region:required", "Required"),
        iconURL: "specs-ekyc.png",
      },
      {
        name: t("region:activationpolicy.title", "ACTIVATION POLICY"),
        value: t("region:activationpolicy.desc", "ACTIVATION POLICY"),
        iconURL: "specs-activation.png",
      },
      {
        name: t("region:activationperiod.title", "ACTIVATION PERIOD"),
        value: t(
          "region:activationperiod.desc",
          "Can be activated within 180 days of purchase."
        ),
        iconURL: "specs-activation.png",
      },

      ...provideCountryFupDetails(props.country),
      {
        name: t("region:other", "OTHER"),
        value: t("region:nonrefundable", "Non-refundable"),
        iconURL: "specs-other.png",
      },
    ],
    [selectedPlanType, selectedValidityDay]
  );

  return (
    <List
      size="sm"
      classNames={{
        root: "mb-4",
        item: `${props.isModal ? "px-4 py-3" : "p-0"} border-b`,
      }}
    >
      {specs.map((spec, index) => (
        <List.Item
          key={index}
          icon={
            <ThemeIcon
              color={spec.invert ? "dark" : "transparent"}
              size={24}
              radius="xl"
            >
              <Image src={getCDNUrl(`/assets/${spec.iconURL}`)} />
            </ThemeIcon>
          }
        >
          <Flex direction={"column"}>
            <Text className="text-neutral text-xs font-semibold">
              {spec.name}
            </Text>
            <Text className="text-base">
              <Trans components={{ br: <br /> }}>{spec.value}</Trans>
            </Text>
          </Flex>
        </List.Item>
      ))}
    </List>
  );
};
export default SpecsList;
