import { useEffect, useState } from "react";

import { useQueryClient } from "react-query";

import { ApiService } from "@gmesim/fe-apis/src";

import { IOrder } from "../../interfaces/IOrder";

interface UseUpdateUsageProps {
  orders?: IOrder[];
}

export const useUpdateUsage = ({ orders }: UseUpdateUsageProps) => {
  const queryClient = useQueryClient();
  const [invalidated, setInvalidated] = useState(false);
  const [ordersLength, setOrdersLength] = useState(0);

  useEffect(() => {
    // If there are no orders, then we don't need to update the usage
    if (!orders || orders.length === 0 || !orders[0]) {
      return;
    }

    // Check if the orders length is the same as the previous orders length
    // If it is, then we don't need to update the usage
    if (invalidated && ordersLength === orders.length) {
      return;
    }

    // Reset the invalidated state
    setInvalidated(false);

    setOrdersLength(orders.length);

    // Get the orders that are not activated
    const notActivatedOrders = orders.filter((order) => !order.isActivated);

    // Get the usage for the orders that are not activated
    const promises = notActivatedOrders.map((order) => {
      return ApiService.getOrder(order.orderId + "", {
        type: "usage",
      });
    });

    // Wait for all promises to be settled before checking if the query should be invalidated
    Promise.allSettled(promises)
      .then((results) => {
        let shouldInvalidate = false;

        results.forEach((result) => {
          if (result.status === "fulfilled") {
            const { data } = result.value.data;

            // If one of the orders is activated, then we need to invalidate the query
            if (data.order.isActivated) {
              shouldInvalidate = true;
            }
          }
        });

        if (shouldInvalidate) {
          setInvalidated(true);
          queryClient.invalidateQueries("orders-active");
        }
      })
      .catch((error) => {});
  }, [orders]);
};
