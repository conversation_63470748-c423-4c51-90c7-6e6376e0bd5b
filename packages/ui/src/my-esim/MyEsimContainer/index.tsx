"use client";

import { useMemo, useState } from "react";

import { Stack } from "@mantine/core";

import InfiniteScroll from "react-infinite-scroll-component";
import { useInfiniteQuery } from "react-query";

import { ApiService } from "@gmesim/fe-apis/src";

import EsimList from "./MyEsimList";
import MyEsimCard from "./MyEsimList/EsimCardDetail/EsimCard";
import LoadingMyEsimCard from "./MyEsimList/LoadingCard";
import { useUpdateUsage } from "./use-update-usage";

const QUERY_LIMIT = 10;

const MyEsimContainer = ({
  status,
  isActive,
  onTopupClick,
}: {
  onTopupClick?: React.ComponentProps<typeof MyEsimCard>["onTopupClick"];
  status: "active" | "expired";
  isActive?: boolean;
}) => {
  const [total, setTotal] = useState(0);

  const { data, fetchNextPage, isFetchingNextPage, isLoading } =
    useInfiniteQuery({
      enabled: isActive,
      keepPreviousData: true,
      queryKey: `orders-${status}`,
      queryFn: async ({ pageParam = 1 }) => {
        if (!isActive) return;
        const data = await ApiService.getOrders({
          sort_by: "id:desc",
          limit: QUERY_LIMIT,
          page: pageParam,
          status: status,
        });
        setTotal(data?.data?.data?.total);
        return data?.data?.data?.data;
      },
      getNextPageParam: (lastPage, allPages) => {
        return allPages.length + 1;
      },
      cacheTime: 100000,
    });

  const allOrders = useMemo(() => {
    return data?.pages.flat(1);
  }, [data]);

  useUpdateUsage({ orders: allOrders, isActiveTab: isActive });

  const hasMoreData = useMemo(() => {
    const fetched = (data?.pages || [])?.length * QUERY_LIMIT;
    return fetched <= total;
  }, [data?.pages, total]);

  return (
    <EsimList
      isLoading={isLoading}
      fetchNextPage={fetchNextPage}
      items={data?.pages || []}
      status={status}
      onTopupClick={onTopupClick}
      renderWrapper={(items, children) => {
        return (
          <InfiniteScroll
            dataLength={items.length} //This is important field to render the next data
            next={fetchNextPage}
            hasMore={hasMoreData}
            loader={
              isLoading || isFetchingNextPage ? (
                <Stack mt={10}>
                  <LoadingMyEsimCard />
                  <LoadingMyEsimCard />
                  <LoadingMyEsimCard />
                  <LoadingMyEsimCard />
                </Stack>
              ) : null
            }
          >
            {children}
          </InfiniteScroll>
        );
      }}
    />
  );
};
export default MyEsimContainer;
