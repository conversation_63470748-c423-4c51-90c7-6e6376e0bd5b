import { Divider, Text } from "@mantine/core";

import { IconClock } from "@tabler/icons-react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

import { IOrder } from "@gmesim/fe-interfaces/src";

import { CardDetail } from "./CardDetail";

interface LGUTopupInfoProps {
  order: IOrder;
}

export function LGUTopupInfo({ order }: LGUTopupInfoProps) {
  const { t } = useTranslation();

  const lguEndDateTime = order.expireTime
    ? format(new Date(order.expireTime), "yyyy-MM-dd/HH:mm")
    : "";

  return (
    <>
      <CardDetail
        icon={<IconClock stroke={2} />}
        label={t("common:topup.endtatetimelgu")}
        value={lguEndDateTime}
      />
      <Divider color="#3E53A3" />

      <Text className="pl-2">
        {t("common:topup.importantinformation.warning4lgu")}
      </Text>
    </>
  );
}
