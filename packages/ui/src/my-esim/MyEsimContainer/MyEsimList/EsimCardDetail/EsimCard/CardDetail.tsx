import { Divider, Group, Text, ThemeIcon } from "@mantine/core";

import { Trans } from "react-i18next";

import { countryAlias } from "@gmesim/fe-utils/src";

export const CardDetail = ({
  icon,
  label,
  value,
}: {
  icon: React.ReactNode;
  label: string;
  value: string;
}) => {
  return (
    <>
      <Divider color="#3E53A3" />
      <Group w="100%" justify={"flex-start"} align={"center"}>
        <ThemeIcon color={"transparent"} size={22} radius="xl">
          {icon}
        </ThemeIcon>
        <Text className="font-semibold text-white">{label}</Text>
        <Text className="ml-auto font-semibold capitalize text-white">
          <Trans i18nKey={`countries:${value}`}>
            {/* @ts-ignore */}
            {countryAlias(value)}
          </Trans>
        </Text>
      </Group>
    </>
  );
};
