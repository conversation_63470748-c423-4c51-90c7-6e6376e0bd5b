import { Carousel } from "@mantine/carousel";

import { Review } from "../../../types";
import { ReviewCard } from "./ReviewCard";

interface ReviewsCarouselProps {
  reviews: Review[];
}

export default function ReviewsCarousel({ reviews }: ReviewsCarouselProps) {
  return (
    <Carousel
      slideSize={{ base: "100%", xs: "50%", sm: "33.333%", md: "25%" }}
      slideGap="md"
      align="start"
      loop
      classNames={{
        container: "p-4",
        root: "w-full",
        controls: "left-[-20px] right-[-20px] lg:left-[-40px] md:right-[-40px]",
      }}
    >
      {reviews.map((review, index) => {
        const key = `${review.id} ${index}`;

        return (
          <Carousel.Slide key={key} className="w-full">
            <ReviewCard review={review} />
          </Carousel.Slide>
        );
      })}
    </Carousel>
  );
}
