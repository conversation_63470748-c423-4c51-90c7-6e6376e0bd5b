import { Button, Flex, Rating, Title } from "@mantine/core";

interface ReviewSummaryProps {
  averageRating?: number;
  totalReviews?: number;
  toggleReviewDrawerOpen: () => void;
}

export default function ReviewSummary({
  averageRating,
  totalReviews,
  toggleReviewDrawerOpen,
}: ReviewSummaryProps) {
  return (
    <Flex className="flex-col justify-center">
      <Flex className="flex-col items-center gap-2 md:flex-row md:justify-center">
        <Rating
          classNames={{
            starSymbol: "md:size-10",
          }}
          size="xl"
          color="app-pink.4"
          value={averageRating}
          fractions={4}
          readOnly
        />
        <Title className="text-primary text-xl font-bold md:text-2xl">
          {averageRating}
        </Title>
      </Flex>
      <Button
        onClick={toggleReviewDrawerOpen}
        className="max-w-40 self-center text-base text-gray-500 underline"
        variant="transparent"
      >
        レビュー数{totalReviews}
      </Button>
    </Flex>
  );
}
