import { Card, Rating, Stack, Text } from "@mantine/core";

import { Review } from "../../../types";

interface ReviewCardProps {
  review: Review;
}

export function ReviewCard({ review }: ReviewCardProps) {
  const createdAt = review.created_at
    ? new Date(review.created_at).toLocaleString(undefined, {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    : null;

  return (
    <Card shadow="md" padding="md" className="h-full w-full">
      <Stack gap="lg">
        <Stack gap="xs">
          <Rating value={Number(review.score)} readOnly />
          {review.name && (
            <Text size="xs" className="text-gray-500">
              {review.name}
            </Text>
          )}
        </Stack>

        <Stack>
          {review.title && (
            <Text fw={400} size="md" lineClamp={4}>
              {review.title}
            </Text>
          )}
          {review.content && (
            <Text fw={200} lineClamp={5} size="sm">
              {review.content}
            </Text>
          )}
          {createdAt && (
            <Text size="xs" className="text-gray-400">
              {createdAt}{" "}
            </Text>
          )}
        </Stack>
      </Stack>
    </Card>
  );
}
