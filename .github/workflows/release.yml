name: ReleaseFlow (PRODUCTION)

on:
  workflow_dispatch:
    inputs:
      message:
        description: Release note
        default: "New Release"
        type: string

      environment:
        description: "Please select environment"
        required: true
        default: "production"
        type: choice
        options:
          - development
          - production
jobs:
  release_processor:
    runs-on: ubuntu-latest
    outputs:
      service: ${{ steps.releaseinfo.outputs.service }}
      version: ${{ steps.releaseinfo.outputs.version }}
      tag: ${{ steps.releaseinfo.outputs.tag }}
    steps:
      - name: Check out repository
        uses: actions/checkout@v2

      - name: Prompt user to select a tag
        run: |
          echo "Available tags:"
          echo "${{ github.ref_name }}"

      - uses: actions/github-script@v7
        id: releases
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            const tag = "${{ github.ref_name }}";
            const [service,version] =  tag.split("-");
            return `${service}:${version}:${tag}`;

      - name: Github scriptss output
        id: releaseinfo
        env:
          all: ${{ steps.releases.outputs.result }}
        run: |
          all=$(echo "$all" | xargs)
          IFS=':' read -r service version tag <<< $all
          echo "service=$service" >> $GITHUB_OUTPUT
          echo "version=$version" >> $GITHUB_OUTPUT
          echo "tag=$tag" >> $GITHUB_OUTPUT

  start:
    name: "Release"
    needs: release_processor
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: ${{  needs.release_processor.outputs.service == 'api' && 'api'||'web'}}
      parameterstore: ${{ needs.release_processor.outputs.service == 'api' && 'prod-ipc-front-dkr.global-esim.env' || needs.release_processor.outputs.service == 'airtrip' && 'prod-ipc-front-dkr.global-esim-jp.env' || needs.release_processor.outputs.service == 'web'&& 'prod-ipc-front-dkr.global-esim-jp.env' || needs.release_processor.outputs.service == 'webjp' &&  'prod-ipc-front-dkr.global-esim-jp.env'}}
      release_tag: ${{needs.release_processor.outputs.tag }}
      port_map: ${{ needs.release_processor.outputs.service == 'api' && '28341:3001' || needs.release_processor.outputs.service == 'web' && '28340:3000' || needs.release_processor.outputs.service == 'webjp' &&  '28345:3000' ||needs.release_processor.outputs.service == 'airtrip' &&  '28346:3000'}}
      environment: "production"
      service: ${{needs.release_processor.outputs.service }}
    secrets: inherit
  notify:
    needs: [start, release_processor]
    runs-on: ubuntu-latest
    steps:
      - uses: InboundPlatform/server-script/.github/actions/releaselog@main
        with:
          tag: ${{needs.release_processor.outputs.tag }}
          service: ${{inputs.service}}
          actor: ${{github.actor}}
          message: ${{ inputs.message }}
          repo: ${{ github.event.repository.name }}
