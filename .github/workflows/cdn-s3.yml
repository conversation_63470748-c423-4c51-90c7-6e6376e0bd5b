name: AWS S3 CDN Sync

# Controls when the workflow will run
on:
  pull_request:
    branches:
      - main
      - develop
  push:
    branches:
      - cdn-s3
      - develop
      - main

  workflow_dispatch:
    inputs:
      environment:
        description: "Please select environment"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - production

      service:
        description: "Please select services"
        required: true
        default: "web"
        type: choice
        options:
          - web
          - webjp
          - airtrip

jobs:
  web:
    permissions:
      id-token: write
      contents: read
    name: Sync Web
    uses: InboundPlatform/server-script/.github/workflows/cdn-s3.yml@main
    secrets: inherit
    with:
      localPath: "./workdir/apps/web/public/"
      remotePath: ${{ github.ref == 'refs/heads/main' && 's3://ipc-cdn-prod-gmobile/global-esim/' || 's3://ipc-cdn-dev-gmobile/global-esim/' }}
      environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'development' }}
    if: |
      github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main' || github.event.inputs.service == 'web'

  webjp:
    if: ${{ github.event.inputs.service == 'webjp' }}
    permissions:
      id-token: write
      contents: read
    name: Sync WebJP
    uses: InboundPlatform/server-script/.github/workflows/cdn-s3.yml@main
    secrets: inherit
    with:
      localPath: "./workdir/apps/webjp/public/"
      remotePath: ${{ github.event.inputs.environment == 'development' && 's3://ipc-cdn-dev-gmobile/global-esim/webjp/' || 's3://ipc-cdn-prod-gmobile/global-esim/webjp/' }}
      environment: ${{ github.event.inputs.environment }}

  airtrip:
    if: ${{ github.event.inputs.service == 'airtrip' }}
    permissions:
      id-token: write
      contents: read
    name: Sync Airtrip
    uses: InboundPlatform/server-script/.github/workflows/cdn-s3.yml@main
    secrets: inherit
    with:
      localPath: "./workdir/apps/airtrip/public/"
      remotePath: ${{ github.event.inputs.environment == 'development' && 's3://ipc-cdn-dev-gmobile/global-esim/airtrip/' || 's3://ipc-cdn-prod-gmobile/global-esim/airtrip/' }}
      environment: ${{ github.event.inputs.environment }}
