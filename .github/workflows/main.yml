# This is a basic workflow to help you get started with Actions
name: Build and Deployment

# Controls when the workflow will run
on:
  push:
    branches:
      - feat/rating-reviews
  # Triggers the workflow on push or pull request events but only for the main branch
  workflow_dispatch:
    inputs:
      environment:
        description: "Please select environment"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - production

      service:
        description: "Please select services"
        required: true
        default: "web"
        type: choice
        options:
          - api
          - workers
          - web
          - webjp
          - airtrip
          - payment

jobs:
  # API Deployment for `feat/reviews-ratings` branch
  deployAPIForReviewsRatings:
    if: ${{ github.ref == 'refs/heads/feat/rating-reviews' }}
    name: Deploy API -> feat/rating-reviews -> development
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: "api"
      release_tag: ""
      port_map: "28341:3001"
      environment: "development"
      service: "api"
      parameterstore: "dev-ipc-front-dkr.global-esim.env"
    secrets: inherit

  buildWorker:
    if: ${{ github.event.inputs.service == 'worker' && github.event.inputs.environment == 'development' }}
    name: ${{ github.event.repository.name }} -> ${{ github.event.inputs.service }} -> ${{  github.event.inputs.environment }}
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: "api"
      release_tag: ""
      port_map: "28341:3001"
      environment: ${{  github.event.inputs.environment }}
      service: ${{ github.event.inputs.service }}
      parameterstore: "dev-ipc-front-dkr.global-esim.env"
    secrets: inherit
  buildAPI:
    if: ${{ github.event.inputs.service == 'api' && github.event.inputs.environment == 'development' }}
    name: ${{ github.event.repository.name }} -> ${{ github.event.inputs.service }} -> ${{  github.event.inputs.environment }}
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: "api"
      release_tag: ""
      port_map: "28341:3001"
      environment: ${{  github.event.inputs.environment }}
      service: ${{ github.event.inputs.service }}
      parameterstore: "dev-ipc-front-dkr.global-esim.env"
    secrets: inherit

  web:
    if: ${{ github.event.inputs.service == 'web'  }}
    name: ${{ github.event.repository.name }} -> ${{ github.event.inputs.service }} -> ${{  github.event.inputs.environment }}
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: "web"
      release_tag: ""
      port_map: "28340:3000"
      environment: ${{  github.event.inputs.environment }}
      service: ${{ github.event.inputs.service }}
      parameterstore: ${{ github.event.inputs.environment == 'development' && 'dev-ipc-front-dkr.global-esim-jp.env' || 'prod-ipc-front-dkr.global-esim-jp.env'}}
    secrets: inherit

  webJP:
    if: ${{ github.event.inputs.service == 'webjp' }}
    name: ${{ github.event.repository.name }} -> ${{ github.event.inputs.service }} -> ${{  github.event.inputs.environment }}
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: "web"
      release_tag: ""
      port_map: "28345:3000"
      environment: ${{  github.event.inputs.environment }}
      service: "webjp"
      parameterstore: ${{ github.event.inputs.environment == 'development' && 'dev-ipc-front-dkr.global-esim-jp.env' || 'prod-ipc-front-dkr.global-esim-jp.env'}}
    secrets: inherit

  airtrip:
    if: ${{ github.event.inputs.service == 'airtrip' }}
    name: ${{ github.event.repository.name }} -> ${{ github.event.inputs.service }} -> ${{  github.event.inputs.environment }}
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: "web"
      release_tag: ""
      port_map: "28346:3000"
      environment: ${{  github.event.inputs.environment }}
      service: "airtrip"
      parameterstore: ${{ github.event.inputs.environment == 'development' && 'dev-ipc-front-dkr.global-esim-jp.env' || 'prod-ipc-front-dkr.global-esim-jp.env'}}
    secrets: inherit

  payment:
    if: ${{ github.event.inputs.service == 'payment' }}
    name: ${{ github.event.repository.name }} -> ${{ github.event.inputs.service }} -> ${{  github.event.inputs.environment }}
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/main.yml@main
    with:
      type: "api"
      name: "payment"
      release_tag: ""
      port_map: "28343:3001"
      environment: ${{  github.event.inputs.environment }}
      service: "api"
      parameterstore: ${{ github.event.inputs.environment == 'development' && 'dev-ipc-front-dkr.global-esim.env' || 'prod-ipc-front-dkr.global-esim.env'}}
    secrets: inherit

  workers:
    if: ${{ github.event.inputs.service == 'workers' }}
    name: ${{ github.event.repository.name }} -> ${{ github.event.inputs.service }} -> ${{  github.event.inputs.environment }}
    permissions:
      id-token: write
      contents: read

    uses: InboundPlatform/server-script/.github/workflows/pm2-build.yml@main
    with:
      type: "workers"
      release_tag: ""
      port_map: "28341:3000"
      environment: ${{  github.event.inputs.environment }}
      service: "api"
      parameterstore: ${{ github.event.inputs.environment == 'development' && ' dev-ipc-front-dkr.global-esim.env' || 'prod-ipc-front-dkr.global-esim.env'}}
    secrets: inherit
