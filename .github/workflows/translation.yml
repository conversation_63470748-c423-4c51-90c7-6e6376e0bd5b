name: Update Translations

on:
  workflow_dispatch:
    inputs:
      service:
        description: "Select the service"
        required: true
        type: choice
        options:
          - airtrip
          - web
          - webjp

jobs:
  update-translations:
    runs-on: ubuntu-latest

    permissions:
      contents: write # <PERSON> write access to the repository
      pull-requests: write # <PERSON> write access to create pull requests

    steps:
      - uses: InboundPlatform/server-script/.github/actions/notify@main
        with:
          CW_TOKEN: ${{secrets.CW_TOKEN}}
          environment: ${{inputs.environment}}
          state: "message"
          message: "Translation updating started for ${{ github.event.inputs.service }}"
          repo: ${{ github.event.repository.name }}

      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          path: workdir

      - name: Where am I?
        run: ls -a && pwd

      - name: Create token.json from secret
        run: echo "$GOOGLE_TOKEN" > ./workdir/apps/translation-sheet/token.json
        env:
          GOOGLE_TOKEN: ${{ secrets.GOOGLE_TOKEN }}

      - name: Set up Node.js and cache dependencies
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "yarn"
          cache-dependency-path: ./workdir/yarn.lock
      - name: Install dependencies
        working-directory: ./workdir
        run: yarn install --frozen-lockfile

      - name: Download translations
        working-directory: ./workdir
        run: yarn run download:translations

      - name: Run service-specific translation script
        working-directory: ./workdir
        run: |
          if [ "${{ github.event.inputs.service }}" == "airtrip" ]; then
            yarn run copy:translations-airtrip;
          elif [ "${{ github.event.inputs.service }}" == "web" ]; then
            yarn run copy:translations-web;
          elif [ "${{ github.event.inputs.service }}" == "webjp" ]; then
            yarn run copy:translations-webjp;
          fi

      - name: Commit and create PR
        working-directory: ./workdir
        run: |
          git config --global user.name "github-actions"
          git config --global user.email "<EMAIL>"
          timestamp=$(date +'%Y-%m-%d-%H-%M')
          branch=translation-update-${{ github.event.inputs.service }}-$timestamp
          git checkout -b $branch
          git add .
          git commit -m "fix: translation update ${{ github.event.inputs.service }} $timestamp"
          git push origin $branch
          gh pr create --base develop --head translation-update --title "fix: translation update ${{ github.event.inputs.service }} $date" --body "Automated translation update for ${{ github.event.inputs.service }}."
          gh pr merge $branch --merge
          gh api -X DELETE repos/${{ github.repository }}/git/refs/heads/$branch
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # Use the default GITHUB_TOKEN with write permissions
      - uses: InboundPlatform/server-script/.github/actions/notify@main
        with:
          CW_TOKEN: ${{secrets.CW_TOKEN}}
          environment: ${{inputs.environment}}
          state: "message"
          message: "Translation updating completed, pr opened / merged for  ${{ github.event.inputs.service }}"
          repo: ${{ github.event.repository.name }}
