name: ReleasePlease Workflow (Auto)

on:
  push:
    branches:
      - main
permissions:
  contents: write
  pull-requests: write

jobs:
  releaseplease:
    runs-on: ubuntu-latest
    permissions: write-all
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - uses: google-github-actions/release-please-action@v3
        id: release
        with:
          command: manifest
          token: ${{secrets.GITHUB_TOKEN}}
          default-branch: main
          monorepo-tags: true
      - name: Dump GitHub contexts
        env:
          RPL: ${{ toJson(steps.release) }}
        run: echo "$RPL"

      - uses: actions/github-script@v7
        if: ${{ steps.release.outputs.releases_created }}
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            const releaselog =  ${{ toJson(steps.release.outputs)}}
            const servicePath = JSON.parse(releaselog.paths_released);
            const tag = releaselog[`${servicePath}--tag_name`]
            const [service, version] = tag.split("-");

            await github.rest.actions.createWorkflowDispatch({
              owner: 'InboundPlatform',
              repo: '${{github.event.repository.name }}',
              workflow_id: 'release.yml',
              ref: tag,
              inputs: {
                message: releaselog[`${servicePath}--body`]
              }
            });
