export const ESIM_API_PATH = process.env.NEXT_PUBLIC_API_HOST;

export const ERR_GENERIC_REQUIRED = "This field is required";
export const REGEX_STRONG_PASSWORD =
  /^(?=.*[A-Z]).{8,}$/;
export const ERR_MIN_PASSWORD_LENGTH =
  "Password must contain at least 8 character(s)";
export const ERR_STRONG_PASSWORD =
  "Password must be a minimum of 8 characters long and contain at least one uppercase letter"
export const AUTH_TOKEN_NAME = "gesim";
export const ERR_PASSWORD_NOT_MATCHED =
  "Username and password combination didnt match.";
export const google_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Google`;
export const fb_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Facebook`;

export const jp_sort_country = new Map();
jp_sort_country.set("australia", 11);
jp_sort_country.set("france", 8);
jp_sort_country.set("germany", 9);
jp_sort_country.set("italy", 7);
jp_sort_country.set("japan", 15);
jp_sort_country.set("philippines", 5);
jp_sort_country.set("singapore", 4,);
jp_sort_country.set("korea", 2,);
jp_sort_country.set("thailand", 3);
jp_sort_country.set("uk", 10);
jp_sort_country.set("usa", 1);
jp_sort_country.set("vietnam", 6);
jp_sort_country.set("new zealand", 12);
jp_sort_country.set("canada", 13);
jp_sort_country.set("guam", 14);