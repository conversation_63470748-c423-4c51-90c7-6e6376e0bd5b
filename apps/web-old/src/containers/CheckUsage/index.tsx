import { ApiService } from "@/api";
import EsimList from "@/components/EsimList";
import UsageProgress<PERSON>hart from "@/components/UsageProgressChart";
import { IOrder } from "@/interfaces/IOrder";
import { IUsage } from "@/interfaces/IUsage";
import { useGlobalState } from "@/store";
import { Flex } from "@mantine/core";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import { useQuery } from "react-query";
import { AxiosError } from "axios";

interface IProps {}
const CheckUsage = (props: IProps) => {
  const router = useRouter();
  const [isGlobalLoading, toggleLoading] = useGlobalState((s) => [
    s.isLoading,
    s.toggleLoading,
  ]);
  const { data, error, isLoading } = useQuery(
    `orders-${router.query.orderId}`,
    async () => {
      const response = await ApiService.getOrder(router.query.orderId + "", {
        type: "usage",
      });
      if (response instanceof AxiosError) throw new Error("Order not found.");
      return response;
    },
    {
      enabled: !!router.query.orderId,
    }
  );

  useEffect(() => {
    if (error) {
      router.push("/app");
      return;
    }
    toggleLoading(isLoading);
  }, [error, isLoading]);

  const orderInfo = React.useMemo<{ order: IOrder; usage: IUsage }>(
    () => data?.data?.data,
    [data?.data?.data]
  );

  return (
    <Flex direction={"column"} gap={20}>
      {orderInfo && (
        <EsimList
          ignoreCardRenderLogic
          esimCardProps={{
            hideButton: true,
          }}
          accordionProps={{
            defaultValue: orderInfo?.order?.orderId,
          }}
          fetchNextPage={console.log}
          items={data ? [[orderInfo.order]] : []}
          status={"active"}
          renderWrapper={(_, children) => <>{children}</>}
        />
      )}
      {orderInfo && (
        <UsageProgressChart
          endDate={orderInfo?.order?.expireTime}
          startDate={orderInfo?.order?.activateDate}
          totalData={orderInfo?.order?.plan?.dataId}
          usedData={orderInfo?.usage?.topup?.usage}
          validitiyDays={orderInfo?.order?.plan?.validityDays}
        />
      )}
    </Flex>
  );
};
export default CheckUsage;
