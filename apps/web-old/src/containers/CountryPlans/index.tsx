import RegionPlanCard from "@/components/RegionPlanCard";
import { Flex, Box } from "@mantine/core";
import { IPlan } from "@/interfaces/IPlan";
import { countryNameException } from "@/utils";

const CountryPlans = (props: { plans: IPlan[] }) => {
  return (
    <Flex gap={"0.5rem"} p={"1rem"} wrap={"wrap"}>
      {props.plans?.map?.((plan, idnex) => (
        <RegionPlanCard
          key={idnex}
          img={[
            `/assets/flags/212x128/${countryNameException(
              //@ts-expect-error
              plan.originalName || plan.country
            )}.png`,
          ]}
          price={plan.price + ""}
          //@ts-ignore
          name={plan?.country?.name || plan.country}
          xe={plan.xe}
          originalName={plan.originalName || ""}
        />
      ))}
    </Flex>
  );
};
export default CountryPlans;
