import { ApiService } from "@/api";
import AppTextInput from "@/components/AppTextInput";
import InternalLink from "@/components/InternalLink";
import SimpleFlexLayout from "@/components/SimpleFlexLayout";
import { useGlobalState, useRegisterUser } from "@/store";
import { isAppOnlyJapanese } from "@/utils";
import { Button, Checkbox } from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import { IconEye, IconEyeOff } from "@tabler/icons-react";
import { Trans, useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React from "react";
import { z } from "zod";
import {
  ERR_GENERIC_REQUIRED,
  ERR_STRONG_PASSWORD,
  REGEX_STRONG_PASSWORD,
} from "../../../consts";

export const signupSchema = z.object({
  firstName: z.string().min(2, ERR_GENERIC_REQUIRED),
  lastName: z.string().min(2, ERR_GENERIC_REQUIRED),
  email: z.string().email(),

  password: z.string().regex(REGEX_STRONG_PASSWORD, ERR_STRONG_PASSWORD),
  termsOfService: z
    .boolean({
      invalid_type_error: "Please agree to the terms and conditions",
      required_error: "Please agree to the terms and conditions",
    })
    .refine((value) => value === true, {
      message: "Please agree to the terms and conditions",
    }),
});
interface IProps {
  locale?: string;
  referral?: string;
}
const SignupContainer = (props: IProps) => {
  const setRegisterEmail = useRegisterUser((s) => s.setEmail);
  const [isShowPassword, setIsShowPassword] = React.useState(false);
  const [setGlobalMessage, setGlobalLoading] = useGlobalState((s) => [
    s.setMessage,
    s.toggleLoading,
  ]);
  const router = useRouter();
  const { t, i18n } = useTranslation();

  const form = useForm({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      termsOfService: "",
    },
    validate: zodResolver(signupSchema),
  });

  const handleSubmit = React.useCallback(
    async (values: { email: string }) => {
      try {
        setGlobalLoading();
        const registerPayload = { ...values, locale: props.locale };
        if (props.referral) {
          //@ts-ignore
          registerPayload.referral = referral;
        }
        await ApiService.register(registerPayload);
        router.push("/verify/process");
        setRegisterEmail(values.email);
      } catch (err: any) {
        setGlobalMessage(err.response.data.message);
      } finally {
        setGlobalLoading();
      }
    },
    [props.referral]
  );

  return (
    <form onSubmit={form.onSubmit(handleSubmit)}>
      <SimpleFlexLayout>
        {isAppOnlyJapanese() ? (
          <AppTextInput
            withAsterisk
            placeholder={t("signup:formfield.lastname.placeholder") as string}
            {...form.getInputProps("lastName")}
          />
        ) : (
          <AppTextInput
            withAsterisk
            placeholder={t("signup:formfield.firstname.placeholder") as string}
            {...form.getInputProps("firstName")}
          />
        )}
        {!isAppOnlyJapanese() ? (
          <AppTextInput
            withAsterisk
            placeholder={t("signup:formfield.lastname.placeholder") as string}
            {...form.getInputProps("lastName")}
          />
        ) : (
          <AppTextInput
            withAsterisk
            placeholder={t("signup:formfield.firstname.placeholder") as string}
            {...form.getInputProps("firstName")}
          />
        )}
        <AppTextInput
          withAsterisk
          placeholder={t("signup:formfield.email.placeholder") as string}
          {...form.getInputProps("email")}
        />
        <AppTextInput
          withAsterisk
          placeholder={t("signup:formfield.password.placeholder") as string}
          type={isShowPassword ? "text" : "password"}
          rightSection={
            isShowPassword ? (
              <IconEyeOff onClick={() => setIsShowPassword(false)} />
            ) : (
              <IconEye onClick={() => setIsShowPassword(true)} />
            )
          }
          {...form.getInputProps("password")}
        />

        <Checkbox
          error={1}
          mt="md"
          label={
            <Trans i18nKey="signup:agreement.txt">
              I agree to Global Mobile&apos;s{" "}
              <InternalLink
                underline
                target="_blank"
                href={
                  isAppOnlyJapanese()
                    ? "https://www.gmobile.biz/terms-of-service/?value=esim"
                    : "/terms-and-conditions"
                }
              >
                Terms and Conditions
              </InternalLink>{" "}
              and{" "}
              <InternalLink
                underline
                target="_blank"
                href={
                  i18n?.language === "jp"
                    ? "https://www.inbound-platform.com/privacy/"
                    : "https://www.inbound-platform.com/en/privacy/"
                }
              >
                Privacy Policy{" "}
              </InternalLink>
              and{" "}
              <InternalLink
                underline
                target="_blank"
                href="/terms-for-membership"
              >
                Terms for membership.
              </InternalLink>
            </Trans>
          }
          {...form.getInputProps("termsOfService", { type: "checkbox" })}
        />

        <Button w={"100%"} type="submit" color="app-dark">
          {t("signup:btn.signup")}
        </Button>
      </SimpleFlexLayout>
    </form>
  );
};

export default SignupContainer;
