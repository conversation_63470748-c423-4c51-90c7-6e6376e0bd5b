import FullPageModal from "@/components/FullPageModal";
import ManualActivation from "@/components/ManualActivation";
import QrCodeSetup from "@/components/QrCodeSetup";
import AppLayout from "@/pages/app/layout";
import { Box, Button, Divider, Flex, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useRouter } from "next/router";
import React from "react";
import { useTranslation } from "next-i18next";
import MethodBox from "@/components/MethodBox";
import { isAppOnlyJapanese } from "@/utils";
import Head from "next/head";


const EsimActivationContainer = (props: {
    referer: string,
    orderInfo: {
        order: {
            qrCodeImgUrl: string,
            downloadLink: string,
            activateCode: string,
            smdp: string
        }
    },
}) => {
    const router = useRouter();
    const [opened, { open, close }] = useDisclosure(false);

    const [method, setMethod] = React.useState<"qr" | "manual">("qr");
    const { t, i18n } = useTranslation()
    return (
        <>
        <Head>
            <meta name="robots" content="noindex"/>
        </Head>
            <AppLayout
                referer={props.referer}
                title={t("activation:header", "Activation")} noNavbar noFooter
                documentTitle={isAppOnlyJapanese() ? "アクティベーション - グロモバeSIM" : t("activation:header", "Activation")}>
                <Flex
                    gap={15}
                    direction={"column"}
                    align={"center"}
                    justify={"center"}
                    p={"lg"}
                    w={"100%"}
                >
                    <Box>
                        <Text weight={500} size={"md"} align="left">
                            {t("activation:pagedescription")}
                        </Text>
                    </Box>
                    <Text size={"sm"}>
                        {t("activation:subsection.title")}

                    </Text>
                    <MethodBox
                        desc={t("activation:method1.txt")}
                        img="/assets/activate-by-qr.png"
                        title={t("activation:method1.title")}
                        onClick={() => {
                            setMethod("qr");
                            open();
                        }}
                    />
                    <Divider labelPosition="center" w={"100%"} label="or" />
                    <MethodBox
                        desc={t("activation:method2.txt")}
                        img="/assets/activate-manual.png"
                        title={t("activation:method2.title")}
                        onClick={() => {
                            setMethod("manual");
                            open();
                        }}
                    />
                </Flex>
            </AppLayout>
            <FullPageModal
                hideHeaderBorder
                showOverlay
                top={"50vh"}
                onClose={close}
                opened={opened}
                title={
                    <Text weight={800}>
                        {method === "qr" ? "QR Code Setup" : "Manual Setup"}
                    </Text>
                }
            >
                <Flex
                    w={"100%"}
                    p="lg"
                    pl="xs"
                    direction={"column"}
                    gap={15}
                    justify={"center"}
                    align={"center"}
                    sx={{
                        overflowY: "hidden",
                        overflowX: "hidden"
                    }}
                >
                    {method === "qr" && (
                        <QrCodeSetup
                            label={t(`activation:${router.query.os}.qr-code-description`)}
                            // img="https://issue.usimsa.com/api/iccid/qrcode/LPA:523$ecprsp.eastcompeace.com$4yb5BRZ6OcwA3STsxuFCd9ApPDa8w38d/120"
                            img={props.orderInfo?.order?.qrCodeImgUrl}
                        />
                    )}
                    {method === "manual" && (
                        <ManualActivation
                            os={(router.query.os as "android") || "android"}
                            activateCode={props.orderInfo.order.activateCode}
                            downloadLink={props.orderInfo.order.downloadLink}
                            smdp={props.orderInfo.order.smdp}
                        />
                    )}
                    <Button fullWidth color="app-dark" onClick={() => {
                        window.open(`/documents/${i18n.language}/manual-${router.query.os}.pdf`, "_blank")
                    }}>
                        {t("activation:btn.opensetupguide")}
                    </Button>
                </Flex>
            </FullPageModal>
        </>
    );
};
export default EsimActivationContainer;
