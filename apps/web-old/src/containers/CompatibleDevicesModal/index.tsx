import CompatibleDevicesList from "@/components/CompatibleDevicesList"
import FullPageModal from "@/components/FullPageModal"
import { Accordion, Box, ModalProps } from "@mantine/core"
import { useTranslation } from "next-i18next"

const CompatibleDevicesModal = (props: ModalProps) => {
    const { t } = useTranslation();

    //@ts-expect-error
    return <FullPageModal
        {...props}
        title={t("compatibility:header.title")}
    >
        <Box p={"lg"}>
            <CompatibleDevicesList />
        </Box>
    </FullPageModal>
}
export default CompatibleDevicesModal