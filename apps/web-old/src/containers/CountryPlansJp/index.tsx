import RegionPlanCardJp from "@/components/RegionPlanCardJp";
import { Flex, Box } from "@mantine/core";
import { IPlan } from "@/interfaces/IPlan";
import { countryNameException } from "@/utils";



const CountryPlansJp = (props: { plans: IPlan[] }) => {
  return (
    <Flex gap={"1rem"} p={"1rem"} wrap={"wrap"}>
      {props.plans?.map?.((plan, index) => (
        <RegionPlanCardJp
          key={index}
          img={[
            `/assets/destination/${
              //@ts-expect-error
              countryNameException(plan.originalName || plan.country)
            }.jpeg`,
          ]}
          price={plan.price + ""}
          //@ts-ignore
          name={plan?.country?.name || plan.country}
          xe={plan.xe}
          originalName={plan.originalName || ""}
        />
      ))}
    </Flex>
  );
};
export default CountryPlansJp;
