import { ApiService } from "@/api";
import EsimList from "@/components/EsimList";
import LoadingMyEsimCard from "@/components/LoadingMyEsimCard";
import { Stack } from "@mantine/core";
import React from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { useInfiniteQuery } from "react-query";

interface IProps {
  status: "active" | "expired";
  isActive?: boolean;
}
const limit = 10
const MyEsimContainer = (props: IProps) => {
  const [total, setTotal] = React.useState(0);

  const {
    data,
    fetchNextPage,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteQuery({
    enabled: props.isActive,
    keepPreviousData: true,
    queryKey: `orders-${props.status}`,
    queryFn: async ({ pageParam = 1 }) => {
      if (!props.isActive) return;
      const data = await ApiService.getOrders({
        sort_by: "id:desc",
        limit,
        page: pageParam,
        status: props.status
      });
      setTotal(data?.data?.data?.total);
      return data?.data?.data?.data;
    },
    getNextPageParam: (lastPage, allPages) => {
      return allPages.length + 1;
    },
    cacheTime: 100000,
  });

  const hasMore = React.useMemo(() => {
    const fetched = (data?.pages || [])?.length * limit;
    return fetched <= total
  }, [data?.pages, total])



  return (
    <EsimList
      isLoading={isLoading}
      fetchNextPage={fetchNextPage}
      items={data?.pages || []}
      status={props.status}
      renderWrapper={(items, children) => {
        return (
          <InfiniteScroll
            dataLength={items.length} //This is important field to render the next data
            next={fetchNextPage}
            hasMore={hasMore}
            loader={
              isLoading || isFetchingNextPage ? (
                <Stack mt={10}>
                  <LoadingMyEsimCard />
                  <LoadingMyEsimCard />
                  <LoadingMyEsimCard />
                  <LoadingMyEsimCard />
                </Stack>
              ) : null
            }
          >

            {children}
          </InfiniteScroll>
        );
      }}
    />
  );
};
export default MyEsimContainer;
