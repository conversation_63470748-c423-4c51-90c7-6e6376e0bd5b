import {
  Card,
  Text,
  Title,
  Flex,
  Badge,
  Image
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { getCDNUrl } from "@/utils";

const Features = ({
  step,
  title,
  content,
  url,
}: {
  step: string;
  title: string;
  content: string;
  url: string;
}) => {
  const { t } = useTranslation();

  return (
    <Card px={0}>
      <Flex>
        <Badge color="app-pink.4" mr="0.5rem">
          {step}
        </Badge>
        <Title
          order={3}
          size={14}
          color="app-pink.4"
        >
          {title}
        </Title>
      </Flex>
      <Flex gap="0.5rem" mt="0.5rem">
        <Image
          src={getCDNUrl(url)}
          height={100}
          width={160}
          alt={title}
          radius="md"
        />
        <Text component="p">
          {content}
        </Text>
      </Flex>
    </Card>
  );
};
export default Features;
