import {
  Paper,
  Title,
  Box,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import FeatureItem from "./FeatureItem";
import CTAButton from "@/components/CTAButton";

const Features = () => {
  const { t } = useTranslation();

  const items = [
    ...Array.from(Array(6).keys()).map((key) => {
      return {
        id: `0${key + 1}`,
        title: t(`home:features.item${key + 1}.title`),
        content: t(`home:features.item${key + 1}.content`),
        url: `/assets/features/step${key + 1}.webp`,
      }
    })
  ]

  return (
    <Paper
      px="1rem"
      pb="1rem"
    >
      <Title
        p="2rem 1rem 1rem"
        order={2} 
        align="center"
        sx={{
          fontSize: "1.4rem",
          lineHeight: "unset !important"
        }}
      >
        {t("home:features.title")}
      </Title>
      {items.map((feature) => (
        <FeatureItem
          key={feature.id}
          step={feature.id}
          title={feature.title}
          content={feature.content}
          url={feature.url}
        />
      ))}
      <Box mt="1rem">
        <CTAButton />
      </Box>
    </Paper>
  );
};
export default Features;
