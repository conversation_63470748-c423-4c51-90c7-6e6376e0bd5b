import { formatDateAndTime, getBytes } from "@/utils";
import {
  Center,
  Divider,
  Flex,
  Group,
  Paper,
  Progress,
  RingProgress,
  Stack,
  Text,
} from "@mantine/core";
import { differenceInDays, isPast } from "date-fns";
import React from "react";
import * as filesize from "filesize";
import { useTranslation } from "next-i18next";
interface IProps {
  usedData: string;
  totalData: string;
  validitiyDays: number;
  startDate: Date | null;
  endDate: Date | null;
}
const colorAsPerDays = (diff: number) => {
  if (diff < 40) {
    return "#0CB21A";
  }
  if (diff >= 40 && diff < 80) {
    return "#FFCD3F";
  }

  return "red";
};

const UsageProgressChart = (props: IProps) => {
  const { t } = useTranslation();
  const totalDays = React.useMemo(() => {
    return props.startDate && props.endDate
      ? differenceInDays(new Date(props.endDate), new Date(props.startDate))
      : 0;
  }, []);
  const diffToday = React.useMemo(() => {
    return props.startDate
      ? differenceInDays(new Date(), new Date(props.startDate))
      : 0;
  }, []);
  const diffDaysInPercentage = React.useMemo(
    () => (diffToday / totalDays) * 100,
    []
  );
  const totalBytes = React.useMemo(() => getBytes(props.totalData || "0"), []);
  const usedBytes = React.useMemo(() => getBytes(props.usedData || "0"), []);

  const remainingData = React.useMemo(
    () => totalBytes - usedBytes,
    [totalBytes, usedBytes]
  );
  const usedPercentage = React.useMemo(() => {
    return (usedBytes / totalBytes) * 100 || 0;
  }, []);

  const isExpired = React.useMemo(
    () => !!(props.endDate && isPast(new Date(props.endDate))),
    []
  );
  const formatBytes = (bytes: number = 0) =>
    (filesize.filesize(bytes, { base: 2, standard: "jedec" }) as
      | string
      | undefined) || "0 Bytes";
  return (
    <Paper w={"100%"} p={"sm"} shadow="sm">
      <Flex w={"100%"} direction={"column"} align={"center"}>
        <Group w={"100%"} position="apart">
          <Text fw={"bold"} tt={"capitalize"}>
            {t("common:siminfo-card.dataplan.title", "DATA USAGE")}
          </Text>
          <Text color="app-pink.4" fw={"bold"}>
            {formatBytes(usedBytes)}/ {formatBytes(totalBytes)}
          </Text>
        </Group>
        <RingProgress
          thickness={20}
          size={250}
          sections={[
            {
              value: usedPercentage,
              color: colorAsPerDays(usedPercentage),
            },
          ]}
          label={
            <Center>
              <Stack spacing={"-lg"}>
                <Text weight={500} align="center" size="sm">
                  {isExpired
                    ? t("common:siminfo-card.tag.expired", "Expired")
                    : t("common:siminfo-card.remainingdata", "Remaining Data")}
                </Text>
                {!isExpired && (
                  <Text color="app-pink.4" fw={"bold"} align="center" size="xl">
                    {!props.endDate
                      ? props.totalData
                      : formatBytes(remainingData) + ""}
                  </Text>
                )}
              </Stack>
            </Center>
          }
        />
        <Divider w={"100%"} />
        <Stack w={"100%"} mt={"1rem"}>
          <Group position="apart">
            <Text fw={"bold"}>
              {t("common:siminfo-card.usageperiod", "Usage Period")}
            </Text>
            <Text color="app-pink.4" fw={"bold"}>
              {t("common:siminfo-card.validity.unit", {
                count: props.validitiyDays, // Get the usage plan days without unit
                defaultValue: "{{ count }} day",
                defaultValue_other: "{{ count }} days",
              })}
            </Text>
          </Group>
          <Progress
            animate={!isExpired}
            mt={"-.8rem"}
            color={colorAsPerDays(diffDaysInPercentage)}
            w={"100%"}
            value={diffDaysInPercentage}
          />
          <Group position="apart" mt={"-xs"}>
            <Text c="dimmed" size={".6rem"}>
              {props.startDate ? formatDateAndTime(props.startDate) : "N/A"}
            </Text>
            <Text c="dimmed" size={".6rem"}>
              {props.endDate ? formatDateAndTime(props.endDate) : "N/A"}
            </Text>
          </Group>
        </Stack>
      </Flex>
    </Paper>
  );
};
export default UsageProgressChart;
