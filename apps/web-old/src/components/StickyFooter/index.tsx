import { Button, ButtonProps, Grid, Text, Transition } from "@mantine/core";
import StickyContainer from "../StickyContainer";
import { IPlan } from "@/interfaces/IPlan";
import { countryAlias, reverseKebabCase, isAppOnlyJapanese } from "@/utils";
import { Trans, useTranslation } from "next-i18next";
import { Currency } from "@/core/Currency";

type Props = {
  onClick: () => void,
  selectedPlan?: IPlan
}
const StickyFooter = (props: ButtonProps & Props) => {
  const { t } = useTranslation();
  const selectedCode = Currency.getSelectedCurrency();
  const { children, onClick, selectedPlan } = props;

  return (
    <Transition
      mounted={Boolean(selectedPlan)}
      transition="slide-up"
      duration={800}
      timingFunction="ease">
      {(transitionStyles) => (
        <StickyContainer
          style={{ ...transitionStyles }}
          p="1.25rem"
        >
          {selectedPlan && (
            <>
              <Text color="app-dark" size={"md"} weight={700}>
                <Trans i18nKey={(`countries:${selectedPlan?.country.name}`)}>
                  {countryAlias(reverseKebabCase(selectedPlan?.country.name))}
                </Trans>
              </Text>
              <Grid align="center" justify="space-between" style={{ margin: "0", paddingBottom: "0.50rem" }}>
                <Text color="app-dark" size={"sm"}>
                  {t("common:siminfo-card.validity.unit_one", {
                    count: selectedPlan?.validityDays,
                    defaultValue: "{{ count }} day",
                    defaultValue_other: "{{ count }} days",
                  })} - {selectedPlan?.dataVolume}{selectedPlan?.dataUnit}/{t("common:siminfo-card.perday.unit")}
                </Text>
                <Text weight={700} color="app-pink.5" size={"lg"}>
                  {Currency.formatToSelected({ xe: selectedPlan?.xe ?? {}, price: selectedPlan?.price ?? 0 })}
                  {!isAppOnlyJapanese() && (
                    selectedCode?.code
                  )}
                </Text>
              </Grid>
            </>
          )}
          <Button fullWidth size="lg" color="app-pink.5" onClick={onClick} disabled={!selectedPlan}>
            {children}
          </Button>
        </StickyContainer>
      )}
    </Transition>
  );
};
export default StickyFooter;
