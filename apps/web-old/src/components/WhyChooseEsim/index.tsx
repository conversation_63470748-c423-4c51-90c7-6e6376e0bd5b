import { Flex, Image, Paper, Text, Title } from "@mantine/core";
import { Carousel } from "@mantine/carousel";
import { useTranslation } from "next-i18next";
import { useRef } from "react";
import Autoplay from "embla-carousel-autoplay";
import { getCDNUrl } from "@/utils";

const WhyChooseEsim = () => {
  const { t, i18n } = useTranslation();
  const autoplay = useRef(Autoplay({ delay: 2000 }));

  const items = [
    {
      icon:
        i18n.language === "jp"
          ? "/assets/local-rates.png"
          : "/assets/local-rates-dollar.png",
      title: "home:home-carousel.affordablelocalrate.txt",
      content: "home:home-carousel.affordablelocalrate.description",
    },
    {
      icon: "/assets/easy-set-up.png",
      title: "home:home-carousel.easysetup.txt",
      content: "home:home-carousel.easysetup.description",
    },
    {
      icon: "/assets/customer-support.png",
      title: "home:home-carousel.englishcustomersupport.txt",
      content: "home:home-carousel.englishcustomersupport.description",
    },
    {
      icon: "/assets/network.png",
      title: "home:home-carousel.stablesafenetwork.txt",
      content: "home:home-carousel.stablesafenetwork.description",
    },
    {
      icon: "/assets/instant-delivery.png",
      title: "home:home-carousel.instantdelivery.txt",
      content: "home:home-carousel.instantdelivery.description",
    },
    {
      icon: "/assets/flexible-activation.png",
      title: "home:home-carousel.flexibleactivationnew.txt",
      content: "home:home-carousel.flexibleactivationnew.description",
    },
  ];

  return (
    <Paper>
      <Flex direction={"column"}>
        <Carousel
          //@ts-expect-error
          plugins={[autoplay.current]}
          mt={"-.5rem"}
          mb={"1.5rem"}
          styles={(theme) => ({
            viewport: {
              // background: "transparent",
              // boxShadow: "0 0 20px 0 rgba(0,0,0,0.1)"
              marginBottom: "1rem",
            },
            indicator: {
              width: 10,
              height: 10,
              transition: "width 250ms ease",
              background: "#DDDDDD",
              "&[data-active]": {
                background: theme.colors["app-pink"][4],
              },
            },
            indicators: {
              bottom: "0rem",
              gap: "0.3rem",
            },
          })}
          withIndicators
          withControls={false}
          // breakpoints={[
          //   { minWidth: "lg", slideSize: "100%", slideGap: 10 },
          //   { maxWidth: "md", slideSize: "100%", slideGap: 10 },
          //   { maxWidth: "sm", slideSize: "100%", slideGap: 10 },
          // ]}
          height={157}
          slideGap={10}
          slideSize="100%"
          p={"md"}
          bg={"transparent"}
          radius={"md"}
        >
          {items.map((item, index) => (
            <Carousel.Slide key={index} bg={"transparent"}>
              <Paper
                sx={{}}
                bg={
                  "linear-gradient(315.75deg, rgba(255,255,255,0.3) 0%, rgba(240,118,179,0) 53.15%, rgba(228,0,113,0.1) 100%)"
                }
                h={157}
                p={20}
                radius={"md"}
              >
                <Flex
                  h={"100%"}
                  w={"100%"}
                  direction={"column"}
                  align="center"
                  justify="center"
                  gap={20}
                >
                  <Image
                    src={getCDNUrl(item.icon)}
                    alt="icon"
                    fit="contain"
                    width={"3rem"}
                    height={"3rem"}
                  />
                  <Flex direction={"column"} align={"center"}>
                    <Text
                      sx={{
                        fontSize: "1rem",
                        fontWeight: 800,
                      }}
                    >
                      {t(item.title)}
                    </Text>
                    <Text
                      size={".8rem"}
                      sx={{
                        color: "#333333",
                        lineHeight: "1rem",
                        marginTop: "10px",
                        textAlign: "center",
                      }}
                    >
                      {t(item.content)}
                    </Text>
                  </Flex>
                </Flex>
              </Paper>
            </Carousel.Slide>
          ))}
        </Carousel>
      </Flex>
    </Paper>
  );
};
export default WhyChooseEsim;
