import { useCallback, useState } from "react";

import Image from "next/image";
import { useRouter } from "next/router";
import {
    CardCvcElement,
    CardExpiryElement,
    CardNumberElement,
    useStripe,
} from "@stripe/react-stripe-js";
import {
    StripeCardCvcElementChangeEvent,
    StripeCardExpiryElementChangeEvent,
} from "@stripe/stripe-js";

import { SimpleGrid, createStyles } from "@mantine/core";
const useStyles = createStyles(() => ({
    cardInputContainer: {
        marginBottom: "0.6rem",
        height: "50px",
        padding: "0.9rem 0.6rem 0 0.6rem",
        boxSizing: "border-box",
        border: "1px solid #DDDDDD",
        borderRadius: "4px",
        backgroundColor: "#FFFFFF"
    }

}))

type FormProps = {
    formRef: React.ForwardedRef<HTMLFormElement>,
    clientSecret?: string | undefined;
    planId?: string | undefined;
    message?: string;
    handlePaymentMethod: (e: any) => void;
};

const baseStyle = {
    color: "#32325d",
    fontSmoothing: "antialiased",
    fontSize: "17px",
    "::placeholder": {
        color: "#aab7c4",
    },
};
const LegacyCheckoutForm: React.FC<FormProps> = ({
    message,
    handlePaymentMethod, ...props
}) => {
    const { classes } = useStyles();
    // const {
    //     register,
    //     handleSubmit,
    //     getValues,
    //     watch,
    //     formState: { errors },
    // } = useForm<Inputs>();

    const router = useRouter();

    // stripe
    const stripe = useStripe();
    const [errorMessage, setErrorMessage] = useState<any>(message ?? null);
    const [isCheckoutDisabled, setIsCheckoutDisabled] = useState({
        number: false,
        expiry: false,
        cvc: false,
    });
    const allCardStatus = Object.values(isCheckoutDisabled).every(
        (value) => value === true
    );

    const onSubmit = async (ev: any) => {
        ev.preventDefault();
        ev.stopPropagation();
        if (!allCardStatus) {
            return false;
        }
        handlePaymentMethod(ev);
    };

    const handleElementChange = useCallback(
        (input: "number" | "cvc" | "expiry") => {
            return (
                e:
                    | StripeCardCvcElementChangeEvent
                    | StripeCardExpiryElementChangeEvent
                    | any
            ) => {
                setErrorMessage(e?.error?.message);
                setIsCheckoutDisabled((isCheckoutDisabled) => ({
                    ...isCheckoutDisabled,
                    [input]: e.complete,
                }));
            };
        },
        []
    );
    return (
        <>
            {/* {errorMessage && <ErrorContainer message={errorMessage} />} */}
            <form
                ref={props.formRef}
                onSubmit={onSubmit}
            >
                <div  >
                    <div  >
                        {/* <Image
                            src={`${basePath}/payment.png`}
                            width={277}
                            height={20}
                            alt={"support payment method"}
                        /> */}
                    </div>
                </div>
                <div  >
                    <div className={(classes.cardInputContainer)} >
                        <CardNumberElement
                            onChange={handleElementChange("number")}
                            options={{
                                showIcon: true,
                                style: {
                                    base: baseStyle,
                                },
                            }}
                        />
                    </div>
                    <SimpleGrid cols={2}>
                        <div className={(classes.cardInputContainer)} >
                            <CardExpiryElement
                                onChange={handleElementChange("expiry")}
                                options={{
                                    style: {
                                        base: baseStyle,
                                    },
                                }}
                            />
                        </div>
                        <div className={(classes.cardInputContainer)} >
                            <CardCvcElement
                                onChange={handleElementChange("cvc")}
                                options={{
                                    style: {
                                        base: baseStyle,
                                    },
                                }}
                            />
                        </div>
                    </SimpleGrid>

                </div>
                <button type="submit" style={{ display: "none" }} />
            </form>
        </>
    );
};

export default LegacyCheckoutForm;
