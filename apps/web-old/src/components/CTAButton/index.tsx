import {
  Text,
  Button,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import Link from "next/link";

const CTAButton = () => {
  const { t } = useTranslation();

  return (
    <Link 
      href="/#select-plan"
      style={{textDecoration: "none"}}
      passHref
    >
      <Button
        fullWidth
        size="lg"
        radius="md"
        variant="filled"
        color="app-pink.4"
      >
        <Text
          weight="bold"
          color="white"
          ml={10}
        >
          {t("common:ctabutton")}
        </Text>
      </Button>
    </Link>
  )
}
export default CTAButton;