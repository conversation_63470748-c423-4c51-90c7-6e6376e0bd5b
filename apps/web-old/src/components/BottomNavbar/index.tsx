import { useProfile } from "@/store";
import { getCDNUrl, isAppOnlyJapanese } from "@/utils";
import { Box, Flex, Image, Text, ThemeIcon } from "@mantine/core";
import Link from "next/link";
import { useRouter } from "next/router";
import React from "react";
import { useTranslation } from "next-i18next";
import { IProfile } from "@/interfaces/IProfile";

const BottomNavbar = () => {
  const { t } = useTranslation();
  const route = useRouter();
  const profile = useProfile((s: { profile?: IProfile }) => s.profile);
  const links = React.useMemo(
    () => [
      {
        link: "/",
        icon: ["/assets/tab-shop.png", "/assets/tab-shop-2.png"],
        label: t("common:navbar.shop.label"),
      },
      {
        link: profile?.id ? "/app" : "/main",
        icon: ["/assets/tab-myesim.png", "/assets/tab-myesim-2.png"],
        label: t("common:navbar.myesim.label"),
      },
      {
        link: "/profile",
        icon: ["/assets/tab-user.png", "/assets/tab-user-2.png"],
        label: isAppOnlyJapanese()
          ? t("common:navbar.account.label")
          : t("common:navbar.profile.label"),
      },
    ],
    [profile]
  );

  const isActiveRoute = React.useCallback((link: string) => {
    if (route.pathname === "/") {
      return link === route.pathname;
    }
    const afterSlash = link.substring(1, link.length);
    return route.pathname.includes(afterSlash || "0");
  }, []);
  return (
    <Box
      sx={{
        position: "sticky",
        background: "white",
        boxShadow: "0 -1px 8px 0 rgba(0,0,0,0.10)",
        bottom: 0,
      }}
    >
      <Flex bg={"white"} align="center" mih={60} justify={"space-around"}>
        {links.map((link, index) => (
          <Link
            key={index}
            href={link.link}
            style={{
              textDecoration: "none",
            }}
          >
            <Flex direction={"column"} align={"center"} justify={"center"}>
              <ThemeIcon bg={"transparent"} size={24} radius="xl">
                <Image
                  src={getCDNUrl(
                    isActiveRoute(link.link) ? link.icon[0] : link.icon[1]
                  )}
                />
              </ThemeIcon>
              <Text
                color="app-dark.1"
                weight={200}
                sx={{
                  fontSize: "0.7rem",
                  fontWeight: 400,
                }}
              >
                {link.label}
              </Text>
            </Flex>
          </Link>
        ))}
      </Flex>
    </Box>
  );
};
export default BottomNavbar;
