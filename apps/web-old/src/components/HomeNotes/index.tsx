import {
  Paper,
  Title,
  Text,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import styles from "./homenotes.module.css";

const HomeNotes = () => {
  const { t } = useTranslation();

  return (
    <Paper
      px="1rem"
      pb="1rem"
    >
      <Title
        p="2rem 1rem 1rem"
        order={2} 
        align="center"
        sx={{
          fontSize: "1.4rem",
          lineHeight: "unset !important"
        }}
      >
        {t("home:note.caution.title")}
      </Title>
      <ul
        className={styles["list"]}
        dangerouslySetInnerHTML={{
          __html: t("home:note.caution.content.gm") as string 
        }}
      />
    </Paper>
  );
};
export default HomeNotes;
