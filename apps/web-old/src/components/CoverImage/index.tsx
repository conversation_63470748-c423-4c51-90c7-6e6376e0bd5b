import { getCDNUrl } from "@/utils";
import { ActionIcon, Flex, Image, ImageProps } from "@mantine/core";
import { IconArrowLeft } from "@tabler/icons-react";
import { useRouter } from "next/router";

export const CoverImage = ({
  isLGU,
  ...props
}: ImageProps & { isLGU?: boolean }) => {
  const router = useRouter();
  return (
    <Flex
      sx={{
        zIndex: 99,
      }}
      pos={"sticky"}
      direction={"column"}
      top={0}
    >
      <Image
        maw={"auto"}
        mx="auto"
        {...props}
        src={getCDNUrl(props.src || "")}
        withPlaceholder
      />
      <Flex
        w={"100%"}
        opacity={0.3}
        sx={{
          objectFit: "cover",
          objectPosition: "bottom",
          boxShadow: "inset 1px 1px 20px 6px black",
          // backgroundImage: "linear-gradient(331deg, #0000, rgb(0 0 0))",
        }}
        pos={"absolute"}
        h={props.height}
        mx="auto"
      ></Flex>
      <ActionIcon
        m={"md"}
        top={"0"}
        pos={"absolute"}
        onClick={() => router.push("/")}
        variant="transparent"
      >
        <IconArrowLeft size={48} strokeWidth={2} color={"white"} />
      </ActionIcon>
    </Flex>
  );
};
