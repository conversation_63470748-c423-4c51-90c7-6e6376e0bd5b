import { Avatar, Button, ButtonProps } from "@mantine/core"
import globePng from "./fa-solid_globe-americas.png";
import { IconChevronDown } from "@tabler/icons-react";

const LanguagePickerButton = ({ children, ...props }: ButtonProps & { onClick: any }) => {
    return <Button
        sx={{
            border: "1px solid #979797",
            backgroundColor: "#ffffff"
        }}
        styles={{
            root: {
                padding: "0.3rem !important",
                marginLeft: "auto"
            }
        }}
        p={"xs"}
        leftIcon={<Avatar src={globePng.src} size={"xs"} />}
        rightIcon={<IconChevronDown size={"1rem"} />}
        variant="outline" color="gray" size="sm" {...props}>
        {children}
    </Button>
}
export default LanguagePickerButton