import { Flex, List, Paper, Text, Title } from "@mantine/core";
import { IconCheck } from "@tabler/icons-react";
import { useTranslation } from "next-i18next";

const WhatIsEsim = () => {
  const { t } = useTranslation();
  const items = [
    {
      title: t("home:benefit.item1.row1"),
      content: t("home:benefit.item1.row2"),
    },
    {
      title: t("home:benefit.item2.row1"),
      content: t("home:benefit.item2.row2"),
    },
    {
      title: t("home:benefit.item3.row1"),
      content: t("home:benefit.item3.row2"),
    },
  ];

  return (
    <Paper>
      <Flex direction={"column"}>
        <Title
          align="center"
          p="1rem"
          order={2}
          mt={"1rem"}
          sx={{
            fontSize: "1.4rem",
            lineHeight: "unset !important",
          }}>
          {t("home:benefit.title")}
        </Title>
        <Text
          component="p"
          px="1rem"
          sx={{
            marginBlockStart: 0,
          }}
        >
          {t("home:benefit.description")}
        </Text>
        <List mt={"-.5rem"} p="1rem" spacing="xs" size="sm" center>
          {items.map((item) => (
            <List.Item
              key={item.title}
              mb={30}
              icon={<IconCheck size={24} strokeWidth={3} color={"#30BF01"} />}
            >
              <Flex direction={"column"}>
                <Title order={3} size={14}>{item.title}</Title>
                <Text
                  color="#333333"
                  component="p"
                  sx={{
                    marginBlockStart: 0,
                    marginBlockEnd: 0
                  }}
                >
                  {item.content}
                </Text>
              </Flex>
            </List.Item>
          ))}
        </List>
      </Flex>
    </Paper>
  );
};
export default WhatIsEsim;
