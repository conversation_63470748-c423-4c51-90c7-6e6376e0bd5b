import { Box, BoxProps } from "@mantine/core";

const StickyContainer = (props: BoxProps) => {
  return (
    <Box
      sx={{
        position: "sticky",
        background: "white",
        boxShadow: "0 -1px 8px 0 rgba(0,0,0,0.15)",
        bottom: 0,
        zIndex: 9,
        ...props.sx,
      }}
      p="sm"
      {...props}
    >
      {props.children}
    </Box>
  );
};
export default StickyContainer;
