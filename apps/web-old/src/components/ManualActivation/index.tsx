import { IOrder } from "@/interfaces/IOrder";
import {
  Button,
  CopyButton,
  Flex,
  Paper,
  Stack,
  Text,
  TextInput,
} from "@mantine/core";
import { useTranslation } from "next-i18next";

const ManualCopyBox = (props: { title: string; copyValue: string }) => {
  const { t } = useTranslation()
  return (
    <Paper w={"100%"} bg="#F9F9F9" p={"sm"}>
      <Stack>
        <Text weight={600}>{props.title}</Text>
        <TextInput
          defaultValue={props.copyValue}
          mt={-15}
          w={"95%"}
          size="xs"
          rightSection={
            <CopyButton value={props.copyValue}>
              {({ copied, copy }) => (
                <Button
                  bg={copied ? "app-dark" : "app-pink.4"}
                  size="xs"
                  onClick={copy}
                  color="app-dark"
                >
                  {copied ? "Copied" : t("common:copyable-item.btn.copy.txt")}
                </Button>
              )}
            </CopyButton>
          }
        />
      </Stack>
    </Paper>
  );
};
const ManualActivation = ({
  activateCode,
  smdp,
  downloadLink,
  os,
}: {
  os: "ios" | "android";
  activateCode: string,
  smdp: string,
  downloadLink: string,
}) => {
  const { t } = useTranslation();
  return (
    <>
      <Text weight={500} size={"sm"}>
        {t(`activation:${os}.manual-description`)}
      </Text>
      {os === "android" ? (
        <ManualCopyBox
          title={os === "android" ? t(`activation:downloadlink`) : t(`activation:smdp.address`)}
          copyValue={downloadLink + ""}
        />
      ) : (
        <>
          <ManualCopyBox
            title={t("activation:smdp.address")}
            copyValue={smdp}
          />
          <ManualCopyBox
            title={t("activation:activation-code.txt")}
            copyValue={activateCode + ""}
          />
        </>
      )}
    </>
  );
};
export default ManualActivation;
