import { getCDNUrl } from "@/utils";
import { Flex, Image, Paper, Stack, Text } from "@mantine/core";

const MethodBox = (props: {
    onClick: (e: React.MouseEvent<HTMLDivElement>) => void;
    title: string;
    desc: string;
    img: string;
}) => (
    <Paper
        sx={{
            cursor: "pointer",
        }}
        w={"100%"}
        h={"10rem"}
        shadow="xs"
        onClick={props.onClick}
    >
        <Flex
            w={"100%"}
            h={"100%"}
            direction="column"
            align={"center"}
            justify="center"
            gap={10}
        >
            <Image fit="contain" src={getCDNUrl(props.img)} width={33} height={33} />
            <Stack align="center">
                <Text>{props.title}</Text>
                <Text mt={-20}>{props.desc}</Text>
            </Stack>
        </Flex>
    </Paper>
);
export default MethodBox;