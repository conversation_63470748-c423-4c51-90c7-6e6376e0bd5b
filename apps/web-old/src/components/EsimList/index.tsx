import { Accordion, AccordionProps, Center, Text } from "@mantine/core";
import MyEsimCard from "../MyEsimCard";
import { IOrder } from "@/interfaces/IOrder";
import EsimDetailCard from "../EsimDetailCard";
import { isPast } from "date-fns";
import Storage from "@/core/Storage";
import React, { useEffect } from "react";
import { Trans } from "next-i18next";

const EsimList = (props: {
  isLoading?: boolean;
  ignoreCardRenderLogic?: boolean;
  fetchNextPage: () => void;
  esimCardProps?: Partial<React.ComponentProps<typeof MyEsimCard>>;
  accordionProps?: Partial<AccordionProps>;
  status: "active" | "expired";
  items: Array<Array<IOrder>>;
  renderWrapper: (
    item: Array<Array<IOrder>>,
    arg: React.ReactNode
  ) => React.ReactNode;
}) => {
  const [activeItem, setActiveItem] = React.useState<string>("");

  useEffect(() => {
    const lastItem = Storage.getInstance().get("lastItem");
    setActiveItem(lastItem);
    Storage.getInstance().set("lastItem", "");
    setTimeout(() => {
      const element = document.getElementById(lastItem);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: "center"
        });
      }
    }, 400)
  }, []);

  useEffect(() => {
    Storage.getInstance().set("lastItem", activeItem);
  }, [activeItem]);

  return (
    <>
      <Accordion
        value={props.accordionProps?.defaultValue ? undefined : activeItem}
        w={"100%"}
        variant="separated"
        radius="sm"
        styles={{
          chevron: {
            background: "#334CA9",
            width: 28,
            height: 28,
            color: "white",
            borderRadius: 100,
            transform: "rotate(-90deg)",
            "&[data-rotate]": {
              transform: "rotate(0deg)",
            },
          },
          control: {
            color: "white",
          },
          item: {
            color: "white",
            background: "linear-gradient(209deg, #082699 0%, #30395c 100%)",
          },
          panel: {
            color: "white",
          },
        }}
        {...(props.accordionProps || {})}
        onChange={(v: string) => {
          setActiveItem(v)
        }}
      >
        {
          !props.isLoading &&
          !props.items.flat().length &&
          <Center w={"100%"} h={"50vh"}>
            <Text size={"sm"} color="dimmed">
              <Trans i18nKey={"myesim:noesim.txt"}>
                No eSIM order yet.
              </Trans>
            </Text>
          </Center>
        }

        {props.renderWrapper(
          props.items,
          <>
            {props.items.map((page) => {
              return page?.map?.((item) => {
                const isExpired = item.expireTime ? isPast(new Date(item.expireTime)) : false

                if (props.ignoreCardRenderLogic) {
                  return (
                    <EsimDetailCard
                      isExpired={isExpired}
                      key={item.id}
                      item={item}
                      esimCardProps={props.esimCardProps}
                    />
                  );
                }
                if (item.expireTime) {
                  if (props.status === "active") {
                    return isExpired ? null : (
                      <EsimDetailCard
                        item={item}
                        key={item.id}
                        esimCardProps={props.esimCardProps}
                      />
                    );
                  } else {
                    return isExpired ? (
                      <EsimDetailCard
                        item={item}
                        key={item.id}
                        esimCardProps={props.esimCardProps}
                      />
                    ) : null;
                  }
                }
                // Orders dont have expire time, which means
                // either they are not activated or we dont have
                // Latest information
                return props.status === "active" ? (
                  <EsimDetailCard
                    key={item.id}
                    item={item}
                    esimCardProps={props.esimCardProps}
                  />
                ) : null;
              });
            })}
          </>
        )}
      </Accordion>
    </>
  );
};
export default EsimList;
