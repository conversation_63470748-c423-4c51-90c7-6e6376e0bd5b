import { Currency } from "@/core/Currency";
import { IPlan } from "@/interfaces/IPlan";
import { countryAlias, getCDNUrl } from "@/utils";
import {
  Box,
  Divider,
  Flex,
  Image,
  List,
  Paper,
  Text,
  ThemeIcon,
  Title,
} from "@mantine/core";
import React from "react";
import { Trans, useTranslation } from "next-i18next";

const PlanConfirmationCard = (props: { plan: IPlan }) => {
  const { t } = useTranslation();
  const selectedCode = React.useMemo(() => Currency.getSelectedCurrency(), []);

  return (
    <Box>
      <Paper
        pos={"relative"}
        p={"2rem"}
        bg={" linear-gradient(324.8deg, #082699 0%, #2B3D82 100%)"}
      >
        <Flex direction={"column"} gap={"sm"} c="white">
          <Flex>
            <Flex direction={"column"} w="100%">
              <Text c="white">{props.plan.planProvider} eSIM</Text>
              <Title
                sx={{
                  textTransform: "capitalize",
                }}
                order={4}
                c="white"
              >
                <Trans i18nKey={`countries:${props.plan.country?.name}`}>
                  {/* @ts-ignore */}
                  {countryAlias(props.plan.country?.name || props.plan.country)}
                </Trans>
              </Title>
            </Flex>
            <Image
              mt={-65}
              fit="contain"
              width={"9rem"}
              height={"5rem"}
              src={getCDNUrl("/assets/eSIMCard.png")}
            />
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <Flex w="100%" justify={"flex-start"} align={"center"}>
              <ThemeIcon color={"transparent"} size={18} radius="xl" mr={5}>
                <Image src={getCDNUrl(`/assets/data.png`)} />
              </ThemeIcon>
              <Flex w="100%" justify={"space-between"} align={"center"}>
                <Text c="white" fw={"600"}>
                  {t("region:data")}
                </Text>
                <Text c="white" fw={"600"}>
                  {props.plan.dataVolume}
                  {props.plan.dataUnit}/{t("common:siminfo-card.perday.unit")}
                </Text>
              </Flex>
            </Flex>
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <Flex w="100%" justify={"flex-start"} align={"center"}>
              <ThemeIcon color={"transparent"} size={18} radius="xl" mr={5}>
                <Image src={getCDNUrl(`/assets/validity.png`)} />
              </ThemeIcon>
              <Flex w="100%" justify={"space-between"} align={"center"}>
                <Text c="white" fw={"600"}>
                  {t("region:validity")}
                </Text>
                <Text c="white" fw={"600"}>
                  {t("common:siminfo-card.validity.unit", {
                    count: props.plan.validityDays, // Get the usage plan days without unit
                    defaultValue: "{{ count }} day",
                    defaultValue_other: "{{ count }} days",
                  })}
                </Text>
              </Flex>
            </Flex>
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <Flex w="100%" justify={"flex-start"} align={"center"}>
              <Flex w="100%" justify={"space-between"} align={"center"}>
                <Flex justify={"center"} align={"center"} gap={5}>
                  <Text c="white" fw={"600"}>
                    {t("payment:price.label")}
                  </Text>
                  {/* <Text size="xs" c="white" fw={"400"}>
                    (Incl. tax)
                  </Text> */}
                </Flex>
                <Flex direction={"column"} align={"end"}>
                  <Text c="white" fw={"600"}>
                    {selectedCode?.code}{" "}
                    {Currency.formatToSelected({
                      xe: props.plan.xe,
                      price: +props.plan.price,
                    })}
                  </Text>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <List c="white" size={"xs"}>
              <List.Item>{t("payment:descriptions1")}</List.Item>
              <List.Item>{t("payment:descriptions2")}</List.Item>
            </List>
          </Flex>
        </Flex>
      </Paper>
    </Box>
  );
};
export default PlanConfirmationCard;
