import { getCDNUrl, isAppOnlyJapanese } from "@/utils";
import {
  Anchor,
  Center,
  Divider,
  Flex,
  Image,
  Paper,
  Text,
} from "@mantine/core";
import Link from "next/link";
import { useTranslation } from "next-i18next";

const Footer = (props: { minHeight?: boolean }) => {
  const { t, i18n } = useTranslation();
  return (
    <Flex direction={"column"}>
      <Divider
        sx={{
          borderTopColor: "#DDD",
        }}
      />
      <Paper>
        <Flex
          mih={props.minHeight ? "500px" : undefined}
          sx={{
            paddingTop: "40px",
            paddingRight: "1rem",
            paddingLeft: "1rem",
            paddingBottom: "10px",
            justifyContent: "space-evenly",
          }}
          direction="column"
          gap={12}
        >
          <Link
            href={
              isAppOnlyJapanese()
                ? "https://www.gmobile.biz/terms-of-service/?value=esim"
                : "/terms-and-conditions?source=footer"
            }
            style={{
              textDecoration: "none",
              color: "#4A4A4A",
              fontSize: "0.75rem",
            }}
          >
            {t("home:termsandconditions")}
          </Link>
          <Link
            href="/terms-for-membership?source=footer"
            style={{
              textDecoration: "none",
              color: "#4A4A4A",
              fontSize: "0.75rem",
            }}
          >
            {t("home:termsformembership", "Terms for Membership")}
          </Link>
          <Anchor
            href={
              i18n?.language === "jp"
                ? "https://www.inbound-platform.com/privacy/"
                : "https://www.inbound-platform.com/en/privacy/"
            }
            target="_blank"
            color="#4A4A4A"
            size="0.75rem"
            sx={{}}
          >
            {t("home:privacypolicy")}
          </Anchor>
          <Link
            href={
              isAppOnlyJapanese()
                ? "https://www.gmobile.biz/law/?value=esim"
                : "/legal-notation?source=footer"
            }
            style={{
              textDecoration: "none",
              color: "#4A4A4A",
              fontSize: "0.75rem",
            }}
          >
            {t("home:legalnotation")}
          </Link>
          <Anchor
            href={`https://www.inbound-platform.com/${
              isAppOnlyJapanese() ? "" : "en/"
            }company/`}
            target="_blank"
            size={"sm"}
            color="#4A4A4A"
          >
            {t("home:aboutus")}
          </Anchor>
           <Link
          href={
            isAppOnlyJapanese()
              ? "/help"
              : `/${i18n.language}/help`
          }
          style={{
            textDecoration: "none",
            color: "#4A4A4A",
            fontSize: "0.75rem",
          }}
        >
          {t("faq:title", "よくあるご質問")}
        </Link>
          <Image src={getCDNUrl("/assets/payment.png")} alt="payment" />
          <div
            style={{
              textDecoration: "none",
              color: "#4A4A4A",
              fontSize: "0.75rem",
            }}
          >
            <Flex direction="column" align="center">
              <Text size={"sm"} opacity={"54.41%"}>
                {t("common:company-name")}
              </Text>
              <Text size={"sm"} opacity={"54.41%"}>
                {t("common:division-name")}
              </Text>
              <Text size={"sm"} opacity={"54.41%"}>
                {t("common:company-address")}
              </Text>
              <a style={{ color: "#E40071" }} href="mailto:<EMAIL>">
                <EMAIL>
              </a>
              <Flex gap={10} sx={{ marginTop: 14 }}>
                <Link href={"/"}>
                  <Image
                    alt="Global Mobile logo"
                    src={getCDNUrl("/assets/hero/gm-logo.svg")}
                    width={76.18}
                    height={30}
                  />
                </Link>
                <Divider
                  opacity={"54.41%"}
                  sx={{
                    borderTopColor: "#DDD",
                  }}
                  orientation="vertical"
                />
                <Link href={"/"}>
                  <Image
                    alt="Global Mobile esim logo"
                    src={getCDNUrl("/assets/hero/gm-esim-logo.svg")}
                    width={48.01}
                    height={29.1}
                  />
                </Link>
              </Flex>
            </Flex>
          </div>
        </Flex>
        <Center sx={{ backgroundColor: "#C72B4D" }}>
          <Text size={"sm"} c="white">
            {" "}
            © 2023 Inbound Platform Corp.
          </Text>
        </Center>
      </Paper>
    </Flex>
  );
};
export default Footer;
