import { reverseKebabCase } from "@/utils";
import { Breadcrumbs, BreadcrumbsProps } from "@mantine/core";

const AppBreadCrumb = (props: BreadcrumbsProps) => {
  return (
    <Breadcrumbs
      styles={{
        root: {
          a: {
            fontSize: "12px",
            color: "#777777",
            textDecoration: "none",
            textTransform: "capitalize",
          },
        },
        ...props.styles,
      }}
      {...props}
    >
      {props.children}
    </Breadcrumbs>
  );
};
export default AppBreadCrumb;
