import { Avatar, Box, Card, Flex, Text } from "@mantine/core";
import Link from "next/link";
import { useTranslation } from "next-i18next";
import { getCDNUrl } from "@/utils";

const UnAuthProfile = () => {
  const { t } = useTranslation();

  return (
    <Card w="100%" shadow="xs">
      <Flex justify={"space-between"} align={"center"}>
        <Box>
          <Link
            href={"/auth/signin"}
            style={{
              textDecoration: "none",
            }}
          >
            <Text
              size={"13px"}
              weight={600} color="app-pink.4" td={"none"}>
              {t("common:btn.login")} / {t("signup:title", "Sign up")}
            </Text>
          </Link>
        </Box>
        <Avatar src={getCDNUrl("/assets/tab-user.png")} color="dark" radius={100} />
      </Flex>
    </Card>
  );
};
export default UnAuthProfile;
