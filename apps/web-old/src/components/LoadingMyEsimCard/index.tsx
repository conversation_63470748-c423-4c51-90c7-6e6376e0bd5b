import { Flex, Group, Paper, Skeleton, Stack } from "@mantine/core";

const LoadingMyEsimCard = () => {
  return (
    <Paper w={"100%"} shadow="sm" mih={120}>
      <Skeleton height={30} radius={"xs"} />
      <Group w={"100%"} mt={10} p={"sm"}>
        <Skeleton width={"20%"} height={50} />
        <Skeleton width={"70%"} height={50} />
      </Group>
    </Paper>
  );
};
export default LoadingMyEsimCard;
