import Storage from "@/core/Storage";
import { getCDNUrl } from "@/utils";
import { Button, ButtonProps, Image } from "@mantine/core";
import { useRouter } from "next/router";

const Btn = (props: ButtonProps) => (
  <Button
    fullWidth
    leftIcon={
      <Image
        src={getCDNUrl("/assets/icon-android.png")}
        width={16}
        height={16}
        fit="contain"
      />
    }
    styles={(theme) => ({
      label: {
        color: "black",
      },
      root: {
        ":hover": {
          background: "#ffffff",
        },
      },
      icon: {
        position: "absolute",
        left: "1rem",
      },
    })}
    bg={"white"}
    {...props}
  >
    {props.children}
  </Button>
);
const ActivateButtons = (props: { orderdId: string }) => {
  const router = useRouter();
  return (
    <>
      <Btn
        leftIcon={
          <Image
            src={getCDNUrl("/assets/icon-ios.png")}
            width={16}
            height={16}
            fit="contain"
          />
        }
        //@ts-expect-error
        onClick={() => {
          Storage.getInstance().set("lastItem", props.orderdId);
          router.push(`/app/orders/${props.orderdId}/ios/activate`)
        }
        }
      >
        iOS
      </Btn>
      <Btn
        leftIcon={
          <Image
            src={getCDNUrl("/assets/icon-android.png")}
            width={16}
            height={16}
            fit="contain"
          />
        }
        //@ts-expect-error
        onClick={() => {
          Storage.getInstance().set("lastItem", props.orderdId);
          router.push(`/app/orders/${props.orderdId}/android/activate`)
        }}
      >
        Android
      </Btn>
    </>
  );
};
export default ActivateButtons;
