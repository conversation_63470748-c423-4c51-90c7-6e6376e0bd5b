import Link, { LinkProps } from "next/link"

const InternalLink = ({ target, defaultColor, underline = false, ...props }: LinkProps & {
    style?: any,
    underline?: boolean,
    target?: string
    children: React.ReactNode,
    defaultColor?: boolean
}) => {

    return (
        <Link
            target={target}
            {...props}
            style={{
                color: !defaultColor ? "#333333" : "blue",
                textDecoration: underline ? "underline" : "none",
                textDecorationColor: "#333333",
                ...(props.style || {}),

            }}>
            {props.children}
        </Link>
    );
}
export default InternalLink