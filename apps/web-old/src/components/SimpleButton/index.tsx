import { ButtonProps, Text } from "@mantine/core";

export const SimpleButton = (
  props: ButtonProps & {
    isActive?: boolean;
  }
) => {
  return (
    <Text
      sx={{
        fontWeight: 600,
        color: props.isActive ? "#333333" : "#999999",
        border: props.isActive ? "2px solid #333333" : "0px",
        borderRadius: ".4rem",
        padding: ".4rem 1rem",
        background: "white",
      }}
    >
      {props.children}
    </Text>
  );
};
