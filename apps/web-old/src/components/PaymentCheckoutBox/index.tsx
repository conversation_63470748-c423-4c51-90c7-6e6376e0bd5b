import { Flex } from "@mantine/core";
import { useGlobalState } from "@/store";
import { useRouter } from "next/router";
import React, { ForwardedRef } from "react";
import CheckoutForm from "./CheckoutForm";

const PaymentCheckoutBox = React.forwardRef(
  ({ planId, price,
    ...props
  }: {
    isAgreementError: boolean,
    clientSecret: string,
    price: string, planId: string,
    setPaymentMethod: (paymentMethod: string) => void,
    setPaymentRequest: (pr: any) => void
    handleCompatibilityStatus: (status: boolean) => void
  }, ref: ForwardedRef<HTMLFormElement>) => {

    return (
      <Flex direction={"column"} gap={10}>
        <CheckoutForm
          isAgreementError={props.isAgreementError}
          setPaymentMethod={props.setPaymentMethod}
          setPaymentRequest={props.setPaymentRequest}
          price={+price}
          planId={planId}
          clientSecret={props.clientSecret}
          ref={ref}
          handleCompatibilityStatus={props.handleCompatibilityStatus}
        />
      </Flex>
    );
  }
);
PaymentCheckoutBox.displayName = "PaymentCheckoutBox";
export default PaymentCheckoutBox;
