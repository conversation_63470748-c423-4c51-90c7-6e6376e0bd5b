import { useRouter } from "next/router";
import React, {
  ForwardedRef,
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";

import {
  Accordion,
  Anchor,
  Avatar,
  Checkbox,
  Group,
  Image,
  Radio,
  Text,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { notifications, showNotification } from "@mantine/notifications";

import { useElements, useStripe } from "@stripe/react-stripe-js";
import { IconCreditCard } from "@tabler/icons-react";
import { Trans, useTranslation } from "next-i18next";

import { getCDNUrl, isAppOnlyJapanese } from "@/utils";

import { ApiService } from "@/api";

import CompatibleDevicesModal from "@/containers/CompatibleDevicesModal";
import { useGlobalState } from "@/store";

import InternalLink from "../InternalLink";
import LegacyCheckoutForm from "../LegacyCheckoutForm";
import countriesJson from "./countries-code.json";

interface ItemProps extends React.ComponentPropsWithoutRef<"div"> {
  image: string;
  label: string;
  description: string;
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(
  ({ image, label, description, ...others }: ItemProps, ref) => (
    <div ref={ref} {...others}>
      <Group noWrap>
        <Avatar src={image} size={"xs"} />

        <div>
          <Text size="sm">{label}</Text>
        </div>
      </Group>
    </div>
  )
);

SelectItem.displayName = "SelectItem";
const CheckoutForm = React.forwardRef(
  (
    {
      isAgreementError,
      planId,
      clientSecret,
      price,
      ...props
    }: {
      planId: string;
      clientSecret: string;
      price: number;
      isAgreementError: boolean;
      setPaymentRequest: (pr: any) => void;
      setPaymentMethod: (paymentMethod: string) => void;
      handleCompatibilityStatus: (status: boolean) => void;
    },
    ref: ForwardedRef<HTMLFormElement>
  ) => {
    const [paymentRequest, setPaymentRequest] = useState(null);
    const [canApplePay, setCanApplePay] = useState(false);
    const [canGooglePay, setCanGooglePay] = useState(false);
    const router = useRouter();
    const [opened, { open, close }] = useDisclosure(false);
    const [cardErrMessage, setCardErrMessage] = React.useState<string | null>(
      null
    );
    const { t, i18n } = useTranslation();
    const [cardCountry, setCardCountry] = React.useState<string | null>(null);
    const toggleLoading = useGlobalState((state) => state.toggleLoading);
    const stripe = useStripe();
    const elements = useElements();

    const [lastClientSecret, setLastClientSecret] = React.useState(null);
    const [checkoutAgreeError, setCheckoutAgreeError] = React.useState<
      string | null
    >(null);
    const [IsCompatibilityChecked, setIsCompatibilityChecked] =
      React.useState(false);
    const [isTermsChecked, setIsTermsChecked] = React.useState(false);
    const [termsError, setTermsError] = React.useState("");
    const setCompatibilityStatus = () => {
      const oldVal = IsCompatibilityChecked;
      setIsCompatibilityChecked(!oldVal);
      props.handleCompatibilityStatus(!oldVal && isTermsChecked);
    };
    const handleLegacyPaymentMethod = async (
      ev: any,
      onSuccess?: () => void,
      onError?: () => void,
      isWallet = false
    ) => {
      if (!IsCompatibilityChecked) {
        // setCheckoutAgreeError(
        //   t("payment:errors.device-confirm") ||
        //     "Please confirm if your device is compatible before proceeding!"
        // );
        return;
      }

      if (!isTermsChecked) {
        // setTermsError(t("payment:errors.agree-to-terms") || "");
        return;
      }

      if (!stripe || !elements) {
        // Stripe.js has not yet loaded.
        // Make sure to disable form submission until Stripe.js has loaded.
        return;
      }

      toggleLoading();
      let result;
      if (planId) {
        const response = await ApiService.purchasePlan({
          sessionSecret: clientSecret,
          locale: i18n.language || "en",
          products: {
            planId,
            qty: 1,
          },
        });
        result = response.data?.data;
        setLastClientSecret(result);

        const { error, paymentIntent } = await stripe.confirmCardPayment(
          result.payment.client_secret,
          {
            payment_method: isWallet
              ? ev.paymentMethod.id
              : {
                  card: elements?.getElement("cardNumber"),
                },
          },
          isWallet ? { handleActions: false } : {}
        );

        toggleLoading(false);
        if (error) {
          // This point will only be reached if there is an immediate error when
          // confirming the payment. Show error to your customer (for example, payment
          // details incomplete)
          onError?.();
          //@ts-expect-error
          setCardErrMessage(error.message);
        } else {
          if (paymentIntent.status === "succeeded") {
            onSuccess?.();
            (router.isReady = true),
              router.replace(
                window.location.protocol +
                  "//" +
                  window.location.host +
                  `/${i18n.language}/app/orders/${result.orderId}/complete`
              );
          }
        }
      }
    };

    const handleSubmit = React.useCallback(
      async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        if (!cardCountry) {
          setCardErrMessage("Please select card issuing country.");
          return;
        }
        setCardErrMessage("");
        if (!IsCompatibilityChecked) {
          setCheckoutAgreeError(
            t("payment:errors.device-confirm") ||
              "Please confirm if your device is compatible before proceeding!"
          );
          return;
        }
        if (!stripe || !elements) {
          return;
        }
        try {
          window?.scrollTo?.({
            left: 0,
            top: 0,
            behavior: "smooth",
          });
          toggleLoading();
          const { error } = await elements.submit();
          if (error) return;
          const paymentIntent = await ApiService.purchasePlan({
            locale: i18n.language || "en",
            sessionSecret: clientSecret,
            products: {
              planId,
              qty: 1,
            },
          });
          const result = await stripe.confirmPayment({
            clientSecret: paymentIntent.data.data.payment.client_secret,
            //`Elements` instance that was used to create the Payment Element
            elements,
            confirmParams: {
              return_url:
                window.location.protocol +
                "//" +
                window.location.host +
                `/${i18n.language}/app/orders/${paymentIntent.data.data.orderId}/complete`,
              payment_method_data: {
                billing_details: {
                  address: {
                    country: cardCountry,
                  },
                },
              },
            },
          });
          if (result.error) {
            notifications.show({
              message:
                result.error.message ||
                "We are facing some issues, please try again.",
              color: "red",
            });
          } else {
            // Your customer will be redirected to your `return_url`. For some payment
            // methods like iDEAL, your customer will be redirected to an intermediate
            // site first to authorize the payment, then redirected to the `return_url`.
          }
        } catch (err) {
          console.log(err);
        } finally {
          toggleLoading();
        }
      },
      [
        stripe,
        cardCountry,
        planId,
        elements,
        clientSecret,
        IsCompatibilityChecked,
      ]
    );

    const countries = useMemo(() => {
      return Object.keys(countriesJson).map((key) => ({
        value: key,
        label:
          //@ts-expect-error
          countriesJson?.[key]?.[i18n.language] || countriesJson?.[key]?.["en"],
        image: getCDNUrl(`/assets/flags/16x16/${key.toLowerCase?.()}.png`),
      }));
    }, [i18n.language]);
    let buttonList = ["credit"];

    useEffect(() => {
      setCardErrMessage(null);
    }, [cardCountry]);

    useEffect(() => {
      setCheckoutAgreeError(null);
    }, [IsCompatibilityChecked]);

    useEffect(() => {
      if (cardErrMessage) {
        showNotification({
          message: cardErrMessage,
          color: "red",
        });
        setCardErrMessage("");
      }
    }, [cardErrMessage]);
    const [activePaymentMethod, setActivePaymentMethod] = useState("credit");
    const handlePaymentMethodClick = useCallback(
      (value: string) => {
        setActivePaymentMethod(value || "credit");
        props.setPaymentMethod(value || "credit");
      },
      [props, IsCompatibilityChecked]
    );

    useEffect(() => {
      if (!paymentRequest) return;
      //@ts-ignore
      paymentRequest.on("paymentmethod", async (ev) => {
        handleLegacyPaymentMethod(
          ev,
          () => {
            ev.complete("success");
          },
          () => {
            ev.complete("fail");
          },
          true
        );
      });

      props.setPaymentRequest(paymentRequest);
    }, [paymentRequest, handleLegacyPaymentMethod]);

    useEffect(() => {
      if (stripe) {
        if (!Number(price || 0)) {
          router.back();
          return;
        }
        const pr = stripe.paymentRequest({
          country: "JP",
          currency: "jpy",
          total: {
            label: "",
            amount: Number(price),
          },
          requestPayerName: true,
          requestPayerEmail: true,
        });

        const checkCanMakePayment = async () => {
          const canMakePaymentObject = await pr.canMakePayment();
          if (canMakePaymentObject) {
            const { applePay, googlePay, link } = canMakePaymentObject;
            if (applePay) {
              setCanApplePay(true);
              if (!buttonList.includes("apple")) {
                buttonList.push("apple");
              }
            }

            if (googlePay) {
              setCanGooglePay(true);
              if (!buttonList.includes("google")) {
                buttonList.push("google");
              }
            }

            //@ts-ignore
            setPaymentRequest(pr);
          }
        };

        checkCanMakePayment();
      }
    }, [stripe]);

    return (
      <>
        <CompatibleDevicesModal opened={opened} onClose={close} />
        <Accordion
          styles={{
            chevron: {
              transform: "rotate(-90deg)",
              display: "none",
              "&[data-rotate]": {
                display: "none",
                transform: "rotate(0deg)",
              },
            },
          }}
          value={activePaymentMethod}
          onChange={handlePaymentMethodClick}
          variant="contained"
          defaultValue="credit"
        >
          <Accordion.Item value="credit">
            <Accordion.Control>
              <Group align="center">
                <Radio
                  readOnly
                  color={"app-pink.4"}
                  checked={activePaymentMethod === "credit"}
                />
                <Group spacing={5} align="center">
                  <IconCreditCard size={35} strokeWidth={1} color={"black"} />
                  <Text weight={600}>Credit Card</Text>
                </Group>
              </Group>
            </Accordion.Control>
            <Accordion.Panel>
              {activePaymentMethod === "credit" && (
                <LegacyCheckoutForm
                  formRef={ref}
                  clientSecret={clientSecret}
                  planId={planId}
                  handlePaymentMethod={handleLegacyPaymentMethod}
                />
              )}
            </Accordion.Panel>
          </Accordion.Item>
          {canApplePay && (
            <Accordion.Item value="apple">
              <Accordion.Control>
                {" "}
                <Group align="center">
                  <Radio
                    readOnly
                    color={"app-pink.4"}
                    checked={activePaymentMethod === "apple"}
                  />
                  <Group spacing={5} align="center">
                    <Image
                      fit="contain"
                      src={getCDNUrl(`/assets/payments/apple_pay_logo.png`)}
                      width={37}
                      height={23}
                      alt={"Apple Pay Logo"}
                    />
                    <Text weight={600}>Apple Pay</Text>
                  </Group>
                </Group>
              </Accordion.Control>
            </Accordion.Item>
          )}
          {canGooglePay && (
            <Accordion.Item value="google">
              <Accordion.Control>
                {" "}
                <Group align="center">
                  <Radio
                    readOnly
                    color={"app-pink.4"}
                    checked={activePaymentMethod === "google"}
                  />
                  <Group spacing={5} align="center">
                    <Image
                      fit="contain"
                      src={getCDNUrl(`/assets/payments/google_pay_logo.png`)}
                      width={37}
                      height={23}
                      alt={"Google Pay Logo"}
                    />
                    <Text weight={600}>Google Pay</Text>
                  </Group>
                </Group>
              </Accordion.Control>
            </Accordion.Item>
          )}
        </Accordion>
        <Checkbox
          error={checkoutAgreeError || isAgreementError}
          onChange={setCompatibilityStatus}
          checked={IsCompatibilityChecked}
          sx={{
            justifyContent: "center",
            alignItems: "center",
          }}
          label={
            <Text color={"#555555"}>
              {t(
                "region:checkoutagreement",
                "Before completing this order, please confirm your device is eSIM comptabile and network-unlocked."
              )}
              <Anchor
                color="#333333"
                onClick={open}
                underline
                sx={{
                  textDecoration: "underline",
                }}
              >
                {" "}
                {t("region:checkcompatibility", "Check Compatibility")}
              </Anchor>
            </Text>
          }
        ></Checkbox>

        <Checkbox
          error={termsError || isAgreementError}
          mt="md"
          checked={isTermsChecked}
          onChange={(e) => {
            setIsTermsChecked(e.target.checked);
            props.handleCompatibilityStatus(
              IsCompatibilityChecked && e.target.checked
            );
          }}
          label={
            <Text color={"#555555"}>
              <Trans i18nKey="signup:agreement.txt">
                I agree to Global Mobile&apos;s
                <InternalLink
                  underline
                  target="_blank"
                  href={
                    isAppOnlyJapanese()
                      ? "https://www.gmobile.biz/terms-of-service/?value=esim"
                      : "/terms-and-conditions"
                  }
                >
                  Terms and Conditions
                </InternalLink>{" "}
                <InternalLink
                  underline
                  target="_blank"
                  href={
                    i18n?.language === "jp"
                      ? "https://www.inbound-platform.com/privacy/"
                      : "https://www.inbound-platform.com/en/privacy/"
                  }
                >
                  Privacy Policy{" "}
                </InternalLink>
                <InternalLink
                  underline
                  target="_blank"
                  href="/terms-for-membership"
                >
                  Terms for membership.
                </InternalLink>
              </Trans>
            </Text>
          }
        />
      </>
    );
  }
);
CheckoutForm.displayName = "CheckoutForm";
export default CheckoutForm;
