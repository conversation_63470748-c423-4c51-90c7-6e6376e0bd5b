import { TextInput, TextInputProps } from "@mantine/core";

const AppTextInput = (props: TextInputProps) => {
  return (
    <TextInput
      styles={{
        error: {
          fontSize: "10px"
        },
        input: {
          boxSizing: "border-box",
          width: "100%",
          border: "1px solid #DDDDDD",
          backgroundColor: "#FFFFFF",
          "::placeholder": {
            color: "#AAAAAA",
          },
        },
        label: {
          fontSize: "12px",
          lineHeight: "18px",
          letterSpacing: 0,
          color: "#555555",
        },
      }}
      size="lg"
      sx={{}}
      {...props}
    />
  );
};
export default AppTextInput;
