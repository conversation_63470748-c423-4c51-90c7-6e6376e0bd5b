import { IOrder } from "@/interfaces/IOrder";
import { <PERSON>ton, Divider, Flex, Image, Text, ThemeIcon } from "@mantine/core";
import { useRouter } from "next/router";
import ActivateButtons from "../ActivateButtons";
import { countryAlias, getCDNUrl } from "@/utils";
import { Trans, useTranslation } from "next-i18next";

const MyEsimCard = ({

  order,
  ...props
}: {
  hideButton?: boolean;
  status?: string
  isExpired?: boolean
  order: IOrder;
}) => {
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <Flex direction={"column"} gap={"sm"} c="white">
      <Text size={11} c="white">
        ICCID: {order.iccid || "N/A"}
      </Text>
      <Divider color="#3E53A3" />
      <Flex>
        <Flex w="100%" justify={"flex-start"} align={"center"}>
          <ThemeIcon color={"transparent"} size={18} radius="xl" mr={5}>
            <Image src={getCDNUrl(`/assets/coverage.png`)} />
          </ThemeIcon>
          <Flex w="100%" justify={"space-between"} align={"center"}>
            <Text c="white" fw={"600"}>
              {t("region:coverage")}
            </Text>
            <Text tt="capitalize" c="white" fw={"600"}>
              <Trans i18nKey={`countries:${order.plan.country.name}`}>
                {/* @ts-ignore */}
                {countryAlias(order?.plan?.country?.name || order?.plan?.country)}
              </Trans>
            </Text>
          </Flex>
        </Flex>
      </Flex>
      <Divider color="#3E53A3" />

      <Flex>
        <Flex w="100%" justify={"flex-start"} align={"center"}>
          <ThemeIcon color={"transparent"} size={18} radius="xl" mr={5}>
            <Image src={getCDNUrl(`/assets/data.png`)} />
          </ThemeIcon>
          <Flex w="100%" justify={"space-between"} align={"center"}>
            <Text c="white" fw={"600"}>
              {t("region:data")}

            </Text>
            <Text c="white" fw={"600"}>
              {order.plan.dataId}
            </Text>
          </Flex>
        </Flex>
      </Flex>
      <Divider color="#3E53A3" />
      <Flex>
        <Flex w="100%" justify={"flex-start"} align={"center"}>
          <ThemeIcon color={"transparent"} size={18} radius="xl" mr={5}>
            <Image src={getCDNUrl(`/assets/validity.png`)} />
          </ThemeIcon>
          <Flex w="100%" justify={"space-between"} align={"center"}>
            <Text c="white" fw={"600"}>
              {t("region:validity")}
            </Text>
            <Text c="white" fw={"600"}>
              {t("common:siminfo-card.validity.unit", {
                count: order.plan.validityDays, // Get the usage plan days without unit
                defaultValue: "{{ count }} day",
                defaultValue_other: "{{ count }} days",
              })}
            </Text>
          </Flex>
        </Flex>
      </Flex>
      <Divider color="#3E53A3" />
      {props.hideButton && props.isExpired &&
        <Button
          styles={{
            label: {
              color: "black",
            },
            root: {
              ":hover": {
                background: "#ffffff",
              },
            },
          }}
          bg={"white"}

          /* @ts-ignore */
          onClick={() => router.push("/app/checkout/plan/" + order.plan.id)}
        >
          {t("region:buyagain")}
        </Button>
      }
      {
        props.status === "expired" &&
        <>
          <Button
            styles={{
              root: {
                borderColor: "white",
              },
              label: {
                color: "white",
              },
            }}
            variant="outline"
            color="white"
            onClick={() => router.push("/app/orders/" + order.orderId)}
          >
            {t("region:details")}
          </Button>
          <Button
            styles={{
              label: {
                color: "black",
              },
              root: {
                ":hover": {
                  background: "#ffffff",
                },
              },
            }}
            bg={"white"}

            /* @ts-ignore */
            onClick={() => router.push("/app/checkout/plan/" + order.plan.id)}
          >
            {t("region:buyagain")}
          </Button>
        </>
      }
      {!props.hideButton && (
        <>
          {order.isActivated && props.status !== "expired" && (
            <>
              <Button
                styles={{
                  label: {
                    color: "#333333",
                  },
                  root: {
                    ":hover": {
                      background: "#ffffff",
                    },
                  },
                }}
                bg={"white"}
                onClick={() => router.push("/app/orders/" + order.orderId)}
              >
                {t("region:checkusage")}
              </Button>

            </>

          )}
          {!order.isActivated && (
            <ActivateButtons orderdId={order.orderId as string} />
          )}
        </>
      )}
    </Flex>
  );
};
export default MyEsimCard;
