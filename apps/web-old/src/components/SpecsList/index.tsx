import { INetwork } from "@/interfaces/INetwork";
import { capitalizeFirstLetter, countryAlias, getCDNUrl, regionalOrCountry } from "@/utils";
import { Anchor, Flex, Image, List, Text, ThemeIcon, Divider } from "@mantine/core";
import React from "react";
import { useTranslation } from "next-i18next";


const SpecsList = (props: {
  isModal?: boolean;
  country: string,
  network: INetwork[],
  onClick?: (option: {
    type: string,
    value: Array<string>
  }) => void
}) => {

  const { t } = useTranslation()
  const specs = React.useMemo(() => [
    {
      name: t("region:coverage"),
      value: regionalOrCountry(props.country) === "Country" ? t(`countries:${props.country}`, capitalizeFirstLetter(countryAlias(props.country || ""))) :
        <Anchor
          td={"underline"}
          color="blue"
          onClick={() => props.onClick?.({
            type: "specs",
            value: []
          })}>{t("region:availablecountries")}</Anchor>
      ,
      iconURL: "coverage.png",
      invert: true,
    },
    {
      name: t("region:network", "NETWORK"),
      value: `${props.network?.map?.(item => `${item.name} ${item.networkGeneration}`?.replace?.(/^\-/, "")).join(" - ")}`,
      iconURL: "specs-network.png",
    },
    {
      name: t("region:plantype", "PLAN TYPE"),
      value: t("region:dataonlynocalls", "Data only (No Calls)"),
      iconURL: "specs-type.png",
    },
    {
      name: t("region:e-kyc", "eKYC (IDENTITY VERIFICATION)"),
      value: t("region:notrequired", "Not required"),
      iconURL: "specs-ekyc.png",
    },
    {
      name: t("region:activationpolicy.title", "ACTIVATION POLICY"),
      value: t("region:activationpolicy.desc", "ACTIVATION POLICY"),
      iconURL: "specs-activation.png",
    },
    {
      name: t("region:activationperiod.title", "ACTIVATION PERIOD"),
      value: t("region:activationperiod.desc", "Can be activated within 180 days of purchase."),
      iconURL: "specs-activation.png",
    },
    {
      name: t("region:other", "OTHER"),
      value: t("region:nonrefundable", "Non-refundable"),
      iconURL: "specs-other.png",
    },
  ], [])

  return (
    <List spacing={props.isModal ? 0 : "1.2rem"} size="sm" center>
      {specs.map((spec, index) => (
        <List.Item
          key={index}
          sx={{
            borderBottom: props.isModal ? "1px solid #ccc" : "unset"
          }}
          px={props.isModal ? "1rem" : 0}
          py={props.isModal ? "0.75rem" : 0}
          icon={
            <ThemeIcon
              color={spec.invert ? "dark" : "transparent"}
              size={24}
              radius="xl"
            >
              <Image src={getCDNUrl(`/assets/${spec.iconURL}`)} />
            </ThemeIcon>
          }
        >
          <Flex direction={"column"}>
            <Text fw={600} c="dimmed" fz={12}>
              {spec.name}
            </Text>
            <Text color="app-dark.3" fw={500} fz={15}>
              {spec.value}
            </Text>
          </Flex>
        </List.Item>
      ))}
    </List>
  );
};
export default SpecsList;
