import {
  Paper,
  Title,
  Box,
  Accordion,
  Text,
  <PERSON><PERSON>,
  Stack,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { IconPhoto, IconLetterQ } from '@tabler/icons-react';
import styles from "./homefaq.module.css";

const HowItWorks = () => {
  const { t } = useTranslation();

  return (
    <Paper bg="transparent">
      <Title
        p="2rem 1rem 1rem"
        order={2}
        align="center"
        sx={{
          fontSize: "1.4rem",
          lineHeight: "unset !important"
        }}
      >
        {t("faq:home.title")}
      </Title>
      <Box p="1rem">
        <Accordion
          variant="separated"
          radius=""
          styles={{
            control: {
              borderRadius: "0.5rem",
              background: "white",
            },
            item: {
              backgroundColor: "transparent !important",
              borderColor: "transparent !important",
            },
            label: {
              fontWeight: "bold",
              fontSize: "14px",
            },
            content: {
              fontSize: "14px",
            }
          }}
        >
          <Accordion.Item value="item1">
            <Accordion.Control icon={
              <Box
                bg="app-pink.4"
                p="0.25rem"
                sx={{
                  borderRadius: "1rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <IconLetterQ color="white" />
              </Box>
            }>
              <Title
                order={3}
                size={14}
              >
                {t("faq:home.item1.q")}
              </Title>
            </Accordion.Control>
            <Accordion.Panel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item1.a")
                }}
                sx={{
                  marginBlockEnd: 0
                }}
              />
            </Accordion.Panel>
          </Accordion.Item>

          <Accordion.Item value="item2">
            <Accordion.Control icon={
              <Box
                bg="app-pink.4"
                p="0.25rem"
                sx={{
                  borderRadius: "1rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <IconLetterQ color="white" />
              </Box>
            }>
              <Title
                order={3}
                size={14}
              >
                {t("faq:home.item2.q")}
              </Title>
            </Accordion.Control>
            <Accordion.Panel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item2.a")
                }}
                sx={{
                  marginBlockEnd: 0
                }}
              />
            </Accordion.Panel>
          </Accordion.Item>

          <Accordion.Item value="item3">
            <Accordion.Control icon={
              <Box
                bg="app-pink.4"
                p="0.25rem"
                sx={{
                  borderRadius: "1rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <IconLetterQ color="white" />
              </Box>
            }>
              <Title
                order={3}
                size={14}
              >
                {t("faq:home.item3.q")}
              </Title>
            </Accordion.Control>
            <Accordion.Panel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item3.a") as string
                }}
                sx={{
                  marginBlockEnd: 0
                }}
              />

              <Box>
                <Text
                  component="p"
                  dangerouslySetInnerHTML={{
                    __html: t("faq:home.item3.list.title") as string
                  }}
                  sx={{
                    marginBlockEnd: 0,
                  }}
                />
                <Text
                  sx={{
                    marginBlockStart: 0,
                  }}
                >
                  <ul
                    className={styles["list"]}
                    dangerouslySetInnerHTML={{
                      __html: t("faq:home.item3.list.items") as string
                    }}
                  />
                </Text>
              </Box>

              <Box>
                <Text
                  component="p"
                  dangerouslySetInnerHTML={{
                    __html: t("faq:home.item3.manual.title") as string
                  }}
                  sx={{
                    marginBlockEnd: 0
                  }}
                />
                <Text
                  sx={{
                    marginBlockStart: 0,
                  }}
                >
                  <ul
                    className={styles["list"]}
                    dangerouslySetInnerHTML={{
                      __html: t("faq:home.item3.manual.items") as string
                    }}
                  />
                </Text>
              </Box>
            </Accordion.Panel>
          </Accordion.Item>

          <Accordion.Item value="item4">
            <Accordion.Control icon={
              <Box
                bg="app-pink.4"
                p="0.25rem"
                sx={{
                  borderRadius: "1rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <IconLetterQ color="white" />
              </Box>
            }>
              <Title
                order={3}
                size={14}
              >
                {t("faq:home.item4.q")}
              </Title>
            </Accordion.Control>
            <Accordion.Panel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item4.a")
                }}
                sx={{
                  marginBlockEnd: 0
                }}
              />
            </Accordion.Panel>
          </Accordion.Item>

          <Accordion.Item value="item5">
            <Accordion.Control icon={
              <Box
                bg="app-pink.4"
                p="0.25rem"
                sx={{
                  borderRadius: "1rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <IconLetterQ color="white" />
              </Box>
            }>
              <Title
                order={3}
                size={14}
              >
                {t("faq:home.item5.q")}
              </Title>
            </Accordion.Control>
            <Accordion.Panel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item5.a")
                }}
                sx={{
                  marginBlockEnd: 0
                }}
              />
            </Accordion.Panel>
          </Accordion.Item>
        </Accordion>
        <Stack spacing="0.5rem" mt="1rem">
          <Button
            fullWidth
            size="lg"
            radius="md"
            variant="outline"
            color="app-pink.4"
            component="a"
            href={"https://www.gmobile.biz/esim/help"}
            target="_blank"
          >
            <Text
              weight="bold"
              color="black"
              ml={10}
            >
              {t("faq:viewmore")}
            </Text>
          </Button>
        </Stack>
      </Box>
    </Paper>
  );
};
export default HowItWorks;
