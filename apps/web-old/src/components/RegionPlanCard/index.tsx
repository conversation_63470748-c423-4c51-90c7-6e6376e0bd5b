import { Card, Flex, Group, Image, Text } from "@mantine/core";
import { IconChevronRight } from "@tabler/icons-react";
import Link from "next/link";
import { countryAlias, countryNameException, getCDNUrl, normalizeAndKebabCase } from "@/utils";
import { Currency } from "@/core/Currency";
import { IPlan } from "@/interfaces/IPlan";
import { Trans, useTranslation } from "next-i18next";

const RegionPlanCard = (props: {
  originalName?: string;
  img: string | string[];
  name: string;
  price: string;
  xe: IPlan["xe"];
}) => {
  const { t, i18n } = useTranslation();
  const { language } = i18n;

  const convertedPrice = Currency.formatToSelected({
    xe: props.xe,
    price: +props.price,
  });


  return (
    <Card
      sx={{
        width: "100%",
        ":hover": {
          background: "#f9f9f9",
        },
        borderRadius: "0.2rem",
      }}
      shadow="0 0 10px 0 rgba(0,0,0,0.15)"
      padding="lg"
      h={60}
    >
      <Card.Section h={"100%"} mt={"0rem !important"}>
        <Link
          style={{
            textDecoration: "none",
            height: "100%",
          }}
          href={
            "/region/" + normalizeAndKebabCase(props.originalName || props.name)
          }
        >
          <Flex h={"100%"} justify={"space-between"} align={"center"}>
            <Group>
              <Image
                fit="contain"
                src={getCDNUrl(props.img[0])}
                width={50}
                height={"auto"}
                alt={props.name}
                withPlaceholder
                style={{
                  marginLeft: "1rem",
                }}
              />
              <Text
                size=".8rem"
                sx={{
                  lineHeight: ".8rem",
                  textTransform: "capitalize",
                }}
                fw={700}
              >
                {props.originalName ? (
                  <Trans i18nKey={`countries:${countryNameException(props.originalName)}`}>
                    {props.originalName}
                  </Trans>
                ) : (
                  <Trans i18nKey={`countries:${countryNameException(props.name)}`}>
                    {props.name}
                  </Trans>
                )}
              </Text>
            </Group>
            <Group mr={10}>
              <Text
                style={{
                  width: language === "jp" ? "4rem" : "6rem",
                }}
              >
                <Trans
                  i18nKey="common:regional-plan-amount"
                  t={t}
                  components={[
                    <Text key={0} component="span" size=".75rem" />,
                    <Text key={1} component="span" fw={600} size=".875rem" />,
                  ]}
                  values={{ amount: convertedPrice }}
                />
              </Text>
              <IconChevronRight
                height={"20px"}
                width={"20px"}
                color="#CCCCCC"
              />
            </Group>
          </Flex>
        </Link>
      </Card.Section>
    </Card>
  );
};
export default RegionPlanCard;