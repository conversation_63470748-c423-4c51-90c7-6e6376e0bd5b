import Image from "next/image";
import { getCDNUrl, isAppOnlyJapanese } from "@/utils";
import { Flex, Text, Title, Box, Divider, Space } from "@mantine/core";

const TopHeader = () => {
  return (
    <>
      <Box
        bg="app-pink.5"
        py={8}
        px={12}
      >
        <Title
          order={1}
          color="white"
          size={12}
          weight="normal"
        >
          グローバルeSIMデータ通信サービス・グロモバeSIM
        </Title>
      </Box>
      <Flex
        w="100%"
        py={8}
        px={12}
        gap={4}
        align={"center"}
      >
        <Image
          alt="Global Mobile logo"
          src={getCDNUrl("/assets/hero/gm-logo.svg")}
          width={87}
          height={35}
        />
        <Divider orientation="vertical" />
        <Image
          alt="Global Mobile esim logo"
          src={getCDNUrl("/assets/hero/gm-esim-logo.svg")}
          width={60}
          height={35}
        />
        <Space w="xs" />
        <Image
          src={getCDNUrl("/assets/jpx-logo.png")}
          width={35}
          height={35}
          alt="jpx-logo"
        />
        <Text 
          sx={{
            fontSize: "8px",
            color: "black",
            whiteSpace: "nowrap",
            lineHeight: "1.5 !important",
            margin: "0 4px"
          }}
        >
          <div>
            {isAppOnlyJapanese() ? "(株)インバウンドプラットフォーム" : "Inbound Platform Corp."}
          </div>
          <div>
            {isAppOnlyJapanese() ? "証券コード:5587" : "(TSE Code: 5587)"}
            
          </div>
        </Text>
      </Flex>
    </>
  )
};

export default TopHeader;