import { Currency } from "@/core/Currency";
import { IPlan } from "@/interfaces/IPlan";
import { Card, Flex, Grid, Text } from "@mantine/core";
import React from "react";
import { useTranslation } from "next-i18next";
import { isAppOnlyJapanese } from "@/utils";

type Props = {
  plan: IPlan;
  isActive?: boolean;
};

const EsimPlanCard = (props: Props) => {
  const { plan, isActive } = props;
  const { t } = useTranslation();

  const selectedCode = Currency.getSelectedCurrency();
  return (
    <Card
      sx={{
        boxShadow: "0 0 10px 0 rgba(0,0,0,0.15);",
        borderRadius: "0.25rem",
        border: isActive ? "1px solid #E40071" : "",
        backgroundColor: isActive ? "#FDEBF4" : "",
        cursor: "pointer",
      }}
    >
      <Grid align="center" justify="space-between">
        <Grid.Col px=".75rem" py="1.25rem">
          <Flex direction="row" justify="space-between" align={"center"}>
            <Text color="app-dark" weight={700} fz={14}>
              {t("common:siminfo-card.validity.unit", {
                count: plan.validityDays, // Get the usage plan days without unit
                defaultValue: "{{ count }} day",
                defaultValue_other: "{{ count }} days",
              })}{" "}
              - {plan.dataVolume}
              {plan.dataUnit}/{t("common:siminfo-card.perday.unit")}
              <br />
              <Text size={"xs"}>
                {/* @ts-ignore */}
                {plan.network.type === "LOCAL" ? "HIGHSPEED" : "BASIC"}
              </Text>
            </Text>
            <Text weight={500} color="app-pink.5" size="0.875rem">
              {!isAppOnlyJapanese() && (
                <>
                  {selectedCode?.code}{" "}
                </>
              )}
              {Currency.formatToSelected({ xe: plan.xe, price: plan.price })}
            </Text>
          </Flex>
        </Grid.Col>
      </Grid>
    </Card>
  );
};
export default EsimPlanCard;
