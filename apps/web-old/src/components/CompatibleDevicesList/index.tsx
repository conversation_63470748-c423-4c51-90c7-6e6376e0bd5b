import { getCDNUrl } from "@/utils"
import { Accordion, Alert, Group, Image, Text, Title } from "@mantine/core"
import { Trans, useTranslation } from "next-i18next"


const iPhoneDevices = (
    <>
        <p>✓ iPhone XR</p>
        <p>✓ iPhone XS</p>
        <p>✓ iPhone XS Max</p>
        <p>✓ iPhone 11</p>
        <p>✓ iPhone 11 Pro</p>
        <p>✓ iPhone SE 2nd generation (2020)</p>
        <p>✓ iPhone 12</p>
        <p>✓ iPhone 12 Mini</p>
        <p>✓ iPhone 12 Pro</p>
        <p>✓ iPhone 12 Pro Max</p>
        <p>✓ iPhone 13</p>
        <p>✓ iPhone 13 Mini</p>
        <p>✓ iPhone 13 Pro</p>
        <p>✓ iPhone 13 Pro Max</p>
        <p>✓ iPhone SE 3rd generation (2022)</p>
        <p>✓ iPhone 14</p>
        <p>✓ iPhone 14 Plus</p>
        <p>✓ iPhone 14 Pro</p>
        <p>✓ iPhone 14 Pro Max</p>
        <p>
            *
            <Trans i18nKey="compatibility:iphonemainlandchina">
                iPhone devices from Mainland China DO NOT have eSIM capability.
            </Trans>
        </p>
        <p>
            *
            <Trans i18nKey="compatibility:iphonechinasar">
                iPhone devices from Hong Kong and Macao (except for the iPhone 13 Mini,
                iPhone 12 Mini, iPhone SE 2nd generation (2020), and iPhone XS) DO NOT
                have eSIM capability.
            </Trans>
        </p>
    </>
);

const ipadDevices = (
    <>
        <p>
            <Trans i18nKey="compatibility:ipadwithwificellular">
                Only iPad devices with Wi-Fi + Cellular features have eSIM capability.
            </Trans>
        </p>
        <p>✓ iPad Air (3rd generation)</p>
        <p>✓ iPad Air (4th generation)</p>
        <p>✓ iPad Pro 11-inch (1st generation)</p>
        <p>✓ iPad Pro 11-inch (2nd generation)</p>
        <p>✓ iPad Pro 11-inch (3rd generation)</p>
        <p>✓ iPad Pro 12.9-inch (3rd generation)</p>
        <p>✓ iPad Pro 12.9-inch (4th generation)</p>
        <p>✓ iPad Pro 12.9-inch (5th generation)</p>
        <p>✓ iPad (7th generation)</p>
        <p>✓ iPad (8th generation)</p>
        <p>✓ iPad (9th generation)</p>
        <p>✓ iPad (10th generation)</p>
        <p>✓ iPad Mini (5th generation)</p>
        <p>✓ iPad Mini (6th generation)</p>
    </>
);
const androidDevices = (
    <>
        <p>✓ Samsung Galaxy S20</p>
        <p>✓ Samsung Galaxy S20+</p>
        <p>✓ Samsung Galaxy S20+ 5g</p>
        <p>✓ Samsung Galaxy S20 Ultra</p>
        <p>✓ Samsung Galaxy S21</p>
        <p>✓ Samsung Galaxy S21+ 5G</p>
        <p>✓ Samsung Galaxy S21+ Ultra 5G</p>
        <p>✓ Samsung Galaxy S22</p>
        <p>✓ Samsung Galaxy S22+</p>
        <p>✓ Samsung Galaxy S22 Ultra</p>
        <p>✓ Samsung Galaxy Note 20</p>
        <p>✓ Samsung Galaxy Note 20 Ultra 5G</p>
        <p>✓ Samsung Galaxy Fold</p>
        <p>✓ Samsung Galaxy Z Fold2 5G</p>
        <p>✓ Samsung Galaxy Z Fold3 5G</p>
        <p>✓ Samsung Galaxy Z Fold4</p>
        <p>✓ Samsung Galaxy Z Flip</p>
        <p>✓ Samsung Galaxy Z Flip3 5G</p>
        <p>✓ Samsung Galaxy Z Flip4</p>
        <p>✓ Samsung Galaxy S23</p>
        <p>✓ Samsung Galaxy S23+</p>
        <p>✓ Samsung Galaxy S23 Ultra</p>

        <p>✓ Google Pixel 2</p>
        <p>✓ Google Pixel 2 XL</p>
        <p>✓ Google Pixel 3</p>
        <p>✓ Google Pixel 3 XL</p>
        <p>✓ Google Pixel 3a</p>
        <p>✓ Google Pixel 3a XL</p>
        <p>✓ Google Pixel 4</p>
        <p>✓ Google Pixel 4a</p>
        <p>✓ Google Pixel 4 XL</p>
        <p>✓ Google Pixel 5</p>
        <p>✓ Google Pixel 5a</p>
        <p>✓ Google Pixel 6</p>
        <p>✓ Google pixel 6a</p>
        <p>✓ Google Pixel 6 Pro</p>
        <p>✓ Google Pixel 7</p>
        <p>✓ Google Pixel 7 Pro</p>

        <p>✓ Huawei P40</p>
        <p>✓ Huawei P40 Pro</p>
        <p>✓ Huawei Mate 40 Pro</p>

        <p>✓ Oppo Find X3 Pro</p>
        <p>✓ Oppo Reno 5A</p>
        <p>✓ Oppo Find X5</p>
        <p>✓ Oppo Find X5 Pro</p>

        <p>✓ Motorola Razr 2019</p>
        <p>✓ Motorola Razr 5G</p>
        <p>✓ Gemini PDA</p>
        <p>✓ Rakuten Mini</p>
        <p>✓ Rakuten Big-S</p>
        <p>✓ Rakuten Big</p>
        <p>✓ Rakuten Hand</p>
        <p>✓ Rakuten Hand 5G</p>
        <p>✓ Sony Xperia 10 III Lite</p>
        <p>✓ Sony Xperia 10 IV</p>
        <p>✓ Xperia 1 IV</p>
        <p>✓ Sony Xperia 5 IV</p>
        <p>✓ Surface Pro X</p>
        <p>✓ Honor Magic 4 Pro</p>
        <p>✓ Fairphone 4</p>
        <p>✓ Sharp Aquos Sense6s</p>
        <p>✓ Sharp Aquos Wish</p>
        <p>✓ Xiaomi 12T Pro</p>
        <p>✓ DOOGEE V30</p>
        <p>
            *
            <Trans i18nKey="compatibility:androidgooglepixel2">
                Google Pixel 2 / 2XL have eSIM capability which is only exclusive to
                Google Fi network.
            </Trans>
        </p>
        <p>
            *
            <Trans i18nKey="compatibility:androidgooglepixel3else">
                Google Pixel 3 / 3 XL devices originating from Australia, Taiwan, and
                Japan, and those bought with service from US or Canadian carriers other
                than Sprint and Google Fi DO NOT have eSIM capability.
            </Trans>
        </p>
        <p>
            *
            <Trans i18nKey="compatibility:androidgooglepixel3aelse">
                Google Pixel 3a / 3a XL devices bought in South East Asia and Japan, and
                with Verizon service DO NOT have eSIM capability.
            </Trans>
        </p>
        <p>
            *
            <Trans i18nKey="compatibility:huaweinotcompatible">
                Huawei P40 Pro+ devices DO NOT have eSIM capability.
            </Trans>
        </p>
        <p>
            *
            <Trans i18nKey="compatibility:opponotcompatible">
                Oppo Find X5 Lite devices DO NOT have eSIM capability.
            </Trans>
        </p>
    </>
);

const CompatibleDevicesList = () => {
    const { t } = useTranslation()
    return <div>
        <Accordion
            w={"100%"}
            variant="separated"
            styles={{
                item: {
                    background: "#FFFFFF",
                    borderRadius: ".5rem",
                    border: "1px solid #D5D5D5",
                },
                control: {
                    background: "#FFFFFF",
                    borderRadius: "1rem",
                    height: "4rem"
                },
                chevron: {
                    background: "#F2F4F6",
                    width: 28,
                    height: 28,
                    color: "#AAAAAA",
                    borderRadius: 100,
                    transform: "rotate(-90deg)",
                    "&[data-rotate]": {
                        transform: "rotate(0deg)",
                    },
                },
            }}
        >
            <Accordion.Item
                value="guide1">
                <Accordion.Control>
                    <Group>
                        <Image
                            src={getCDNUrl("/assets/icon-ios.png")}
                            width={14}
                            height={14}
                            fit="contain"
                        />

                        <Title fw={"bold"} size={"12"} order={3}>
                            <Trans i18nKey="compatibility:forios12later">
                                iOS iPhone (iOS 12.1 or later)
                            </Trans>
                        </Title>
                    </Group>

                </Accordion.Control>
                <Accordion.Panel>
                    <Text size={"sm"}>
                        {iPhoneDevices}
                    </Text>
                </Accordion.Panel>
            </Accordion.Item>
            <Accordion.Item
                value="guide2">
                <Accordion.Control>
                    <Group>
                        <Image
                            src={getCDNUrl("/assets/icon-ios.png")}
                            width={14}
                            height={14}
                            fit="contain"
                        />
                        <Title fw={"bold"} size={"12"} order={3}>
                            iPad
                        </Title>
                    </Group>
                </Accordion.Control>
                <Accordion.Panel>
                    <Text size={"sm"}>
                        {ipadDevices}
                    </Text>
                </Accordion.Panel>
            </Accordion.Item>
            <Accordion.Item
                value="guide3">
                <Accordion.Control>
                    <Group>
                        <Image
                            src={getCDNUrl("/assets/icon-android.png")}
                            width={14}
                            height={14}
                            fit="contain"
                        />
                        <Title fw={"bold"} size={"12"} order={3}>
                            Android
                        </Title>
                    </Group>
                </Accordion.Control>
                <Accordion.Panel>
                    <Text size={"sm"}>
                        {androidDevices}
                    </Text>
                </Accordion.Panel>
            </Accordion.Item>

        </Accordion>
        <br />
        <Alert color="yellow">
            {t("compatibility:compatibilitywarning", "Please note: Your device might not support eSIM depending on the country of purchase, even if it's listed on our compatibility list. Some region-specific models are sold with eSIM restrictions. You must also make sure that your device is not SIM-locked.")}
        </Alert>
    </div>
}
export default CompatibleDevicesList