import { TabProps, Tabs, TabsValue, Text, createStyles, Title } from "@mantine/core";

const useStyles = createStyles((_) => ({
  wrapper: {
    color: "#AAAAAA",
    fontSize: "14px",
    letterspacing: 0,
    fontweight: "700",
    textDecoration: "none",
  },
  isActive: {
    fontsize: "141px",
    color: "#E40071",
  },
}));
interface IProps {
  headers: { name: string; code?: string; children?: React.ReactNode }[];
  defaultValue: string | null;
  onTabChange(value: TabsValue): void;
  activeTab: string | null;
}
const AppTabs = (props: Partial<Omit<TabProps, "defaultValue">> & IProps) => {
  const { classes } = useStyles();

  return (
    <Tabs
      w={"100%"}
      defaultValue={props.defaultValue}
      onTabChange={props.onTabChange}
      styles={(theme) => ({
        panel: { background: "#f3f3f3" },
        tabsList: {
          borderColor: "transparent",
          "button[data-active]:hover": {
            borderColor: theme.colors["app-pink"]["4"],
          },
          "button[data-active]": {
            borderColor: theme.colors["app-pink"]["4"],
          },
        },
      })}
    >
      <Tabs.List position="center" color="red">
        {props.headers.map((header) => (
          <Tabs.Tab
            value={header.code || header.name}
            key={header.code || header.name}
            w={"50%"}
            bg={"white"}
          >
            <Title
              order={3}
              className={`${classes.wrapper} ${props.activeTab === (header.code || header.name) &&
                classes.isActive
                }`}
              fw={"700"}
            >
              {header.name}
            </Title>
          </Tabs.Tab>
        ))}
      </Tabs.List>
      {props.headers.map((header) =>
        header.children ? (
          <Tabs.Panel
            key={header.code || header.name}
            value={header.code || header.name}
            pt="xs"
          >
            {header.children}
          </Tabs.Panel>
        ) : null
      )}
    </Tabs>
  );
};
export default AppTabs;
