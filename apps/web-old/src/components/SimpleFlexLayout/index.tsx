import { AlertMessageType, useGlobalState } from "@/store";
import { Alert, Flex, FlexProps } from "@mantine/core";
import { IconAlertCircle, IconCircleCheck } from "@tabler/icons-react";
import { useEffect } from "react";
import { useTranslation } from "next-i18next";
import { isAppOnlyJapanese } from "@/utils";

const SimpleFlexLayout = (props: FlexProps) => {
  const { t } = useTranslation();
  const [message, setMessage, messageType] = useGlobalState((s) => [
    s.message,
    s.setMessage,
    s.messageType,
  ]);
  useEffect(() => {
    return () => {
      setMessage("", AlertMessageType.ERROR);
    };
  }, []);

  return (
    <Flex direction={"column"} gap={12} {...props}>
      {message && (
        <Alert
          icon={
            messageType === AlertMessageType.ERROR ? (
              <IconAlertCircle size="1rem" />
            ) : (
              <IconCircleCheck size={"1rem"} />
            )
          }
          onClose={() => setMessage("")}
          closeButtonLabel="Close alert"
          title={messageType === AlertMessageType.ERROR ? isAppOnlyJapanese() ? "入力内容に誤りがあります。" : "Alert!" : "Success"}
          color={messageType === AlertMessageType.ERROR ? "red" : "green"}
          withCloseButton
        >
          {message === "Email address already exists." ? t("signup:emailalreadyexists") : message}
        </Alert>
      )}
      {props.children}
    </Flex>
  );
};

export default SimpleFlexLayout;
