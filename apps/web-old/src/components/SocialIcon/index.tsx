import { getCDNUrl } from "@/utils";
import { Box, Center, Image } from "@mantine/core";

const bgColors = {
  facebook: "#1C72E2",
  google: "#FFFFFF;",
};
interface IProps {
  icon: "google" | "facebook";
}
const SocialLogin = (props: IProps) => {
  return (
    <Center
      sx={{
        background: bgColors[props.icon],
        height: "3rem",
        width: "3rem",
        boxShadow: "0 2px 4px 0 rgba(0,0,0,0.15)",
        borderRadius: 4,
      }}
    >
      <Image src={getCDNUrl(`/assets/${props.icon}.png`)} width={20} height={20} />
    </Center>
  );
};
export default SocialLogin;
