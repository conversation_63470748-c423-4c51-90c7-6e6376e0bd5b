import { getCDNUrl, isAppOnlyJapanese, withBasePath } from "@/utils";
import { Flex, Image, Text, Box } from "@mantine/core";
import { Trans, useTranslation } from "next-i18next";
import LanguagePickerButton from "../LanguagePickerButton";
import LanguageSelectModal, { LANGUAGES_LIST } from "../LanguageSelectModal";
import { useDisclosure } from "@mantine/hooks";

const Subheader = (props: { children: React.ReactNode }) => (
  <Text
    sx={{
      fontWeight: "bolder",
      color: "#4A4A4A",
      fontSize: "1.2rem",
      lineHeight: "18px",
      //   letterSpacing: "-0.5px",
      marginBottom: ".50rem",
      textAlign: "center",
    }}
  >
    {props.children}
  </Text>
);
const LandingCoverHeader = () => {
  const { t, i18n } = useTranslation()
  const [languageSelectModalOpened, { open: openLanguage, close: closeLanguage }] = useDisclosure(false);

  const overseaView = <>
    <LanguageSelectModal
      title={t("common:language.title")}
      onSelect={(lang) => {
        i18n.changeLanguage(lang as string,)
        window.location.href = withBasePath(lang as string)
      }}
      onClose={closeLanguage} opened={languageSelectModalOpened} />
    <Flex
      h={"19rem"}
      sx={{
        background: `url(${getCDNUrl("/assets/background.png")})`,
      }}
      align={"center"}
      direction={"column"}
      p={"lg"}
      gap={20}
    >
      <Flex w="100%" align={"center"}>
        <Image src={getCDNUrl("/assets/logo.png")} width={128} height={17.65} />
        <Image
          src={getCDNUrl("/assets/jpx-logo.png")}
          width={40}
          height={40}
          alt="jpx-logo"
          sx={{
            marginLeft: "4px"
          }}
        />
        <Text 
          sx={{
            fontSize: "8px",
            color: "black",
            whiteSpace: "nowrap",
            lineHeight: "1.5 !important",
            margin: "0 4px"
          }}
        >
          <div>
            Inbound Platform Corp. 
          </div>
          <div>
            (TSE Code: 5587)
          </div>
        </Text>
        {isAppOnlyJapanese() ? null :
          <LanguagePickerButton onClick={openLanguage}>
            {/* @ts-ignore */}
            {LANGUAGES_LIST[i18n.language]}
          </LanguagePickerButton>
        }
      </Flex>
      <Flex direction={"column"} mt={"30px"}>
        <Subheader>
          <Trans i18nKey={"home:stayconnected1"}>
            Stay connected, wherever you
          </Trans>
        </Subheader>
        <Subheader>
          {t("home:stay-connected2", " ")}
        </Subheader>
        <Text
          align={"center"}
          sx={{
            fontSize: "0.9rem"
          }}>
          {t("home:headersubtitle")}
        </Text>
      </Flex>
      <Image
        fit="contain"
        maw={"auto"}
        width={"9rem"}
        height={"5rem"}
        src={getCDNUrl("/assets/ratings.png")}
        alt="Cover image"
      />
    </Flex>
  </>
  const japaneseView = <>
    <Box
      h={"22rem"}
      sx={{
        background: `url(${getCDNUrl("/assets/hero/hero-background.webp")})`,
        backgroundPosition: "center",
        backgroundSize: "cover",
        overflow: "hidden",
      }}
      pos="relative"
      p={"lg"}
    >
      <Image
        src={getCDNUrl("/assets/hero/wifi-icon.svg")}
        width={100}
        height={100}
        alt="wifi-icon"
        sx={{
          position: "absolute",
          top: 50,
          left: 15
        }}
      />
      <Image
        src={getCDNUrl("/assets/hero/kv-girl.webp")}
        width={135}
        height={215}
        alt="esim-use"
        sx={{
          position: "absolute",
          bottom: 0,
          left: 0
        }}
      />
      <Image
        src={getCDNUrl("/assets/hero/100countries.svg")}
        width={120}
        height={70}
        alt="100 countries"
        sx={{
          position: "absolute",
          bottom: 45,
          left: 225
        }}
      />
      <Image
        src={getCDNUrl("/assets/hero/esim-icon.svg")}
        width={48}
        height={60}
        alt="100 countries"
        sx={{
          position: "absolute",
          bottom: 50,
          left: 160
        }}
      />
      <Box
        ml={95}
        mt={20}
      >
        <Text
          color="white"
          weight={900}
          size={20}
        >
          もっと気軽に世界を楽しむ
        </Text>
        <Image
          src={getCDNUrl("/assets/hero/esim-logo-white.svg")}
          width="100%"
          height="100%"
          alt="global mobile esim"
          sx={{
            marginLeft: -8,
            marginBottom: 8,
            marginTop: 8,
          }}
        />
        <Text
          color="white"
          weight={700}
          size={14}
        >
          すぐ発行＆使用できるから待ち時間ゼロ！<br />
          現地の安定高速ネットで、<br />
          思いっきり自由に旅を満喫しよう！
        </Text>
      </Box>
    </Box>
  </>

  return (
    <>
      {isAppOnlyJapanese() ? japaneseView : overseaView}
    </>
  );
};
export default LandingCoverHeader;
