import {
  SegmentedControl,
  Paper,
  Group,
  Text,
  <PERSON><PERSON>,
  Stack,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { useState } from "react";
import {
  IconBrandAndroid, 
  IconBrandApple,
  IconFileTypePdf,
} from "@tabler/icons-react";
import CTAButton from "@/components/CTAButton";

const SetupGuide = ({
  compact,
}: {
  compact?: boolean;
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("ios");

  return (
    <Paper
      px="1rem"
      pb="1rem"
      sx={{
        borderBottomLeftRadius: "2rem",
        borderBottomRightRadius: "2rem",
        background: compact ? "transparent" : "white",
      }}
    >
      <SegmentedControl
        w={"100%"}
        radius="md"
        mb="0.5rem"
        defaultValue={activeTab}
        value={activeTab}
        onChange={setActiveTab}
        data={[
          {
            value: "ios",
            label: (
              <Group
                position="center"
                spacing="0.25rem"
                h={"2rem"}
              >
                <IconBrandApple />
                <Text weight="bold">iOS</Text>
              </Group>
            ),
          },
          {
            value: "android",
            label: (
              <Group
                position="center"
                spacing="0.25rem"
                h={"2rem"}
              >
                <IconBrandAndroid color="#78C257"/>
                <Text weight="bold">Android</Text>
              </Group>
            ),
          },
        ]}
      />

      <iframe
        width={"100%"}
        height="250px"
        src={
          activeTab === "android"
            ? "https://www.youtube.com/embed/8amWEmAw_4Y?si=aLFJR7Q5CqtOUSWI"
            : "https://www.youtube.com/embed/5Hdie7mq0HE?si=CL7cA1ca0WWd5d_W"
        }
        title="esim setup guide"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
      ></iframe>
      <Stack spacing="1rem" mt="1rem">
        <Button
          fullWidth
          size="lg"
          radius="md"
          variant="outline"
          bg="white"
          color="app-pink.4"
          component="a"
          href={activeTab === "android" ?
            "https://www.gmobile.biz/esim/documents/jp/manual-android.pdf" :
            "https://www.gmobile.biz/esim/documents/jp/manual-ios.pdf"
          }
          target="_blank"
        >
          <IconFileTypePdf size={30} />
          <Text
            weight="bold"
            color="black"
            ml={10}
          >
            {activeTab === "android" ? t("compatibility:home.android") : t("compatibility:home.ios")}
          </Text>
        </Button>
        {!compact && (
          <CTAButton />
        )}
      </Stack>
    </Paper>
  );
};

export default SetupGuide;
