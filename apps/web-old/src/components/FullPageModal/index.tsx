import { Modal, ModalProps } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import React from "react";

const FullPageModal = (
  props: {
    top?: string;
    hideHeaderBorder?: boolean;
    showOverlay?: boolean;
    children: React.ReactNode;
  } & ModalProps
) => {
  return (
    <Modal
      {...props}
      overlayProps={{
        display: props.showOverlay ? "block" : "none",
        zIndex: 999,
      }}
      sx={{
        overflowY: "hidden",
        overflowX: "hidden"
      }}
      styles={{
        content: { maxHeight: "100vh !important", },
        title: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
          fontWieght: "bold",
          padding: ".5rem",
          fontWeight: 700,
          fontSize: "1rem"
        },
        header: {
          borderBottom: props.hideHeaderBorder ? "0px" : "1px solid #dfdfdf",
        },
        inner: {
          paddingTop: "0px !important",
          paddingBottom: "0px !important",
          paddingRight: "0rem !important",
          paddingLeft: "0rem !important",
          top: props.top,
          zIndex: 1000,
        },
        body: {
          height: "100vh",
          background: "white",
          paddingTop: "0px !important",
          paddingBottom: "0px !important",
          paddingRight: "0rem !important",
          paddingLeft: "0rem !important",
        },
      }}
      size="md"
      opened={props.opened}
      title={props.title || "Currency"}
      transitionProps={{ transition: "pop" }}
    >
      {props.children}
    </Modal>
  );
};
export default FullPageModal;
