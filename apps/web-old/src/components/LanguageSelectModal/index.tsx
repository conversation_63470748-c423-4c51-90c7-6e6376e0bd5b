import FullPageModal from "../FullPageModal";
import { Accordion, ModalProps, Text, Box, Image, Flex } from "@mantine/core";
import React, { useState } from "react";
import { IconCheck } from "@tabler/icons-react";
import Storage from "@/core/Storage";
import { useTranslation } from "next-i18next";
import { isDev, getCDNUrl, isAppOnlyJapanese } from "@/utils";

export const getFlagCode = (lang: string) => {
  switch (lang) {
    case "en":
      return "us";
    case "vi":
      return "vn";
    default:
      return lang;
  }
};

const LANGUAGES_LIST_EN = {
  en: "English",
  // kr: "한국어",
  // zt: "繁體中文",
  // zh: "简体中文",
  es: "Español",
  fr: "Français",
  ph: "Filipino",
  vi: "Tiếng Việt",
  // // ms: "Malay"
  jp: "日本語",
};

const LANGUAGES_LIST_JP = {
  jp: "日本語",
  en: "英語",
  es: "スペイン語",
  fr: "フランス語",
  ph: "フィリピン語",
  vi: "ベトナム語",
};

export const LANGUAGES_LIST = isAppOnlyJapanese()
  ? LANGUAGES_LIST_JP
  : LANGUAGES_LIST_EN;

const LanguageSelectModal = (
  props: ModalProps & { onSelect: (key: string) => void }
) => {
  const { i18n } = useTranslation();
  const handleSelect = React.useCallback((v: any) => {
    Storage.getInstance().set("locale", v);
    const basePath = !!process.env.NEXT_PUBLIC_BASE_PATH;
    if ((basePath && v === "jp") || (!basePath && v === "jp")) {
      window.location.href = isDev()
        ? "http://localhost:3232/esim/profile"
        : (process.env.NEXT_PUBLIC_ESIM_JP ||
            "https://www-dev.gmobile.biz/esim") + "/profile";
      return;
    }

    window.location.href = isDev()
      ? "http://localhost:3232/" + v
      : "https://esim.gmobile.biz/" + v;
    return;
    // props.onSelect?.(v)
  }, []);

  return (
    //@ts-expect-error
    <FullPageModal {...props}>
      <Accordion
        onChange={handleSelect}
        variant="contained"
        chevron={
          <Text c={"dimmed"} fw={900}>
            &gt;
          </Text>
        }
        disableChevronRotation
      >
        {Object.keys(LANGUAGES_LIST).map((lang, index) => (
          <Accordion.Item value={lang} key={index}>
            <Accordion.Control
              chevron={i18n.language === lang ? <IconCheck /> : " "}
            >
              <Flex align="center">
                <Box
                  mr={4}
                  sx={{
                    border: "1px solid #ccc",
                  }}
                >
                  <Image
                    alt={lang}
                    src={getCDNUrl(
                      `/assets/flags/svg/${getFlagCode(lang)}.svg`
                    )}
                    height={15}
                  />
                </Box>
                {/* @ts-ignore */}
                <Text>{LANGUAGES_LIST[lang]}</Text>
              </Flex>
            </Accordion.Control>
          </Accordion.Item>
        ))}
      </Accordion>
    </FullPageModal>
  );
};
export default LanguageSelectModal;
