import { Currency } from "@/core/Currency";
import { IPlan } from "@/interfaces/IPlan";
import { Card, Flex, Badge, Text, Box, Stack, Grid } from "@mantine/core";
import React from "react";
import { useTranslation } from "next-i18next";
import { IconCheck } from "@tabler/icons-react";

type Props = {
  plan: IPlan;
  isActive?: boolean;
  isFixedPlan?: boolean;
};

const EsimPlanCard = (props: Props) => {
  
  const { plan, isActive } = props;
  const { t } = useTranslation();

  const getNetworkGeneration = React.useMemo(() => {
    try {
      const networkGenerations = props.plan.network.networkGeneration.replaceAll(" ", "").split("/");
      return (
        networkGenerations.filter((item) => item !== "").map((item) => (
          <Badge
            key={item}
            size="sm"
            color="black"
            variant="outline"
            mr={4}
            styles={{
              root: {
                borderColor: "black",
                background: "white",
                borderRadius: "6px",
                flexShrink: 0
              },
              inner: {
                color: "black"
              }
            }}
          >
            {item}
          </Badge>
        ))
      )
    } catch (err) {
      console.log(err)
    }

    return null
  }, [plan]);

  return (
    <Card
      sx={{
        boxShadow: "0 0 10px 0 rgba(0,0,0,0.15);",
        borderRadius: "0.5rem",
        border: isActive ? "2px solid #E40071" : "1px solid #DDDDDD",
        backgroundColor: isActive ? "#FDEBF4" : "#F2F4F6",
        cursor: "pointer",
      }}
    >
      <Stack spacing="0.75rem">
        <Grid>
          <Grid.Col sm={5} xs={6} span={6}>
            <Text
              size={12}
              color="#777777"
              weight={600}
            >
              {t("common:dataplan.capacity")}
            </Text>
            <Text
              size={20}
              weight="bold"
            >
              {plan.dataVolume}{plan.dataUnit}{!props.isFixedPlan ? `/${t("common:siminfo-card.perday.unit")}` : ""}
            </Text>
          </Grid.Col>
          <Grid.Col sm={7} xs={6} span={6}>
            <Text size={12}>{t("common:dataplan.usage-days")}</Text>
            <Text
              size={20}
              weight="bold"
            >
              {t("common:siminfo-card.validity.unit", {
                count: plan.validityDays, // Get the usage plan days without unit
                defaultValue: "{{ count }}day",
                defaultValue_other: "{{ count }}days",
              })}
            </Text>
          </Grid.Col>
        </Grid>
        <Box>
          <Text component="span" color="#777777" size={12}>{t("common:dataplan.communication-line")}：</Text>
          {getNetworkGeneration}
          {props.plan.network.name && props.plan.network.name !== "-" && (
            <Text
              component="span"
              size={12}
              weight={600}
            >
              {props.plan.network.name}
            </Text>
          )}
        </Box>

        <Flex align="center" justify="space-between">
          <Text size={14} weight="bold">
            {Currency.formatToSelected({ xe: plan.xe, price: plan.price })}
          </Text>
          <Box
            bg={isActive ? "app-pink.4" : "white"}
            w={28}
            h={28}
            sx={{borderRadius: "100%"}}
            p={2}
          >
            {isActive ? (
              <IconCheck size={24} color="white" />
            ) : null}
          </Box>
        </Flex>
      </Stack>
    </Card>
  );
};
export default EsimPlanCard;
