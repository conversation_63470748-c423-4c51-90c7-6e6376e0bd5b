import { Accordion, Paper, Text, Title, Image, Flex, Box } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import CurrencySelectModal from "../CurrencySelectModal";
import Link from "next/link";
import { useEffect, useState } from "react";
import Storage from "@/core/Storage";
import { useProfile } from "@/store";
import { ICurrency } from "@/interfaces/ICurrency";
import { AUTH_TOKEN_NAME } from "../../../consts";
import { useTranslation } from "next-i18next";
import LanguageSelectModal, {
  LANGUAGES_LIST,
  getFlagCode,
} from "../LanguageSelectModal";
import { withBasePath, isAppOnlyJapanese, getCDNUrl } from "@/utils";
import { getCurrencyIcon } from "@/utils/display_helper";

const ProfileMenu = (props: { name?: string }) => {
  const { t, i18n } = useTranslation();
  const profile = useProfile((s) => s.profile);
  const [currency, setCurrency] = useState<ICurrency | undefined>(
    Storage.getInstance().get("currency")
  );
  const [opened, { open, close }] = useDisclosure(false);
  const [
    languageSelectModalOpened,
    { open: openLanguage, close: closeLanguage },
  ] = useDisclosure(false);

  useEffect(() => {
    setCurrency(Storage.getInstance().get("currency"));
  }, [profile]);

  return (
    <>
      <CurrencySelectModal
        title={t("currencies:title")}
        onClose={close}
        opened={opened}
      />
      <LanguageSelectModal
        title={t("common:language.title")}
        onSelect={(lang) => {
          i18n.changeLanguage(lang as string);
          window.location.href = withBasePath(lang + "/profile");
        }}
        onClose={closeLanguage}
        opened={languageSelectModalOpened}
      />
      <Paper
        sx={{
          boxShadow: " 0 0 10px 0 rgba(0,0,0,0.1)",
        }}
      >
        <Accordion
          styles={{
            chevron: {
              width: "8rem",
              justifyContent: "end",
              paddingRight: "12px",
            },
            control: {
              color: "#333333",
              fontSize: "13px",
              lineHeight: "20px",
            },
            item: {
              a: {
                textDecoration: "none",
              },
            },
          }}
          variant="contained"
          chevron={
            <Text c={"dimmed"} fw={900}>
              &gt;
            </Text>
          }
          disableChevronRotation
        >
          {props.name && (
            <>
              <Accordion.Item
                value="profile"
                h={"4rem"}
                style={{
                  borderBottom: "0px",
                }}
              >
                <Accordion.Control chevron={" "}>
                  <Title order={4}>{props.name}</Title>
                </Accordion.Control>
              </Accordion.Item>
              <Accordion.Item value="accountInformation">
                <Link href={"/app/account/profile"}>
                  <Accordion.Control>
                    {t("common:accountinformation") || "Account Information"}
                  </Accordion.Control>
                </Link>
              </Accordion.Item>
            </>
          )}

          <Accordion.Item value="language">
            <Accordion.Control
              onClick={openLanguage}
              chevron={
                <Text size={"sm"}>
                  <Flex align="center" justify="end">
                    <Box
                      mr={4}
                      sx={{
                        border: "1px solid #ccc",
                      }}
                    >
                      <Image
                        alt={i18n.language}
                        src={
                          isAppOnlyJapanese()
                            ? `/esim/assets/flags/svg/${getFlagCode(
                                i18n.language
                              )}.svg`
                            : `/assets/flags/svg/${getFlagCode(
                                i18n.language
                              )}.svg`
                        }
                        height={15}
                      />
                    </Box>
                    {/* @ts-ignore */}
                    {LANGUAGES_LIST[i18n.language]}
                  </Flex>
                </Text>
              }
            >
              {t("common:language.title")}
            </Accordion.Control>
          </Accordion.Item>

          <Accordion.Item onClick={open} value="Currency">
            <Accordion.Control
              chevron={
                currency ? (
                  <>
                    {getCurrencyIcon(currency?.code)}
                    <Text size={"sm"} ml={4}>
                      {currency?.code}
                    </Text>
                  </>
                ) : null
              }
            >
              {t("currencies:title")}
            </Accordion.Control>
          </Accordion.Item>
          {!isAppOnlyJapanese() && (
            <Accordion.Item value="contactus">
              <Accordion.Control
                onClick={() => {
                  window.location.href = "mailto:<EMAIL>";
                }}
              >
                {t("help:support.link.contactus")}
              </Accordion.Control>
            </Accordion.Item>
          )}
          {props.name && (
            <Accordion.Item value="logout">
              <Accordion.Control
                chevron={" "}
                onClick={() => {
                  Storage.getInstance().set(AUTH_TOKEN_NAME, "");
                  window.location.href = withBasePath(i18n.language);
                }}
              >
                {t("common:logout", "Log out") || "Logout"}
              </Accordion.Control>
            </Accordion.Item>
          )}
        </Accordion>
      </Paper>
    </>
  );
};
export default ProfileMenu;
