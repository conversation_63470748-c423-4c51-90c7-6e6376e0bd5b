import {
  Paper,
  Stack,
  Title,
  Text,
  Button,
} from "@mantine/core";
import WorkGuide from "../WorkGuide";
import { useTranslation } from "next-i18next";
import { isAppOnlyJapanese } from "@/utils";
import { IconDevices } from "@tabler/icons-react";

const HowItWorks = () => {
  const { t } = useTranslation();

  return (
    <Paper>
      <Title
        p="2rem 1rem 1rem"
        order={2} 
        align="center"
        sx={{
          fontSize: "1.4rem",
          lineHeight: "unset !important"
        }}
      >
        {t("home:howitworks.title")}
      </Title>
      <Stack mt={"-0rem"}>
        <WorkGuide
          count="01"
          iconURl="/assets/check-compatibility.png"
          header={t("home:work-step1.title")}
          content={<> 
            <Text
              align="center"
              dangerouslySetInnerHTML={{
                __html: t("home:work-step1.details")
              }}
            />
            {isAppOnlyJapanese() && (
              <Button
                fullWidth
                size="lg"
                radius="md"
                variant="outline"
                color="app-pink.4"
                component="a"
                href="/esim/compatible-devices"
              >
                <IconDevices size={30} />
                <Text
                  weight="bold"
                  color="black"
                  ml={10}
                >
                  eSIM対応機種はこちら
                </Text>
              </Button>
            )}
          </>}
        />
        <WorkGuide
          count="02"
          iconURl="/assets/get-your-esim.png"
          header={t("home:work-step2.title")}
          content={
            <Text
              align="center"
              dangerouslySetInnerHTML={{
                __html: t("home:work-step2.details")
              }}
            />
          }
        />
        <WorkGuide
          count="03"
          iconURl="/assets/install-your-esim.png"
          header={t("home:work-step3.title")}
          content={
            <Text
              align="center"
              dangerouslySetInnerHTML={{
                __html: t("home:work-step3.details")
              }}
            />
          }
        />
      </Stack>
    </Paper>
  );
};
export default HowItWorks;
