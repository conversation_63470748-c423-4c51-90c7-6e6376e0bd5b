import React from "react";
import { ApiService } from "@/api";
import { useQuery } from "react-query";
import { IProfile } from "@/interfaces/IProfile";
import { useProfile } from "@/store";
import Storage from "@/core/Storage";
import { AUTH_TOKEN_NAME } from "../../../../consts";
import { useRouter } from "next/router";
// import { getFirebaseCurrentToken } from "../../../../firebase";
import { LoadingOverlay, Modal } from "@mantine/core";

interface IProps {
  children?: React.ReactNode | JSX.Element | any;
  skipLoginRedirect?: boolean
}

const AuthLayout: React.FC<IProps> = ({ children, skipLoginRedirect }) => {
  const setPorfile = useProfile((state) => state.setPorfile);
  const profile = useProfile((state) => state.profile);
  const router = useRouter();

  const profileQuery = useQuery<IProfile>(
    "profileQuery",
    async () => {
      try {
        let firebaseToken;
        try {
          // if (router.pathname.includes("app") && process.env.NEXT_PUBLIC_IS_PUSH_NOTIFICAION === "true") {
          // firebaseToken = await getFirebaseCurrentToken();
          // }
        } catch {
          firebaseToken = null
        }

        const { data } = await ApiService.getProfile({
          token: firebaseToken || undefined
        });
        return data?.data;
      } catch (err: unknown) {
        if (!skipLoginRedirect)
          router.push("/auth/signin");
        // @ts-expect-error
        if (err.response.status) {
          Storage.getInstance().set(AUTH_TOKEN_NAME, "")
        }
      }
    },
    {
      enabled: !profile?.id,
    }
  );

  React.useEffect(() => {
    if (profile?.firstName) return;
    //@ts-ignore
    setPorfile(profileQuery.data);
  }, [profileQuery]);

  if (skipLoginRedirect) return children
  return children
};

export default AuthLayout;
