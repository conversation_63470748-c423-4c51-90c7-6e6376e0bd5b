import { getCDNUrl } from "@/utils";
import {
  Bad<PERSON>,
  Card,
  Flex,
  Text,
  ThemeIcon,
  Image,
  Title,
} from "@mantine/core";

interface IProps {
  count: string;
  iconURl: string;
  content: React.ReactNode | string;
  header: string;
}
const WorkGuide = (props: IProps) => {
  return (
    <Card>
      <Flex
        direction={"column"}
        align={"center"}
        justify={"space-between"}
        gap={16}
      >
        <Badge
          size="xl"
          mr={10}
          sx={{
            background: "rgba(241, 123, 149, 0.1)"
          }}
        >
          <Text
            color="app-pink.4"
            fw={"bolder"}
            mx={20}
          >
            STEP {props.count}
          </Text>
        </Badge>
        {/* <ThemeIcon radius="lg" size="xl" color="app-blue.0">
        
        </ThemeIcon> */}
        <Image src={getCDNUrl(props.iconURl)} width={55} height={56} fit="contain" />
        <Title order={3} size={14}>{props.header}</Title>
        {props.content}
      </Flex>
    </Card>
  );
};
export default WorkGuide;
