import FullPageModal from "../FullPageModal";
import { Accordion, ModalProps, Text, Flex } from "@mantine/core";
import React, { useState } from "react";
import { IconCheck } from "@tabler/icons-react";
import Storage from "@/core/Storage";
import { useProfile } from "@/store";
import { getCurrencyIcon } from "@/utils/display_helper";
import { ICurrency } from "@/interfaces/ICurrency";
import { useTranslation } from "next-i18next";

const CurrencySelectModal = (props: ModalProps) => {
  const { t } = useTranslation();

  const currencies = [
    {
      code: "JPY",
      name: t("currencies:jpy"),
      sign: "¥"
    },
    {
      code: "USD",
      name: t("currencies:usd"),
      sign: "$"
    },
    {
      code: "EUR",
      name: t("currencies:eur"),
      sign: "€",
    },

    {
      code: "AUD",
      name: t("currencies:aud"),
      sign: "$"
    },
    {
      code: "CAD",
      name: t("currencies:cad"),
      sign: "$"
    },
    {
      code: "TWD",
      name: t("currencies:twd"),
      sign: "$"
    },
    {
      code: "HKD",
      name: t("currencies:hkd"),
      sign: "$"
    },
    {
      code: "CNY",
      name: t("currencies:cny"),
      sign: "¥",
    },
    {
      code: "GBP",
      name: t("currencies:gbp"),
      sign: "£",
    },
  ];
  const setProfile = useProfile((s) => s.setPorfile);
  const [savedCurrency, setCurrency] = useState(
    Storage.getInstance().get("currency")
  );
  const handleSelect = React.useCallback((v: any) => {
    if (!v) return;
    const currency = currencies.find((c) => c.code === v) as ICurrency;
    Storage.getInstance().set("currency", currency as any, "cookie");
    props.onClose();
    setProfile({
      currency,
    });
    setCurrency(currency);
  }, []);
  return (
    //@ts-expect-error
    <FullPageModal {...props}>
      <Accordion
        onChange={handleSelect}
        variant="contained"
        chevron={
          <Text c={"dimmed"} fw={900}>
            &gt;
          </Text>
        }
        disableChevronRotation
      >
        {currencies.map((currency, index) => (
          <Accordion.Item value={currency.code} key={index}>
            <Accordion.Control
              chevron={
                savedCurrency?.code === currency.code ? <IconCheck /> : " "
              }
            >
              <Flex align="center">
                {getCurrencyIcon(currency.code)}
                <Text ml={4}>{currency.name}</Text>
              </Flex>
            </Accordion.Control>
          </Accordion.Item>
        ))}
      </Accordion>
    </FullPageModal>
  );
};
export default CurrencySelectModal;
