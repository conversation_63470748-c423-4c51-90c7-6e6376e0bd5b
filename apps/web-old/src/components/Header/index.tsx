import { getCDNUrl } from "@/utils";
import { ActionIcon, Divider, Flex, Image, Title } from "@mantine/core";
import Head from "next/head";
import { useRouter } from "next/router";

const Header = (props: {
  referer?: string;
  title: string;
  hideBackButton?: boolean;
  hideDivider?: boolean;
  documentTitle?: string | null;
}) => {
  const router = useRouter();
  return (
    <>
      <Head>
        <title>{props.documentTitle}</title>
      </Head>
      <Flex
        w={"100%"}
        sx={{
          background: "white",
          zIndex: 999,
        }}
        p={20}
        direction={"row"}
        align={"center"}
        pos={"sticky"}
        top={0}
      >
        {!props.hideBackButton &&
          <ActionIcon onClick={() => {
            const referer = props.referer;
            if (router.query.source === "email") {
              router.push("/app")
              return
            }
            if (referer?.startsWith(window.location.origin)) {
              router.back()
            } else {
              router.push("/")
            }
          }} variant="transparent">
            <Image src={getCDNUrl("/assets/left-arrow.png")} />
          </ActionIcon>
        }
        <Flex w={"100%"} justify={"center"}>
          <Title order={1} size={16}>{props.title}</Title>
        </Flex>
      </Flex>
      {props.hideDivider && <Divider />}
    </>
  );
};
export default Header;
