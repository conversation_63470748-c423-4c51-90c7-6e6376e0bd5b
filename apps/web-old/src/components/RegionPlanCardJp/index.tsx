import { Card, Flex, Title, Image, Text, Stack } from "@mantine/core";
import Link from "next/link";
import { getCDNUrl, normalizeAndKebabCase } from "@/utils";
import { Currency } from "@/core/Currency";
import { IPlan } from "@/interfaces/IPlan";
import { Trans } from "next-i18next";
import { countryNameException } from "@/utils";

const RegionPlanCard = (props: {
  originalName?: string,
  img: string | string[];
  name: string;
  price: string;
  xe: IPlan["xe"];
}) => {
  const selectedCode = Currency.getSelectedCurrency();

  const convertedPrice = Currency.formatToSelectedNoCode({
    xe: props.xe,
    price: +props.price,
  });

  return (
    <Card
      sx={{
        ":hover": {
          background: "#f9f9f9"

        },
        borderRadius: "0.5rem"
      }}
      shadow="0 0 10px 0 rgba(0,0,0,0.15)"
      padding="0"
      w={"calc(50% - 0.5rem)"}
    >
      <Card.Section h={"100%"} mt={"0rem !important"}>
        <Link
          style={{
            textDecoration: "none",
            height: "100%"
          }}
          href={"/region/" + normalizeAndKebabCase(props.originalName || props.name)}
        >
          <Image
            fit="cover"
            src={getCDNUrl(props.img[0])}
            width={"100%"}
            height={"125px"}
            alt={props.name}
            withPlaceholder
          />
          <Stack
            justify={"center"}
            align={"center"}
            p="1rem"
            spacing="0.5rem"
          >
            <Title
              sx={{
                textTransform: "capitalize",
              }}
              size={16}
              fw={700}
              order={4}
            >
              {props.originalName ? (
                <Trans
                  i18nKey={`countries:${countryNameException(
                    props.originalName
                  )}`}
                >
                  {props.originalName}
                </Trans>
              ) : (
                <Trans i18nKey={`countries:${countryNameException(props.name)}`}>
                  {props.name}
                </Trans>
              )}
            </Title>
            {/* <Text
              weight="bold"
              size={10}
              px={5}
              bg="app-pink.4"
              color="white"
            >
              500MB/日～ 2GB/日
            </Text> */}
            <Text
              weight="bold"
              sx={{
                letterSpacing: "-0.5px"
              }}
            >
              {selectedCode?.code} {selectedCode?.sign}
              <Text
                component="span"
                color="app-pink.4"
                size={"24px"}
                weight="bolder"
              >
                {convertedPrice}
              </Text>
              /日〜
            </Text>
          </Stack>
        </Link>
      </Card.Section>
    </Card>
  );
};
export default RegionPlanCard;
