import { IOrder } from "@/interfaces/IOrder";
import { country<PERSON><PERSON><PERSON>, formatDate, getCDNUrl, regionalOrCountry } from "@/utils";
import {
  Accordion,
  Badge,
  Flex,
  Group,
  Image,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import MyEsimCard from "../MyEsimCard";
import { useEffect, useState } from "react";
import { ApiService } from "@/api";
import { isPast } from "date-fns";
import { Trans, useTranslation } from "next-i18next";

const EsimDetailCard = ({
  item: sourceOrder,
  ...props
}: {
  esimCardProps?: Partial<React.ComponentProps<typeof MyEsimCard>>;
  isExpired?: boolean
  item: IOrder & { status?: string };
}) => {
  const { t } = useTranslation()
  const [item, setItem] = useState<IOrder & { status?: string }>(sourceOrder);
  useEffect(() => {
    if (!item || !item.orderId || item.activateDate) return;
    if (item.status === "expired") return
    if (item.expireTime) return

    ApiService.getOrder(item.orderId + "", { type: "usage" }).then(
      (response) => {
        // setItem(response?.data?.data?.order);
      }
    ).catch(e => {
      console.log("Failed to get updates for", item.orderId)
    });
  }, [item?.orderId]);

  if (!item) return null;
  return (
    <Accordion.Item value={item.orderId + ""} key={item.orderId}>
      <Flex h={35} p="xs" bg="#ffffff30" justify={"space-around"}>
        <Group position={"apart"} w={"100%"}>
          <Text color="white" size={"sm"}>
            {t("common:siminfo-card.orderdate")} {formatDate(item.createdAt)}
          </Text>
          {!isPast(new Date(item.expireTime || "")) ? (
            item.isActivated ? (
              <Badge bg="#0CB21A" radius="xs" variant="filled">
                <Trans i18nKey={"common:siminfo-card.tag.activated"}>
                  ACTIVE
                </Trans>
              </Badge>
            ) : (
              <Badge bg="#FF913F" radius="xs" variant="filled">
                <Trans i18nKey={"common:siminfo-card.tag.notactivated"}>
                  INACTIVE
                </Trans>
              </Badge>
            )
          ) : null}
        </Group>
      </Flex>
      <Accordion.Control>
        <Group id={item.orderId + ""}>
          <Image
            fit="contain"
            radius={"sm"}
            height={48}
            width={90}
            src={getCDNUrl("/assets/card-esim.png")}
          />
          <Stack spacing={-20}>
            <Text size="sm" color="white">
              {regionalOrCountry(item?.plan?.country?.name) === "Country" ?
                t("common:country-esim") :
                t("common:regional-esim")
              }
            </Text>
            <Title tt="capitalize" color="white" order={6}>
              <Trans i18nKey={`countries:${item.plan?.country?.name}`}>
                {/* @ts-ignore */}
                {countryAlias(item?.plan?.country?.name || item?.plan?.country || "")}
              </Trans>

            </Title>{" "}
          </Stack>
        </Group>
      </Accordion.Control>
      <Accordion.Panel>
        <MyEsimCard
          isExpired={props.isExpired}
          status={item.status}
          order={item} {...props.esimCardProps} />
      </Accordion.Panel>
    </Accordion.Item>

  );
};
export default EsimDetailCard;
