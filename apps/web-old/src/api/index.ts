import Storage from "@/core/Storage";
import axios from "axios";
import { AUTH_TOKEN_NAME } from "../../consts";
import { IProfile } from "@/interfaces/IProfile";

const baseURL = process.env.NEXT_PUBLIC_API_HOST;

const httpClient = axios.create({
  baseURL,
});
const resendVerificationEmail = (email: string) =>
  httpClient.get("/auth/resend-verification", {
    params: {
      email,
    },
  });
export const privateHttpClient = axios.create({
  baseURL,
  validateStatus: (status) => status >= 200 && status < 300, // default,
});

privateHttpClient.interceptors.request.use((config) => {
  const token = Storage.getInstance().get(AUTH_TOKEN_NAME);

  if (config.headers && token?.accessToken)
    config.headers["Authorization"] = `Bearer ${token?.accessToken || ""}`;

  return config;
});

const login = (payload: object) => httpClient.post("/auth/login", payload);
const register = (payload: object) =>
  httpClient.post("/auth/register", payload);

const getProfile = (query: object) =>
  privateHttpClient.get("/auth/profile", {
    params: query,
  });

const getPlans = (query: object) =>
  httpClient.get("/plans", {
    params: query,
  });
const getPlanById = (planId: string) => httpClient.get("/plans/" + planId, {});

const getClientSecret = (payload: {}) => {
  return privateHttpClient.post("/payment/setup", payload);
};
const purchasePlan = (plan: object) =>
  privateHttpClient.post("/esim/subscribe", plan);

const updateProfile = (profile: Pick<IProfile, "firstName" | "lastName">) =>
  privateHttpClient.post("/auth/profile", profile);

const getOrders = (options: object) =>
  privateHttpClient.get("/esim/orders", {
    params: options,
  });
const getOrder = (orderId: string, options?: object) =>
  privateHttpClient.get("/esim/orders/" + orderId, {
    params: options,
  });

const forgotPassword = (email: string) =>
  httpClient.post("/auth/forgot-password", {
    email,
  });

const resetPassword = (payload: {
  confirmationCode: string;
  newPassword: string;
  email: string;
}) => httpClient.post("/auth/confirm-password", payload);

const saveUserFirebaseToken = (token: string) => {
  return privateHttpClient.post("/notifications/register-token", {
    token,
  });
};
export const ApiService = {
  saveUserFirebaseToken,
  resetPassword,
  getOrder,
  forgotPassword,
  getOrders,
  purchasePlan,
  getClientSecret,
  login,
  getPlanById,
  getProfile,
  register,
  getPlans,
  updateProfile,
  resendVerificationEmail,
};
