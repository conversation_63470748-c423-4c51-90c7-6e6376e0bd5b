import React from "react";
//@ts-expect-error
import { useAuth } from "@ri/fe-auth";
import jwt_decode from "jwt-decode";
import { AUTH_TOKEN_NAME, ERR_PASSWORD_NOT_MATCHED } from "../../consts";
import Storage from "@/core/Storage";
import { ApiService } from "@/api";
import { useGlobalState } from "@/store";
import Link from "next/link";
import { Text } from "@mantine/core";
import { withBasePath } from "@/utils";

const useLogin = () => {
  const auth = useAuth();
  const [setGlobalMessage, setGlobalLoading, isLoading] = useGlobalState(
    (s) => [s.setMessage, s.toggleLoading, s.isLoading]
  );

  const logout = React.useCallback(() => {
    auth?.endSession();
    Storage.getInstance().set(AUTH_TOKEN_NAME, "");
    setTimeout(() => {
      //Buffer time for state clearance
      window.location.href = withBasePath("auth/signin");
    }, 100);
  }, []);

  const login = React.useCallback(
    (payload: { email: string; password: string }) => {
      setGlobalLoading(true);
      ApiService.login(payload)
        .then(({ data }) => {
          Storage.getInstance().set(AUTH_TOKEN_NAME, data.data);
          const decoded = jwt_decode(data.data.accessToken) as {
            email: string;
            user_id: string;
            name: string;
            companyId: string;
          };
          auth?.startSession({
            id: decoded.user_id,
            displayName: decoded.name,
            email: decoded.email,
            data: {
              companyId: data.data.companyId,
            },
          });
        })
        .catch((err) => {
          setGlobalMessage(
            <>
              {
                err?.response?.data?.message || ERR_PASSWORD_NOT_MATCHED
              }
              {" "}
              {
                err?.response?.data?.message === "User is not confirmed." &&
                <Link href={"/account/resend-email"} style={{
                }}>
                  <Text size={"sm"} color="app-dark" sx={{
                  }}>
                    Resend Email
                  </Text>
                </Link>
              }
            </>
          );
        })
        .finally(() => {
          setGlobalLoading(false);
        });
    },
    []
  );

  return {
    isLoading,
    login,
    logout,
  };
};
export default useLogin;
