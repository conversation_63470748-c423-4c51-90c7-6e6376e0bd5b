{"iphone": "✓ iPhone XR<br/>✓ iPhone XS<br/>✓ iPhone XS Max<br/>✓ iPhone 11<br/>✓ iPhone 11 Pro<br/>✓ iPhone SE thế hệ thứ 2 (2020)<br/>✓ iPhone 12<br/>✓ iPhone 12 Mini<br/>✓ iPhone 12 Pro<br/>✓ iPhone 12 Pro Max<br/>✓ iPhone 13<br/>✓ iPhone 13 Mini<br/>✓ iPhone 13 Pro<br/>✓ iPhone 13 Pro Max<br/>✓ iPhone SE thế hệ thứ 3 (2022)<br/>✓ iPhone 14<br/>✓ iPhone 14 Plus<br/>✓ iPhone 14 Pro<br/>✓ iPhone 14 Pro Max", "iphonemainlandchina": "<PERSON><PERSON><PERSON> thiết bị iPhone từ Trung Quốc Đại lục KHÔNG hỗ trợ eSIM.", "iphonechinasar": "<PERSON><PERSON><PERSON> thi<PERSON><PERSON> bị iPhone từ Hồng Kông và <PERSON> (ngoại trừ iPhone 13 Mini, iPhone 12 Mini, iPhone SE thế hệ thứ 2 (2020) và iPhone XS) KHÔNG hỗ trợ eSIM.", "ipadwithwificellular": "Chỉ các thiết bị iPad có tính năng Wi-Fi + Cellular mới hỗ trợ eSIM.", "ipad": "✓ iPad Air (thế hệ thứ 3)\r<br/>✓ iPad Air (thế hệ thứ 4)\r<br/>✓ iPad Pro 11 inch (thế hệ 1)\r<br/>✓ iPad Pro 11 inch (thế hệ 2)\r<br/>✓ iPad Pro 11 inch (thế hệ thứ 3)\r<br/>✓ iPad Pro 12.9 inch (thế hệ 3)\r<br/>✓ iPad Pro 12.9 inch (thế hệ thứ 4)\r<br/>✓ iPad Pro 12.9 inch (thế hệ thứ 5)\r<br/>✓ iPad (thế hệ thứ 7)\r<br/>✓ iPad (thế hệ thứ 8)\r<br/>✓ iPad (thế hệ thứ 9)\r<br/>✓ iPad (thế hệ thứ 10)\r<br/>✓ iPad Mini (thế hệ thứ 5)\r<br/>✓ iPad Mini (thế hệ thứ 6)", "android": "✓ Samsung Galaxy S20<br/>✓ Samsung Galaxy S20+<br/>✓ Samsung Galaxy S20+ 5g<br/>✓ Samsung Galaxy S20 Ultra<br/>✓ Samsung Galaxy S21<br/>✓ Samsung Galaxy S21+ 5G<br/>✓ Samsung Galaxy S21+ Ultra 5G<br/>✓ Samsung Galaxy S22<br/>✓ Samsung Galaxy S22+<br/>✓ Samsung Galaxy S22 Ultra<br/>✓ Samsung Galaxy Note 20<br/>✓ Samsung Galaxy Note 20 Ultra 5G <br/>✓ Samsung Galaxy Fold<br/>✓ Samsung Galaxy Z Fold2 5G<br/>✓ Samsung Galaxy Z Fold3 5G<br/>✓ Samsung Galaxy Z Fold4<br/>✓ Samsung Galaxy Z Flip<br/>✓ Samsung Galaxy Z Flip3 5G<br/>✓ Samsung Galaxy Z Flip4<br/>✓ Samsung Galaxy S23<br/>✓ Samsung Galaxy S23+<br/>✓ Samsung Galaxy S23 Ultra<br/><br/>✓ Google Pixel 2<br/>✓ Google Pixel 2 XL<br/>✓ Google Pixel 3 <br/>✓ Google Pixel 3 XL<br/>✓ Google Pixel 3a<br/>✓ Google Pixel 3a XL<br/>✓ Google Pixel 4<br/>✓ Google Pixel 4a<br/>✓ Google Pixel 4 XL<br/>✓ Google Pixel 5<br/>✓ Google Pixel 5a<br/>✓ Google Pixel 6<br/>✓ Google pixel 6a<br/>✓ Google Pixel 6 Pro<br/>✓ Google Pixel 7<br/>✓ Google Pixel 7 Pro<br/><br/>✓ Huawei P40<br/>✓ Huawei P40 Pro<br/>✓ Huawei Mate 40 Pro<br/><br/>✓ Oppo Find X3 Pro<br/>✓ Oppo Reno 5A<br/>✓ Oppo Find X5<br/>✓ Oppo Find X5 Pro<br/><br/>✓ Motorola Razr 2019<br/>✓ Motorola Razr 5G<br/>✓ Gemini PDA<br/>✓ Rakuten Mini<br/>✓ Rakuten Big-S<br/>✓ Rakuten Big<br/>✓ Rakuten Hand<br/>✓ Rakuten Hand 5G<br/>✓ Sony Xperia 10 III Lite<br/>✓ Sony Xperia 10 IV<br/>✓ Xperia 1 IV<br/>✓ Sony Xperia 5 IV<br/>✓ Surface Pro X<br/>✓ Honor Magic 4 Pro<br/>✓ Fairphone 4<br/>✓ Sharp Aquos Sense6s<br/>✓ Sharp Aquos Wish<br/>✓ Xiaomi 12T Pro<br/>✓ DOOGEE V30", "androidgooglepixel2": "Google Pixel 2 / 2XL hỗ trợ eSIM chỉ dành riêng cho mạng Google Fi.", "androidgooglepixel3else": "<PERSON><PERSON><PERSON> thiết bị Google Pixel 3/3 XL có nguồn gốc từ <PERSON>, <PERSON><PERSON><PERSON> và Nhật Bản cũng như các thiết bị được mua với dịch vụ từ các nhà mạng của Hoa Kỳ hoặc Canada ngoài Sprint và Google Fi KHÔNG hỗ trợ eSIM.", "androidgooglepixel3aelse": "<PERSON><PERSON><PERSON> thiết bị Google Pixel 3a / 3a XL được mua ở Đông Nam Á và Nhật Bản, đồng thời với dịch vụ của Verizon KHÔNG hỗ trợ eSIM.", "huaweinotcompatible": "<PERSON><PERSON><PERSON> thi<PERSON><PERSON> bị <PERSON>ei P40 Pro+ KHÔNG hỗ trợ eSIM.", "opponotcompatible": "<PERSON><PERSON><PERSON> thi<PERSON><PERSON> bị Oppo Find X5 Lite KHÔNG hỗ trợ eSIM.", "title": "<PERSON><PERSON><PERSON><PERSON> bị hỗ trợ eSIM", "header": {"title": "Hỗ trợ eSIM - Global Mobile eSIM", "description": "Tìm hiểu xem thiết bị của bạn có tương thích với eSIM không. Xem qua danh sách các thiết bị được hỗ trợ của chúng tôi và tìm hiểu cách kích hoạt eSIM trên điện thoại thông minh hoặc máy tính bảng của bạn."}, "forios12later": "iOS iPhone (iOS 12.1 trở lên)", "2ndgeneration": "<PERSON><PERSON><PERSON> h<PERSON> thứ 2", "3rdgeneration": "<PERSON><PERSON><PERSON> h<PERSON> thứ 3", "i-pad-air3rdgeneration": "iPad Air (th<PERSON> hệ thứ 3)", "i-pad-air4thgeneration": "iPad Air (th<PERSON> hệ thứ 4)", "i-pad-pro11inch1stgeneration": "iPad Pro 11-inch (th<PERSON> h<PERSON> thứ 1)", "i-pad-pro11inch2ndgeneration": "iPad Pro 11-inch (<PERSON><PERSON> <PERSON><PERSON> thứ 2)", "i-pad-pro11inch3rdgeneration": "iPad Pro 11-inch (<PERSON><PERSON> <PERSON><PERSON> thứ 3)", "i-pad7thgeneration": "iPad (th<PERSON> hệ thứ 7)", "i-pad8thgeneration": "iPad (th<PERSON> hệ thứ 8)", "i-pad9thgeneration": "iPad (th<PERSON> hệ thứ 9)", "i-pad10thgeneration": "iPad (th<PERSON> hệ thứ 10)", "i-pad-mini5thgeneration": "iPad Mini (th<PERSON> h<PERSON> thứ 5)", "i-pad-mini6thgeneration": "iPad Mini (th<PERSON> <PERSON><PERSON> thứ 6)", "i-pad-pro12": {"9inch3rdgeneration": "iPad Pro 12.9-inch (th<PERSON> <PERSON><PERSON> thứ 3)", "9inch4thgeneration": "iPad Pro 12.9-inch (<PERSON><PERSON> <PERSON><PERSON> thứ 4)", "9inch5thgeneration": "iPad Pro 12.9-inch (th<PERSON> <PERSON><PERSON> thứ 5)"}, "compatibilitywarning": "Lưu ý: <PERSON><PERSON><PERSON><PERSON> bị của bạn có thể không hỗ trợ eSIM tùy thuộc vào quốc gia mua hàng, ngay cả khi nó được liệt kê trên danh sách tương thích của chúng tôi. Một số mẫu cụ thể cho một khu vực cụ thể được bán với các hạn chế về eSIM. Bạn cũng phải đảm bảo rằng thiết bị của bạn không bị khóa SIM."}