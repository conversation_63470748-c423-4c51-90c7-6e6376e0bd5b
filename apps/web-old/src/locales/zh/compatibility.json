{"iphone": "✓ iPhone XR<br/>✓ iPhone XS<br/>✓ iPhone XS Max<br/>✓ iPhone 11<br/>✓ iPhone 11 Pro<br/>✓ iPhone SE 2nd generation (2020)<br/>✓ iPhone 12<br/>✓ iPhone 12 Mini<br/>✓ iPhone 12 Pro<br/>✓ iPhone 12 Pro Max<br/>✓ iPhone 13<br/>✓ iPhone 13 Mini<br/>✓ iPhone 13 Pro<br/>✓ iPhone 13 Pro Max<br/>✓ iPhone SE 3rd generation (2022)<br/>✓ iPhone 14<br/>✓ iPhone 14 Plus<br/>✓ iPhone 14 Pro<br/>✓ iPhone 14 Pro Max", "iphonemainlandchina": "中国大陆的 iPhone 设备没有 eSIM 功能。", "iphonechinasar": "香港和澳门的 iPhone 设备（除了 iPhone 13 Mini、iPhone 12 Mini、iPhone SE 第二代（2020 年）和 iPhone XS）没有 eSIM 功能。", "ipadwithwificellular": "只有带有 Wi-Fi + 蜂窝数据 功能的 iPad 设备具有 eSIM 功能。", "ipad": "✓ iPad Air (第三世代)<br/>✓ iPad Air (第四世代)<br/>✓ iPad Pro 11英吋(第一世代)<br/>✓ iPad Pro 11-英吋 (第二世代)<br/>✓ iPad Pro 11-英吋(第三世代)<br/>✓ iPad Pro 12.9-英吋(第三世代)<br/>✓ iPad Pro 12.9-英吋 (第四時代)<br/>✓ iPad Pro 12.9-英吋 (第五世代)<br/>✓ iPad (第七世代)<br/>✓ iPad (第八世代)<br/>✓ iPad (第九世代)<br/>✓ iPad (第十世代)<br/>✓ iPad Mini (第五世代)<br/>✓ iPad Mini (第六世代)", "android": "✓ Samsung Galaxy S20<br/>✓ Samsung Galaxy S20+<br/>✓ Samsung Galaxy S20+ 5g<br/>✓ Samsung Galaxy S20 Ultra<br/>✓ Samsung Galaxy S21<br/>✓ Samsung Galaxy S21+ 5G<br/>✓ Samsung Galaxy S21+ Ultra 5G<br/>✓ Samsung Galaxy S22<br/>✓ Samsung Galaxy S22+<br/>✓ Samsung Galaxy S22 Ultra<br/>✓ Samsung Galaxy Note 20<br/>✓ Samsung Galaxy Note 20 Ultra 5G <br/>✓ Samsung Galaxy Fold<br/>✓ Samsung Galaxy Z Fold2 5G<br/>✓ Samsung Galaxy Z Fold3 5G<br/>✓ Samsung Galaxy Z Fold4<br/>✓ Samsung Galaxy Z Flip<br/>✓ Samsung Galaxy Z Flip3 5G<br/>✓ Samsung Galaxy Z Flip4<br/>✓ Samsung Galaxy S23<br/>✓ Samsung Galaxy S23+<br/>✓ Samsung Galaxy S23 Ultra<br/><br/>✓ Google Pixel 2<br/>✓ Google Pixel 2 XL<br/>✓ Google Pixel 3 <br/>✓ Google Pixel 3 XL<br/>✓ Google Pixel 3a<br/>✓ Google Pixel 3a XL<br/>✓ Google Pixel 4<br/>✓ Google Pixel 4a<br/>✓ Google Pixel 4 XL<br/>✓ Google Pixel 5<br/>✓ Google Pixel 5a<br/>✓ Google Pixel 6<br/>✓ Google pixel 6a<br/>✓ Google Pixel 6 Pro<br/>✓ Google Pixel 7<br/>✓ Google Pixel 7 Pro<br/><br/>✓ Huawei P40<br/>✓ Huawei P40 Pro<br/>✓ Huawei Mate 40 Pro<br/><br/>✓ Oppo Find X3 Pro<br/>✓ Oppo Reno 5A<br/>✓ Oppo Find X5<br/>✓ Oppo Find X5 Pro<br/><br/>✓ Motorola Razr 2019<br/>✓ Motorola Razr 5G<br/>✓ Gemini PDA<br/>✓ Rakuten Mini<br/>✓ Rakuten Big-S<br/>✓ Rakuten Big<br/>✓ Rakuten Hand<br/>✓ Rakuten Hand 5G<br/>✓ Sony Xperia 10 III Lite<br/>✓ Sony Xperia 10 IV<br/>✓ Xperia 1 IV<br/>✓ Sony Xperia 5 IV<br/>✓ Surface Pro X<br/>✓ Honor Magic 4 Pro<br/>✓ Fairphone 4<br/>✓ Sharp Aquos Sense6s<br/>✓ Sharp Aquos Wish<br/>✓ Xiaomi 12T Pro<br/>✓ DOOGEE V30", "androidgooglepixel2": "谷歌Pixel 2/2XL具有eSIM功能，但此功能仅限于Google Fi网络使用", "androidgooglepixel3else": "来自澳大利亚、台湾和日本的谷歌Pixel 3/3 XL设备，以及使用美国或加拿大运营商（Sprint和Google Fi除外）服务购买的设备均不具备eSIM功能", "androidgooglepixel3aelse": "-在东南亚和日本购买的谷歌Pixel 3a / 3a XL设备以及使用Verizon服务的设备也不具备eSIM功能", "huaweinotcompatible": "华为P40 Pro+不具备eSIM功能<br/>", "opponotcompatible": "Oppo Find X5 Lite不具备eSIM功能", "title": "eSIM兼容设备", "header": {"title": "eSIM兼容性 - Global Mobile eSIM", "description": "请查看我们支持的设备列表，了解如何在您的智能手机或平板电脑上激活eSIM，并确认您的设备是否兼容"}, "forios12later": "iOS iPhone（iOS 12.1或更高版本）", "2ndgeneration": "第2代", "3rdgeneration": "第3代", "i-pad-air3rdgeneration": "iPad Air（第3代）", "i-pad-air4thgeneration": "iPad Air（第4代）", "i-pad-pro11inch1stgeneration": "iPad Pro 11英寸（第1代）", "i-pad-pro11inch2ndgeneration": "iPad Pro 11英寸（第2代）", "i-pad-pro11inch3rdgeneration": "iPad Pro 11英寸（第3代）", "i-pad7thgeneration": "iPad（第7代）", "i-pad8thgeneration": "iPad（第8代）", "i-pad9thgeneration": "iPad（第9代）", "i-pad10thgeneration": "iPad（第10代）", "i-pad-mini5thgeneration": "iPad Mini（第5代）", "i-pad-mini6thgeneration": "iPad Mini（第6代）", "i-pad-pro12": {"9inch3rdgeneration": "iPad Pro 12.9英寸（第3代）", "9inch4thgeneration": "iPad Pro 12.9英寸（第4代）", "9inch5thgeneration": "iPad Pro 12.9英寸（第5代）"}, "compatibilitywarning": "\r<br/>请注意：根据购买国家的不同，您的设备可能不支持eSIM，即使在我们的兼容性列表上有列出。一些特定地区的型号可能带有eSIM的限制。您还必须确保您的设备未被锁定。\r<br/>\r<br/>\r<br/>\r<br/>\r<br/>\r", "home": {}}