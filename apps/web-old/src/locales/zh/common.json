{"logout": "登出", "accountinformation": "账户资料", "country-esim": "国家eSIM", "regional-esim": "区域eSIM", "resendanemail": "重新发送邮件", "features": "特点", "compatibility": "相容性", "specs": "技术规格 ", "language": {"title": "语言", "or": "或"}, "copyable-item": {"btn": {"copy": {"txt": "复制"}}}, "dataplan": {"fixeditem": {"no-price": "{{ dataPlan }} / 30天", "with-price": "{{ dataPlan }} / 30天 ${{ price }}"}, "item": "每天${{ price }}美元起{{ dataPlan }}", "recommended": "特别推荐", "bestseller": "人气商品"}, "btn": {"login": "登录", "getyouresim": "btn.get-your-esim"}, "navbar": {"profile": {"label": "个人资料"}, "shop": {"label": "购物"}, "home": {"label": "首页"}, "myesim": {"label": "我的eSIM"}, "help": {"label": "帮助"}, "account": {"label": "账号"}}, "siminfo-card": {"remainingdata": "剩余数据", "usageperiod": "使用期限", "plantype": "eSIM套餐：", "reminded-message": "*在达到{{dataType}}后，如果网络繁忙，我们可能会暂时降低数据速度。", "notinuse": {"txt": "尚未使用"}, "notavailable": "N/A", "tag": {"expired": "已过期", "activated": "使用中", "notactivated": "未启用"}, "orderdate": "订单日期", "countryplaceholder": "country placeholder", "fixedday": {"type": "流量包套餐"}, "perday": {"type": "单日流量套餐", "unit": "天"}, "order-id": "订单ID", "currentdatausage": "当前数据使用情况", "usagedays": {"title": "使用天数", "usageplanunit_one": "{{ count }} 天", "usageplanunit_other": "{{ count }} 天", "usageplanunit_many": "siminfoCard.usage-days.usage-plan-unit_many"}, "resettimenotification": "*数据使用量将在每天自动重置更新，具体时间为", "dataplan": {"title": "数据使用量"}, "validity": {"unit_one": "{{ count }} 天", "unit_other": "{{ count }} 天", "unit_many": "siminfoCard.validity.unit_many"}, "expiredate": {"title": "到期日期"}, "info": {"txt": "选择您的设备操作系统以激活此eSIM。"}, "provider": "Global Mobile eSIM"}, "page-header": {"btn": {"back": "back btn", "close": "close btn"}}, "social-login": {"fbicon": {"alt": "FB icon"}, "link": {"facebook": "使用Facebook账号继续", "google": "使用Google账号继续"}, "googleicon": {"alt": "Google icon"}}, "social-signup": {"link": {"facebook": "使用Facebook账号继续", "google": "使用Google账号继续"}}, "payment-method": {"creditcard": "信用卡"}, "regional-plan-amount": "<0>From</0> <1>{{ amount }}</1>", "company-name": "Inbound Platform Corp.", "division-name": "Global Mobile Division", "company-address": "SW Shinbashi Building 4th floor, 6-14-5 Shinbashi, Minato-ku, Tokyo 105-0004"}