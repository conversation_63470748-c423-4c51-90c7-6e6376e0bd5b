{"emailsentverification": "이메일을 보냈습니다.{{ email}}. 가입을 완료하기 위해서 링크로된 이메일을 클릭해 주세요.", "verify-fail": {"title": "본인확인실패", "header": "본인 확인 인증에 실패 하였습니다.", "txt": "인증에 실패했습니다. 다시한번 이메일주소를 확인하신 후 인증을 해 주세요", "btn": {"continue": "확인"}}, "closebtn": {"image": "close btn"}, "verifyemail": {"title": "본인 확인을 위해, 이메일주소의 인증을 부탁드립니다.", "header": {"title": "계정 인증  - Global Mobile eSIM"}}, "verificationemailsent": "인증메일이 발송되었습니다.", "verificationemailnotsent": "에러가 발생하였습니다. 다시한번 인증메일을 보내주세요", "verifyemailimage": "메일을 인증", "emailnotification": {"txt": "<b>{{email}}</b> 으로 인증용메일을 보냈습니다.  메일을 확인하신 후, 메일에 기재된 URL를 클릭하시고 회원등록을 완료해 주세요."}, "btn": {"resendemail": "인증 메일을 재 전송"}, "verify-success": {"title": "이메일 인증에 성공했습니다.", "header": {"title": "인증성공"}, "close": {"image": "close btn"}, "success": {"image": "메일을 인증"}, "btn": {"continue": "확인"}}}