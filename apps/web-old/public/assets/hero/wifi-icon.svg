<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_537_2775)">
<g filter="url(#filter0_d_537_2775)">
<path d="M50 75H50.0417" stroke="url(#paint0_linear_537_2775)" stroke-width="8.33333" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_d_537_2775)">
<path d="M38.2166 63.2167C41.342 60.0921 45.5805 58.3369 49.9999 58.3369C54.4193 58.3369 58.6578 60.0921 61.7832 63.2167" stroke="url(#paint1_linear_537_2775)" stroke-width="8.33333" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_d_537_2775)">
<path d="M26.4292 51.4292C29.5245 48.3337 33.1992 45.8782 37.2435 44.203C41.2878 42.5277 45.6225 41.6655 50 41.6655C54.3776 41.6655 58.7122 42.5277 62.7565 44.203C66.8008 45.8782 70.4756 48.3337 73.5709 51.4292" stroke="url(#paint2_linear_537_2775)" stroke-width="8.33333" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
</g>
<defs>
<filter id="filter0_d_537_2775" x="35.8333" y="60.8333" width="28.375" height="28.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.894118 0 0 0 0 0 0 0 0 0 0.443137 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_537_2775"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_537_2775" result="shape"/>
</filter>
<filter id="filter1_d_537_2775" x="24.0498" y="44.1702" width="51.9001" height="33.2131" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.894118 0 0 0 0 0 0 0 0 0 0.443137 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_537_2775"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_537_2775" result="shape"/>
</filter>
<filter id="filter2_d_537_2775" x="12.2625" y="27.4988" width="75.4751" height="38.097" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.894118 0 0 0 0 0 0 0 0 0 0.443137 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_537_2775"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_537_2775" result="shape"/>
</filter>
<linearGradient id="paint0_linear_537_2775" x1="50.0208" y1="75" x2="50.0208" y2="76" gradientUnits="userSpaceOnUse">
<stop offset="0.09" stop-color="white"/>
<stop offset="0.435" stop-color="white" stop-opacity="0.84"/>
</linearGradient>
<linearGradient id="paint1_linear_537_2775" x1="49.9999" y1="58.3369" x2="49.9999" y2="63.2167" gradientUnits="userSpaceOnUse">
<stop offset="0.09" stop-color="white"/>
<stop offset="0.435" stop-color="white" stop-opacity="0.84"/>
</linearGradient>
<linearGradient id="paint2_linear_537_2775" x1="50" y1="41.6655" x2="50" y2="51.4292" gradientUnits="userSpaceOnUse">
<stop offset="0.09" stop-color="white"/>
<stop offset="0.435" stop-color="white" stop-opacity="0.84"/>
</linearGradient>
<clipPath id="clip0_537_2775">
<rect width="100" height="100" fill="white"/>
</clipPath>
</defs>
</svg>
