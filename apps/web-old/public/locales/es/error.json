{"validation": {"minlength1": {"firstname": "El nombre debe tener al menos un carácter", "lastname": "El apellido debe tener al menos un carácter", "profileimage": "La imagen del perfil debe tener al menos un carácter"}, "minlength2": {"defaultpaymentmethod": "El método de pago predeterminado debe tener al menos 2 caracteres", "locale": "El idioma predeterminado debe tener al menos 2 caracteres", "referral": "El código de referencia predeterminado debe tener al menos 2 caracteres", "website": "La URL del sitio web predeterminado debe tener al menos 2 caracteres."}, "wrongusernamepassword": "Nombre de usuario o contraseña incorrectos", "failed": "Los datos ingresados no pasaron las reglas de validación."}, "invalid": {"email": "La dirección de correo electrónico proporcionada no es válida.", "password": "Contraseña inválida", "resetpasswordcode": "Código de restablecimiento de contraseña inválido.", "verifyemailcode": "Código de verificación de correo electrónico inválido", "firstname": "Nombre inválido", "lastname": "Apellido(s) inválido", "username": {"0": "E", "1": "l", "2": " ", "3": "n", "4": "o", "5": "m", "6": "b", "7": "r", "8": "e", "9": " ", "10": "d", "11": "e", "12": " ", "13": "u", "14": "s", "15": "u", "16": "a", "17": "r", "18": "i", "19": "o", "20": " ", "21": "p", "22": "r", "23": "o", "24": "p", "25": "o", "26": "r", "27": "c", "28": "i", "29": "o", "30": "n", "31": "a", "32": "d", "33": "o", "34": " ", "35": "n", "36": "o", "37": " ", "38": "c", "39": "u", "40": "m", "41": "p", "42": "l", "43": "e", "44": " ", "45": "c", "46": "o", "47": "n", "48": " ", "49": "l", "50": "o", "51": "s", "52": " ", "53": "r", "54": "e", "55": "q", "56": "u", "57": "i", "58": "s", "59": "i", "60": "t", "61": "o", "62": "s", "63": ".", "password": "La combinación de nombre de usuario y contraseña no coincide."}, "newpasswordsameoldpassword": "La nueva contraseña no puede ser la misma que la anterior", "plan": "Plan inválido", "order": {"dataoptionid": "Opción de datos inválida", "quantity": "Cantidad de pedido inválida", "topupid": "Número de recarga inválido", "iccid": "ICCID de pedido inválido"}, "usernotconfirmed": "Usuario no confirmado", "request": "La solicitud está mal formada o faltan parámetros requeridos.", "json": "El cuerpo de la solicitud no está en formato JSON válido.", "phone": {"found": "El número de teléfono ya existe."}, "token": "El token de autenticación proporcionado es inválido o ha expirado.", "operation": "No se puede realizar la operación solicitada en el recurso."}, "unable": {"resendverificationemail": "No se puede reenviar el correo de verificación", "forgotpassword": "No se puede enviar el correo de restablecimiento de contraseña", "changepassword": "No es posible cambiar la contraseña", "confirmpassword": "No se puede confirmar la contraseña"}, "login": {"social": {"email": "Error al iniciar sesión a través de la cuenta de correo electrónico", "social": "Error al iniciar sesión con una cuenta social."}, "wrongpassword": "Contrase<PERSON> ", "error": {"wrong": {"password": "Es posible que su contraseña o dirección de correo electrónico sea incorrecta."}}}, "alreadyexists": {"email": "Ya existe una cuenta registrada con este correo electrónico", "phone": "El número de teléfono ya está registrado a nombre de otro usuario"}, "user": {"notfound": "Usuario no encontrado", "not": {"found": "El usuario especificado no existe."}}, "esim": {"subscribe": "Error al suscribirse/comprar la eSIM", "checkusage": "Error al obtener el estado de uso de la eSIM comprada", "unauthorizedaccess": "Acceso no autorizado a la información de la eSIM comprada", "getorders": "Error al iniciar sesión con tu correo electrónico.", "notfound": "eSIM no encontrada"}, "plan": {"notfound": "Plan no encontrado", "allplans": "Error al recuperar todos los planes"}, "request": {"missingparameters": "La solicitud está mal formada o le faltan parámetros requeridos", "invalidformat": "El cuerpo de la solicitud no está en formato JSON válido", "duplicatecontent": "El contenido ya existe", "missingtoken": "El código de autenticación falta en los encabezados de la solicitud", "expiredtoken": "El código de autenticación proporcionado es inválido o ha expirado", "insufficientpermissions": "El usuario no tiene permisos suficientes para realizar la acción", "accessdenied": "El usuario no tiene permiso para acceder al recurso solicitado", "userblocked": "La cuenta del usuario está bloqueada o desactivada", "resourcenotfound": "El recurso solicitado no existe", "resourceconflict": "La acción solicitada entra en conflicto con el estado actual del recurso", "validationfailed": "Los datos ingresados no cumplen con las reglas de validación", "operationconflict": "La operación solicitada no se puede realizar en el recurso"}, "server": {"unexpectederror": "Ocurrió un error inesperado en el servidor", "databaseoperationfailed": "La operación en la base de datos falló", "externalserviceerror": "Ocurrió un error al comunicarse con un servicio externo"}, "code": {"4000": "La dirección de correo electrónico ya existe."}, "email": {"not": {"verified": "La dirección de correo electrónico no está verificada."}}, "account": {"not": {"idp": {"manual": "La cuenta fue creada usando un inicio de sesión social."}}}, "content": {"conflict": "Contenido duplicado."}, "missing": {"token": "Falta el token de autenticación en los encabezados de la solicitud."}, "insufficient": {"permissions": "El usuario no tiene permisos suficientes para realizar esta acción."}, "access": {"denied": "El usuario no tiene permitido acceder al recurso solicitado."}, "blocked": {"user": "La cuenta de usuario está bloqueada o desactivada."}, "resource": {"not": {"found": "El recurso solicitado no existe."}}, "product": {"not": {"found": "El producto especificado no existe."}}, "duplicate": {"resource": "Ya existe un recurso con el mismo identificador."}, "conflicting": {"state": "La acción solicitada entra en conflicto con el estado actual del recurso."}, "internal": {"server": {"error": "Se produjo un error inesperado en el servidor."}}, "database": {"error": "Falló la operación de la base de datos."}, "external": {"service": {"error": "Ocurrió un error al comunicarse con un servicio externo."}}}