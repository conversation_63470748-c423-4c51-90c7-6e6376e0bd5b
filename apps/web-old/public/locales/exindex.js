const fs = require("fs");
const path = require("path");

function traverseDirectories(rootDir) {
  const nestedJson = {};

  function traverse(dirPath, nestedObj) {
    const files = fs.readdirSync(dirPath);
    files.forEach((file) => {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        nestedObj[file] = {};
        traverse(filePath, nestedObj[file]);
      } else if (file.endsWith(".json")) {
        nestedObj[
          file.slice(0, -5)
        ] = `https://esim.gmobile.biz/locales/${path.relative(
          __dirname,
          filePath
        )}`;
      }
    });
  }

  traverse(rootDir, nestedJson);
  return nestedJson;
}

const rootDirectory = "../locales";
const nestedJson = traverseDirectories(rootDirectory);
console.log(JSON.stringify(nested<PERSON><PERSON>, null, 2));
