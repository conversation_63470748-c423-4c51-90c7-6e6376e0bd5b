{"validation": {"minlength1": {"firstname": "名字至少必须包含1个字符。", "lastname": "姓氏至少必须包含1个字符。", "profileimage": "个人资料图片至少必须包含1个字符。"}, "minlength2": {"defaultpaymentmethod": "默认付款方式至少必须包含2个字符。", "locale": "默认语言（语系）至少必须包含2个字符。", "referral": "默认推荐码至少必须包含2个字符。", "website": "默认网站至少必须包含2个字符。"}, "wrongusernamepassword": "用户名或密码错误。", "failed": "输入数据未通过验证规则。"}, "invalid": {"email": "提供的电子邮件地址无效。", "password": "输入的密码无效。", "resetpasswordcode": "输入的重置密码码无效。", "verifyemailcode": "输入的验证电子邮件码无效。", "firstname": "输入的名字无效。", "lastname": "输入的姓氏无效。", "username": {"0": "提", "1": "供", "2": "的", "3": "用", "4": "户", "5": "名", "6": "不", "7": "符", "8": "合", "9": "要", "10": "求", "11": "。", "password": "提供的用户名和密码组合不匹配。"}, "newpasswordsameoldpassword": "新密码不能与旧密码相同。", "plan": "输入的方案无效。", "order": {"dataoptionid": "输入的数据选项无效。", "quantity": "订单数量无效。", "topupid": "输入的充值ID无效。", "iccid": "输入的订单ICCID无效。"}, "usernotconfirmed": "用户尚未确认。", "request": "请求格式错误或缺少必需的参数。", "json": "请求内容不是有效的JSON格式。", "phone": {"found": "电话号码已存在。"}, "token": "提供的身份验证令牌无效或已过期。", "operation": "无法对该资源执行请求的操作。"}, "unable": {"resendverificationemail": "无法重新发送验证电子邮件。", "forgotpassword": "无法发送重置密码的电子邮件。", "changepassword": "无法更改密码。", "confirmpassword": "无法确认密码。"}, "login": {"social": {"email": "通过电子邮件登录时发生错误。", "social": "通过社交登录时发生错误。"}, "wrongpassword": "输入的密码错误。", "error": {"wrong": {"password": "您的密码或电子邮件地址可能有误。"}}}, "alreadyexists": {"email": "使用该电子邮件的账户已注册。", "phone": "电话号码已注册至其他用户。"}, "user": {"notfound": "找不到用户。", "not": {"found": "指定的用户不存在。"}}, "esim": {"subscribe": "订阅/购买eSIM时发生错误。", "checkusage": "无法检索已购买eSIM的使用状态。", "unauthorizedaccess": "未授权访问已购买的eSIM详细信息。", "getorders": "通过电子邮件登录时发生错误。", "notfound": "找不到eSIM。"}, "plan": {"notfound": "找不到方案。", "allplans": "无法检索所有方案。"}, "request": {"missingparameters": "请求格式错误或缺少必要的参数。", "invalidformat": "请求正文不是有效的JSON格式。", "duplicatecontent": "内容已存在。", "missingtoken": "认证令牌缺失于请求头中。", "expiredtoken": "提供的认证令牌无效或已过期。", "insufficientpermissions": "用户没有足够的权限执行此操作。", "accessdenied": "用户不被允许访问请求的资源。", "userblocked": "用户账户被封锁或已停用。", "resourcenotfound": "请求的资源不存在。", "resourceconflict": "请求的操作与当前资源状态冲突。", "validationfailed": "输入的数据未通过验证规则。", "operationconflict": "无法对该资源执行请求的操作。"}, "server": {"unexpectederror": "服务器发生了意外错误。", "databaseoperationfailed": "数据库操作失败。", "externalserviceerror": "与外部服务通信时发生错误。"}, "code": {"4000": "电子邮件地址已存在。"}, "email": {"not": {"verified": "电子邮件地址未验证。"}}, "account": {"not": {"idp": {"manual": "账户是使用社交登录创建的。"}}}, "content": {"conflict": "重复的内容。"}, "missing": {"token": "请求头中缺少身份验证令牌。"}, "insufficient": {"permissions": "用户没有足够的权限执行此操作。"}, "access": {"denied": "不允许用户访问请求的资源。"}, "blocked": {"user": "用户账户被封锁或停用。"}, "resource": {"not": {"found": "请求的资源不存在。"}}, "product": {"not": {"found": "指定的产品不存在。"}}, "duplicate": {"resource": "已经存在具有相同标识符的资源。"}, "conflicting": {"state": "请求的操作与资源的当前状态冲突。"}, "internal": {"server": {"error": "服务器发生了意外错误。"}}, "database": {"error": "数据库操作失败。"}, "external": {"service": {"error": "与外部服务通信时发生错误。"}}}