{"validation": {"minlength1": {"firstname": "First name must be at least 1 character long", "lastname": "Last name must be at least 1 character long", "profileimage": "Profile image must be at least 1 character long"}, "minlength2": {"defaultpaymentmethod": "Default payment method must be at least 2 characters long", "locale": "Default locale (language) must be at least 2 characters long", "referral": "Default referral must be at least 2 characters long", "website": "Default website must be at least 2 characters long"}, "wrongusernamepassword": "Wrong username or password", "failed": "Input data failed validation rules."}, "invalid": {"email": "The provided email address is not valid.", "password": "Invalid password entered", "resetpasswordcode": "Invalid reset password code entered", "verifyemailcode": "Invalid verify email code entered", "firstname": "Invalid first name entered", "lastname": "Invalid last name entered", "username": {"0": "T", "1": "h", "2": "e", "3": " ", "4": "p", "5": "r", "6": "o", "7": "v", "8": "i", "9": "d", "10": "e", "11": "d", "12": " ", "13": "u", "14": "s", "15": "e", "16": "r", "17": "n", "18": "a", "19": "m", "20": "e", "21": " ", "22": "d", "23": "o", "24": "e", "25": "s", "26": " ", "27": "n", "28": "o", "29": "t", "30": " ", "31": "m", "32": "e", "33": "e", "34": "t", "35": " ", "36": "t", "37": "h", "38": "e", "39": " ", "40": "r", "41": "e", "42": "q", "43": "u", "44": "i", "45": "r", "46": "e", "47": "m", "48": "e", "49": "n", "50": "t", "51": "s", "52": ".", "password": "The provided username and password combination does not match."}, "newpasswordsameoldpassword": "New password cannot be the same as the old password", "plan": "Invalid plan entered", "order": {"dataoptionid": "Invalid data option entered", "quantity": "Invalid order quantity", "topupid": "Invalid top up ID entered", "iccid": "Invalid order ICCID entered"}, "usernotconfirmed": "User is not confirmed", "request": "The request is malformed or missing required parameters.", "json": "The request body is not a valid JSON format.", "phone": {"found": "Phone number already exists."}, "token": "The provided authentication token is invalid or expired.", "operation": "The requested operation cannot be performed on the resource."}, "unable": {"resendverificationemail": "Unable to resend verification email", "forgotpassword": "Unable to send forgot password email", "changepassword": "Unable to change password", "confirmpassword": "Unable to confirm password"}, "login": {"social": {"email": "Error logging in through email account", "social": "Error logging in through social login"}, "wrongpassword": "Wrong password entered", "error": {"wrong": {"password": "Your password or email address may be wrong. "}}}, "alreadyexists": {"email": "Account with this email already registered", "phone": "Phone number already registered to another user"}, "user": {"notfound": "User not found", "not": {"found": "The specified user does not exist."}}, "esim": {"subscribe": "Error subscribing/buying eSIM", "checkusage": "Error retrieving usage status of purchased eSIM", "unauthorizedaccess": "Unauthorized access to purchased eSIM details", "getorders": "Error logging in through email account", "notfound": "eSIM not found"}, "plan": {"notfound": "Plan not found", "allplans": "Error retrieving all plans"}, "request": {"missingparameters": "The request is malformed or missing required parameters", "invalidformat": "The request body is not in valid JSON format", "duplicatecontent": "Content already exists", "missingtoken": "Authentication token is missing in the request headers", "expiredtoken": "Provided authentication token is invalid or expired", "insufficientpermissions": "User does not have sufficient permissions to perform the action", "accessdenied": "User is not allowed to access the requested resource", "userblocked": "User account is blocked or deactivated", "resourcenotfound": "Requested resource does not exist", "resourceconflict": "Requested action conflicts with the current state of the resource", "validationfailed": "Input data failed validation rules", "operationconflict": "Requested operation cannot be performed on the resource"}, "server": {"unexpectederror": "Unexpected error occurred on the server", "databaseoperationfailed": "Database operation failed", "externalserviceerror": "Error occurred while communicating with an external service"}, "code": {"4000": "Email address already exists."}, "email": {"not": {"verified": "Email address is not verified."}}, "account": {"not": {"idp": {"manual": "Account was created using social login."}}}, "content": {"conflict": "Duplicate content."}, "missing": {"token": "Authentication token is missing in the request headers."}, "insufficient": {"permissions": "The user does not have sufficient permissions to perform the action."}, "access": {"denied": "The user is not allowed to access the requested resource."}, "blocked": {"user": "The user account is blocked or deactivated."}, "resource": {"not": {"found": "The requested resource does not exist."}}, "product": {"not": {"found": "The specified product does not exist."}}, "duplicate": {"resource": "A resource with the same identifier already exists."}, "conflicting": {"state": "The requested action conflicts with the current state of the resource."}, "internal": {"server": {"error": "An unexpected error occurred on the server."}}, "database": {"error": "Database operation failed."}, "external": {"service": {"error": "Error occurred while communicating with an external service."}}}