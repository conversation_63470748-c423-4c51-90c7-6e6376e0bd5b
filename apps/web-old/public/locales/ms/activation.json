{"pagedescription": "<PERSON><PERSON> akan mula menghitung hari k<PERSON><PERSON><PERSON> (hari pengguna<PERSON>) apabila pelanggan telah mengaktifkan eSIM pertama kali. Tempoh sah akan dikira secara 24 jam bermula pada tarikh dan masa pengaktifan pertama seperti yang dinyatakan dalam sejarah pembelian.", "ios": {"qr-code-description": "<PERSON><PERSON> ke [<PERSON><PERSON><PERSON>] > [selular] > [<PERSON><PERSON> pelan selular] dan imbas kod QR ini.", "manual-description": "<PERSON><PERSON> ke [<PERSON><PERSON><PERSON>] > [selular] > [<PERSON>bah pelan selular] > [Ma<PERSON>kkan butiran secara manual] dan taipkan kod."}, "android": {"qr-code-description": "<PERSON><PERSON> ke [Te<PERSON>pan] > [Rangkaian & Internet] > [SIM] > [Muat turun SIM?] Dan imbas kod QR ini.", "manual-description": "<PERSON>gi ke [Te<PERSON>pan] > [Rangkaian & Internet] > [SIM] > [Muat turun Si<PERSON>?] > [<PERSON><PERSON> bantuan?] > [Masukkan secara manual] dan taipkan kod."}, "title": "Cara penga<PERSON>", "header": "Pengaktifan", "subsection": {"title": "<PERSON><PERSON><PERSON> kaedah untuk mengaktifkan eSIM ini."}, "method1": {"title": "Kaedah 1", "txt": "Pengaktifan Kod QR"}, "manualsetup": {"image": "", "title": "Pengaktifan Manual"}, "method2": {"title": "Kaedah 2", "txt": "Pengaktifan Manual (SM-DP+)"}, "qrcodesetup": {"title": "Pengaktifan Kod QR"}, "qrcode": {"image": "QR Code"}, "smdp": {"address": "<PERSON><PERSON> al<PERSON>t SM-DP+"}, "downloadlink": "<PERSON><PERSON> turun p<PERSON> (Kod)", "activation-code": {"txt": "<PERSON><PERSON>"}, "btn": {"opensetupguide": "Buka Panduan Pengaktifan"}, "accountactivation": "Account user", "setuppage": {}}