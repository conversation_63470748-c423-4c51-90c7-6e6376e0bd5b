/**
 * Run this javascript snippet on plans list page of macaroon.
 * to get only japan plans , filter it and run this command
 */
var table = document.querySelector(
  '.table.table-bordered.table-striped.table-condensed.table-hover',
);
let rows = Array.from(table.querySelectorAll('tbody tr'));

const plans = [];
for (let i = 1; i < rows.length; i++) {
  const row = rows[i];
  const tds = [...row.querySelectorAll('td')].map((item) => item.innerText);
  const [id] = tds;
  if (tds.length < 30) continue;
  const header = [...rows[0].querySelectorAll('th')].map(
    (item) => item.innerText,
  );
  const obj = {};
  header.forEach((key, index) => {
    Object.assign(obj, {
      [key]: tds[index],
    });
  });
  plans.push(obj);
}

function transformPlans(plans) {
  return plans.map((originalPlan) => {
    // Create a new plan object to store the transformed data
    var transformedPlan = {};

    // Map ID to optionId
    transformedPlan.optionId = originalPlan.ID;

    // Extract country from plan name
    var countryMatch = originalPlan['Plan Name'].match(/([a-zA-Z]+)/);
    transformedPlan.country = countryMatch ? countryMatch[0].toLowerCase() : '';

    // Determine plan provider based on plan name
    transformedPlan.planProvider = originalPlan['Plan Name']
      .toLowerCase()
      .includes('roaming')
      ? 'ROAMING'
      : 'LOCAL';

    // Map price to provisionPrice and leave cost price empty
    transformedPlan.provisionPrice = originalPlan.Price
      ? parseFloat(originalPlan.Price.replace('$', ''))
      : null;
    transformedPlan.price = 1;
    transformedPlan.costPrice = '';

    // Determine package type based on plan validity
    transformedPlan.packageType = originalPlan['Plan Name'].includes('/day')
      ? 'PER_DAY'
      : 'FIXED_DAY';

    // Extract data volume and unit from plan name
    var dataMatch = originalPlan['Plan Name'].match(/(\d+)\s*(GB|MB)(?:\/day)?/i)
    transformedPlan.dataVolume = dataMatch ? dataMatch[1] : null;
    transformedPlan.dataUnit =  dataMatch ? dataMatch[2] : null;
    
    transformedPlan.dataId =  transformedPlan.dataVolume + " " +  transformedPlan.dataUnit

    // Determine validity days and cycle based on plan validity
    var validityMatch =
      originalPlan['Plan Validity'].match(/(\d+)\s*Day\(s\)/i);
    transformedPlan.validityDays = validityMatch
      ? parseInt(validityMatch[1])
      : null;
    transformedPlan.validityDaysCycle = 'days';
    transformedPlan.name =  transformedPlan.validityDays + " " +  transformedPlan.validityDaysCycle

    // Set network provider to 'japan wireless esim' if not found
    transformedPlan.networkProvider = 'JapanWireless eSIM';

    // Set network type and qos based on rule of speed limit
    transformedPlan.networkType = '4G';
    transformedPlan.networkQos = originalPlan['Rule of Speed Limit']
      ? originalPlan['Rule of Speed Limit']
      : '';

    // Set network APN
    transformedPlan.netowkrApn = 'www.dtac.co.th';

    // Set service provider ID
    transformedPlan.serviceProviderId = 'UROCOMM';
    return transformedPlan;
  });
}
// Log the transformed plan
console.log(transformPlans(plans));
