import { fstat } from 'fs';
import xlsx from 'node-xlsx';
import * as fs from 'fs';
const workSheetsFromFile = xlsx.parse(
  './apps/api/data/esim-fare-table.xlsx',
  {},
);
const splitNumberAndString = (word: any) => {
  const [number, string] = word.match(/(\d+)\s*([a-zA-Z]+)/)?.slice(1) || [
    word,
  ];
  return [number, string].filter(Boolean);
};

const planSheet = workSheetsFromFile.find(
  (item) => item.name === 'Copy of Global MASTER v2',
);
planSheet?.data.shift();
planSheet?.data.shift();

const plans = planSheet?.data
  .map(
    ([
      _,
      country,
      ,
      days,
      data,
      packageType,
      networkProvider,
      networkType,
      network,
      networkQos,
      netowkrApn,
      _1,
      _2,
      provisionPrice,
      percent,
      afterTaxPrice,
      newPrice,
      newAfterTaxPrice,
      optionId,
      ...rest
    ]) => {
      if (!optionId) return;
      return {
        country: country.toLowerCase(),
        provisionPrice,
        price: +Number(newAfterTaxPrice || afterTaxPrice).toFixed(2),
        optionId,
        planProvider: network.toUpperCase(),
        name: days,
        dataId: data,
        dataVolume: splitNumberAndString(data || '')[0],
        dataUnit: splitNumberAndString(data || '')[1],
        validityDays: +days?.split(' ').shift(),
        validityDaysCycle: days?.split(' ').pop(),
        packageType: packageType === 'FIXED' ? 'FIXED_DAY' : 'PER_DAY',
        networkProvider,
        networkType,
        networkQos,
        netowkrApn,
        serviceProviderId: 'USIMSA',
      };
    },
  )
  .filter((v) => !!v?.dataId);

fs.writeFileSync(
  './apps/api/data/master-formatted-plan.json',
  JSON.stringify(plans),
  'utf-8',
);
