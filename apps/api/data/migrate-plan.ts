import * as fs from 'fs';
import { groupBy, find } from 'lodash';

const usimsaPlans = JSON.parse(
  fs.readFileSync('./master-formatted-plan.json', 'utf-8'),
);

function extractValues(inputString) {
  // Define a regular expression pattern to match the required format
  const pattern = /(\d+) (\w+) \/ .* (\d+)(\w+)/;

  // Use match method to extract values based on the pattern
  const match = inputString.match(pattern);

  // Check if match is found
  if (match) {
    // Extract validityDays, dataVolume, and dataUnit from the matched groups
    const validityDays = parseInt(match[1]);
    const dataVolume = parseInt(match[3]);
    const dataUnit = match[4];

    // Return an object containing extracted values
    return {
      validityDays,
      dataVolume,
      dataUnit,
    };
  } else {
    // If no match is found, return null
    return null;
  }
}

function transformData(inputData) {
  // Split the input data by lines
  const lines = inputData.split('\n').filter(String);
  if (!lines) return;
  // Initialize an array to store transformed JSON objects
  const result = [];

  // Iterate over each line of input data
  lines.forEach((line) => {
    // Split each line by tab to extract individual values
    const [country, name, provisionPrice, optionId] = line.split('\t');

    // Extract additional values from the 'name' field
    const { validityDays, dataVolume, dataUnit } = extractValues(name);

    // Extract the name excluding the additional values
    const trimmedName = name.replace(/\d+ [a-zA-Z]+ \/ /, '');

    // Build the JSON object
    const jsonObject = {
      country: country?.trim?.(),
      provisionPrice: parseFloat(provisionPrice.trim()),
      price: 10.48, // Assuming this is a constant value
      optionId: optionId?.trim?.(),
      planProvider: 'LOCAL', // Assuming this is a constant value
      name: trimmedName?.trim?.(),
      dataId: dataVolume + dataUnit,
      dataVolume: dataVolume,
      dataUnit: dataUnit?.trim?.(),
      validityDays: validityDays,
      validityDaysCycle: 'days', // Assuming this is a constant value
      packageType: 'PER_DAY', // Assuming this is a constant value
      networkProvider: 'DTAC', // Assuming this is a constant value
      networkType: '4G', // Assuming this is a constant value
      networkQos: '384kbps', // Assuming this is a constant value
      networkApn: 'www.dtac.co.th', // Assuming this is a constant value
      serviceProviderId: 'USIMSA', // Assuming this is a constant value
    };

    // Push the JSON object into the result array
    result.push(jsonObject);
  });

  // Return the transformed JSON array
  return result;
}

const plansOfCountry = transformData(`
france	1 day / daily speed drop after 500MB	0.86	5F3B473D-4E2B-EE11-87DD-28187860CAA9
france	2 days / daily speed drop after 500MB	1.72	603B473D-4E2B-EE11-87DD-28187860CAA9
france	3 days / daily speed drop after 500MB	2.58	613B473D-4E2B-EE11-87DD-28187860CAA9
france	4 days / daily speed drop after 500MB	3.12	623B473D-4E2B-EE11-87DD-28187860CAA9
france	5 days / daily speed drop after 500MB	3.61	633B473D-4E2B-EE11-87DD-28187860CAA9
france	6 days / daily speed drop after 500MB	3.98	643B473D-4E2B-EE11-87DD-28187860CAA9
france	7 days / daily speed drop after 500MB	4.63	653B473D-4E2B-EE11-87DD-28187860CAA9
france	10 days / speed down after 500MB daily	5.92	663B473D-4E2B-EE11-87DD-28187860CAA9
france	12 days / speed down after 500MB daily	7	673B473D-4E2B-EE11-87DD-28187860CAA9
france	15 days / speed down after 500MB daily	7.65	683B473D-4E2B-EE11-87DD-28187860CAA9
france	20 days / daily speed drop after 500MB	10.18	693B473D-4E2B-EE11-87DD-28187860CAA9
france	30 days / daily speed drop after 500MB	12.76	6A3B473D-4E2B-EE11-87DD-28187860CAA9
france	1 day / decrease speed after 1GB every day	1.4	6B3B473D-4E2B-EE11-87DD-28187860CAA9
france	2 days / daily speed drop after 1GB	2.05	6C3B473D-4E2B-EE11-87DD-28187860CAA9
france	3 days / daily speed drop after 1GB	3.12	6D3B473D-4E2B-EE11-87DD-28187860CAA9
france	4 days / daily speed drop after 1GB	4.79	6E3B473D-4E2B-EE11-87DD-28187860CAA9
france	5 days / daily speed drop after 1GB	5.33	6F3B473D-4E2B-EE11-87DD-28187860CAA9
france	6 days / daily speed drop after 1GB	6.46	703B473D-4E2B-EE11-87DD-28187860CAA9
france	7 days / daily speed decrease after 1GB	6.89	713B473D-4E2B-EE11-87DD-28187860CAA9
france	10 days / daily speed drop after 1GB	9.1	723B473D-4E2B-EE11-87DD-28187860CAA9
france	12 days / daily speed drop after 1GB	10.18	733B473D-4E2B-EE11-87DD-28187860CAA9
france	15 days / daily speed drop after 1GB	11.31	743B473D-4E2B-EE11-87DD-28187860CAA9
france	20 days / daily speed drop after 1GB	14.54	753B473D-4E2B-EE11-87DD-28187860CAA9
france	30 days / daily speed drop after 1GB	18.68	763B473D-4E2B-EE11-87DD-28187860CAA9
france	1 day / decrease speed after 2GB every day	2.53	773B473D-4E2B-EE11-87DD-28187860CAA9
france	2 days / daily speed drop after 2GB	3.88	783B473D-4E2B-EE11-87DD-28187860CAA9
france	3 days / daily speed drop after 2GB	6.19	793B473D-4E2B-EE11-87DD-28187860CAA9
france	4 days / daily speed drop after 2GB	6.73	7A3B473D-4E2B-EE11-87DD-28187860CAA9
france	5 days / daily speed drop after 2GB	7.81	7B3B473D-4E2B-EE11-87DD-28187860CAA9
france	6 days / daily speed drop after 2GB	9.42	7C3B473D-4E2B-EE11-87DD-28187860CAA9
france	7 days / daily speed decrease after 2GB	10.07	7D3B473D-4E2B-EE11-87DD-28187860CAA9
france	10 days / daily speed drop after 2GB	13.57	7E3B473D-4E2B-EE11-87DD-28187860CAA9
france	12 days / daily speed drop after 2GB	15.35	7F3B473D-4E2B-EE11-87DD-28187860CAA9
france	15 days / daily speed drop after 2GB	16.69	803B473D-4E2B-EE11-87DD-28187860CAA9
france	20 days / daily speed drop after 2GB	22.08	813B473D-4E2B-EE11-87DD-28187860CAA9
france	30 days / daily reduction after 2GB	28	823B473D-4E2B-EE11-87DD-28187860CAA9
france	1 day / decrease speed after 3GB every day	3.39	833B473D-4E2B-EE11-87DD-28187860CAA9
france	2 days / daily speed drop after 3GB	5.12	843B473D-4E2B-EE11-87DD-28187860CAA9
france	3 days / daily speed drop after 3GB	8.35	853B473D-4E2B-EE11-87DD-28187860CAA9
france	4 days / daily speed drop after 3GB	10.18	863B473D-4E2B-EE11-87DD-28187860CAA9
france	5 days / daily speed decrease after 3GB	12.92	873B473D-4E2B-EE11-87DD-28187860CAA9
france	6 days / daily speed decreased after 3GB	15.35	883B473D-4E2B-EE11-87DD-28187860CAA9
france	7 days / daily speed decreased after 3GB	16.15	893B473D-4E2B-EE11-87DD-28187860CAA9
france	10 days / daily speed drop after 3GB	18.85	8A3B473D-4E2B-EE11-87DD-28187860CAA9
france	12 days / daily speed decreased after 3GB	22.08	8B3B473D-4E2B-EE11-87DD-28187860CAA9
france	15 days / daily speed drop after 3GB	26.38	8C3B473D-4E2B-EE11-87DD-28187860CAA9
france	20 days / daily speed decrease after 3GB	31.23	8D3B473D-4E2B-EE11-87DD-28187860CAA9
france	30 days / daily speed drop after 3GB	36.62	8E3B473D-4E2B-EE11-87DD-28187860CAA9
`);

usimsaPlans.forEach((item) => {
  if (item.country !== plansOfCountry[1].country) {
    return;
  }

  const plan = find(plansOfCountry, {
    // dataId: item.dataId,
    dataVolume: +item.dataVolume,
    dataUnit: item.dataUnit,
    validityDays: +item.validityDays,
    packageType: 'PER_DAY',
    // planProvider: 'ROAMING',
  });
  if (plan) {
    item.optionId = plan.optionId;
  }
});
fs.writeFileSync(
  './master-formatted-plan-new.json',
  JSON.stringify(usimsaPlans),
);
