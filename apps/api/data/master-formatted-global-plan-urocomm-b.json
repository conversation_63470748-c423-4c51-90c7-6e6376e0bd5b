[{"country": "Guam", "provisionPrice": 6.28, "price": 8.16, "optionId": 16961, "planProvider": "ROAMING", "name": "3 days", "dataId": "1 GB", "dataVolume": 1, "dataUnit": "GB", "validityDays": 3, "validityDaysCycle": "days", "packageType": "PER_DAY", "networkProvider": "mobile.three.com.hk or share.three.com.hk", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "mobile.three.com.hk or share.three.com.hk", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 6.28, "price": 8.16, "optionId": 16962, "planProvider": "ROAMING", "name": "3 days", "dataId": "1 GB", "dataVolume": 1, "dataUnit": "GB", "validityDays": 3, "validityDaysCycle": "days", "packageType": "PER_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 10.56, "price": 13.73, "optionId": 16963, "planProvider": "ROAMING", "name": "3 days", "dataId": "3 GB", "dataVolume": 3, "dataUnit": "GB", "validityDays": 3, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 12.57, "price": 16.34, "optionId": 16964, "planProvider": "ROAMING", "name": "3 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 3, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 14.95, "price": 19.44, "optionId": 16965, "planProvider": "ROAMING", "name": "3 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 3, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 12.88, "price": 16.74, "optionId": 16966, "planProvider": "ROAMING", "name": "5 days", "dataId": "3 GB", "dataVolume": 3, "dataUnit": "GB", "validityDays": 5, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 15.33, "price": 19.93, "optionId": 16967, "planProvider": "ROAMING", "name": "5 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 5, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 18.24, "price": 23.71, "optionId": 16968, "planProvider": "ROAMING", "name": "5 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 5, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 18.7, "price": 24.31, "optionId": 16969, "planProvider": "ROAMING", "name": "7 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 7, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 22.26, "price": 28.94, "optionId": 16970, "planProvider": "ROAMING", "name": "7 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 7, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 26.49, "price": 34.44, "optionId": 16971, "planProvider": "ROAMING", "name": "7 days", "dataId": "20 GB", "dataVolume": 20, "dataUnit": "GB", "validityDays": 7, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 22.82, "price": 29.67, "optionId": 16972, "planProvider": "ROAMING", "name": "10 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 10, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 27.15, "price": 35.3, "optionId": 16973, "planProvider": "ROAMING", "name": "10 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 10, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 32.31, "price": 42, "optionId": 16974, "planProvider": "ROAMING", "name": "10 days", "dataId": "20 GB", "dataVolume": 20, "dataUnit": "GB", "validityDays": 10, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 27.84, "price": 36.19, "optionId": 16975, "planProvider": "ROAMING", "name": "15 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 15, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 33.13, "price": 43.07, "optionId": 16976, "planProvider": "ROAMING", "name": "15 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 15, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 39.42, "price": 51.25, "optionId": 16977, "planProvider": "ROAMING", "name": "15 days", "dataId": "20 GB", "dataVolume": 20, "dataUnit": "GB", "validityDays": 15, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 46.91, "price": 60.98, "optionId": 16978, "planProvider": "ROAMING", "name": "15 days", "dataId": "30 GB", "dataVolume": 30, "dataUnit": "GB", "validityDays": 15, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 33.96, "price": 44.15, "optionId": 16979, "planProvider": "ROAMING", "name": "30 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 40.42, "price": 52.55, "optionId": 16980, "planProvider": "ROAMING", "name": "30 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 48.1, "price": 62.53, "optionId": 16981, "planProvider": "ROAMING", "name": "30 days", "dataId": "20 GB", "dataVolume": 20, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "New Zealand", "provisionPrice": 57.23, "price": 74.4, "optionId": 16982, "planProvider": "ROAMING", "name": "30 days", "dataId": "30 GB", "dataVolume": 30, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or internet.proximus.be", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or internet.proximus.be", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 10.56, "price": 13.73, "optionId": 16983, "planProvider": "ROAMING", "name": "3 days", "dataId": "3 GB", "dataVolume": 3, "dataUnit": "GB", "validityDays": 3, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 12.57, "price": 16.34, "optionId": 16984, "planProvider": "ROAMING", "name": "3 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 3, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 12.88, "price": 16.74, "optionId": 16985, "planProvider": "ROAMING", "name": "5 days", "dataId": "3 GB", "dataVolume": 3, "dataUnit": "GB", "validityDays": 5, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 15.33, "price": 19.93, "optionId": 16986, "planProvider": "ROAMING", "name": "5 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 5, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 18.24, "price": 23.71, "optionId": 16987, "planProvider": "ROAMING", "name": "5 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 5, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 18.7, "price": 24.31, "optionId": 16988, "planProvider": "ROAMING", "name": "7 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 7, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 22.26, "price": 28.94, "optionId": 16989, "planProvider": "ROAMING", "name": "7 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 7, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 22.82, "price": 29.67, "optionId": 16990, "planProvider": "ROAMING", "name": "10 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 10, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 27.15, "price": 35.3, "optionId": 16991, "planProvider": "ROAMING", "name": "10 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 10, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 27.84, "price": 36.19, "optionId": 16992, "planProvider": "ROAMING", "name": "15 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 15, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 33.13, "price": 43.07, "optionId": 16993, "planProvider": "ROAMING", "name": "15 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 15, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 39.42, "price": 51.25, "optionId": 16994, "planProvider": "ROAMING", "name": "15 days", "dataId": "20 GB", "dataVolume": 20, "dataUnit": "GB", "validityDays": 15, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 33.96, "price": 44.15, "optionId": 16995, "planProvider": "ROAMING", "name": "30 days", "dataId": "5 GB", "dataVolume": 5, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 40.42, "price": 52.55, "optionId": 16996, "planProvider": "ROAMING", "name": "30 days", "dataId": "10 GB", "dataVolume": 10, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 48.1, "price": 62.53, "optionId": 16997, "planProvider": "ROAMING", "name": "30 days", "dataId": "20 GB", "dataVolume": 20, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}, {"country": "Canada", "provisionPrice": 57.23, "price": 74.4, "optionId": 16998, "planProvider": "ROAMING", "name": "30 days", "dataId": "30 GB", "dataVolume": 30, "dataUnit": "GB", "validityDays": 30, "validityDaysCycle": "days", "packageType": "FIXED_DAY", "networkProvider": "cmlink or orange", "networkType": "3G/4G/5G", "networkQos": "256kbps", "networkApn": "cmlink or orange", "serviceProviderId": "UROCOMM"}]