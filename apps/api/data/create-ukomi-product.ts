import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import FormData from 'form-data';
import { pick } from 'lodash';
import { capitalizeFirstLetter } from '../src/utils';

const apiKey = process.env.UKOMI_API_KEY!;
const apiSecret = process.env.UKOMI_API_SECRET!;
const ukomiBaseUrl = 'https://api.u-komi.com';
const appEnv = process.env.APP_ENV;

async function getUkomiAccessToken(): Promise<string> {
  if (!apiKey || !apiSecret) {
    console.error(
      'Missing required environment variables: UKOMI_API_KEY and/or UKOMI_API_SECRET',
    );
    process.exit(1);
  }

  const formData = new FormData();
  formData.append('api_key', apiKey);
  formData.append('api_secret', apiSecret);
  try {
    const authResponse = await axios.post(
      `${ukomiBaseUrl}/auth/access_token`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } },
    );
    return authResponse.data.data.access_token;
  } catch (error) {
    console.error('Error fetching Ukomi access token:', error);
    throw error;
  }
}

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: `${process.env.DATABASE_URL}?connection_limit=200&pool_timeout=0`,
    },
  },
});

async function createUkomiProducts() {
  const plans = await prisma.plans.findMany({
    where: { enabled: true },
    include: { network: true, country: true },
  });

  // Base payload
  const payload = {
    access_token: '',
    currency_iso: 'USD',
    products: {},
  };

  for (const plan of plans) {
    const { network, country, ...rest } = plan;
    const envPrefix = appEnv === 'production' ? '' : 'TEST-';
    const countryName = capitalizeFirstLetter(country.name);

    const name = `${envPrefix}GMobile eSIM ${countryName} [${plan.name}], ${plan.dataVolume}${plan.dataUnit}/Day`;
    const url = `https://esim.gmobile.biz/region/${country.name}`;
    const image = `https://cdn-dev.japan-wireless.com/global-esim/assets/plan-images/plan-${plan.id}.png`;
    const description = `International travel eSIM for ${countryName.toUpperCase()}`;
    const group_name = `gmobile-esim-${country.name}`;
    const tag = 'gmobile-esim';

    // Collect specs: packageType + network details
    const specs = {
      ...pick(rest, ['packageType']),
      networkName: network.name,
      qos: network.qos,
      generation: network.networkGeneration,
    };

    payload.products[`${envPrefix}GLOBAL-MOBILE-ESIM-${plan.id}`] = {
      url,
      name,
      image,
      description,
      group_name,
      price: plan.price,
      tag,
      specs,
      created_at: plan.createdAt,
      updated_at: plan.updatedAt,
    };
  }

  payload.access_token = await getUkomiAccessToken();

  try {
    const response = await axios.post(
      `${ukomiBaseUrl}/products/${apiKey}/create`,
      payload,
    );
    console.log('Ukomi create response:', response.data);
  } catch (error: any) {
    console.error(
      'Error creating Ukomi products:',
      error.response?.data || error.message,
    );
  }
}

createUkomiProducts().catch((e) => {
  console.error('Unhandled error:', e);
  process.exit(1);
});
