{"compilerOptions": {"typeRoots": ["./node_modules/@types", "../node_modules/@types", "../../node_modules/@types"], "module": "Node16", "declaration": true, "removeComments": true, "moduleResolution": "node16", "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "types": ["node"], "target": "es2020", "lib": ["es2020", "dom"]}}