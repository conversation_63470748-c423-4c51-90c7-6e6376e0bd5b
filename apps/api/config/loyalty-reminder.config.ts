import { registerAs } from '@nestjs/config';

export const loyaltyReminder = registerAs('loyalty-reminder', () => ({
  testMode: process.env.APP_ENV === 'development',
  cron: {
    test: '0,10,20,30,40,50 * * * *', // Every 10 minutes
    prod: '0 10,13,16,19 * * *', // 10 AM, 1 PM, 4 PM, 7 PM JST
  },
  thresholds: {
    threeMonths: {
      prod: 3,
      test: 10, // 10 minutes before the scheduler time (send 3 months email to purchase made between -10 to 0th minute of reference point of scheduler run time)
    },
    sixMonths: {
      prod: 6,
      test: 20, // 20 minutes before the scheduler time (send 6 months email to purchase made between -20 to -10th minutes of reference point of scheduler run time)
    },
  },
}));
