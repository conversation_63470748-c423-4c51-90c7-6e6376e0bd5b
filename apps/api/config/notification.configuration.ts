import { registerAs } from "@nestjs/config";
import { DataConsumedRule } from "src/esim-usage-rules/core/rules/DataConsumedRule";
import { PackageAboutToExpire } from "src/esim-usage-rules/core/rules/PackageAboutToExpire";
import { PackageExprired } from "src/esim-usage-rules/core/rules/PackageExpired";

export default registerAs('notification', () => {
    return {
        enableEsimUsageNotifier: true,
        enableSessionCartNotifier: true,
        // empty array means for all users
        allowedUsers: (process.env.NOTIFICATION_ALLOWED_USERS ? JSON.parse(process.env.NOTIFICATION_ALLOWED_USERS || "[]") : []) || [],
        esimUsageExpireInHour: 3,
        staleOrdersTimeInMinutes: 55,
        userDataConsumeThreshold: 80,
        userHasBelowThreshold: 80,
        planDateAboutToFinishNotifyInHour: 4,
        packageExpiredNotifyLimit: 1,
        dataConsumedRuleNotifyLimit: 1,
        packageAboutToExpireNotifyLimit: 1,
        usageEnabledRules: [DataConsumedRule.NAME, PackageAboutToExpire.NAME, PackageExprired.NAME]
    }
});
