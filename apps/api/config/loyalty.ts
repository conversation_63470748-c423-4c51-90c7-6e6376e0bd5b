import { registerAs } from '@nestjs/config';

export const loyalty = registerAs('loyalty', () => ({
  featureEnabled: true,
  couponPrefix: 'LY',
  couponExpiry: null, // never expires

  // map “conversion count (i.e. number of past successful orders),” → “discount %”
  discountByConversion: {
    1: 35,
    2: 35,
    3: 20,
    4: 20,
    5: 15,
    default: 10, // for 6th+ conversions
  } as Record<string, number>,
}));
