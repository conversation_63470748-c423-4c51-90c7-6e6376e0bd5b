export default (configService: any = {}) => {
  const connection = {
    connection: {
      host: process.env.REDIS_HOST,
      port: 6379,
      db:
        process.env.NODE_ENV === 'development'
          ? undefined
          : process.env.REDIS_DB_INDEX
          ? +process.env.REDIS_DB_INDEX
          : 1,
      retryStrategy: function (times) {
        return Math.max(Math.min(Math.exp(times), 20000), 1000); // Exponential backoff
      },
      maxRetriesPerRequest: null, // Fail after 5 retries
      // enableOfflineQueue: false, // Fail fast if Redis is offline
    },
    queue: {
      removeOnComplete: {
        age: 3600 * 12, // keep up to 1 hour
        count: 1000, // keep up to 1000 jobs
      },
      removeOnFail: {
        age: 48 * 3600, // keep up to 48 hours
      },
      delay: 5000,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 60000,
      },
    },
  };
  return connection;
};
