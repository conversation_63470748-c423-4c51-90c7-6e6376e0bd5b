import { Injectable, ExecutionContext } from '@nestjs/common';
import { I18nResolver, I18nResolverOptions } from 'nestjs-i18n';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class CustomLanguagePayloadResolveer implements I18nResolver {
  constructor(
    @I18nResolverOptions() private keys: string[] = []) { }

  resolve(context: ExecutionContext) {
    let req: any;

    switch (context.getType() as string) {
      case 'http':
        req = context.switchToHttp().getRequest();
        break;
      case 'graphql':
        [, , { req }] = context.getArgs();
        break;
    }

    return req?.body?.language;
  }
}
