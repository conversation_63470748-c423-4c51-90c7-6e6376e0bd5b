import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  BadRequestException,
  Query,
  UsePipes,
  ValidationPipe,
  UnprocessableEntityException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UsersQueryDto } from './dto/users-query-dto';
import { AuthGuard } from '@nestjs/passport';
import { ApiExcludeController } from '@nestjs/swagger';
import { SentryInterceptor } from 'src/SentryInterceptor';

@Controller('/admin/users')
@ApiExcludeController()
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
@UseGuards(AuthGuard('headerapikey'))
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  // @Post()
  // create(@Body() createUserDto: CreateUserDto) {
  //   return this.usersService.create(createUserDto);
  // }

  @Get()
  @UsePipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  )
  async findAll(@Query() query?: UsersQueryDto) {
    try {
      const { page, search, sort_by, limit, ...filters } = query;
      const orderBy = {};
      if (sort_by) {
        const [column, sort] = sort_by.split(':');
        orderBy[column] = sort || 'asc';
      }
      return await this.usersService.findAll(
        {
          createdAt: query.created_at,
          email: query.email,
          firstName: query.first_name,
          lastName: query.last_name,
          userId: query.user_id,
          stripeId: query.stripe_id,
          id: isNaN(+query.id) ? undefined : +query.id,
        } || {},
        {
          page: Number(page || 1),
          sort: orderBy,
          search,
          limit: +limit,
        },
      );
    } catch (err) {
      console.log(err);
      throw new UnprocessableEntityException();
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    try {
      return await this.usersService.findOne(+id);
    } catch {
      throw new BadRequestException();
    }
  }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
  //   return this.usersService.update(+id, updateUserDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.usersService.remove(+id);
  // }
}
