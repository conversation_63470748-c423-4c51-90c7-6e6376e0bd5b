import { ApiPropertyOptional } from '@nestjs/swagger';
import { users } from '@prisma/client';
import { IsOptional, IsString } from 'class-validator';

export class UsersQueryDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  id: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  user_id: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  stripe_id: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  first_name: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  last_name: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  email: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  created_at: Date;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  updated_at: Date;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  profile_image: string;

  @IsOptional()
  @ApiPropertyOptional({
    description: 'Page number for pagination',
  })
  page: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: 'Search by email, first_name and last_name',
  })
  search: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description:
      'sorts the result, provide in format like "id:desc" or "id:asc"',
  })
  sort_by: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: 'Number of result to be fetched, default is 10',
  })
  limit: string;
}
