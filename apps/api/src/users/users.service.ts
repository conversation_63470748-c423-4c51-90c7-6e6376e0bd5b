import { Injectable, Logger } from '@nestjs/common';
import { IDENTITY_PROVIDER, users } from '@prisma/client';
import { PrismaService } from 'src/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { LocalUserPool } from './enums/LocalUserPool';

@Injectable()
export class UsersService {
  private logger = new Logger(UsersService.name);
  constructor(private prismaService: PrismaService) { }

  getUserInfo(userId: string, userPool?: LocalUserPool) {
    return this.prismaService.users.findFirst({
      where: {
        userId: { contains: userId },
        userPool,
      },
      include: {
        corporates_users: {
          include: {
            corporate: {
              include: {
                corporate_apps: true,
              },
            },
          },
        },
      },
    });
  }
  getUserInfoUserAccounts(where: {
    cognitoUserName?: string,
    cognitoUserSub?: string,
    usersId?: number
  }, userPool?: LocalUserPool) {
    return this.prismaService.users_accounts.findFirst({
      where: {
        ...where
      },
      include: {
        user: true
      }
    });
  }

  async subscribeUserEmailToNotification(userId: number) {
    const user = await this.prismaService.users.findFirst({
      where: {
        id: userId,
      },
    });
    try {
      // const audience = await this.notificationService.getAudienceIdOfUser(
      //   userId,
      // );
      // return this.notificationService.subscribeAudienceToChannel(
      //   audience.audienceId,
      //   'EMAIL',
      //   user.email,
      // );
    } catch (err) {
      this.logger.log(err);
      // return await this.notificationService.createAudience(userId);
    }
  }

  upsertUser(payload: Partial<users>) {
    const { userId, stripeId } = payload;
    return this.prismaService.users.upsert({
      where: {
        userId,
      },
      create: {
        username: payload.username,
        email: payload.email,
        userId,
        stripeId: '',
        firstName: payload.firstName,
        lastName: payload.lastName || '',
        source: payload.source,
        phone_number: payload.phone_number,
        locale: payload.locale,
        userPool: payload.userPool,
        profileImage: payload.profileImage,
      },
      update: {
        stripeId,
      },
    });
  }
  // todo remove
  upsertUserByUsername(payload: Partial<users>) {
    const { userId, stripeId } = payload;
    return this.prismaService.users.upsert({
      where: {
        username: payload.username,
      },
      create: {
        username: payload.username,
        email: payload.email,
        userId,
        stripeId: '',
        firstName: payload.firstName,
        lastName: payload.lastName || '',
        source: payload.source,
        phone_number: payload.phone_number,
        locale: payload.locale,
        userPool: payload.userPool,
        profileImage: payload.profileImage,
      },
      update: {
        stripeId,
      },
    });
  }
  updateUser(where: Partial<users>, data: Partial<users>) {
    return this.prismaService.users.update({
      data,
      where: { userId: where.userId, ...where },
    });
  }

  hasUser(
    userId: string,
    where?: {
      email?: string;
      userPool?: LocalUserPool;
      phone_number?: string;
    },
  ) {
    return this.prismaService.users.findFirst({
      where: {
        OR: {
          userId,
          phone_number: where?.phone_number,
          email: where?.email,
        },
      },
    });
  }

  getUserByStripeCustomerId(stripeCustomerId: string) {
    return this.prismaService.users.findFirst({
      where: {
        stripeId: stripeCustomerId,
      },
    });
  }
  async getUserByEmail(
    email: string,
    userPool?: LocalUserPool,
    options?: { idpProvider?: IDENTITY_PROVIDER },
  ) {
    const users = await this.prismaService.users.findMany({
      where: {
        email,
        idpProvider: options?.idpProvider || undefined,
        userPool: userPool || LocalUserPool.GLOBAL,
      },
    });
    return users?.[0];
  }

  getUserByUsername(username: string): Promise<users> {
    //@ts-expect-error
    return this.prismaService.users.findFirstOrThrow({
      where: {
        username,
      },
      select: {
        id: true,
        username: true,
        email: true,
        phone_number: true,
        firstName: true,
        lastName: true,
        source: true,
        userPool: true,
        profileImage: true,
        stripeId: true,
        locale: true,
        isVerified: true,
        idpProvider: true,
        userId: true,
        isPhoneVerified: true,
      },
    });
  }

  getUser(filter: {
    phone_number?: string;
    email?: string;
    username?: string;
    source?: string;
  }) {
    return this.prismaService.users.findFirst({
      where: {
        OR: [
          {
            email: filter.email,
            source: filter.source,
          },
          {
            phone_number: filter.phone_number,
            source: filter.source,
          },
        ],
      },
    });
  }
  create(createUserDto: CreateUserDto) {
    return 'This action adds a new user';
  }

  async findAll(
    filter: Partial<Omit<users, 'response' | 'orders'> & {}>,
    options?: {
      page?: number;
      search?: string;
      sort?: {
        [key: string]: string;
      };
      limit?: number;
    },
  ) {
    const { page, sort } = options || { page: 1, sort: {} };
    const perPage = options.limit || 10;
    let skip, take;
    if (page) {
      (skip = page === 1 || !page ? 0 : perPage * (Number(page || 1) - 1)),
        (take = perPage);
    }

    let nextFilter: Parameters<
      typeof this.prismaService.users.findMany
    >['0']['where'] = filter;

    if (options.search) {
      nextFilter = {
        OR: [
          {
            email: {
              startsWith: options.search,
            },
          },
          {
            firstName: {
              startsWith: options.search,
            },
          },
          {
            lastName: {
              startsWith: options.search,
            },
          },
        ],
      };
    }
    const result = await this.prismaService.$transaction([
      this.prismaService.users.count({
        where: { ...nextFilter, isVerified: true },
      }),
      this.prismaService.users.findMany({
        where: { ...nextFilter, isVerified: true },
        skip,
        take,
        orderBy: sort,
      }),
    ]);

    return {
      total: result[0] ?? 0,
      data: result[1],
    };
  }

  findOne(id: number) {
    return this.prismaService.users.findFirst({
      where: {
        id,
      },
    });
  }

  shouldSourceHaveIsolatedUsers(source: string) {
    if (source === 'airtrip') {
      return true;
    }
    return false;
  }

  async findUserByEmailIsolatedSource({
    email,
    phone,
    userPool,
    idpProvider,
  }: {
    idpProvider?: IDENTITY_PROVIDER | IDENTITY_PROVIDER[];
    email?: string;
    phone?: string;
    userPool?: LocalUserPool;
  }) {

    let filter: any = {
      where: {
        idpProvider: idpProvider ? {
          in: Array.isArray(idpProvider) ? idpProvider : idpProvider ? [idpProvider] : undefined
        } : undefined,
        OR: [
          email ? { email, userPool } : {},
          phone ? { phone_number: phone, userPool } : {},
        ],
      },
    };


    return this.prismaService.users.findFirst(filter);
  }

  async createUserAccounts(params: { cognitoUserName: string, cognitoUserSub: string, usersId: number, idpProvider: IDENTITY_PROVIDER }) {
    const data = await this.prismaService.users_accounts.upsert({
      create: {
        ...params
      },
      update: {
        ...params
      },
      where: {
        cognitoUserName: params.cognitoUserName
      }
    });

    return data
  }


}
