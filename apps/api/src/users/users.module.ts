import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { PrismaService } from 'src/prisma.service';
import { PassportModule } from '@nestjs/passport';
import { APIKeyStrategy } from 'src/auth/api-key.strategy';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { AppsService } from 'src/apps/apps.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { BullModule } from '@nestjs/bullmq';
import { SEND_NOTIFICATION } from 'src/constants';
import redisConnection from 'config/redis-connection';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailTemplateService } from 'src/email-template/email-template.service';

@Module({
  imports: [PassportModule.register({})],
  controllers: [UsersController],
  providers: [
    UsersService,
    KeyValueStoreService,
    APIKeyStrategy,
    PrismaService,
    AppsService,
    NotificationsService,
    EmailTemplateService,
  ],
})
export class UsersModule {}
