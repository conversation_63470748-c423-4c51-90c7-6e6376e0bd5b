{"subject": "{brandNameSubject}{couponDiscount} offクーポンをお贈りします", "thank-you-title": "{brandName}をご利用いただき、誠にありがとうございます。\n", "appreciation-message": "感謝の気持ちを込めて、次回ご購入時にご利用いただける【{couponDiscount}%割引クーポン】をお贈りいたします。\n", "coupon-code": "クーポンコード：{couponCode}\n", "one-time-notice": "※クーポンはお一人様一回限りのご利用となります。\n", "closing-message": "改めまして、{brandName}をお選びいただきありがとうございました。皆さまの旅が素晴らしいものになりますように。またのご利用を心よりお待ちしております。\n", "sign-off": "またのご利用を心よりお待ちしております。\n", "team-name": "{brandName}\n"}