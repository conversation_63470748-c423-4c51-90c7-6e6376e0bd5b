import {
  Body,
  Controller,
  Logger,
  Post,
  Req,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiTags } from '@nestjs/swagger';
import { Recaptcha } from '@nestlab/google-recaptcha';
import axios from 'axios';
import { Request, Response } from 'express';
import { I18n, I18nContext } from 'nestjs-i18n';
import { EmailsService } from './emails.service';
import { ContactUsDto } from './dto/contact-us.dto';

@ApiTags('Emails')
@Controller('emails')
export class EmailsController {
  private logger: Logger = new Logger('emails');
  constructor(
    private readonly emailsService: EmailsService,
    private configService: ConfigService,
  ) {}

  @Post('contact-us')
  @UsePipes(
    new ValidationPipe({
      whitelist: true,
    }),
  )
  @Recaptcha({ response: (req) => req.body.recaptha })
  async contactUs(
    @Body() payload: ContactUsDto,
    @Req() request: Request,
    @Res() res: Response,
    @I18n() i18n: I18nContext,
  ) {
    const lang = payload.language;
    const translatedMailResponse = await axios.post(
      `http://127.0.0.1:${
        this.configService.getOrThrow('APP_PORT') || 3001
      }/api/v1/payment/esim/mail/translate`,
      {
        template: 'customer-inquiry',
        ...payload,
      },
    );

    const subjectLang = {
      jp: '「新規顧客からのお問い合わせ」',
      en: 'New Customer Inquiry',
    };

    return this.emailsService
      .sendEmail({
        origin: payload?.origin,
        lang: lang,
        to: payload.emailAddress,
        message: translatedMailResponse.data,
        subject: `${subjectLang[lang] ?? subjectLang['en']} - ${
          payload?.orderNumber
            ? `${lang === 'jp' ? '注文' : 'Order'} #${payload?.orderNumber}`
            : payload.problemCategory
        }`,
      })
      .then(() => {
        this.logger.log(`Inquiry email sent from ${payload.emailAddress}`);
        res.json({ success: true });
      })
      .catch((err) => {
        this.logger.error(err);
        res.sendStatus(500);
      });
  }
}
