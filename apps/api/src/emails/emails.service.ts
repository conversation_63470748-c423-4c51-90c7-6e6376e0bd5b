import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as nodemailer from 'nodemailer';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';
import { isAppEnvDev, isDev } from 'src/utils';
import { CreateEmailTemplateDto } from './create-email-template.dto';
import axiosRetry from 'axios-retry';

type EmailParams = {
  bcc?: string[];
  subject?: string;
  to?: string;
  message?: string;
  content?: string;
  lang?: string;
  origin?: ServicePlansEnum;
};
@Injectable()
export class EmailsService {
  private transporter;
  private axiosClient = axios.create();

  constructor(private logger: Logger, private configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: configService.get('GMESIM_MAIL_HOST'),
      port: configService.get('GMESIM_MAIL_HOST_PORT'),
      secure: false,
      auth: {
        user: configService.get('GMESIM_MAIL_HOST_USERNAME'),
        pass: configService.get('GMESIM_MAIL_HOST_PASSWORD'),
      },
    });

    axiosRetry(this.axiosClient, {
      onRetry: (d) => {
        this.logger.log(
          `Email translation failed and retried ${JSON.stringify(d)}`,
        );
      },
      retries: 3,
      retryDelay: (retryCount) => {
        return retryCount * 10000;
      },
      retryCondition: (error) => {
        // if retry condition is not specified, by default idempotent requests are retried
        return error.response.status >= 400;
      },
    });
  }

  sendToDevelop(message: string, subject?: string) {
    return this.sendEmail({
      subject: subject || '[URGENT] eSIM Issue',
      to: '<EMAIL>',
      message,
    });
  }

  sendToSupport(params: EmailParams) {
    return this.sendEmail(params);
  }

  async sendEmail(params: EmailParams) {
    this.logger.log(`Sending email to ${params.to}`);
    let response;
    try {
      if (isDev()) {
        response = await this.sendToEtheralEmailServer(params);
      } else {
        response = await this.sendToRemoteMailServer(params);
      }
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
    this.logger.log(`Sent email to ${params.to}`);
    return response;
  }

  private sendToRemoteMailServer(params: EmailParams) {
    const bcc = [...(params.bcc || [])];
    if (this.configService.getOrThrow('APP_ENV') === 'production') {
      bcc.push('<EMAIL>');
    }
    const host = isAppEnvDev()
      ? 'http://local-dev-esim-mail.inbound-platform.net'
      : 'http://local-esim-mail.inbound-platform.net';
    return axios.post(
      host +
        (params.origin === 'GLOBAL_ESIM_AIRTRIP'
          ? '/sendmail_airtrip'
          : '/sendmail_gm'),
      {
        bcc,
        subject: params.subject,
        to: params.to,
        message: params.message || params.content,
        type: 1,
        source: 'esim-gmobile',
        lang: params.lang || 'en',
      },
    );
  }

  /**
   * Please check this if you are not sure about it
   * https://ethereal.email/
   */
  private sendToEtheralEmailServer(params: EmailParams) {
    // console.log('assume mail sent to', params.to, params.subject);
    // return;

    this.logger.log(
      `Send to Ethereal Service: ${params.subject} <${params.to}>`,
    );
    return this.transporter.sendMail({
      from: '"Global esim" <<EMAIL>>', // sender address
      to: params.to, // list of receivers
      subject: params.subject, // Subject line
      // text:params., // plain text body
      html: params.message || params.content, // html body
    });
  }

  async translateEmail(payload: any) {
    try {
      const translatedMailResponse = await this.axiosClient.post(
        `http://127.0.0.1:${
          this.configService.getOrThrow('APP_PORT') || 3001
        }/api/v1/payment/esim/mail/translate`,
        {
          ...payload,
        },
      );
      return translatedMailResponse;
    } catch (err) {
      this.logger.error(err);
      throw new Error('Unable to transform email.');
    }
  }
}
