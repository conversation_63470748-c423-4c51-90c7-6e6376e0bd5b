import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';

export class ContactUsDto {
  @IsString()
  @ApiProperty()
  lastName: string;

  @IsString()
  @ApiProperty()
  firstName: string;

  @IsString()
  @ApiProperty()
  emailAddress: string;

  @IsString()
  @IsOptional()
  orderNumber: string;

  @IsString()
  @ApiProperty()
  problemCategory: string;

  @IsString()
  @ApiProperty()
  problem: string;

  @IsString()
  @ApiProperty()
  informationSource: string;

  @IsString()
  @ApiProperty()
  language: string;

  @IsString()
  @IsOptional()
  origin: ServicePlansEnum;
}
