import { HttpModule } from '@nestjs/axios';
import { Lo<PERSON>, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EsimOrdersModule } from 'src/esim-orders/esim-orders.module';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { EsimService } from 'src/esim/esim.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { XchangeService } from 'src/xchange/xchange.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { PaymentService } from 'src/payment/payment.service';
import { PlansService } from 'src/plans/plans.service';
import { PrismaService } from 'src/prisma.service';
import { RateService } from 'src/rate/rate.service';
import { UsersService } from 'src/users/users.service';
import { EmailsController } from './emails.controller';
import { EmailsService } from './emails.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 60000,
        maxRedirects: 5,
        baseURL: configService.get('USIMSA_HOST_NAME'),
      }),
      inject: [ConfigService],
    }),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    EsimOrdersModule,
  ],
  controllers: [EmailsController],
  providers: [
    EmailsService,
    RateService,
    EmailTemplateService,
    RateService,
    EsimOrdersService,
    PrismaService,
    EsimService,
    Logger,
    XchangeService,
    KeyValueStoreService,
    EsimProviderBuilder,
    EsimStocksService,
    UsersService,
    NotificationsService,
    ConfigService,
    PlansService,
    CouponsService,
    JwtService,
    AwsCognitoService,
    PaymentService,
    ReferralsService,
  ],
  exports: [EmailsService],
})
export class EmailsModule { }
