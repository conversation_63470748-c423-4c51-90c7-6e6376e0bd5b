import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { ApiErrorCodes } from 'src/constants/errors';

@Injectable()
export class CustomValidationPipe implements PipeTransform<any> {
  async transform(value: any, metadata: ArgumentMetadata) {
    const { metatype } = metadata;
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }
    const object = plainToClass(metatype, value);
    const errors = await validate(object);
    if (errors.length > 0) {
      const error = errors[0];
      const errorCode = this.mapErrorToCode(error);
      throw new BadRequestException({
        errorCode,
        message: error.constraints[Object.keys(error.constraints)[0]],
      });
    }
    return value;
  }

  private toValidate(metatype: Function): boolean {
    const types = [String, Boolean, Number, Array, Object];
    //@ts-expect-error
    return !types.includes(metatype);
  }

  private mapErrorToCode(error: any): number {
    switch (error.property) {
      case 'email':
        return ApiErrorCodes.ERR_400_INVALID_EMAIL.errorCode;
      case 'password':
        return ApiErrorCodes.ERR_400_INVALID_USERNAME_PASSWORD.errorCode;
      default:
        return ApiErrorCodes.ERR_400_INVALID_REQUEST.errorCode;
    }
  }
}
