import { Injectable } from '@nestjs/common';
import { users } from '@prisma/client';
import { kebabCase } from 'lodash';
import { AppsService } from 'src/apps/apps.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { IUser } from 'src/interface/IUser';
import { PrismaService } from 'src/prisma.service';
import { encodeToBase64 } from 'src/utils';
import { CreateCorporateDto } from './dto/create-corporate.dto';
import { UpdateCorporateDto } from './dto/update-corporate.dto';

@Injectable()
export class CorporatesService {
  constructor(
    private prismaService: PrismaService,
    private appsService: AppsService,
    private awsCognitoService: AwsCognitoService,
  ) { }
  /**
   * Make this in transaction once it hit some scale
   * @param createCorporateDto
   */
  async create(createCorporateDto: CreateCorporateDto) {
    // We create first corporate
    const [corporate, user, apps, corporateUsers, corporateApps] =
      await this.prismaService.$transaction(async (tx) => {
        const corporate = await tx.corporates.create({
          data: {
            code: kebabCase(createCorporateDto.code || createCorporateDto.name),
            emailAddress: createCorporateDto.emailAddress,
            name: createCorporateDto.name,
            contact: createCorporateDto.contact,
            logo: createCorporateDto.logo,
            website: createCorporateDto.website,
          },
        });
        const password = encodeToBase64(Date.now() + '!@!');
        const user = (await this.awsCognitoService.registerUser({
          email: createCorporateDto.emailAddress,
          firstName: createCorporateDto.name,
          lastName: 'Admin',
          password,
          referral: 'internal-corporate',
          locale: "jp",//createCorporateDto.lang,
        })) as users;
        const apps = await this.appsService.create(
          {
            emails: [createCorporateDto.emailAddress],
            name: corporate.code,
            webhooks: [],
          },
          { appId: user.id as number } as IUser,
        );
        const corporateUsers = await tx.corporates_users.create({
          data: {
            corporateId: corporate.id,
            enabled: true,
            usersId: user.id,
          },
        });
        const corporateApps = await tx.corporate_apps.create({
          data: {
            appsId: apps.id,
            corporateId: corporate.id,
            enabled: true,
          },
        });
        return [
          corporate,
          { ...user, password },
          apps,
          corporateUsers,
          corporateApps,
        ];
      });

    return {
      corporateApps,
      corporateUsers,
      corporate,
      user,
      apps,
    };
  }

  addCorporateApp() {
    return this.prismaService.corporate_apps.create({});
  }
  findAll() { }

  findOne(id: number) {
    return `This action returns a #${id} corporate`;
  }

  update(id: number, updateCorporateDto: UpdateCorporateDto) {
    return `This action updates a #${id} corporate`;
  }

  remove(id: number) {
    return `This action removes a #${id} corporate`;
  }
}
