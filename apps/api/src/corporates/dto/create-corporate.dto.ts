import {
  IsEmail,
  IsOptional,
  IsString,
  IsUrl,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateCorporateDto {
  @IsString()
  @MinLength(3)
  name: string;

  @IsString()
  @MinLength(3)
  @IsOptional()
  code: string;

  @IsString()
  @MinLength(3)
  description: string;

  @IsUrl()
  @IsOptional()
  logo: string;

  @IsUrl()
  website: string;

  @IsEmail()
  emailAddress: string;

  @IsString()
  @IsOptional()
  contact?: string;

  @IsString()
  @IsOptional()
  lang?: string;
}
