import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Get,
  Logger,
  Param,
  Post,
  UnauthorizedException,
  UseGuards,
  UsePipes,
  ValidationPipe
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { users } from '@prisma/client';
import { GetUser } from 'src/auth/get-user.decorator';
import { CorporatesService } from './corporates.service';
import { CreateCorporateDto } from './dto/create-corporate.dto';

@Controller('corporates')
export class CorporatesController {
  private logger = new Logger('corporates');
  constructor(private readonly corporatesService: CorporatesService) { }

  private isAuthorizedUserCheck(userId: string) {
    const isAuthorized =
      userId === '87146a78-8021-702b-d6c7-32c5b5e9c2df' ||
      userId === 'c7c44a58-5061-70ab-919b-e5662922e217';
    if (!isAuthorized) {
      throw new UnauthorizedException();
    }
    return true;
  }

  @Post()
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('headerapikey'))
  async create(
    @Body() createCorporateDto: CreateCorporateDto,
    @GetUser() user: users,
  ) {
    try {
      const corporate = await this.corporatesService.create(createCorporateDto);
      return corporate;
    } catch (err) {
      this.logger.error(err);
      if (err.code === 'P2002') {
        throw new ConflictException('Duplicate corporate code.');
      }
      throw new BadRequestException(err.message);
    }
  }

  @Get()
  @UseGuards(AuthGuard())
  findAll(@GetUser() user: users) {
    this.isAuthorizedUserCheck(user.userId);
    return this.corporatesService.findAll();
  }

  @Get(':id')
  @UseGuards(AuthGuard())
  findOne(@Param('id') id: string, @GetUser() user: users) {
    this.isAuthorizedUserCheck(user.userId);
    return this.corporatesService.findOne(+id);
  }
}
