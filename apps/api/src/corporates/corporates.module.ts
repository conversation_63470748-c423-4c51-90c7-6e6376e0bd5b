import { Lo<PERSON>, <PERSON>du<PERSON> } from '@nestjs/common';
import { CorporatesService } from './corporates.service';
import { CorporatesController } from './corporates.controller';
import { PrismaService } from 'src/prisma.service';
import { AppsService } from 'src/apps/apps.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { PaymentService } from 'src/payment/payment.service';
import { UsersService } from 'src/users/users.service';
import { PlansService } from 'src/plans/plans.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { EmailsService } from 'src/emails/emails.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { EsimService } from 'src/esim/esim.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { CouponsService } from 'src/coupons/coupons.service';
import { JwtService } from '@nestjs/jwt';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [],
  controllers: [CorporatesController],
  providers: [
    CorporatesService,
    PrismaService,
    AppsService,
    AwsCognitoService,
    PaymentService,
    UsersService,
    Logger,
    PlansService,
    EsimOrdersService,
    XchangeService,
    EmailsService,
    NotificationsService,
    KeyValueStoreService,
    EsimService,
    EmailTemplateService,
    EsimProviderBuilder,
    CouponsService,
    JwtService,
    EsimStocksService,
    ReferralsService,
  ],
})
export class CorporatesModule {}
