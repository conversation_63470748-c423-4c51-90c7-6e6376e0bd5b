import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Cache } from 'cache-manager';
import FormData from 'form-data';
import { ONE_DAY_IN_MILLISECONDS } from 'src/constants';
import {
  Review,
  ReviewOptions,
  ReviewSummaryAll,
} from './interfaces/review-options.interface';

@Injectable()
export class UkomiReviewService {
  private readonly logger = new Logger(UkomiReviewService.name);
  private static ukomiClient = axios.create({
    baseURL: 'https://api.u-komi.com/',
    headers: { 'Accept-Encoding': 'gzip,deflate,compress' },
  });

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Fetch all reviews for GMobile eSIM (sorted by stars)
   */
  async findAll(options: ReviewOptions) {
    try {
      const { access_token } = await this.getUkomiAccessToken();
      const apiKey = this.configService.get<string>('UKOMI_API_KEY');

      // Build base query parameters
      const queryParams = new URLSearchParams({
        access_token,
        review_type: options?.review_type || 'all',
      });

      // Add sort parameters if provided
      if (options?.sort) queryParams.append('sort', options.sort);
      if (options?.sort_order)
        queryParams.append('sort_order', options.sort_order);

      // Add group_name only if country is provided
      if (options?.country) {
        const normalizedCountry = options.country.toLowerCase().trim();
        const groupName = `gmobile-esim-${normalizedCountry}`;
        queryParams.append('group_name', groupName);
      }

      // Add optional parameters
      if (options?.from_id) queryParams.append('from_id', options.from_id);
      if (options?.customer_id)
        queryParams.append('customer_id', options.customer_id);
      if (options?.from_date)
        queryParams.append('from_date', options.from_date);
      if (options?.updated_date)
        queryParams.append('updated_date', options.updated_date);
      if (options?.count) queryParams.append('count', options.count.toString());
      if (options?.page) queryParams.append('page', options.page.toString());
      if (options?.likes !== undefined)
        queryParams.append('likes', options.likes ? '1' : '0');
      if (options?.custom_questions !== undefined)
        queryParams.append(
          'custom_questions',
          options.custom_questions ? '1' : '0',
        );
      if (options?.question_public !== undefined)
        queryParams.append(
          'question_public',
          options.question_public ? '1' : '0',
        );
      if (options?.question_enable !== undefined)
        queryParams.append(
          'question_enable',
          options.question_enable ? '1' : '0',
        );
      if (options?.deleted !== undefined)
        queryParams.append('deleted', options.deleted ? '1' : '0');
      if (options?.stars) queryParams.append('stars', options.stars.toString());
      if (options?.stars_sorting)
        queryParams.append('stars_sorting', options.stars_sorting);
      if (options?.post_type)
        queryParams.append('post_type', options.post_type);
      if (options?.product_id)
        queryParams.append('product_id', options.product_id);
      if (options?.group !== undefined)
        queryParams.append('group', options.group ? 'true' : 'false');
      if (options?.label) queryParams.append('label', options.label);

      const url = `reviews/${apiKey}/view?${queryParams.toString()}`;

      this.logger.log(
        `Fetching reviews for country: ${options?.country || 'all'}`,
      );
      this.logger.log(
        `Group name: ${
          options?.country
            ? `gmobile-esim-${options.country.toLowerCase().trim()}`
            : 'none (all reviews)'
        }`,
      );
      this.logger.log(`URL: ${url}`);

      const response = await UkomiReviewService.ukomiClient.get(url);
      const data = response.data;

      this.logger.log(`Ukomi response status: ${data.status}`);

      // Check if successful
      if (data.status === 'OK') {
        this.logger.log(
          `Successfully fetched ${data.data?.length || 0} reviews for ${
            options?.country
          }`,
        );
        return data;
      }

      // Handle error responses
      if (data.status === 'error') {
        this.logger.warn(
          `Ukomi API error for ${options?.country || 'all'}: ${data.message}`,
        );
        return {
          status: 'OK',
          code: '200',
          response: {
            review: [],
            message: `No reviews available${
              options?.country ? ` for ${options.country}` : ''
            }`,
            error: data.message,
          },
        };
      }

      return data;
    } catch (error: any) {
      this.logger.error(
        `Error fetching reviews for ${options?.country || 'all'}:`,
        error.response?.data || error.message,
      );

      // Return empty reviews for any error
      return {
        status: 'OK',
        code: '200',
        response: {
          review: [],
          message: `No reviews available${
            options?.country ? ` for ${options.country}` : ''
          }`,
          error: error.response?.data?.message || error.message,
        },
      };
    }
  }

  /**
   * Fetch summary (total and average) for GMobile eSIM reviews
   */
  async getSummary(countryCode: string) {
    if (process.env.APP_ENV !== 'production') {
      return {
        status: 'OK',
        code: '200',
        data: { total_reviews: '0', average_ratings: 0 },
      };
    }

    const cacheKey = `gmobile-esim-review-summary-${countryCode}`;
    try {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) return cached;
    } catch {
      // ignore
    }

    const { access_token } = await this.getUkomiAccessToken();
    const slug = `gmobile-esim-${countryCode}`;
    const url = `products/${process.env.UKOMI_API_KEY}/${slug}/false/review_summary?access_token=${access_token}&sort=stars`;

    const response = await UkomiReviewService.ukomiClient.get(url);
    const data = response.data;

    await this.cacheManager.set(cacheKey, data, 3600 * 24);
    return data;
  }

  /**
   * Fetch all reviews for all countries
   */
  async getAllReviews(): Promise<Review[]> {
    if (!process.env.UKOMI_API_KEY) {
      throw new Error('Ukomi key not properly set up');
    }

    const cacheKey = `gmobile-esim-review-all`;
    const cacheKeyStale = `gmobile-esim-review-all-stale`;

    try {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as Review[];
      }
    } catch {
      // ignore
    }

    try {
      const { access_token } = await this.getUkomiAccessToken();

      const url = `reviews/${process.env.UKOMI_API_KEY}/view?access_token=${access_token}&count=10`;

      const response = await UkomiReviewService.ukomiClient.get(url);

      if (response.data.status === 'OK') {
        const reviewsData = response.data.response.review;

        const filteredReviewsData = reviewsData.map(
          ({ id, title, content, score, created_at, name }) => ({
            id,
            title,
            content,
            score,
            created_at,
            name,
          }),
        );

        this.cacheManager.set(
          cacheKey,
          filteredReviewsData,
          ONE_DAY_IN_MILLISECONDS,
        );

        // do not set expiry for stale cache
        this.cacheManager.set(cacheKeyStale, filteredReviewsData);

        return filteredReviewsData;
      } else {
        throw new Error(response.data.message);
      }
    } catch (error: any) {
      try {
        const staleCache = await this.cacheManager.get(cacheKeyStale);
        this.logger.log(
          'Failed to fetch reviews, returning stale cached data.',
        );
        if (staleCache) return staleCache as Review[];
      } catch {
        // ignore
      }

      this.logger.error('Failed to fetch reviews:', error.message);
      return [];
    }
  }

  /**
   * Fetch all summary for all countries
   */
  async getAllSummary(): Promise<ReviewSummaryAll | null> {
    if (!process.env.UKOMI_API_KEY) {
      throw new Error('Ukomi key not properly set up');
    }

    const cacheKey = `gmobile-esim-review-summary-all`;
    const cacheKeyStale = `gmobile-esim-review-summary-all-stale`;

    try {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as ReviewSummaryAll;
      }
    } catch {
      // ignore
    }

    const url = `reviews/${process.env.UKOMI_API_KEY}/all/review_summary`;

    try {
      const response = await UkomiReviewService.ukomiClient.get(url);

      if (response.data.status === 'OK') {
        const summaryData = response.data.data;

        this.cacheManager.set(cacheKey, summaryData, ONE_DAY_IN_MILLISECONDS);

        // do not set expiry for stale cache
        this.cacheManager.set(cacheKeyStale, summaryData);

        return summaryData;
      } else {
        throw new Error(response.data.message);
      }
    } catch (error: any) {
      try {
        this.logger.log(
          'Failed to fetch summary, returning stale cached data.',
          error.message,
        );

        const staleCache = await this.cacheManager.get(cacheKeyStale);

        if (staleCache) {
          return staleCache as ReviewSummaryAll;
        }
      } catch {
        // ignore
      }

      this.logger.error('Failed to fetch summary:', error.message);

      // Hardcoded values for now
      return {
        total_reviews: 3037,
        average_ratings: 4.73,
      };
    }
  }

  async getAllSummaryWithReview(): Promise<{
    summary: ReviewSummaryAll | null;
    reviews: Review[];
    errors: string[];
  }> {
    try {
      const [summaryResult, reviewsResult] = await Promise.allSettled([
        this.getAllSummary(),
        this.getAllReviews(),
      ]);

      const response = {
        summary: null,
        reviews: [],
        errors: [],
      };

      if (summaryResult.status === 'fulfilled') {
        response.summary = summaryResult.value;
      } else {
        this.logger.error('Failed to fetch summary:', summaryResult.reason);
        response.errors.push('Summary fetch failed');
      }

      if (reviewsResult.status === 'fulfilled') {
        response.reviews = reviewsResult.value;
      } else {
        this.logger.error('Failed to fetch reviews:', reviewsResult.reason);
        response.errors.push('Reviews fetch failed');
      }

      return response;
    } catch (error: any) {
      this.logger.error(
        'Unexpected error in getAllSummaryWithReview:',
        error.message,
      );
      return {
        summary: null,
        reviews: [],
        errors: ['Unexpected error occurred'],
      };
    }
  }

  /**
   * Retrieve an access token from Ukomi
   */
  async getUkomiAccessToken(): Promise<{ access_token: string }> {
    const apiKey = this.configService.get<string>('UKOMI_API_KEY');
    const apiSecret = this.configService.get<string>('UKOMI_API_SECRET');

    if (!apiKey || !apiSecret) {
      throw new Error('Ukomi keys not properly set up');
    }

    const formData = new FormData();
    formData.append('api_key', apiKey);
    formData.append('api_secret', apiSecret);

    const authResponse = await UkomiReviewService.ukomiClient.post(
      'auth/access_token',
      formData,
      {
        headers: {
          ...formData.getHeaders?.(),
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return authResponse.data.data;
  }
}
