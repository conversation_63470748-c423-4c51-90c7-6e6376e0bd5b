import {
  Controller,
  Get,
  Query,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { ReviewQueryDto } from './dto/review-query.dto';
import { UkomiReviewService } from './ukomi-review.service';

@Controller('reviews/gmobile')
@UseInterceptors(SentryInterceptor)
export class UkomiReviewController {
  constructor(private readonly reviewService: UkomiReviewService) {}

  @Get()
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  findAll(@Query() query: ReviewQueryDto) {
    return this.reviewService.findAll(query);
  }

  @Get('info')
  getSummary(@Query('country') countryCode: string) {
    return this.reviewService.getSummary(countryCode);
  }

  @Get('with-all-summary')
  getAllSummaryWithReview() {
    return this.reviewService.getAllSummaryWithReview();
  }
}
