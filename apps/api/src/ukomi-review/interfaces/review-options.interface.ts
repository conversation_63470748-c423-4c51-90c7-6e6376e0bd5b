export interface ReviewOptions {
  country?: string;
  from_id?: string;
  customer_id?: string;
  from_date?: string;
  updated_date?: string;
  count?: number;
  page?: number;
  likes?: boolean;
  custom_questions?: boolean;
  question_public?: boolean;
  question_enable?: boolean;
  deleted?: boolean;
  sort?: string;
  sort_order?: 'asc' | 'desc';
  stars?: number;
  stars_sorting?: 'greater' | 'less' | 'equal';
  post_type?: string;
  review_type?: 'site' | 'product' | 'all';
  product_id?: string;
  group?: boolean;
  label?: string;
}

export interface ReviewSummaryAll {
  total_reviews: number;
  average_ratings: number;
}

export interface Review {
  id: string;
  title: string;
  content: string;
  score: number;
  created_at: string;
  name: string;
}
