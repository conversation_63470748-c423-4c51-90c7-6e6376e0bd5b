import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsDateString,
  IsIn,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class ReviewQueryDto {
  @ApiProperty({
    description: 'Country code (e.g., japan, usa etc)',
    required: true,
    example: 'japan',
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: 'Get review after selected id',
    required: false,
    example: '12345',
  })
  @IsOptional()
  @IsString()
  from_id?: string;

  @ApiProperty({
    description: 'Filter by customer ID',
    required: false,
    example: 'cust123',
  })
  @IsOptional()
  @IsString()
  customer_id?: string;

  @ApiProperty({
    description: 'Get reviews after date (YYYY-MM-DD)',
    required: false,
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  from_date?: string;

  @ApiProperty({
    description: 'Get data updated/created after date (YYYY-MM-DD)',
    required: false,
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  updated_date?: string;

  @ApiProperty({
    description: 'Number of reviews to return',
    required: false,
    example: '10',
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  count?: number;

  @ApiProperty({
    description: 'Page number for pagination',
    required: false,
    example: '1',
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiProperty({
    description: 'Add likes in result (true/false)',
    required: false,
    example: 'true',
  })
  @IsOptional()
  @IsIn(['true', 'false'])
  @Transform(({ value }) => value === 'true')
  likes?: boolean;

  @ApiProperty({
    description: 'Add custom questions in result (true/false)',
    required: false,
    example: 'true',
  })
  @IsOptional()
  @IsIn(['true', 'false'])
  @Transform(({ value }) => value === 'true')
  custom_questions?: boolean;

  @ApiProperty({
    description: 'Get private/public questions (true/false)',
    required: false,
    example: 'true',
  })
  @IsOptional()
  @IsIn(['true', 'false'])
  @Transform(({ value }) => value === 'true')
  question_public?: boolean;

  @ApiProperty({
    description: 'Get disable/enable questions (true/false)',
    required: false,
    example: 'true',
  })
  @IsOptional()
  @IsIn(['true', 'false'])
  @Transform(({ value }) => value === 'true')
  question_enable?: boolean;

  @ApiProperty({
    description: 'Include unpublished reviews (true/false)',
    required: false,
    example: 'false',
  })
  @IsOptional()
  @IsIn(['true', 'false'])
  @Transform(({ value }) => value === 'true')
  deleted?: boolean;

  @ApiProperty({
    description:
      'Sort by: date, stars, like, sentiment, buyer, picture, response',
    required: false,
    example: 'date',
    enum: [
      'date',
      'stars',
      'like',
      'sentiment',
      'buyer',
      'picture',
      'response',
    ],
  })
  @IsOptional()
  @IsIn(['date', 'stars', 'like', 'sentiment', 'buyer', 'picture', 'response'])
  sort?: string;

  @ApiProperty({
    description: 'Sort order: asc or desc',
    required: false,
    example: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sort_order?: 'asc' | 'desc';

  @ApiProperty({
    description: 'Filter by star rating (1-5)',
    required: false,
    example: '5',
    minimum: 1,
    maximum: 5,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsIn([1, 2, 3, 4, 5])
  stars?: number;

  @ApiProperty({
    description: 'Star filtering: greater, less, equal',
    required: false,
    example: 'equal',
    enum: ['greater', 'less', 'equal'],
  })
  @IsOptional()
  @IsIn(['greater', 'less', 'equal'])
  stars_sorting?: 'greater' | 'less' | 'equal';

  @ApiProperty({
    description:
      'Post type: email, import, website, line, inline_form (comma separated)',
    required: false,
    example: 'email,website',
  })
  @IsOptional()
  @IsString()
  post_type?: string;

  @ApiProperty({
    description: 'Review type: site, product, all',
    required: false,
    example: 'product',
    enum: ['site', 'product', 'all'],
  })
  @IsOptional()
  @IsIn(['site', 'product', 'all'])
  review_type?: 'site' | 'product' | 'all';

  @ApiProperty({
    description: 'Filter by product ID',
    required: false,
    example: 'prod123',
  })
  @IsOptional()
  @IsString()
  product_id?: string;

  @ApiProperty({
    description: 'Group status (true/false)',
    required: false,
    example: 'false',
  })
  @IsOptional()
  @IsIn(['true', 'false'])
  @Transform(({ value }) => value === 'true')
  group?: boolean;

  @ApiProperty({
    description: 'Label name (comma separated for multiple)',
    required: false,
    example: 'premium,featured',
  })
  @IsOptional()
  @IsString()
  label?: string;
}
