import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UsePipes,
  ValidationPipe,
  UseGuards,
  ConflictException,
  Logger,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  UseInterceptors,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ER_DUP_ENTRY } from 'src/constants';
import { AppsService } from './apps.service';
import { CreateAppDto } from './dtos/create-app.dto';
import { UpdateAppDto } from './dtos/update-app.dto';
import { users } from '@prisma/client';
import { GetUser } from 'src/auth/get-user.decorator';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { GetApp } from './get-app.decorator';
import { CreateOrderDto } from './dtos/create-order.dto';
import { UsageDto } from 'src/esim/dto/UsageDto';
import { ApiExcludeController, ApiExcludeEndpoint } from '@nestjs/swagger';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { PlansService } from 'src/plans/plans.service';
import {
  getAllConstraintMessages,
  getFormmattedOrderId,
  getPlanDetailsApi,
} from 'src/utils';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { ESIM_PROVIDER } from 'src/esim/providers/EsimProvider';
import { ValidationError } from 'class-validator';
import { HttpErrorByCode } from '@nestjs/common/utils/http-error-by-code.util';
import { ContentConflictError } from 'src/errors/ContentConflict';

@Controller('apps')
@ApiExcludeController()
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class AppsController {
  private logger = new Logger('apps.controller');
  constructor(
    private readonly appsService: AppsService,
    private esimOrderService: EsimOrdersService,
    private plansService: PlansService,
    private esimBuilder: EsimProviderBuilder,
  ) {}

  private isAuthorizedUserCheck(userId: string) {
    const isAuthorized =
      userId.includes('4794cab8-20d1-70e3-8344-27dba904161f') ||
      userId.includes('f7140a18-5041-70a8-02ea-bcf296bfc27f');
    if (!isAuthorized) {
      throw new UnauthorizedException();
    }
    return true;
  }
  @Post()
  @UseGuards(AuthGuard())
  @UsePipes(ValidationPipe)
  async create(@Body() createAppDto: CreateAppDto, @GetUser() user: users) {
    try {
      // Only specified user will be allowed to create apps and its hard coded as we havent implmented ACL yet on our system
      this.isAuthorizedUserCheck(user.userId);
      const data = await this.appsService.create(createAppDto, user);
      if (!data) {
        throw new ConflictException('An app with same name exists already.');
      }
      return data;
    } catch (err) {
      if (err.code === ER_DUP_ENTRY) {
        throw new ConflictException();
      }
      this.logger.error(err.message);
      throw err;
    }
  }

  @Get()
  @UseGuards(AuthGuard())
  async findAll(@GetUser() user: users) {
    this.isAuthorizedUserCheck(user.userId);
    const apps = await this.appsService.findAll(user);
    if (!apps) return [];
    return apps.map(({ key, secret, ...app }) => app);
  }

  @Get(':id')
  @UseGuards(AuthGuard())
  async findOne(@Param('id') id: string, @GetUser() user: users) {
    this.isAuthorizedUserCheck(user.userId);
    const app = await this.appsService.findOne(id);
    if (app.usersId !== user.id) throw new NotFoundException();
    return app;
  }

  @Put(':id')
  @UseGuards(AuthGuard())
  update(
    @Param('id') id: string,
    @Body() updateAppDto: UpdateAppDto,
    @GetUser() user: users,
  ) {
    this.isAuthorizedUserCheck(user.userId);
    return this.appsService.update(+id, updateAppDto);
  }

  @Delete(':id')
  @UseGuards(AuthGuard())
  remove(@Param('id') id: string, @GetUser() user: users) {
    this.isAuthorizedUserCheck(user.userId);
    return this.appsService.remove(id);
  }

  @Post('/order')
  @UseGuards(AuthGuard('headerapikey'))
  @UseGuards(AuthGuard('APIRequestCheckStrategy'))
  @UsePipes(ValidationPipe)
  async order(@Body() payload: CreateOrderDto, @GetApp() app) {
    try {
      this.logger.log(
        `Request recieved from app ${app.name} ${JSON.stringify(payload)}`,
      );
      const plan = await this.plansService.getPlan(+payload.planId);
      if (!plan) {
        throw new BadRequestException('Invalid plan');
      }
      const esim = await this.esimBuilder.build(
        plan.serviceProvider.name as ESIM_PROVIDER,
      );
      const validationErrors = await esim.validatePayload(payload);
      if (validationErrors.length > 0) {
        throw new HttpErrorByCode[HttpStatus.BAD_REQUEST](
          getAllConstraintMessages(validationErrors),
        );
      }
      const order = await this.esimOrderService.createOrderRecord({
        planId: +payload.planId,
        metadata: payload.metadata || {},
        bookingNo: payload.metadata.bookingNo,
        appsId: app.id,
        provisionPrice: plan.provision_price,
        language: payload.language,
      });

      if (order instanceof ContentConflictError) {
        throw new ConflictException(order.message);
      }

      const response = await this.esimOrderService.queueESIMPurchaseRequest({
        appsId: app.id,
        planId: payload.planId,
        lang: payload.language,
        chargeAmount: payload.chargeAmount,
        metadata: payload.metadata || {},
        orderId: order.id,
        planUrl: getPlanDetailsApi(payload.planId),
      });
      this.logger.log(`Added to queue as ${response.id}`);
      const transactionKey = AppsService.getTransactionKey(
        payload.metadata.bookingNo + '',
        order.id + '',
      );

      return {
        orderId: getFormmattedOrderId(order.id),
        transactionKey,
      };
    } catch (err) {
      if (err instanceof ContentConflictError) {
        throw new ConflictException(err.message);
      }
      this.logger.log(err);
      throw err;
    }
  }

  @Post('/order/usage')
  @UseGuards(AuthGuard('headerapikey'))
  @UseGuards(AuthGuard('APIRequestCheckStrategy'))
  @UsePipes(ValidationPipe)
  async orderUsage(@Body() payload: UsageDto, @GetApp() app) {
    try {
      this.logger.log(`Getting usage records from ${JSON.stringify(payload)}`);

      return await this.esimOrderService.orderUsage(payload);
    } catch (err) {
      this.logger.log(err?.response || err);
      if (err?.response?.data) {
        return err.response.data;
      }
      throw new BadRequestException(err.message);
    }
  }
}
