import { IsInt, <PERSON><PERSON><PERSON>ber, IsOptional, IsString } from 'class-validator';
import { IOrderMetadata } from 'src/esim-orders/interfaces/IOrderMetadata';
import { LGUSubscribeOrderDTO } from 'src/esim/providers/LGUProvider/LGUSubscribeDto';

export class CreateOrderDto {
  @IsString()
  planId: string;

  @IsString()
  @IsOptional()
  language?: string;

  @IsInt()
  @IsOptional()
  chargeAmount: string;

  metadata?: IOrderMetadata;
}
