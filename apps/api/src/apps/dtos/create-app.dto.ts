import {
  Is<PERSON>rray,
  IsEmail,
  IsOptional,
  IsString,
  <PERSON><PERSON>rl,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateAppDto {
  @IsString()
  @MaxLength(50)
  @MinLength(8)
  name: string;

  @IsArray()
  @IsUrl({}, { each: true, message: 'Invalid URL format' })
  @IsOptional()
  webhooks: string[];

  @IsArray()
  @IsEmail({}, { each: true, message: 'Invalid email format' })
  @IsOptional()
  emails: string[];
}
