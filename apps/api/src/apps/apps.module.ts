import { Lo<PERSON>, Module } from '@nestjs/common';
import { AppsService } from './apps.service';
import { AppsController } from './apps.controller';
import { PrismaService } from 'src/prisma.service';
import { PassportModule } from '@nestjs/passport';
import { APIKeyStrategy } from '../auth/api-key.strategy';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { EsimService } from 'src/esim/esim.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { XchangeModule } from 'src/xchange/xchange.module';
import { XchangeService } from 'src/xchange/xchange.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { BullModule } from '@nestjs/bullmq';
import {
  QUEUE_EMAIL_DISPATCHER,
  QUEUE_NEW_ESIM,
  QUEUE_ESIM_CREATE_BUY,
  QUEUE_WEBHOOK_EXECUTOR,
} from 'src/constants';
import redisConnection from 'config/redis-connection';
import { PlansService } from 'src/plans/plans.service';
import { CacheModule } from '@nestjs/cache-manager';
import { CouponsService } from 'src/coupons/coupons.service';
import { EsimPurchaseQueueWorker } from 'src/processors/esim-create-buy.worker';
import { EmailsService } from 'src/emails/emails.service';
import { JwtService } from '@nestjs/jwt';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { PaymentService } from 'src/payment/payment.service';
import { UsersService } from 'src/users/users.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 5000,
        maxRedirects: 5,
        baseURL: configService.get('USIMSA_HOST_NAME'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    AppsService,
    PrismaService,
    APIKeyStrategy,
    KeyValueStoreService,
    EsimOrdersService,
    CouponsService,
    EsimService,
    Logger,
    XchangeService,
    EsimProviderBuilder,
    PlansService,
    JwtService,
    EsimStocksService,
    AwsCognitoService,
    PaymentService,
    UsersService,
    EmailsService,
    ReferralsService,
  ],
  controllers: [AppsController],
})
export class AppsModule {}
