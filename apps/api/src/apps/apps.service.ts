import { Injectable, NotFoundException } from '@nestjs/common';
import { v4 as uuid, v5 as uuidv5 } from 'uuid';
import { CreateAppDto } from './dtos/create-app.dto';
import { UpdateAppDto } from './dtos/update-app.dto';
import { PrismaService } from 'src/prisma.service';
import { users } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4, v1 as uuidv1 } from 'uuid';
import { IUser } from 'src/interface/IUser';
import { SHA1, SHA512 } from 'crypto-js';
import { ConfigService } from '@nestjs/config';

export const apiIdWrapper = (apiId: string) => `API_ID_${apiId}`;

const PKCE_CODE_VERIFIER = process.env['PKCE_CODE_VERIFIER'];
@Injectable()
export class AppsService {
  constructor(private prismaService: PrismaService) {}

  static getTransactionKey(bookingNo: string, orderId: string) {
    return SHA1(bookingNo + orderId + PKCE_CODE_VERIFIER).toString();
  }

  /**
   * Will create an app with key and secret where
   * audiences and notification can kept
   * @param createAppDto
   * @param user
   */
  async create(createAppDto: CreateAppDto, user: IUser) {
    const oldApp = await this.prismaService.apps.findFirst({
      where: {
        userId: {
          id: +user.appId,
        },
        name: createAppDto.name,
      },
    });
    if (oldApp) {
      return null;
    }
    const apiKey = uuidv4();
    const apiId = uuidv1();
    const signatureKey = uuidv4();

    const salt = await bcrypt.genSalt();
    const hashedApiKey = await bcrypt.hash(apiKey, salt);

    const app = await this.prismaService.apps.create({
      data: {
        emails: createAppDto.emails,
        signatureKey: signatureKey,
        webhooks: createAppDto.webhooks,
        key: apiIdWrapper(apiId),
        secret: hashedApiKey,
        isActive: true,
        name: createAppDto.name,
        activateDate: new Date(),
        usersId: +user.appId,
      },
    });

    return {
      id: app.id,
      apiId,
      apiKey,
      signatureKey,
      name: app.name,
    };
  }

  async findAll(user: users) {
    return this.prismaService.apps.findMany({
      where: {
        usersId: user.id,
      },
    });
  }

  async findOne(id: string) {
    return this.prismaService.apps.findFirstOrThrow({
      where: {
        key: id,
      },
    });
  }

  update(id: number, updateAppDto: UpdateAppDto) {}

  async remove(id: string) {
    // const app = await this.appRepository.findOne({ where: { key: id } });
    // if (!app) throw new NotFoundException();
    // return await this.connection.manager.remove(app);
  }
}
