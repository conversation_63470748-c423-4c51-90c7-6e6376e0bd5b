import { BullModule } from '@nestjs/bullmq';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import redisConnection from 'config/redis-connection';
import {
  BULK_ESIM_PURCHASE,
  INCOMPLETE_ORDERS_PROCESS,
  QUEUE_EMAIL_DISPATCHER,
  QUEUE_ESIM_CREATE_BUY,
  QUEUE_LOYALTY_REMINDER,
  QUEUE_NEW_ESIM,
  QUEUE_WEBHOOK_EXECUTOR,
  SEND_NOTIFICATION,
  USAGE_QUEUE,
} from 'src/constants';

@Global()
@Module({
  imports: [
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: BULK_ESIM_PURCHASE,
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: BULK_ESIM_PURCHASE,
        };
      },
    }),
    BullModule.registerQueueAsync({
      name: USAGE_QUEUE,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: USAGE_QUEUE,
        };
      },
    }),
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: INCOMPLETE_ORDERS_PROCESS,
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: INCOMPLETE_ORDERS_PROCESS,
        };
      },
    }),
    BullModule.registerQueueAsync({
      name: SEND_NOTIFICATION,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: SEND_NOTIFICATION,
        };
      },
    }),

    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_WEBHOOK_EXECUTOR,
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: QUEUE_WEBHOOK_EXECUTOR,
        };
      },
    }),
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_EMAIL_DISPATCHER,
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: QUEUE_EMAIL_DISPATCHER,
        };
      },
    }),
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_ESIM_CREATE_BUY,
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: QUEUE_ESIM_CREATE_BUY,
        };
      },
    }),
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_NEW_ESIM,

      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: QUEUE_NEW_ESIM,
        };
      },
    }),
  ],
  exports: [BullModule],
})
export class QueueModule {}
