import { Injectable } from '@nestjs/common';
import { countries, orders, plans } from '@prisma/client';
import { renderToStream } from '@react-pdf/renderer';
import axios from 'axios';
import { format } from 'date-fns';
import { startCase } from 'lodash';
import { createElement } from 'react';
import { PlansService } from 'src/plans/plans.service';
import * as countryList from './countries';

type EsimPdfPayload = orders & { plan: plans & { country: countries } };

import esimSample from './payload-sample';
import EsimTemplate from './templates/esim';
import EsimTemplateJP from './templates/esim-jp';
@Injectable()
export class PdfService {
  constructor(private readonly plansService: PlansService) {}

  async generateEsimPdf(payload: EsimPdfPayload) {
    const response = await axios.get(payload.qrCodeImgUrl, {
      responseType: 'arraybuffer',
    });

    const imageBuffer = Buffer.from(response.data, 'binary');
    const qrCode = imageBuffer.toString('base64');
    const plan = payload.plan;
    const esimPlan =
      payload.plan.packageType === 'FIXED_DAY'
        ? `Fixed Plan (${payload.plan.dataId})`
        : 'Unlimited Plan';

    const isSpecialAPNPlan = await this.plansService.checkIsSpecialAPNPlan(
      plan,
    );

    const esimPlanJP =
      plan.packageType === 'FIXED_DAY'
        ? `データプラン (${payload.plan.dataId})`
        : '無制限プラン';
    const esimPdfPayload: typeof esimSample = {
      companyLogo: '',
      isSpecialAPNPlan,
      countryCode: payload.plan.country?.code,
      esimInfo: {
        'Country / Region': `${
          payload.lang === 'jp' &&
          payload.plan.country?.name?.toLowerCase() in countryList
            ? countryList[payload.plan.country?.name?.toLowerCase()] + ' / '
            : ''
        }${startCase(payload.plan.country?.name)} (${
          payload.plan.country?.code
        })`,
        'eSIM Plan': payload.lang === 'jp' ? esimPlanJP : esimPlan,
        'Usage Days':
          payload.lang === 'jp'
            ? `${payload.plan.validityDays}日`
            : payload.plan.validityDays + ' day(s)',

        'Activation Code': payload.activateCode,
        'Download Link': payload.downloadLink,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-expect-error
        'SM-DP+ Address': payload.response.products[0].smdp,
        ICCID: payload.iccid,
        'QR code': ``,
      },
      orderDate: format(payload.orderCreatedAt, 'yyyy/M/d'),
      orderNumber: payload.orderId,
      qrCodeValue: payload.qrCodeImgUrl,
      plan: payload.plan,
      service:
        payload.lang === 'jp'
          ? payload.source.includes('airtrip')
            ? 'airtrip'
            : 'jp'
          : 'english',
    };
    delete esimPdfPayload.esimInfo['QR code'];
    if (
      payload.plan.country?.name?.toLowerCase() === 'korea' &&
      payload.iccid !== payload.topupId
    ) {
      esimPdfPayload.esimInfo[
        payload.lang === 'jp' ? '電話番号' : 'Phone number'
      ] =
        payload.lang === 'jp'
          ? `${payload.topupId} ※韓国到着後に付与`
          : `${payload.topupId} ※(Activates after arrival)`;
    }
    esimPdfPayload.esimInfo['QR code'] = `data:image/png;base64,${qrCode}`;

    const MyDocument = () =>
      createElement(
        payload.lang === 'jp' ? EsimTemplateJP : EsimTemplate,
        esimPdfPayload,
      );
    const pdfStream = await renderToStream(createElement(MyDocument));
    return pdfStream;
  }
}
