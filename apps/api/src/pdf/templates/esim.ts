import * as React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  Image,
  StyleSheet,
  Link,
  Font,
} from '@react-pdf/renderer';
import { startCase } from 'lodash';

Font.register({
  family: 'Noto Sans',
  src: 'http://fonts.gstatic.com/s/notosans/v6/LeFlHvsZjXu2c3ZRgBq9nKCWcynf_cDxXwCLxiixG1c.ttf', // URL for Noto Sans .ttf file
  fontWeight: 'normal', // Explicitly set the weight
});

// Register Noto Serif Font (with explicit fontWeight)
Font.register({
  family: 'Noto Serif',
  src: 'http://fonts.gstatic.com/s/notoserif/v4/eCpfeMZI7q4jLksXVRWPQy3USBnSvpkopQaUR-2r7iU.ttf', // URL for Noto Serif .ttf file
  fontWeight: 'normal', // Explicitly set the weight
});

// Styles for the PDF document
const styles = StyleSheet.create({
  page: {
    backgroundColor: 'white',
    fontFamily: 'Helvetica',
    fontSize: 12,
    padding: 30,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  logo: {
    width: 200,
    height: 52,
  },
  title: {
    textAlign: 'center',
    marginBottom: 20,
  },
  infoContainer: {
    display: 'flex',
    flexDirection: 'column',
    // width: 400,
    // backgroundColor: "red",
    padding: 10,
    border: '1px solid grey',
    borderRadius: '5px',
  },
  infoCell: {
    display: 'flex',
    flexDirection: 'row',
    padding: 5,
    borderBottom: '1px solid grey',
  },
  infoCellNoBorder: {
    display: 'flex',
    flexDirection: 'row',
    padding: 5,
  },
  infoCellHeader: {
    width: '20%',
    borderRight: '1px solid grey',
  },
  infoCellContainer: {
    width: '80%',
    marginLeft: '10px',
    textAlign: 'justify',
    fontSize: '10px',
  },
});

// Main PDF Document Component
const MyDocument = (props: any) => {
  const keys = Object.keys(props.esimInfo);
  return /*#__PURE__*/ React.createElement(
    Document,
    {
      style: {
        backgroundColor: 'white',
      },
    },
    /*#__PURE__*/ React.createElement(
      Page,
      {
        size: 'A4',
        style: styles.page,
      },
      /*#__PURE__*/ React.createElement(
        View,
        {
          style: styles.header,
        },
        /*#__PURE__*/ React.createElement(Image, {
          src: 'data:image/png;base64,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',
          style: styles.logo,
        }),
        /*#__PURE__*/ React.createElement('br', null),
        /*#__PURE__*/ React.createElement(
          View,
          {
            style: {
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-end',
              alignItems: 'flex-end',
            },
          },
          /*#__PURE__*/ React.createElement(
            Text,
            null,
            'Order Date: ',
            props.orderDate,
          ),
          /*#__PURE__*/ React.createElement(
            Text,
            null,
            'Order Number: ',
            props.orderNumber,
          ),
        ),
      ),
      /*#__PURE__*/ React.createElement(
        View,
        {
          style: {
            width: '100%',
          },
        },
        /*#__PURE__*/ React.createElement(
          View,
          {
            style: styles.infoContainer,
          },
          keys.map((key, index) => {
            return /*#__PURE__*/ React.createElement(
              View,
              {
                key: index,
                style:
                  index === keys.length - 1
                    ? styles.infoCellNoBorder
                    : styles.infoCell,
              },
              /*#__PURE__*/ React.createElement(
                Text,
                {
                  style: styles.infoCellHeader,
                },
                key,
              ),
              key === 'QR code'
                ? /*#__PURE__*/ React.createElement(Image, {
                    src: {
                      uri: props.esimInfo[key],
                      method: 'GET',
                      headers: {
                        'Cache-Control': 'no-cache',
                      },
                      body: '',
                    },
                    style: {
                      width: '250px',
                      height: '250px',
                    },
                  })
                : /*#__PURE__*/ React.createElement(
                    Text,
                    {
                      style: styles.infoCellContainer,
                    },
                    props.esimInfo[key],
                  ),
            );
          }),
        ),
      ),
      /*#__PURE__*/ React.createElement(
        View,
        {
          style: {
            marginTop: '10px',
            display: 'flex',
            flexDirection: 'column',
            gap: 5,
            fontSize: '10px',
            fontStyle: 'italic',
          },
        },
        /*#__PURE__*/ React.createElement(
          Text,
          null,
          `* This eSIM can only be used ${
            startCase(props.plan.country?.name) === 'worldwide'
              ? ''
              : 'in ' + startCase(props.plan.country?.name)
          }. `,
        ),
        /*#__PURE__*/ React.createElement(
          Text,
          null,
          '* This eSIM is designed specifically for data communications and does not support SMS or voice calls.',
        ),
        /*#__PURE__*/ React.createElement(
          Text,
          null,
          '* For 4G plans, unless you added the insurance option, once an order has been placed, it CANNOT be cancelled.',
        ),
        /*#__PURE__*/ React.createElement(
          Text,
          null,
          '* For 5G plans, orders CANNOT be cancelled under any circumstances.',
        ),
        /*#__PURE__*/ React.createElement(
          Text,
          null,
          '* Please activate your eSIM within 30 days or it will expire.',
        ),
        /*#__PURE__*/ React.createElement(
          Text,
          null,
          props.isKDDI
            ? `* There are no daily data caps on KDDI's Unlimited plan. However, in cases of heavy data usage or prolonged connections that could impact other users, KDDI may limit connection speeds to ensure uninterrupted general smartphone usage for all.
`
            : `* Unlimited plans have a daily Fair Usage Policy (FUP) limit. Once this threshold is reached, the speed may be reduced`,
        ),
        /*#__PURE__*/ React.createElement(
          View,
          null,
          /*#__PURE__*/ React.createElement(
            Text,
            null,
            '* Please refer to the eSIM setup guide.',
          ),
          /*#__PURE__*/ React.createElement(
            View,
            {
              style: {
                marginLeft: '5px',
              },
            },
            /*#__PURE__*/ React.createElement(
              Link,
              {
                src: props.isKDDI
                  ? 'https://bit.ly/manual-5g-kddi-esim-ios'
                  : props.isSpecialAPNPlan
                  ? 'https://bit.ly/esim-manual-ios-jp-esim'
                  : 'https://bit.ly/esim-manual-ios',
              },
              `- iOS Setup Guide ${
                props.isKDDI
                  ? 'https://bit.ly/manual-5g-kddi-esim-ios'
                  : props.isSpecialAPNPlan
                  ? 'https://bit.ly/esim-manual-ios-jp-esim'
                  : 'https://bit.ly/esim-manual-ios'
              }`,
            ),
            //
            /*#__PURE__*/ React.createElement(
              Link,
              {
                src: props.isKDDI
                  ? 'https://bit.ly/manual-5g-kddi-esim'
                  : props.isSpecialAPNPlan
                  ? 'https://bit.ly/esim-manual-android-jp-esim'
                  : 'https://esim.gmobile.biz/setup?os=ios',
              },
              `- Android Setup Guide ${
                props.isKDDI
                  ? 'https://bit.ly/manual-5g-kddi-esim'
                  : props.isSpecialAPNPlan
                  ? 'https://bit.ly/esim-manual-android-jp-esim'
                  : 'https://esim.gmobile.biz/setup?os=android'
              }`,
            ),
            /*#__PURE__*/ React.createElement(
              Text,
              {
                style: {
                  fontSize: '10px',
                },
              },
              "If it's still not working, please don't hesitate to contact us at: <EMAIL>",
            ),
          ),
        ),
      ),
    ),
  );
};

// Sample Usage

export default MyDocument;
