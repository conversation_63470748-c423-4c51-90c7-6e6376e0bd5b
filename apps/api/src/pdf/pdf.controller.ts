import { Controller, Get, Res } from '@nestjs/common';
import { PdfService } from './pdf.service';
import { Response } from 'express';

@Controller('pdfs')
export class PdfController {
  constructor(private readonly pdfService: PdfService) {}

  @Get('/generate')
  async getPdf(@Res() res: Response) {
    res.send(200);
    // const pdfStream = await this.pdfService.generatePdf();
    // res.set({
    //   'Content-Type': 'application/pdf',
    //   'Content-Disposition': 'attachment; filename="document.pdf"',
    // });
    // pdfStream.pipe(res);
  }
}
