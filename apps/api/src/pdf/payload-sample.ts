const esimSample = {
  orderDate: '2012/12/12',
  orderNumber: 'E1234',
  companyLogo: '',
  countryCode: '',
  qrCodeValue: '',
  isSpecialAPNPlan: true,
  esimInfo: {
    'Country / Region': 'string',
    'eSIM Plan': 'Unlimited Plan',
    'Usage Days': '2 days',
    'Activation Code': '6B1E767C886A2C8F46EAC648A3FED5F4',
    'SM-DP+ Address': 'sm-v4-081-a-gtm.pr.go-esim.com',
    ICCID: 'your random iccid',
    'Download Link':
      'LPA:1$sm-v4-081-a-gtm.pr.go-esim.com$6B1E767C886A2C8F46EAC648A3FED5F4',
    'QR code':
      'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAV4AAAFeAQMAAAD35eVZAAAABlBMVEX///8AAABVwtN+AAAACXBIWXMAAA7EAAAOxAGVKw4bAAAB9ElEQVRoge2Zy63rMAxECbgAl6TWXZIKMMD7zL8DvP0YGC6URDrKZsSfJEL7ui0NEznt8xbVLaKX2HDk+iYMCm8T8tAQ1Of+IQbr/lkgDAg/gj5zJrzrmwsxPIeB8AfgHVyIfN5C+Hvw9cTdGO7wZMLgsNTcPfZqpM//xGfCOLCGzRj7GsIIg8Jt02Fvn/pFCAPCJahlSeP6HMxVwqCwtYyXbbMY+wxWvV612nGXMCDsqrbcOefb6hx00iQMCLubuq92sXOWT+vrXwhjwdIdxp4Xb+Mc+LAJY8LWcES+rNwo1f7nYSAMC4tkuaop9yh2VsVdwqjwen2LHalv3sjlKSAMCfe1Tb09Fecl7GhHCMPB1TJOOyNVTq8lDAqvKlJXhVxJfbNvPNKnCWPC8fRbBZB981jsYbjvwgnDwd4jvhpF09d9tZC+zyEMBsd7hcjrHdHQ6irfIZcwGixZs2qomsWOavpvHQbCiPDh0sacFUCeQ49xsdpngzAgLH0F19tWZk4N1yWMCbctjTonNG+RbcMmjAmHoFX71NXpTKnVehAGhF3zmOv3qPDfRLJwJQwI253bVD/zpU4j/AHY56LXaNeNMEwYH95x5+Y7OgL72yJhWFjyp7cZ0YmEZevxmzQJ48AZUzNpZshdWdaG+kIYE6Z92/4A14grnP/cKG0AAAAASUVORK5CYII=',
  },
  service: 'string',
  plan: {},
};
export default esimSample;
