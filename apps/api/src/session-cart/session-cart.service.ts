import { Injectable } from '@nestjs/common';
import { CreateSessionCartDto } from './dto/create-session-cart.dto';
import { PrismaService } from 'src/prisma.service';
import { CART_STATUS } from '@prisma/client';

@Injectable()
export class SessionCartService {

  constructor(
    private prismaService: PrismaService,
  ) { }

  create(createSessionCartDto: CreateSessionCartDto) {
    return this.prismaService.session_carts.upsert({
      create: {
        status: CART_STATUS.PENDING,
        planId: createSessionCartDto.planId,
        userId: createSessionCartDto.userId,
        sessionId: createSessionCartDto.sessionId,
      },
      update: {
        sessionId: createSessionCartDto.sessionId,
        status: CART_STATUS.PENDING,
        planId: createSessionCartDto.planId,
        userId: createSessionCartDto.userId
      },
      where: {
        //@ts-ignore
        sessionId: createSessionCartDto.sessionId,
      }
    })
  }

  updateSessionCartStatus(sessionId: string, status: CART_STATUS) {
    return this.prismaService.session_carts.update({
      where: {
        sessionId
      },
      data: {
        status
      }
    })
  }



}
