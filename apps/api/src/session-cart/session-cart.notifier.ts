import { NotificationsService } from "src/notifications/notifications.service";
import { Session<PERSON><PERSON><PERSON>ron } from "./session-cart.cron";
import { SessionCartProcessor } from "./session-cart.processor";
import { Injectable, } from "@nestjs/common";
import { NotificationNotifier } from '../notifications/NotificationNotifier';

@Injectable()
export class SessionCartNotifier extends NotificationNotifier {
    constructor(
        private sessionCron: SessionCartCron,
        private sessionProccessor: SessionCartProcessor,
    ) {
        super();
    }

    enableProcess() {
        this.sessionCron.enable();
        this.sessionProccessor.enable()
    }
    register(notifier: NotificationsService): Promise<void> {
        try {
            this.enableProcess()
        } catch (err) {
            console.error(err)
        }
        return
    }

}