import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job, Queue, Worker } from 'bullmq';
import { INCOMPLETE_ORDERS_PROCESS } from 'src/constants';
import { IIncompleteSessionOrder } from './interfaces/IIncompleteSessionOrder';
import { NotificationsService } from 'src/notifications/notifications.service';
import { NotificationChannelEnum } from 'src/notifications/NotificationChannelEnum';
import {
  capitalizeFirstLetter,
  getCountryFlagImage,
  getProductPlanImage,
} from 'src/utils';
import redisConnection from 'config/redis-connection';

@Injectable()
export class SessionCartProcessor extends WorkerHost {
  private enabled: boolean;
  private readonly logger = new Logger(SessionCartProcessor.name);

  constructor(protected notifcationService: NotificationsService) {
    super();
  }

  async process(job: Job<IIncompleteSessionOrder, any, string>): Promise<any> {
    if (!this.enabled) return;

    const { plan, user } = job.data;

    if (!this.notifcationService.isAllowedToThisUser(user.id)) {
      return null;
    }

    const pushNotification = {
      title: `eSIM ${capitalizeFirstLetter(plan.country.name)}`,
      link: process.env['FRONTEND_HOST'] + '/region/' + plan.country.name,
      image: getProductPlanImage(plan.id),
      value: `Your esim plan for ${plan.country.name} is waiting you!`,
    };
    const audience = await this.notifcationService.getAudiencesIdOfUser(
      user.id,
    );
    await this.notifcationService.createNotificationQueue({
      audiences: audience.map((item) => item.audienceId),
      messages: [
        {
          channel: NotificationChannelEnum.EMAIL,
          value: JSON.stringify({
            title: 'You left something in your cart',
            value: {
              language: 'en',
              templateName: 'incomplete-order',
              data: {
                ...job.data,
                frontendHost: process.env['FRONTEND_HOST'],
              },
            },
          }),
        },
        // {
        //     channel: NotificationChannelEnum.PUSH_NOTIFICATION,
        //     //@ts-expect-error
        //     value: pushNotification
        // }
      ],
      type: 'URGENT',
    });
    return;
  }

  enable() {
    this.enabled = true;
    const worker = new Worker(
      INCOMPLETE_ORDERS_PROCESS,
      async (job) => {
        try {
          const response = await this.process(job);
          return response;
        } catch (err) {
          this.logger.log(err);
        }
      },
      {
        connection: redisConnection().connection,
        // limiter: {
        // max: 1,
        // duration: 36000
        // }
      },
    );

    worker.on('error', async (error) => {
      this.logger.log(error);
    });

    worker.on('completed', async (job) => {
      console.log(`Jobs completed, ${job.id}`);
    });
  }
}
