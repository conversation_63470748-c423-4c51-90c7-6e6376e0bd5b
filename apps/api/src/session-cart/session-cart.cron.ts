import { InjectQueue } from '@nestjs/bullmq';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Queue } from 'bullmq';
import { Cache } from 'cache-manager';
import { uniqBy } from 'lodash';
import { INCOMPLETE_ORDERS_PROCESS } from 'src/constants';
import { PrismaService } from 'src/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

@Injectable()
export class SessionCartCron {
  private logger: Logger = new Logger(SessionCartCron.name);
  private enabled: boolean;

  constructor(
    private prismaService: PrismaService,
    @InjectQueue(INCOMPLETE_ORDERS_PROCESS)
    private incompleteOrderQueue: Queue,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
  ) {}

  @Cron(CronExpression.EVERY_HOUR) // Adjust the cron expression as needed
  async checkIncompleteOrderSessions() {
    try {
      if (!this.enabled) return;

      //@ts-ignore
      const timeInterval = new Date(
        Date.now() -
          this.configService.get(
            //@ts-expect-error
            'notification.staleOrdersTimeInMinutes' || 55,
          ) *
            60 *
            1000,
      ); // Adjusted to 55 minutes
      const orders = await this.prismaService.session_carts.findMany({
        where: {
          createdAt: {
            lte: timeInterval,
          },
          status: 'PENDING',
        },
        include: {
          plan: {
            include: {
              country: true,
            },
          },
          user: true,
        },
        // distinct: ["userId"],
        orderBy: {
          createdAt: 'desc',
        },
      });

      const distinctOrders = uniqBy(orders, 'userId');
      // orders[0].userId
      await this.prismaService.session_carts.updateMany({
        data: {
          status: 'PICKED_FOR_FOLLOW_UP',
        },
        where: {
          id: {
            in: orders.map((item) => item.id),
          },
        },
      });
      distinctOrders.forEach((item) => {
        this.incompleteOrderQueue.add('incomplete-order:' + item.id, item, {
          jobId: item.id + '-' + item.plan.id + '-' + item.userId,
        });
      });
    } catch (err) {
      this.logger.log(err.message, err);
    }
  }

  enable() {
    this.enabled = true;
  }
}
