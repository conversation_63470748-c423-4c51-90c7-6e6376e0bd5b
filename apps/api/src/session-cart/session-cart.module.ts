import { Lo<PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { SessionCartService } from './session-cart.service';
import { PrismaService } from 'src/prisma.service';
import { BullModule } from '@nestjs/bullmq';
import { INCOMPLETE_ORDERS_PROCESS, SEND_NOTIFICATION } from 'src/constants';
import redisConnection from 'config/redis-connection';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SessionCartNotifier } from './session-cart.notifier';
import { SessionCartCron } from './session-cart.cron';
import { SessionCartProcessor } from './session-cart.processor';

@Module({
  imports: [],
  providers: [
    SessionCartService,
    PrismaService,
    Logger,
    EmailTemplateService,
    NotificationsService,
    SessionCartNotifier,
    SessionCartCron,
    SessionCartProcessor,
  ],
})
export class SessionCartModule {}
