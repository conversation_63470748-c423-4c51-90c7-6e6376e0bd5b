import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Inject,
  Logger,
  NotFoundException,
  Param,
  ParseFilePipe,
  Post,
  Query,
  Res,
  UnauthorizedException,
  UnprocessableEntityException,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiExcludeController, ApiExcludeEndpoint } from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { Cache } from 'cache-manager';
import { Response } from 'express';
import { Parser } from 'json2csv';
import { GetApp } from 'src/apps/get-app.decorator';
import { AN_HOUR_IN_MILLISECONDS } from 'src/constants';
import { EsimOrderDto } from 'src/esim-orders/dtos/esim-order-dto';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { AdminService } from './admin.service';

@Controller('admin')
@ApiExcludeController()
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class AdminController {
  constructor(
    private esimOrderService: EsimOrdersService,
    private logger: Logger,
    private adminService: AdminService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}
  @Get('/esim-orders/export')
  @ApiExcludeEndpoint()
  @UseGuards(AuthGuard('headerapikey'))
  public async export(
    @Res() res: Response,
    @GetApp() app: any,
    @Query()
    query: {
      type: string;
      startDate?: string;
      endDate?: string;
      limit?: string;
      page?: string;
    },
  ) {
    this.checkIfJWAdmin(app.name);
    const page = query.page ? parseInt(query.page.toString(), 10) : 1;
    const pageSize = query.limit ? parseInt(query.limit.toString(), 10) : 10;

    const offset = (page - 1) * pageSize;
    const result = await this.esimOrderService.export({
      filter: {
        startDate: query.startDate,
        endDate: query.endDate,
        limit: pageSize, // limit and offset as numbers for pagination
        offset: offset,
      },
      usageLinkHost: 'https://esim.gmobile.biz/api/v1/esim-orders/',
    });
    if (query.startDate && query.endDate && query.type === 'csv') {
      try {
        // Convert JSON to CSV
        const json2csvParser = new Parser();
        const csv = json2csvParser.parse(result || []);

        // Set headers for CSV download
        res.header('Content-Type', 'text/csv');
        res.header(
          'Content-Disposition',
          `attachment; filename="esim-orders-${Date.now()}-${query.startDate}-${
            query.endDate
          }.csv"`,
        );
        this.logger.log(
          `CSV Downloaded esim-orders-${Date.now()}-${query.startDate}-${
            query.endDate
          }.csv"`,
        );
        return res.send(csv);
      } catch (error) {
        // Handle any error that may occur during JSON to CSV conversion
        this.logger.error(error);
        return res.status(500).json({ error: 'Failed to generate CSV' });
      }
    }
    return res.json(result);
  }

  @Get('/esim-orders/:orderId')
  @ApiExcludeEndpoint()
  @UseGuards(AuthGuard('headerapikey'))
  public async details(
    @Query() query: { type: 'usage' },
    @GetApp() app: any,
    @Param() params: { orderId: string },
  ) {
    this.checkIfJWAdmin(app.name);
    try {
      const usageOrder = this.esimOrderService.orderDetailAndUsage(
        params.orderId,
        query,
      );
      return usageOrder;
    } catch (err) {
      throw new NotFoundException(err.message);
    }
  }

  @Post('/external-service/slack')
  @ApiExcludeEndpoint()
  @Throttle(50, 60000)
  async handleExternal(@Body() body: { text: string }) {
    try {
      //@ts-expect-error
      if (body.api_app_id !== 'A07SX5RLVPC' && body.team_id !== 'T0600FL8E0Z')
        return 'Unauthorized';
      const cachedResult = await this.cacheManager.get(body.text);
      if (cachedResult) {
        this.logger.log(`Serving from cache ${body.text}`);
        return cachedResult;
      }

      this.logger.log(`Serving from DB
         ${body.text}`);
      const response = await this.adminService.handleSlackMessage(body.text);
      await this.cacheManager.set(body.text, response, AN_HOUR_IN_MILLISECONDS);
      return response;
    } catch (err) {
      return { message: err.message };
    }
  }

  @Get('/esim-orders')
  @ApiExcludeEndpoint()
  @UseGuards(AuthGuard('headerapikey'))
  @UsePipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  )
  async index(@GetApp() app: any, @Query() query?: EsimOrderDto) {
    this.checkIfJWAdmin(app.name);
    try {
      const { page, search, limit, sort_by, booked_date, ...filters } = query;
      const orderBy = {};
      if (sort_by) {
        const [column, sort] = sort_by.split(':');
        orderBy[column] = sort || 'asc';
      }
      const payload = {
        jpyPrice: query.jpy_price,
        orderId: query.order_id,
        activateCode: query.activate_code,
        createdAt: query.created_at,
        iccid: query.iccid,
        updatedAt: query.updated_at,
        orderCreatedAt: query.order_created_at,
        planId: query.plan_id,
        topupId: query.topup_id,
        type: query.type,
        paymentStatus: query.payment_status,
        appsId: query.app_id,
      };
      if (query.user_id) {
        //@ts-expect-error
        payload.userId = +query.user_id;
      }
      return await this.esimOrderService.index(
        //@ts-expect-error
        payload || {},
        {
          page: Number(page || 1),
          sort: orderBy,
          search,
          limit: +limit,
          bookedDate: booked_date,
        },
      );
    } catch (err) {
      this.logger.log(err);
      throw new UnprocessableEntityException(err.message);
    }
  }

  @Post('/plans/update/:country')
  @UseInterceptors(FileInterceptor('file'))
  @UseGuards(AuthGuard('headerapikey'))
  async updatePlans(
    @UploadedFile(new ParseFilePipe({ validators: [] }))
    file: Express.Multer.File,
    @Param('country')
    country: string,
    @GetApp() app: any,
    @Query('sheetId') sheetId?: string,
  ) {
    // this.checkIfJWAdmin(app.name);
    try {
      const token = JSON.parse(file.buffer.toString());
      await this.adminService.update(token, country, sheetId);
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }

  @Post('/cache-clear')
  @UseGuards(AuthGuard('headerapikey'))
  async clearCache(@Body('key') key: string, @GetApp() app: any) {
    try {
      this.checkIfJWAdmin(app.name);
      if (key) {
        const removed = await this.cacheManager.del(key);
        return removed;
      }
      // await this.cacheManager.reset();
    } catch {
      return null;
    }
  }

  private checkIfJWAdmin(appName: string) {
    if (appName != 'japan-wireless-esim') {
      throw new UnauthorizedException('Not allowed for this app.');
    }
  }

  @Post('/update/campaign')
  @UseInterceptors(FileInterceptor('file'))
  @UseGuards(AuthGuard('headerapikey'))
  async syncCampaign(
    @UploadedFile(new ParseFilePipe({ validators: [] }))
    file: Express.Multer.File,
    @GetApp() app: any,
    @Query('sheetId') sheetId?: string,
  ) {
    // this.checkIfJWAdmin(app.name);
    try {
      const token = JSON.parse(file.buffer.toString());
      await this.adminService.syncCampaignWithSheet(token, sheetId);
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }
}
