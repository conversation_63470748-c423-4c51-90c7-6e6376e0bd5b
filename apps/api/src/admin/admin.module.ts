import { Lo<PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { PrismaService } from 'src/prisma.service';
import { EsimService } from 'src/esim/esim.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { PlansService } from 'src/plans/plans.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { JwtService } from '@nestjs/jwt';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { UsersService } from 'src/users/users.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { PaymentService } from 'src/payment/payment.service';
import { EmailsService } from 'src/emails/emails.service';
import { CampaignsService } from 'src/campaigns/campaigns.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [],
  controllers: [AdminController],
  providers: [
    AdminService,
    PrismaService,
    EsimService,
    EsimProviderBuilder,
    PlansService,
    XchangeService,
    KeyValueStoreService,
    EsimOrdersService,
    Logger,
    CouponsService,
    JwtService,
    EsimStocksService,
    UsersService,
    NotificationsService,
    EmailTemplateService,
    AwsCognitoService,
    PaymentService,
    EmailsService,
    CampaignsService,
    ReferralsService,
  ],
})
export class AdminModule {}
