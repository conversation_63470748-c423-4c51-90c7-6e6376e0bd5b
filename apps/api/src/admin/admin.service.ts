import tablemark from '../utils/tablemark';
import { flatten } from '../utils/flatten';

import { Injectable, Logger } from '@nestjs/common';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { PlansService } from 'src/plans/plans.service';
import { kebabCase, pick, pickBy, result, startCase } from 'lodash';
import { UsersService } from 'src/users/users.service';
import { deserializeFromDotNotation } from 'src/utils';
import { CampaignsService } from 'src/campaigns/campaigns.service';

@Injectable()
export class AdminService {
  constructor(
    private plansService: PlansService,
    private esimOrderService: EsimOrdersService,
    private userService: UsersService,
    private logger: Logger,
    private campaignService: CampaignsService,
  ) {}

  async syncCampaignWithSheet(token: JSON, sheetId?: string) {
    const doc = await this.initGoogleSpreadSheet(token, sheetId);
    const originalSheet = doc.sheetsByTitle['campaigns'];
    const rawRows = await originalSheet.getRows();
    for (const campaignRow of rawRows) {
      const {
        campaign: {
          code,
          name,
          startDate,
          endDate,
          coupons,
          coverImages,
          metadata,
          enabled,
        },
      } = deserializeFromDotNotation(campaignRow) as any;

      const allCoupons = coupons.split(',');
      const campaign = await this.campaignService.create({
        name,
        code,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        coupons: allCoupons,
        coverImages: coverImages || [],
        metadata: metadata || {},
        enabled: enabled === 'TRUE',
      });
    }
  }
  async syncCouponsWithSheet() {}

  async update(token: JSON, country: string, sheetId?: string) {
    const doc = await this.initGoogleSpreadSheet(
      token,
      sheetId || '1gt2xitpZvBVIM3Qcu8pjFAs80qVFjXP9J1bUOnPx8bg',
    );
    if (country === 'countries') {
      return this.updatePlansOfAllCountries(doc, country);
    }

    return this.updatePlansOfCountry(doc, country);
  }
  async updatePlansOfAllCountries(doc: any, country: string) {
    const originalSheet = doc.sheetsByTitle[country];
    if (!originalSheet) throw new Error('Sheets not found.');
    const rawRows = await originalSheet.getRows();
    for (const country of rawRows) {
      const { name, enabled, code, subCountries } = country;
      await this.plansService.createCountry({
        countryName: name,
        enabled: enabled == '0' ? false : true,
        subCountries,
        code,
      });
    }
  }
  async updatePlansOfCountry(doc: any, country: string) {
    const originalSheet = doc.sheetsByTitle[country];
    if (!originalSheet) throw new Error('Sheets not found.');
    const rawRows = await originalSheet.getRows();
    await this.plansService.seedCountryPlans(
      {
        countryName: country,
      },
      rawRows,
    );
  }

  private async initGoogleSpreadSheet(token: any, sheetId: string) {
    const doc = new GoogleSpreadsheet(sheetId);
    await doc.useServiceAccountAuth({
      client_email: token.client_email,
      private_key: token.private_key,
    });
    await doc.loadInfo();
    return doc;
  }

  async handleSlackMessage(text: string) {
    const [command, value] = text.split(' ');
    let results;
    if (!value || !command) return 'Please send some command';
    this.logger.log(`Command executed, ${{ command, value }}`);
    switch (command) {
      case 'c':
      case 'couuntry':
        const countryPlans = await this.plansService.getAllPlans({
          country: value,
        });

        results = flatten(
          countryPlans.reduce(
            (acc, cur) => ({
              ...acc,
              [`${value} ${cur.id}`]: `${cur.dataId} / ${cur.validityDays}days / ${cur.packageType} / ${cur.planId}/ $${cur.price} `,
            }),
            {
              PlanId: `DataVolume / Validity Days / Package Type / Plan Id / Price `,
            },
          ),
          {},
        );

        break;
      case 'p':
      case 'plan':
        const plans = await this.plansService.getPlanById(+value, {
          serviceProvider: {
            enabled: undefined,
          },
        });
        results = flatten(plans, {});
        break;
      case 'd':
      case 'data':
        const orderTemp = await this.esimOrderService.orderDetailAndUsage(
          value,
          { type: 'none' },
        );
        const usage = await this.esimOrderService.orderUsage({
          topupid: orderTemp.order.topupId,
          orderId: orderTemp.order.orderId,
        });
        results = flatten(usage, {});
        break;
      case 'u':
      case 'user':
        const user = await this.userService.getUserByEmail(value);
        results = flatten(user, {});
        break;
      case 'o':
      case 'order':
        const order = await this.esimOrderService.orderDetailAndUsage(value, {
          type: 'none',
        });

        delete order.order.response;
        delete order.usage;

        const { plan, apps } = order.order;
        delete order.order.plan;
        delete order.order.apps;
        results = flatten(
          {
            ...order.order,
            ...pick(plan, ['dataId', 'dataName']),
            ...pick(apps, ['name']),
          },
          {},
        );

        break;

      default:
        results = { message: 'No command found!' };
        break;
    }

    return {
      response_type: 'in_channel',
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text:
              '```' +
              Object.keys(results || {})
                .map((item) => {
                  return `${startCase(item)}: ${results[item]}`;
                })
                .join(`\n`) +
              '```',
          },
        },
      ],
    };
  }
}
