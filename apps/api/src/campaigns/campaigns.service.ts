import { Injectable, Logger } from '@nestjs/common';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { PrismaService } from 'src/prisma.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { xor } from 'lodash';
import { campaigns } from '@prisma/client';

@Injectable()
export class CampaignsService {
  constructor(
    private prismaService: PrismaService,
    private couponsService: CouponsService,
    private logger: Logger,
  ) {}
  async create(createCampaignDto: CreateCampaignDto) {
    const coupons = await this.couponsService.findManyByCode(
      createCampaignDto.coupons,
    );
    const existingCoupons = coupons.map((item) => item.code);
    const couponsNotExisted = xor(createCampaignDto.coupons, existingCoupons);
    if (couponsNotExisted.length)
      throw new Error(
        `${couponsNotExisted} doesnt exist, Please create these coupons first.`,
      );

    const { coupons: c, ...campaignData } = {
      ...createCampaignDto,
      code: createCampaignDto.code,
      name: createCampaignDto.name,
      campaignCoupons: {
        create: coupons.map((coupon) => ({
          couponId: coupon.id,
        })),
      },
    };

    const campaign = await this.prismaService.campaigns.upsert({
      where: {
        code: createCampaignDto.code,
      },
      create: {
        ...campaignData,
      },
      update: {
        ...campaignData,
      },
    });
    return 'This action adds a new campaign';
  }

  async findAll() {
    // const campaigns = await this.couponsService.
  }

  findOne(id: number) {
    return `This action returns a #${id} campaign`;
  }

  update(id: number, updateCampaignDto: UpdateCampaignDto) {
    return `This action updates a #${id} campaign`;
  }

  remove(id: number) {
    return `This action removes a #${id} campaign`;
  }
}
