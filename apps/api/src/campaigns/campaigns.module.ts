import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { CampaignsService } from './campaigns.service';
import { CampaignsController } from './campaigns.controller';
import { PrismaService } from 'src/prisma.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { EmailsService } from 'src/emails/emails.service';

@Module({
  controllers: [CampaignsController],
  providers: [
    CampaignsService,
    PrismaService,
    CouponsService,
    Logger,
    EmailsService,
  ],
})
export class CampaignsModule {}
