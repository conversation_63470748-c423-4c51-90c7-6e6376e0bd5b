import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EmailsService } from 'src/emails/emails.service';
import { PrismaService } from 'src/prisma.service';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class RateService {
  constructor(
    private prismaService: PrismaService,
    private userService: UsersService,
    private emailService: EmailsService,
    private configService: ConfigService,
    private logger: Logger,
  ) {}

  async findHighRating(userId: number) {
    return this.prismaService.rate.findFirst({
      where: { userId, rating: { gte: 4 } },
    });
  }

  async findOldestUnratedActivatedOrder(
    userId: number,
    validActivationDate: Date,
  ) {
    return this.prismaService.orders.findFirst({
      where: {
        userId,
        isActivated: true,
        activateDate: { lte: validActivationDate },
        isRated: false,
      },
      orderBy: { activateDate: 'asc' },
      select: { id: true },
    });
  }

  async findAllOrders(userId: number) {
    return this.prismaService.orders.findMany({
      where: {
        userId,
      },
      select: {
        id: true,
        isRated: true,
      },
    });
  }

  async countHighRatings(userId: number) {
    return this.prismaService.rate.count({
      where: { userId, rating: { gte: 4 } },
    });
  }

  async resetOrdersRatedStatus(userId: number) {
    await this.prismaService.orders.updateMany({
      where: {
        userId,
      },
      data: { isRated: false },
    });
  }

  async findRateByOrderAndUser(orderId: number, userId: number) {
    return this.prismaService.rate.findFirst({
      where: { orderId, userId },
    });
  }

  async createRate(userId: number, orderId: number) {
    return this.prismaService.rate.create({
      data: { userId, orderId },
    });
  }

  async updateRate(rateId: number, rating: number, comment?: string) {
    return this.prismaService.rate.update({
      where: { id: rateId },
      data: { rating, comment },
    });
  }

  async updateOrderRatedStatus(orderId: number, isRated: boolean) {
    await this.prismaService.orders.update({
      where: { id: orderId },
      data: { isRated },
    });
  }

  async sendLowRatingEmail(
    userId: number,
    orderId: number,
    rating: number,
    comment: string,
  ) {
    try {
      const user = await this.userService.findOne(userId);

      if (!user) {
        this.logger.error(`Failed to find user with ID: ${userId}`);
        return;
      }
      await this.emailService.sendToSupport({
        bcc:
          this.configService.getOrThrow('NODE_ENV') !== 'development'
            ? ['<EMAIL>']
            : [],
        subject: 'Low rating received ⭐️!',
        to: '<EMAIL>',
        message: `
          A user just rated this service low.
          <br>
          <strong>User's Name:</strong> ${user.firstName} ${user.lastName}
          <br>
          <strong>User's Email:</strong> ${user.email}
          <br>
          <strong>Rating:</strong> ${rating}
          <br>
          <strong>Feedback Message:</strong> ${comment || 'N/A'}
          <br>
          <br>
        `,
      });
      this.logger.log(
        `Low rating email sent successfully for order ${orderId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send low rating email for order ${orderId}: ${error.message}`,
      );
    }
  }
}
