import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Logger,
  Post,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { GetUser } from 'src/auth/get-user.decorator';
import { ONE_DAY_IN_MILLISECONDS } from 'src/constants';
import { IUser } from 'src/interface/IUser';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { RateService } from './rate.service';
import { SaveRateDto } from './dto/save-rate.dto';
import { CheckRateResponseDto } from './dto/check-rate-response.dto';
import { SaveRateResponseDto } from './dto/save-rate-response.dto';

@Controller('rate')
@ApiTags('Rate')
@UseInterceptors(SentryInterceptor)
export class RateController {
  constructor(
    private readonly rateService: RateService,
    private logger: Logger,
  ) {}

  @Get('/check')
  @UsePipes(ValidationPipe)
  @ApiOkResponse({ type: CheckRateResponseDto })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  async checkRate(@GetUser() user: IUser) {
    try {
      const userId = user.appId;
      const validActivationDate = new Date(
        Date.now() - ONE_DAY_IN_MILLISECONDS,
      );

      const [highRating, order] = await Promise.all([
        this.rateService.findHighRating(userId),
        this.rateService.findOldestUnratedActivatedOrder(
          userId,
          validActivationDate,
        ),
      ]);

      if (highRating) {
        return { data: { isShowPopup: false } };
      }

      const allOrders = await this.rateService.findAllOrders(userId);
      const allOrdersRated = allOrders.every((order) => order.isRated);

      if (allOrdersRated) {
        const validRatingsCount = await this.rateService.countHighRatings(
          userId,
        );
        if (validRatingsCount === 0) {
          await this.rateService.resetOrdersRatedStatus(userId);
        }
      }

      if (!order) {
        return { data: { isShowPopup: false } };
      }

      const rate = await this.rateService.findRateByOrderAndUser(
        order.id,
        userId,
      );

      if (!rate) {
        await this.rateService.createRate(userId, order.id);
        return { data: { isShowPopup: true, orderId: order.id } };
      }

      const lastUpdated = new Date(rate.updatedAt);
      const timeSinceLastUpdate = Date.now() - lastUpdated.getTime();

      if (
        (rate.rating === null || rate.rating <= 3) &&
        timeSinceLastUpdate >= ONE_DAY_IN_MILLISECONDS
      ) {
        return { data: { isShowPopup: true, orderId: order.id } };
      }

      return { data: { isShowPopup: false } };
    } catch (error) {
      this.logger.error(error.message);
      throw new BadRequestException(error.message);
    }
  }

  @Post('/')
  @UsePipes(ValidationPipe)
  @ApiOkResponse({ type: SaveRateResponseDto })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  async saveRate(@Body() body: SaveRateDto, @GetUser() user: IUser) {
    try {
      const userId = user.appId;
      const { orderId, rating, comment } = body;

      const rate = await this.rateService.findRateByOrderAndUser(
        orderId,
        userId,
      );

      if (!rate) {
        throw new BadRequestException('Rate record not found.');
      }

      await Promise.all([
        await this.rateService.updateRate(rate.id, rating, comment),
        await this.rateService.updateOrderRatedStatus(orderId, true),
      ]);

      if (rating <= 3) {
        this.rateService.sendLowRatingEmail(
          user.appId,
          orderId,
          rating,
          comment,
        );
      }

      return { message: 'Rate saved successfully.' };
    } catch (error) {
      this.logger.error(error.message);
      throw new BadRequestException(error.message);
    }
  }
}
