import { HttpModule } from '@nestjs/axios';
import { <PERSON><PERSON>, Module } from '@nestjs/common';
import { RateService } from './rate.service';
import { RateController } from './rate.controller';
import { PassportModule } from '@nestjs/passport';
import { EsimOrdersModule } from 'src/esim-orders/esim-orders.module';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { PrismaService } from 'src/prisma.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EsimService } from 'src/esim/esim.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { XchangeService } from 'src/xchange/xchange.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { PlansService } from 'src/plans/plans.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { JwtService } from '@nestjs/jwt';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { UsersService } from 'src/users/users.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EmailsService } from 'src/emails/emails.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { PaymentService } from 'src/payment/payment.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 60000,
        maxRedirects: 5,
        baseURL: configService.get('USIMSA_HOST_NAME'),
      }),
      inject: [ConfigService],
    }),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    EsimOrdersModule,
  ],
  providers: [
    RateService,
    EsimOrdersService,
    PrismaService,
    EsimService,
    Logger,
    XchangeService,
    KeyValueStoreService,
    EsimProviderBuilder,
    EsimStocksService,
    UsersService,
    NotificationsService,
    EmailTemplateService,
    EmailsService,
    ConfigService,
    PlansService,
    CouponsService,
    JwtService,
    AwsCognitoService,
    PaymentService,
    ReferralsService,
  ],
  controllers: [RateController],
})
export class RateModule {}
