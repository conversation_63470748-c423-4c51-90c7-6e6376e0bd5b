import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as iconv from 'iconv-lite';

@Injectable()
export class ShiftJisMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const originalBody: Buffer[] = [];
    req.on('data', (chunk) => {
      originalBody.push(chunk);
    });

    req.on('end', () => {
      const bodyBuffer = Buffer.concat(originalBody);
      const decodedBody = iconv.decode(bodyBuffer, 'shift_jis');

      // Parse the decoded body
      const formBody = new URLSearchParams(decodedBody);
      const bodyObj: Record<string, string> = {};
      formBody.forEach((value, key) => {
        bodyObj[key] = value;
      });
      req.body = bodyObj;

      next();
    });
  }
}
