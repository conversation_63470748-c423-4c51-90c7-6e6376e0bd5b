import { Injectable, NestMiddleware } from '@nestjs/common';

@Injectable()
export class AdminBasicAuthMiddleware implements NestMiddleware {
    use(req: any, res: any, next: () => void) {
        const auth = { login: process.env.ADMINJS_USER, password: process.env.ADMINJS_PASS };

        // Parse login and password from headers
        const b64auth = (req.headers.authorization || '').split(' ')[1] || '';
        const [login, password] = Buffer.from(b64auth, 'base64').toString().split(':');

        // Verify login and password are set and correct
        if (login && password && login === auth.login && password === auth.password) {
            return next();
        }

        // Access denied
        res.set('WWW-Authenticate', 'Basic realm="AdminJS"');
        res.status(401).send('Authentication required.');
    }
} 