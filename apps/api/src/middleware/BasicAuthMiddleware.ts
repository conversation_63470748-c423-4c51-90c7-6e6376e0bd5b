import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import basicAuth from 'express-basic-auth';

@Injectable()
export class BasicAuthMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    basicAuth({
      users: { superAccess: process.env['PKCE_CODE_VERIFIER'] },
      challenge: true,
    })(req, res, next);
  }
}
