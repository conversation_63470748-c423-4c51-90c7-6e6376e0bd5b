import { Global, INestApplication, Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

interface PaginationOptions {
  page?: number;
  limit?: number;
}

interface SearchOptions {
  search?: string;
}

interface FilteringOptions {
  [key: string]: any;
}
type DynamicModel = keyof PrismaClient;
type IndexOptions<T> = PaginationOptions & SearchOptions & DynamicFilter<T>;
type DynamicFilter<T> = {
  [K in keyof T]?: T[K] | {
    equals?: T[K];
    in?: T[K][];
    not?: T[K];
    notIn?: T[K][];
    contains?: T[K];
    startsWith?: T[K];
    endsWith?: T[K];
    lt?: T[K];
    lte?: T[K];
    gt?: T[K];
    gte?: T[K];
  };
};

@Global()
@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {

  }

  async enableShutdownHooks(app: INestApplication) {
    this.$on('beforeExit', async () => {
      await app.close();
    });
  }

  async index<T extends DynamicModel>(
    model: T,
    filter: DynamicFilter<PrismaClient[T]>,
    options?: IndexOptions<PrismaClient[T]>,
  ) {

    const { page = 1, limit = 10, search, ...additionalFilters } = options || {};

    const skip = (page - 1) * limit;
    const take = limit;
    //@ts-expect-error
    let where: PrismaClient[T] = { ...filter };

    if (search) {
      //@ts-expect-error
      const searchableFields = Object.keys(this[model].delegations?.findUnique.args.select);
      where = {
        ...where,
        OR: searchableFields.map((field) => ({
          [field]: { contains: search },
        })),
      };
    }

    //@ts-expect-error
    const totalItems = await this[model].count({ where });

    //@ts-expect-error
    const items = await this[model].findMany({
      where,
      //@ts-expect-error
      orderBy: options?.sort,
      skip,
      take,
      ...additionalFilters,
    });

    return { totalItems, items };
  }
}
