import { MiddlewareConsumer, Module } from '@nestjs/common';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { PrismaService } from 'src/prisma.service';
import { ApikeyService } from './apikey.service';
import { ApikeyController } from './apikey.controller';
import { BasicAuthMiddleware } from 'src/middleware/BasicAuthMiddleware';

@Module({
  providers: [ApikeyService, KeyValueStoreService, PrismaService],
  controllers: [ApikeyController],
})
export class ApikeyModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(BasicAuthMiddleware)
      .forRoutes('admin/apikey/generate', '/health');
  }
}
