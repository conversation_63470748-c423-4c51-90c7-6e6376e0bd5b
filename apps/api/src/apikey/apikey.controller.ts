import {
  Controller,
  Get,
  InternalServerErrorException,
  UseInterceptors,
} from '@nestjs/common';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { ApikeyService } from './apikey.service';
import { ApiExcludeController } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';

@Controller('/admin/apikey')
@ApiExcludeController()
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class ApikeyController {
  constructor(
    private apiKeyService: ApikeyService,
    private configService: ConfigService, // Inject ConfigService
  ) {}

  @Get('generate')
  generateKey() {
    if (this.configService.get('DISABLE_API_KEY_GENERATE') === 'true') return;
    return this.apiKeyService.generateRandomApiKey();
  }
}
