import { Injectable } from '@nestjs/common';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import * as bcrypt from 'bcrypt';
import { apiIdWrapper } from 'src/utils';
import { v4 as uuidv4, v1 as uuidv1 } from 'uuid';

@Injectable()
export class ApikeyService {
  constructor(private kvStore: KeyValueStoreService) {}
  async createNewAPIKey(apiId: string, apiKey: string) {
    const salt = await bcrypt.genSalt();
    const hashedApiKey = await bcrypt.hash(apiKey, salt);

    //Seed default API access
    await this.kvStore.set(apiIdWrapper(apiId), {
      value: hashedApiKey,
      descritpion: '',
    });
  }

  async generateRandomApiKey() {
    const apiId = uuidv1();
    const apiKey = uuidv4();

    await this.createNewAPIKey(apiId, apiKey);
    return {
      apiId,
      apiKey,
    };
  }
}
