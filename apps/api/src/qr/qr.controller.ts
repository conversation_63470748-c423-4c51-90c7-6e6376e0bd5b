import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Res,
  Query,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { QrService } from './qr.service';
import { CreateQrDto } from './dto/create-qr.dto';
import { Response } from 'express';
import { Throttle } from '@nestjs/throttler';

@Controller('qr')
export class QrController {
  constructor(
    private readonly qrService: QrService,
    private readonly logger: Logger,
  ) {}

  @Get('generate')
  @Throttle(1000, 60000)
  async create(@Query() createQrDto: CreateQrDto, @Res() res: Response) {
    try {
      const tag = Date.now();
      res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin ');
      res.setHeader('ETag', `"${tag}"`); // Unique identifier
      res.setHeader('Last-Modified', new Date().toUTCString());

      res.setHeader('Content-Type', 'image/png');
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // Cache for 1 year
      this.logger.log(`Generating QR ${createQrDto.size}  ${createQrDto.data}`);
      await this.qrService.create(res, createQrDto);
      this.logger.log(`Generated QR  ${createQrDto.size} ${createQrDto.data}`);
    } catch (err) {
      console.log(err);
      throw new BadRequestException();
    }
  }
}
