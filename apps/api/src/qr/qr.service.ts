import { Injectable } from '@nestjs/common';
import * as qr from 'qrcode';
import { isAppEnvDev } from 'src/utils';
import { CreateQrDto } from './dto/create-qr.dto';

@Injectable()
export class QrService {
  async create(stream, createQrDto: CreateQrDto) {
    let [width, height] = createQrDto.size?.split?.('x') || ['250', '250'];

    const scale = isNaN(+createQrDto.scale) ? undefined : +createQrDto.scale;
    width = isNaN(+width) ? undefined : width;

    if (!scale) {
      width = '250';
    }
    const streamResponse = await qr.toFileStream(
      stream,
      createQrDto.data || 'Bad Requesst',
      {
        margin: 1,
        scale,
        width: isNaN(+width) ? undefined : +width,
      },
    );
    return streamResponse;
  }

  static getUrlOfQRByCodeAndSMDP(smdp: string, activationCode: string) {
    return `https://${isAppEnvDev() ? "esim-dev" : "esim"}.gmobile.biz/api/v1/qr/generate?size=350x350&data=LPA:1$${smdp}$${activationCode}`
  }
}
