import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as Sentry from '@sentry/nestjs';
import { Job, Worker, WorkerOptions } from 'bullmq';
import redisConnection from 'config/redis-connection';
import * as os from 'os';
import { EmailsService } from 'src/emails/emails.service';
import { EVENT_DEV_NOTIFY } from 'src/events/handlers/event-dev-notify.listener';

export abstract class BaseWorker {
  protected worker: Worker;
  protected logger: Logger;
  protected workerConfig: Omit<WorkerOptions, 'connection'> = {};
  constructor(
    protected queueName: string,
    protected emailsService: EmailsService,
    protected eventEmitter: EventEmitter2,
  ) {
    this.logger = new Logger(queueName);
  }

  startWorker() {
    try {

      this.worker = new Worker(
        this.queueName,
        async (job) => {
          try {
            const response = await this.process(job);
            return response;
          } catch (err) {
            this.logger.error(err);
            throw err;
          }
        },
        {
          name: `${this.queueName}:${os.hostname()}`,
          connection: {
            ...redisConnection().connection,
            retryStrategy: function (times) {
              const retryDelays = [180000, 540000, 3600000]; // 3 min, 9 min, 1 hr

              if (times <= retryDelays.length) {
                return retryDelays[times - 1]; // Return the delay based on retry count
              }

              return null; // Stop retrying after the third attempt
            },
            maxRetriesPerRequest: null, // Fail after 5 retries
            enableOfflineQueue: true, // Fail fast if Redis is offline
          },

          lockDuration: 15000,
          concurrency: 2,
          limiter: {
            max: 1,
            duration: 15000,
          },

          ...(this.workerConfig || {}),
        },
      );

      this.logger.log(`Started worker for queue ${this.queueName}`);
      this.worker.on('error', this.onError.bind(this));
      this.worker.on('completed', this.onCompleted.bind(this));
      this.worker.on('failed', this.onFailed.bind(this));
      this.worker.on('paused', this.onPaused.bind(this));
      return this.worker;
    } catch (err) {
      this.logger.error(err);
    }
  }

  abstract process(job: Job<unknown>);



  onError(job) {
    Sentry.captureException(job.stack);
    this.logger.error('error', job.stack);
    this.eventEmitter.emit(
      EVENT_DEV_NOTIFY,
      {
        error: job.stack,
        subject: `Urgent queue worker issue ${this.queueName}`,
        message: job.message,
      },
    );
  }

  onCompleted(job) {
    this.logger.log(`${this.queueName} Job processed ${job.id} `);
  }

  onPaused() {
    this.logger.log(`${this.queueName} Job paused   `);
  }

  async onFailed(job, error) {
    try {
      Sentry.captureException(error);
      this.eventEmitter.emit(
        EVENT_DEV_NOTIFY,
        {
          error: job.stacktrace ? job.stacktrace.join(',') : error,
          subject: `eSIM order or purchase issue on job #${job.id}`,
          message: job.failedReason,
        },
      );
    } catch (err) {
      this.logger.error(err);
      Sentry.captureException(error);
    }
  }
}
