import { QUEUE_WEBHOOK_EXECUTOR } from 'src/constants';
import axios from 'axios';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { Job } from 'bullmq';
import { AppsService } from 'src/apps/apps.service';
import { BaseWorker } from './base.worker';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EmailsService } from 'src/emails/emails.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class WebhookExecutorWorker extends BaseWorker {
  constructor(
    protected eventEmitter: EventEmitter2,
    protected emailService: EmailsService,
    protected appsService: AppsService,
    private esimOrderService: EsimOrdersService,
  ) {
    super(QUEUE_WEBHOOK_EXECUTOR, emailService, eventEmitter);
    this.workerConfig = {
      concurrency: 1,
      limiter: {
        duration: 5000,
        max: 1,
      },
    };
  }

  async process(
    job: Job<
      {
        appsId: number;
        orderId: number;
      },
      any,
      string
    >,
  ): Promise<any> {
    const { orderId, appsId } = job.data;
    if (!orderId) {
      this.logger.error(`Empty data here webhook executor`, job);
      return;
    }
    const order = await this.esimOrderService.get({ id: orderId });
    const apps = order.apps;

    const esim = {
      topupId: order.topupId,
      planId: order.planId,
      iccid: order.iccid,
      //@ts-expect-error
      smdp: order.response.products[0].smdp,
      activateCode: order.activateCode,
      downloadLink: order.downloadLink,
      qrCodeImgUrl: order.qrCodeImgUrl,
    };
    const responses = {};
    if (!apps) {
      throw new Error('Vendor not reachable.');
    }
    if (apps.webhooks && Array.isArray(apps.webhooks)) {
      const webhooks = apps.webhooks as string[];
      for (const webhook of webhooks) {
        const payload = {
          esim,
          transactionKey: AppsService.getTransactionKey(
            order.bookingNo,
            order.id + '',
          ),
          language: order.lang,
          appId: order.appsId,
          // jpyPrice: order.jpyPrice,
          bookingNo: order.bookingNo,
          orderId: order.orderId,
        };
        try {
          this.logger.log(`Sending a webhook request to ${webhook}}`);

          const response = await axios.post(webhook as string, payload);
          responses[webhook] = {
            response: response.data,
            payload,
          };
          this.logger.log(`Sent a webhook request to ${webhook}}`);
        } catch (err) {
          this.logger.error(
            `Failed executing webhook for ${webhook} in job ${
              job.id
            } ${JSON.stringify(payload)}`,
          );
          throw new Error(
            err.message + ' ' + JSON.stringify(err?.response?.data || {}),
          );
        }
      }
      return responses;
    }
  }
}
