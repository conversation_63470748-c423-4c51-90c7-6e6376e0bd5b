import { OnWorkerEvent, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as Sentry from '@sentry/nestjs';
import { Job } from 'bullmq';
import { EventDevNotify } from 'src/events/handlers/event-dev-notify';
import { EVENT_DEV_NOTIFY } from 'src/events/handlers/event-dev-notify.listener';

export abstract class BaseProcessor extends WorkerHost {
  protected logger: Logger = new Logger();
  protected eventEmitter: EventEmitter2;

  @OnWorkerEvent('error')
  onError(job: Job<any>, error: any) {
    this.logger.error(
      `Failed job ${job.id} of type ${job.name}: ${
        error?.message
      } : ${JSON.stringify(job.data || {})}`,
      error?.stack,
    );
    if (error) Sentry.captureException(error);

    this.eventEmitter.emit(
      EVENT_DEV_NOTIFY,
      new EventDevNotify(
        job.failedReason,
        job.stacktrace.join(','),
        `Esim issue job #${job.id}`,
      ),
    );
  }

  @OnWorkerEvent('failed')
  onCompleted(job, error) {
    this.logger.error(
      `Failed job ${job.id} of type ${job.name}: ${
        error?.message
      } : ${JSON.stringify(job.data || {})}`,
      error?.stack,
    );
    if (error) Sentry.captureException(error);

    this.eventEmitter.emit(
      EVENT_DEV_NOTIFY,
      new EventDevNotify(
        job.failedReason,
        job.stacktrace.join(','),
        `Esim Issue #${job.id}`,
      ),
    );
  }
}
