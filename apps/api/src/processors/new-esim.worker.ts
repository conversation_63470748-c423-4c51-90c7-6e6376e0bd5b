import { InjectQueue } from '@nestjs/bullmq';
import {
  QUEUE_EMAIL_DISPATCHER,
  QUEUE_NEW_ESIM,
  QUEUE_WEBHOOK_EXECUTOR,
} from 'src/constants';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { Injectable } from '@nestjs/common';
import { Job, Queue } from 'bullmq';
import { PAYMENT_STATUS } from '@prisma/client';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';
import { getFormmattedOrderId } from 'src/utils';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { NEW_SUCCESSFUL_ORDER } from 'src/esim-orders/events';
import { OrderUpdateEvent } from 'src/esim-orders/events/order-update.event';
import { ConfigService } from '@nestjs/config';
import { AppsService } from 'src/apps/apps.service';
import axios from 'axios';
import { BaseWorker } from './base.worker';
import { EmailsService } from 'src/emails/emails.service';

@Injectable()
export class NewEsimWorker extends BaseWorker {
  constructor(
    @InjectQueue(QUEUE_WEBHOOK_EXECUTOR)
    private webhookExecutorQueue: Queue,
    @InjectQueue(QUEUE_EMAIL_DISPATCHER)
    private emailDispatcherQueue: Queue,
    protected eventEmitter: EventEmitter2,
    protected emailService: EmailsService,
    protected appsService: AppsService,
    private esimOrderService: EsimOrdersService,
    private configService: ConfigService,
  ) {
    super(QUEUE_NEW_ESIM, emailService, eventEmitter);
  }

  async process(
    job: Job<
      {
        appsId: string;
        orderId: number;
        orderResponse: IEsimPurchaseResponse;
        userId: string;
      },
      any,
      string
    >,
  ): Promise<any> {
    const { orderResponse, orderId, appsId, userId } = job.data;
    if (!orderResponse?.products) {
      this.logger.error(`Empty data here New esim worker`, job);
      return;
    }

    const [esim] = orderResponse?.products;

    const order = await this.esimOrderService.updateOrder(
      {
        activateCode: esim.activateCode || '',
        iccid: esim.iccid || '',
        qrCodeImgUrl: esim.qrcodeImgUrl || esim.qrCodeImgUrl || '',
        topupId: esim.topupId || '',
        downloadLink: esim.downloadLink || '',
        orderCreatedAt: new Date(),
        paymentStatus: appsId
          ? PAYMENT_STATUS.THIRD_PARTY_PURCHASE
          : PAYMENT_STATUS.SUCCESS,
        response: {
          code: orderResponse?.code,
          message: orderResponse?.message,
          products: [
            {
              ...(orderResponse.products?.[0] || {}),
              smdp: esim.smdp,
            },
          ],
        },
      },
      {
        id: orderId,
      },
    );
    if (appsId && order.apps.webhooks[0]) {
      try {
        await this.webhookExecutorQueue.add(
          getFormmattedOrderId(orderId) + ':' + order.apps.webhooks[0],
          {
            name: order.apps.name,
            appsId,
            orderId,
          },
          {
            ...(this.configService.get('redis').queue || {}),
            jobId: getFormmattedOrderId(orderId) + ':' + order.apps.webhooks[0],
            attempts: 3,
            delay: 10000,
            backoff: {
              type: 'exponential',
              delay: 60000,
            },
          },
        );
      } catch (err) {
        this.logger.error(err);
      }
    }

    const customer = order.user || {
      //@ts-expect-error
      email: order?.orderMetaData?.email,
      //@ts-expect-error
      id: order?.orderMetaData?.bookingNo,
      //@ts-expect-error
      firstName: order?.orderMetaData?.firstName,
      //@ts-expect-error
      lastName: order?.orderMetaData?.lastName,
    };
    //@ts-expect-error
    if (order?.orderMetaData?.sendEmail || userId) {
      try {
        await this.emailDispatcherQueue.add(
          `${getFormmattedOrderId(orderId)}:${customer.email}`,
          {
            appsId,
            orderId,
          },
          {
            ...(this.configService.get('redis').queue || {}),
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 60000,
            },
          },
        );
      } catch (err) {
        this.logger.error(err);
      }
    }
    // if (user && order) {
    //   await this.esimOrderService.addOrderNotificationEntry(user.id, order.id);
    // }

    this.logger.log(`After successful charge for order ${order.id}`);

    this.eventEmitter.emit(
      NEW_SUCCESSFUL_ORDER,
      new OrderUpdateEvent({
        order,
        orderInformation: {},
        //@ts-expect-error
        user: customer,
        plan: order.plan,
      }),
    );
  }

  async webhookExec(orderId: number) {
    // const { orderId, appsId } = job.data;
    const order = await this.esimOrderService.get({ id: orderId });
    const apps = order.apps;

    const esim = {
      topupId: order.topupId,
      planId: order.planId,
      iccid: order.iccid,
      //@ts-expect-error
      smdp: order.response.products[0].smdp,
      activateCode: order.activateCode,
      downloadLink: order.downloadLink,
      qrCodeImgUrl: order.qrCodeImgUrl,
    };
    const responses = {};
    if (!apps) {
      throw new Error('Vendor not reachable.');
    }
    if (apps.webhooks && Array.isArray(apps.webhooks)) {
      const webhooks = apps.webhooks as string[];
      for (const webhook of webhooks) {
        const payload = {
          esim,
          transactionKey: AppsService.getTransactionKey(
            order.bookingNo,
            order.id + '',
          ),
          language: order.lang,
          appId: order.appsId,
          // jpyPrice: order.jpyPrice,
          bookingNo: order.bookingNo,
          orderId: order.orderId,
        };
        try {
          this.logger.log(`Sending a webhook request to ${webhook}}`);

          const response = await axios.post(webhook as string, payload);
          responses[webhook] = {
            response: response.data,
            payload,
          };
          this.logger.log(`Sent a webhook request to ${webhook}}`);
        } catch (err) {
          this.logger.error(
            `Failed executing webhook for ${webhook} in job   ${JSON.stringify(
              payload,
            )}`,
          );
          throw new Error(
            err.message + ' ' + JSON.stringify(err?.response?.data || {}),
          );
        }
      }
      return responses;
    }
  }
}
