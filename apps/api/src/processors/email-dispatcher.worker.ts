import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import axios from 'axios';
import { Job } from 'bullmq';
import { AppsService } from 'src/apps/apps.service';
import { QUEUE_EMAIL_DISPATCHER } from 'src/constants';
import { EmailsService } from 'src/emails/emails.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { IAppsMetaData } from 'src/interface/IAppsMetadata';
import { BaseWorker } from './base.worker';

@Injectable()
export class QueueEmailDispatcherWorker extends BaseWorker {
  constructor(
    protected eventEmitter: EventEmitter2,
    protected emailService: EmailsService,
    protected appsService: AppsService,
    private esimOrderService: EsimOrdersService,
    private configService: ConfigService,
  ) {
    super(QUEUE_EMAIL_DISPATCHER, emailService, eventEmitter);
    this.workerConfig = {
      concurrency: 4,
      limiter: {
        duration: 60000,
        max: 30,
      },
    };
  }

  async process(
    job: Job<
      {
        appsId: number;
        orderId: number;
      },
      any,
      string
    >,
  ): Promise<any> {
    const { orderId, appsId } = job.data;
    const order = await this.esimOrderService.get({ id: orderId });
    const { plan, user, apps } = order;
    const appsMetadata: IAppsMetaData = (apps?.metadata as Object) || undefined;
    const customer = user || {
      //@ts-expect-error
      email: order.orderMetaData.email,
      //@ts-expect-error
      id: order.orderMetaData.bookingNo,
      //@ts-expect-error
      firstName: order.orderMetaData.firstName,
      //@ts-expect-error
      lastName: order.orderMetaData.lastName,
    };

    const isNeedCmlink = ['hongkong', 'taiwan'].includes(plan?.country?.name);

    const orderInformation = {
      thirdPartyApp: order.appsId,
      isNeedCmlink,
      language: order.lang,
      esimPlan: plan.packageType,
      orderId: order.orderId,
      dataSize: plan.dataVolume + plan.dataUnit,
      iccid: order.iccid,
      dataPlan: plan.dataId,
      packageType: plan.packageType,
      isPerday: plan.packageType === 'PER_DAY',
      isFixedPlan: plan.packageType === 'FIXED_DAY',
      priceJPY: order.jpyPrice,
      countryName: plan?.country?.name,
      usageDays: plan.name,
      //@ts-expect-error
      qrCode: order.qrcodeImgUrl || order.qrCodeImgUrl || '',
      //@ts-expect-error
      smdp: order.response.products?.[0].smdp,
      activateCode: order.activateCode,
      downloadLink: order.downloadLink,
      jpyPrice:
        order.lang === 'jp' || order.appsId
          ? new Intl.NumberFormat('ja-JP', {
              style: 'currency',
              currency: 'JPY',
            }).format(order.jpyPrice as unknown as number)
          : new Intl.NumberFormat('ja-JP', {
              style: 'currency',
              currency: 'USD',
            }).format(order.price as unknown as number),
      feHost: this.configService.getOrThrow('FRONTEND_HOST'),
      qrCodeFormatted: encodeURIComponent(
        //@ts-expect-error
        order.qrcodeImgUrl || order.qrCodeImgUrl || '',
      ),
      usageDayCount: {
        count: plan.validityDays,
      },
      email: customer.email,
      userSource: order?.user?.source,
      //@tood make this dynamic from database
      esimExpireCount:
        order?.plan?.country?.name.toLowerCase?.() === 'korea' ? 90 : 30,
      template:
        appsMetadata?.template ||
        //@ts-expect-error
        order.orderMetaData?.template ||
        'order-confirm-layout',
    };

    this.logger.log(
      `Initiate order confirmation email for ${customer.email} for order number ${orderId}`,
    );

    try {
      const translatedMailResponse = await axios.post(
        `http://127.0.0.1:${
          this.configService.getOrThrow('APP_PORT') || 3001
        }/api/v1/payment/esim/mail/translate`,
        orderInformation,
      );
      // i18n.t('order-email.your-esim-confirmed', {
      //   args: {
      //     orderId: payload.orderId,
      //   },
      // })
      /**
       * This is very temporary solution
       */

      let enSubject = `Your eSIM order confirmed ${
        appsMetadata?.isHideSubjectOrderId ? '' : order.orderId
      }`;
      let jpSubject = `【${
        order?.user?.source === 'airtrip' ? 'エアトリeSIM' : 'グロモバeSIM'
      }】ご注文ありがとうございます ${
        appsMetadata?.isHideSubjectOrderId ? '' : order.orderId
      }`;

      if (
        appsMetadata?.subject === 'order-email.airtrip.campaign-subject' ||
        appsMetadata?.template === 'airtrip-campaign-layout'
      ) {
        enSubject =
          '[AirTrip eSIM] eSIM gift! Thank you for answering the survey.';
        jpSubject =
          '【エアトリeSIM】eSIMプレゼント！アンケートのご回答ありがとうございます。';
      } else if (
        // TODO: update these values base on GM campaign apps keys
        //@ts-expect-error
        order.orderMetaData.template === 'gm-esim-campaign-layout'
      ) {
        enSubject =
          '[グロモバ eSIM] eSIM gift! Thank you for answering the survey.';
        jpSubject =
          '【グロモバeSIM】eSIMプレゼント！会員登録ありがとうございます。';
      }

      const orderConfirmSubject = {
        en: enSubject,
        jp: jpSubject,
      };
      const lang = Object.keys(orderConfirmSubject).includes(
        orderInformation.language,
      )
        ? orderInformation.language
        : 'en';
      await this.emailService.sendEmail({
        to: customer.email,
        message: translatedMailResponse.data,
        subject: orderConfirmSubject[lang],
      });

      this.logger.log(
        `Order confirmation email dispatched to ${customer.email} for order number ${orderId}`,
      );
      return null;
    } catch (err) {
      this.logger.error(
        'Error while sending email to order',
        orderId,
        err.message,
      );
      this.logger.error(err.message);
      throw err;
    }
  }
}
