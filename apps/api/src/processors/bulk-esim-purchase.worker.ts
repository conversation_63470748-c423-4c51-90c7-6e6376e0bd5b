import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as Sentry from '@sentry/nestjs';
import axios from 'axios';
import { Job } from 'bullmq';
import * as CryptoJS from 'crypto-js';
import { AppsService } from 'src/apps/apps.service';
import { BULK_ESIM_PURCHASE } from 'src/constants';
import { EmailsService } from 'src/emails/emails.service';
import { IEsimStock } from 'src/esim-importer/core/EsimCSVParser';
import { IEsimPurchaseRequestPayload } from 'src/esim-importer/core/IParserStratefy';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { apiIdWrapper, decryptAppStandard, decryptFromJWT } from 'src/utils';
import { BaseWorker } from './base.worker';

@Injectable()
export class BulkEsimPurchaseWorker extends BaseWorker {
  constructor(
    protected eventEmitter: EventEmitter2,
    protected emailService: EmailsService,
    protected appsService: AppsService,
    private configService: ConfigService,
    private esimStockService: EsimStocksService,
  ) {
    super(BULK_ESIM_PURCHASE, emailService, eventEmitter);
    this.workerConfig = {
      concurrency: 5,
      limiter: {
        duration: 5000,
        max: 10,
      },
    };
  }

  /**
   *
   * @param timestamp current timestamp
   * @param httpMethod http method used for calling api
   * @param pathAndQuery path and query excluding hostname of the api
   * @returns Signuature
   */
  private generateSignature({ stringToSign, secretKey }: any) {
    const base64Key = CryptoJS.enc.Base64.parse(secretKey + '');
    const hashedString = CryptoJS.enc.Utf8.parse(stringToSign);
    const hash = CryptoJS.HmacSHA256(hashedString, base64Key);
    return hash.toString(CryptoJS.enc.Base64);
  }

  private decodeCredentials(credentials: string) {
    return JSON.parse(
      decryptAppStandard(
        //@ts-expect-error
        decryptFromJWT(credentials).credentials,
      ),
    ) as unknown as {
      apiId: string;
      apiKey: string;
      requestSignatureKey: string;
    };
  }

  async process(
    job: Job<
      { payload: IEsimPurchaseRequestPayload; credentials: string },
      any,
      string
    >,
  ): Promise<any> {
    if (job.name === 'STOCK_ESIM') {
      const result = await this.esimStockService.addEsims(
        job.data as unknown as IEsimStock,
      );
      return result;
    }
    job.progress = 10;
    const credentials = this.decodeCredentials(job.data.credentials);

    const apiId = credentials.apiId;
    const apiKey = credentials.apiKey;
    const signatureKey = credentials.requestSignatureKey;

    try {
      this.logger.log(`processing order request ${JSON.stringify(job.data)}`);
      const instance = axios.create();
      instance.interceptors.request.use((config) => {
        config.headers['x-api-key'] = apiKey;
        config.headers['x-api-id'] = apiId;

        const currentDate = new Date();
        const timestamp = currentDate.valueOf(); // current timestamp (epoch)
        const url = new URL(config.baseURL + config.url);
        const httpMethod = config.method.toUpperCase();
        const pathAndQuery = url.pathname + url.search;
        const stringToSign = `${httpMethod} ${pathAndQuery}\n${timestamp}\n${apiId}\n${JSON.stringify(
          config.data,
        )}`;
        const signature = this.generateSignature({
          stringToSign,
          secretKey: signatureKey,
        });

        config.headers['x-gat-timestamp'] = timestamp;
        config.headers['x-gat-access-key'] = apiId;
        config.headers['x-gat-signature'] = signature;
        return config;
      });
      const response = await instance.post(
        `${this.configService.getOrThrow(
          'GLOBAL_ESIM_HOST',
        )}/api/v1/apps/order`,
        {
          ...job.data.payload,
          planId: job.data.payload.planId + '',
          chargeAmount: +job.data.payload.chargeAmount,
        },
        {
          validateStatus: (status) => status >= 200 && status <= 300,
        },
      );

      // No Error means it was proccess here.
      this.logger.log(
        `Order with topup id  ${response.data.data.topupId} and booking id ${job.data.payload.metadata.bookingNo} is now processed successfully, no further interaction needed.`,
      );
      job.progress = 100;
      return response.data;
    } catch (err: any) {
      this.logger.log(
        `Unable order with booking id  ${job.data.payload.metadata.bookingNo}, please try again.`,
      );
      this.logger.error(err.message, err);
      throw err?.response?.data?.message
        ? new Error(err?.response?.data?.message)
        : err;
    }
  }

  async onFailed(job: any, error: any): Promise<void> {
    try {
      Sentry.captureException(error);
      const credentials = this.decodeCredentials(job.data.credentials);
      const apps = await this.appsService.findOne(
        apiIdWrapper(credentials.apiId),
      );

      if (apps.emails && Array.isArray(apps.emails)) {
        const payload = job.data;
        delete payload.credentials;

        await this.emailService.sendEmail({
          bcc: [...apps.emails.slice(1)] as string[],
          message: `Error: ${job.failedReason} 
          <br/> Data: ${JSON.stringify(job.data)}`,
          subject: `Order import issue job #${job.id}`,
          to: apps.emails[0] as string,
        });
      }
    } catch (err) {
      this.logger.error(err);
      Sentry.captureException(error);
    }
  }
}
