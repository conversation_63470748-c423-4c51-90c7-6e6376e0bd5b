import { Global, Logger, Module } from '@nestjs/common';
import { QUEUE_NEW_ESIM, QUEUE_ESIM_CREATE_BUY } from 'src/constants';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { EsimService } from 'src/esim/esim.service';
import { PrismaService } from 'src/prisma.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { EmailsService } from 'src/emails/emails.service';
import { PlansService } from 'src/plans/plans.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { NewEsimWorker } from './new-esim.worker';
import { CouponsService } from 'src/coupons/coupons.service';
import { AppsService } from 'src/apps/apps.service';
import { JwtService } from '@nestjs/jwt';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { PaymentService } from 'src/payment/payment.service';
import { UsersService } from 'src/users/users.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Global()
@Module({
  imports: [],
  providers: [
    EsimOrdersService,
    PrismaService,
    EsimService,
    EsimProviderBuilder,
    Logger,
    EmailsService,
    EmailTemplateService,
    PlansService,
    XchangeService,
    KeyValueStoreService,
    NewEsimWorker,
    AppsService,
    CouponsService,
    JwtService,
    EsimStocksService,
    AwsCognitoService,
    PaymentService,
    UsersService,
    ReferralsService,
  ],
  controllers: [],
  exports: [NewEsimWorker],
})
export class EsimPurchaseQueueProcessorModule {}
