import { Job } from 'bullmq';
import { Inject, Injectable } from '@nestjs/common';
import { BaseWorker } from './base.worker';
import { EmailsService } from '../emails/emails.service';
import { UsersService } from '../users/users.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { QUEUE_LOYALTY_REMINDER } from 'src/constants';
import { PrismaService } from 'src/prisma.service';
import { subMinutes, subMonths } from 'date-fns';
import { CouponsService } from 'src/coupons/coupons.service';
import {
  COUPON_DISCOUNT_TYPE,
  EMAIL_STATUS,
  EMAIL_TYPE,
  users,
  coupons,
  coupons_orders,
  reminder_emails,
  orders,
  orders_loyalty_coupons,
} from '@prisma/client';
import { I18nService } from 'nestjs-i18n';
import { endOfUTCDay, startOfUTCDay } from 'src/utils';
import { ArrayUtils } from 'src/utils/ArrayUtils';
import { loyaltyReminder } from 'config/loyalty-reminder.config';
import { ConfigType } from '@nestjs/config';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';

type UsersThresholdMonth = Partial<
  users & { source: string } & {
    users_coupons: Array<{
      coupon: coupons & {
        coupons_orders: coupons_orders[];
        orders_loyalty_coupon: orders_loyalty_coupons & {
          order: orders;
        };
      };
    }>;
  }
>;

type PromiseWithUserContext = {
  promise: Promise<any>;
  context: {
    userId: number;
    couponId: number;
    emailType: EMAIL_TYPE;
    source: string;
  };
};

@Injectable()
export class LoyaltyReminderWorker extends BaseWorker {
  constructor(
    protected eventEmitter: EventEmitter2,
    protected emailService: EmailsService,
    protected usersService: UsersService,
    protected prismaService: PrismaService,
    protected couponService: CouponsService,
    protected i18nService: I18nService,
    @Inject(loyaltyReminder.KEY)
    private readonly loyaltyReminderConfig: ConfigType<typeof loyaltyReminder>,
  ) {
    super(QUEUE_LOYALTY_REMINDER, emailService, eventEmitter);
  }

  async process(
    job: Job<{ userId: number; couponCode: string; couponDiscount: number }>,
  ) {
    try {
      // Get user details
      const [usersThreeMonths, usersSixMonths] = await Promise.all([
        this.getUserWithLoyaltyCouponAtThresholdDate(3),
        this.getUserWithLoyaltyCouponAtThresholdDate(6),
      ]);

      const threeMonthsReminderEmail = await this.handleThresholdMonthsBatch(
        usersThreeMonths,
        'THREE_MONTHS',
      );

      const sixMonthsReminderEmail = await this.handleThresholdMonthsBatch(
        usersSixMonths,
        'SIX_MONTHS',
      );

      this.logger.log(
        `Reminder email worker for three months ${JSON.stringify(
          usersThreeMonths,
          null,
          4,
        )}`,
      );

      this.logger.log(
        `Reminder email worker for six months ${JSON.stringify(
          usersSixMonths,
          null,
          4,
        )}`,
      );

      return {
        '3_months': {
          succeeded: threeMonthsReminderEmail.succeeded,
          failed: threeMonthsReminderEmail.failed,
        },
        '6_months': {
          succeeded: sixMonthsReminderEmail.succeeded,
          failed: sixMonthsReminderEmail.failed,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to process loyalty reminder: ${error.message}`);
      throw error;
    }
  }

  async sendReminderEmail(
    user: UsersThresholdMonth,
    payload: {
      orderCount?: string;
      couponCode: string;
      couponDiscount: number;
      discountType: COUPON_DISCOUNT_TYPE;
      user: Partial<users>;
      order: orders;
    },
  ) {
    const template = await this.emailService.translateEmail({
      template: 'loyalty-reminder',
      ...payload,
      ...payload.order,
      language: payload.order?.lang || 'en',
      userSource: payload.order?.source || 'global-esim',
    });

    const subject = this.i18nService.t('loyalty-reminder.subject', {
      lang: payload.order?.lang || 'en',
      args: {
        couponDiscount:
          payload.discountType === 'PERCENTAGE'
            ? `${payload.couponDiscount}%`
            : payload.couponDiscount,
      },
    });

    return this.emailService.sendEmail({
      to: user.email,
      subject,
      content: template.data,
      lang: ['en', 'jp'].includes(payload.order?.lang)
        ? payload.order.lang
        : 'en',
      origin: [
        UserRegisterSource.GLOBAL_ESIM_JP,
        UserRegisterSource.GLOBAL_ESIM,
        UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
        UserRegisterSource.GLOBAL_ESIM_IOS_APP,
      ].includes(payload.order.source as UserRegisterSource)
        ? ServicePlansEnum.GLOBAL_ESIM_DEFAULT
        : ServicePlansEnum.GLOBAL_ESIM_AIRTRIP,
    });
  }

  async getUserWithLoyaltyCouponAtThresholdDate(monthsAgo: number) {
    const { gte, lte } = this.getThresholdRange(monthsAgo);

    return await this.prismaService.users.findMany({
      where: {
        users_coupons: {
          some: {
            enabled: true,
            coupon: {
              code: {
                startsWith: 'LY',
              },
              createdAt: {
                gte,
                lte,
              },
              orders_loyalty_coupon: {
                order: {},
              },
              coupons_orders: {
                every: {
                  state: {
                    not: 'COMPLETED', // Coupon doesn't exist in coupons_orders or coupon exists in order but none in the state COMPLETED
                  },
                },
              },
            },
          },
        },
      },
      select: {
        id: true,
        userId: true,
        firstName: true,
        lastName: true,
        email: true,
        source: true,
        userPool: true,
        locale: true,
        users_coupons: {
          where: {
            enabled: true,
            coupon: {
              code: {
                startsWith: 'LY',
              },
              createdAt: {
                gte,
                lte,
              },
              coupons_orders: {
                every: {
                  // Or `none: { state: 'COMPLETED' }`
                  state: { not: 'COMPLETED' },
                },
              },
            },
          },
          include: {
            coupon: {
              include: {
                coupons_orders: true,
                orders_loyalty_coupon: {
                  include: {
                    order: true,
                  },
                },
              },
            },
          },
        },
      },
    });
  }

  async handleThresholdMonthsBatch(
    userThresholdMonths: UsersThresholdMonth[],
    emailType: EMAIL_TYPE,
    batchSize = 10,
  ) {
    const allSucceeded: reminder_emails[] = [];
    const allFailed: reminder_emails[] = [];

    const userBatches = ArrayUtils.batchArray(userThresholdMonths, batchSize);

    // Create a flat array of all email operations
    const emailOperations: {
      user: UsersThresholdMonth;
      coupon: coupons;
      order: orders;
    }[] = [];

    for (const userBatch of userBatches) {
      const promisesWithContext: PromiseWithUserContext[] = [];

      for (const user of userBatch) {
        for (const userCoupon of user.users_coupons) {
          const order = userCoupon.coupon.orders_loyalty_coupon.order;
          emailOperations.push({
            user,
            coupon: userCoupon.coupon,
            order,
          });
        }
      }

      // Check existing emails in bulk
      const existingRecords = await this.prismaService.reminder_emails.findMany(
        {
          where: {
            OR: emailOperations.map((op) => ({
              userId: op.user.id,
              couponId: op.coupon.id,
              emailType,
              status: 'SUCCESS',
            })),
          },
        },
      );

      const existingMap = new Map(
        existingRecords.map((record) => [
          `${record.userId}-${record.couponId}-${record.emailType}`,
          record,
        ]),
      );

      const emailsToSend = emailOperations.filter((op) => {
        const key = `${op.user.id}-${op.coupon.id}-${emailType}`;
        const record = existingMap.get(key);
        return !record;
      });

      // Create the promise and bundle it with the context needed for logging later
      promisesWithContext.push(
        ...emailsToSend.map(({ user, coupon, order }) => ({
          promise: this.sendReminderEmail(user, {
            couponCode: coupon.code,
            couponDiscount: coupon.discount,
            discountType: coupon.type,
            user,
            order,
          }),
          context: {
            userId: user.id,
            couponId: coupon.id,
            emailType,
            source: order?.source || 'global-esim',
          },
        })),
      );

      if (ArrayUtils.isEmpty(promisesWithContext)) {
        continue; // Nothing to process in this batch
      }

      // Execute email sending in parallel for the batch
      const settledEmailResults = await Promise.allSettled(
        promisesWithContext.map((p) => p.promise),
      );

      // Prepare and execute database logging in parallel for the batch
      const dbWritePromises = settledEmailResults.map((result, index) => {
        const { context } = promisesWithContext[index];
        if (result.status === 'fulfilled') {
          return this.upsertReminderEmail(
            context.userId,
            context.couponId,
            context.emailType,
            EMAIL_STATUS.SUCCESS,
            context.source,
          );
        } else {
          this.logger.error(
            `Reminder email failed to sent for user ${context.userId}: ${result.reason?.message}, will be retired in next scheduler attempt`,
          );
          return this.upsertReminderEmail(
            context.userId,
            context.couponId,
            context.emailType,
            EMAIL_STATUS.FAILED,
            context.source,
            result.reason?.message || 'Unknown error',
          );
        }
      });

      const dbResults = await Promise.all(dbWritePromises);

      for (const record of dbResults) {
        if (record.status === 'SUCCESS') {
          allSucceeded.push(record);
        } else {
          allFailed.push(record);
        }
      }
    }

    return {
      succeeded: allSucceeded,
      failed: allFailed,
    };
  }

  private async upsertReminderEmail(
    userId: number,
    couponId: number,
    emailType: EMAIL_TYPE,
    status: EMAIL_STATUS,
    service: string,
    errorMessage?: string,
  ): Promise<reminder_emails> {
    return this.prismaService.reminder_emails.upsert({
      where: {
        userId_couponId_emailType: {
          userId,
          couponId,
          emailType,
        },
      },
      update: {
        status,
        service,
        retryCount: status === 'SUCCESS' ? 0 : { increment: 1 },
        errorMessage: errorMessage || null,
      },
      create: {
        userId,
        emailType,
        status,
        service,
        retryCount: status === 'FAILED' ? 1 : 0,
        couponId,
        errorMessage: errorMessage || null,
        reminderCount: emailType === 'THREE_MONTHS' ? 1 : 2, // Three months -> 1st reminder , six months -> 2nd reminder
      },
    });
  }

  private getThresholdRange(monthsAgo: number): { gte: Date; lte: Date } {
    const now = new Date();
    const isTest = this.loyaltyReminderConfig.testMode;

    if (isTest) {
      const minutesAgo =
        monthsAgo === 6
          ? this.loyaltyReminderConfig.thresholds.sixMonths.test
          : this.loyaltyReminderConfig.thresholds.threeMonths.test;

      const gte = subMinutes(now, minutesAgo);
      const lte = subMinutes(now, minutesAgo - 10); // 10 minutes window
      return { gte, lte };
    } else {
      const months =
        monthsAgo === 6
          ? this.loyaltyReminderConfig.thresholds.sixMonths.prod
          : this.loyaltyReminderConfig.thresholds.threeMonths.prod;

      const targetDate = subMonths(now, months);
      return {
        gte: startOfUTCDay(targetDate),
        lte: endOfUTCDay(targetDate),
      };
    }
  }
}
