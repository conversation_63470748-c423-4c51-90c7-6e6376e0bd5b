import { Injectable } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PAYMENT_STATUS } from '@prisma/client';
import axios from 'axios';
import { Job } from 'bullmq';
import { AppsService } from 'src/apps/apps.service';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { CURRENCY_LOCALE_MAP, QUEUE_ESIM_CREATE_BUY } from 'src/constants';
import { EmailsService } from 'src/emails/emails.service';
import { EsimPurchaseRequestDto } from 'src/esim-orders/dtos/esim-purchase-request.dto';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { NEW_SUCCESSFUL_ORDER } from 'src/esim-orders/events';
import { OrderUpdateEvent } from 'src/esim-orders/events/order-update.event';
import { IAppsMetaData } from 'src/interface/IAppsMetadata';
import { EsimWebhookDto } from 'src/payment/dtos/EsimWebhookDto';
import { PlansService } from 'src/plans/plans.service';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { priceFormatted } from 'src/utils';
import { BaseWorker } from './base.worker';

@Injectable()
export class EsimPurchaseQueueWorker extends BaseWorker {
  constructor(
    protected eventEmitter: EventEmitter2,
    protected emailService: EmailsService,
    protected appsService: AppsService,
    private esimOrderService: EsimOrdersService,
    private plansService: PlansService,
    private configService: ConfigService,
  ) {
    super(QUEUE_ESIM_CREATE_BUY, emailService, eventEmitter);
    this.workerConfig = {};
  }

  async process(
    job: Job<{ payload: EsimWebhookDto | EsimPurchaseRequestDto }, any, string>,
  ): Promise<any> {
    await ConfigModule.envVariablesLoaded;
    let payload = job.data.payload as EsimPurchaseRequestDto;
    this.logger.log(`Recieved job ${job.id}:${JSON.stringify(job.data)}`);
    job.log(`Received job ${job.id}: ${JSON.stringify(job.data)}`);
    if (!payload) {
      const [orderId, bookingNo, os, stripeChargeId] = job.id.split('-');
      const order = await this.esimOrderService.get({ orderId });
      if (!order) {
        job.log('No Order found');
        throw new Error('No Order found');
      }
      this.logger.log(
        `Job data lost issue, recovering from database now. ${job.id}`,
      );
      job.log(`Job data lost issue, recovering from database now. ${job.id}`);
      payload = {
        chargeAmount: order.jpyPrice + '',
        planId: order.planId + '',
        appsId: order.appsId,
        lang: order.lang,
        //@ts-expect-error
        metadata: order.orderMetaData || {},
        orderId: order.id,
        userId: order.userId,
        stripeChargeId: order.stripeLatestChargeId || stripeChargeId,
        paln: order.plan,
      };
    }
    this.logger.log('Starting esim purchase process');
    job.log('Starting esim purchase process');
    const response = await this.esimOrderService.initiateOrder({
      chargeAmount: payload.chargeAmount,
      planId: payload.planId,
      appsId: payload.appsId,
      lang: payload.lang,
      metadata: payload.metadata,
      plan: payload.plan,
      userId: payload.userId,
      orderId: payload.orderId,
      stripeChargeId: payload.stripeChargeId,
      iccid: payload.iccid,
    });
    job.log('Order initiated');
    // dont skip
    await this.newEsimWorker(
      response.orderResponse,
      response.order.id,
      response.order.appsId,
      response.order.userId,
      response.order.paymentMethod,
      job
    );
    job.log('eSIM worker completed');
    return response.orderResponse;
  }

  async newEsimWorker(orderResponse, orderId, appsId, userId, paymentMethod, job) {
    const [esim] = orderResponse?.products;

    const order = await this.esimOrderService.updateOrder(
      {
        activateCode: esim.activateCode || '',
        iccid: esim.iccid || '',
        qrCodeImgUrl: esim.qrcodeImgUrl || esim.qrCodeImgUrl || '',
        topupId: esim.topupId || '',
        downloadLink: esim.downloadLink || '',
        orderCreatedAt: new Date(),
        paymentStatus: appsId
          ? PAYMENT_STATUS.THIRD_PARTY_PURCHASE
          : paymentMethod === 'invoice'
            ? PAYMENT_STATUS.INVOICE_PAYMENT_PENDING
            : PAYMENT_STATUS.SUCCESS,

        response: {
          code: orderResponse?.code,
          message: orderResponse?.message,
          products: [
            {
              ...(orderResponse.products?.[0] || {}),
              smdp: esim.smdp,
            },
          ],
        },
      },
      {
        id: orderId,
      },
    );
    job.log(`Order updated in DB for orderId: ${orderId}`);
    if (appsId && order.apps.webhooks[0]) {
      try {
        await this.webhookExec(orderId, job);
        job.log('Webhook executed');
      } catch (err) {
        this.logger.error(err);
        job.log(`Webhook execution failed: ${err?.message}`);
      }
    }

    const customer = order.user || {
      //@ts-expect-error
      email: order?.orderMetaData?.email,
      //@ts-expect-error
      id: order?.orderMetaData?.bookingNo,
      //@ts-expect-error
      firstName: order?.orderMetaData?.firstName,
      //@ts-expect-error
      lastName: order?.orderMetaData?.lastName,
    };
    //@ts-expect-error
    if (order?.orderMetaData?.sendEmail || userId) {
      try {
        this.logger.log(
          `Calling API of confirmation email to ${order.user.email} for ${order.orderId}`,
        );
        job.log(`Sending confirmation email to ${order.user.email}`);
        // Write email send code here
        await this.emailSend(orderId, appsId, job);
        job.log('Confirmation email sent');
      } catch (err) {
        this.logger.error(err);
        job.log(`Email send failed: ${err?.message}`);
      }
    }
    // if (user && order) {
    //   await this.esimOrderService.addOrderNotificationEntry(user.id, order.id);
    // }

    this.logger.log(`After successful charge for order ${order.id}`);
    job.log(`After successful charge for order ${order.id}`);

    this.eventEmitter.emit(
      NEW_SUCCESSFUL_ORDER,
      new OrderUpdateEvent({
        order,
        orderInformation: {},
        //@ts-expect-error
        user: customer,
        plan: order.plan,
      }),
    );
    job.log('Order event emitted');
  }

  async webhookExec(orderId: number, job) {
    // const { orderId, appsId } = job.data;
    const order = await this.esimOrderService.get({ id: orderId });
    const apps = order.apps;

    const esim = {
      topupId: order.topupId,
      planId: order.planId,
      iccid: order.iccid,
      //@ts-expect-error
      smdp: order.response.products[0].smdp,
      activateCode: order.activateCode,
      downloadLink: order.downloadLink,
      qrCodeImgUrl: order.qrCodeImgUrl,
    };
    const responses = {};
    if (!apps) {
      job.log('Vendor not reachable.');
      throw new Error('Vendor not reachable.');
    }
    if (apps.webhooks && Array.isArray(apps.webhooks)) {
      const webhooks = apps.webhooks as string[];
      for (const webhook of webhooks) {
        const payload = {
          esim,
          transactionKey: AppsService.getTransactionKey(
            order.bookingNo,
            order.id + '',
          ),
          language: order.lang,
          appId: order.appsId,
          // jpyPrice: order.jpyPrice,
          bookingNo: order.bookingNo,
          orderId: order.orderId,
        };
        try {
          this.logger.log(`Sending a webhook request to ${webhook}}`);
          job.log(`Sending a webhook request to ${webhook}`);

          const response = await axios.post(webhook as string, payload);
          responses[webhook] = {
            response: response.data,
            payload,
          };
          this.logger.log(`Sent a webhook request to ${webhook}}`);
          job.log(`Sent a webhook request to ${webhook}`);
        } catch (err) {
          this.logger.error(
            `Failed executing webhook for ${webhook} in job   ${JSON.stringify(
              payload,
            )}`,
          );
          job.log(`Failed executing webhook for ${webhook}: ${err?.message}`);
          throw new Error(
            err?.message + ' ' + JSON.stringify(err?.response?.data || {}),
          );
        }
      }
      return responses;
    }
  }

  async emailSend(orderId, appsId, job?) {
    const order = await this.esimOrderService.get({ id: orderId });
    this.logger.log(
      `Sending confirmation email to ${order.user.email} for ${order.orderId}`,
    );
    if (job) job.log(`Sending confirmation email to ${order.user.email} for ${order.orderId}`);
    const { plan, user, apps } = order;
    const appsMetadata: IAppsMetaData = (apps?.metadata as Object) || undefined;
    const customer = user || {
      //@ts-expect-error
      email: order.orderMetaData.email,
      //@ts-expect-error
      id: order.orderMetaData.bookingNo,
      //@ts-expect-error
      firstName: order.orderMetaData.firstName,
      //@ts-expect-error
      lastName: order.orderMetaData.lastName,
    };
    const isSpecialAPN =
      this.plansService.checkIsSpecialAPNPlan(plan) &&
      order?.plan?.country?.name.toLowerCase?.() === 'japan';

    // const isSpecialAPN =
    // [2, 3, 4, 5].includes(plan.validityDays) &&
    // order.plan.name === 'unlimited' &&
    // order?.plan?.country?.name.toLowerCase?.() === 'japan';

    const orderInformation = {
      isLGU: order.plan?.serviceProvider?.name?.includes('LGU'),
      thirdPartyApp: order.appsId,
      topupId: order.topupId,
      isNeedCmlink: ['hongkong', 'taiwan'].includes(plan?.country?.name),
      language: order.lang,
      esimPlan: plan.packageType,
      orderId: order.orderId,
      dataSize: plan.dataVolume + plan.dataUnit,
      iccid: order.iccid,
      dataPlan: plan.dataId,
      apn: plan.network.apn,
      packageType: plan.packageType,
      isPerday: plan.packageType === 'PER_DAY',
      isFixedPlan: plan.packageType === 'FIXED_DAY',
      priceJPY: order.jpyPrice,
      countryName: plan?.country?.name,
      usageDays: plan.name,
      //@ts-expect-error
      qrCode: order.qrcodeImgUrl || order.qrCodeImgUrl || '',
      //@ts-expect-error
      smdp: order.response.products?.[0].smdp,
      activateCode: order.activateCode,
      downloadLink: order.downloadLink,
      formattedPrice: priceFormatted(
        CURRENCY_LOCALE_MAP[order.currency || 'JPY'],
        order?.currency || 'JPY',
        order.finalPrice || order.jpyPrice,
      ),
      feHost: this.configService.getOrThrow('FRONTEND_HOST'),
      qrCodeFormatted: encodeURIComponent(
        //@ts-expect-error
        order.qrcodeImgUrl || order.qrCodeImgUrl || '',
      ),
      usageDayCount: {
        count: plan.validityDays,
      },
      email: customer.email,
      userSource: order?.user?.source,
      //@tood make this dynamic from database
      esimExpireCount:
        order?.plan?.country?.name.toLowerCase?.() === 'korea' ? 90 : 30,
      template:
        appsMetadata?.template ||
        //@ts-expect-error
        order.orderMetaData?.template ||
        'order-confirm-layout',
      isSpecialAPNPlan: isSpecialAPN,
      isRegularAPNPlan: !isSpecialAPN,
    };

    try {
      const translatedMailResponse = await axios.post(
        `http://127.0.0.1:${this.configService.getOrThrow('APP_PORT') || 3001
        }/api/v1/payment/esim/mail/translate`,
        orderInformation,
      );
      // i18n.t('order-email.your-esim-confirmed', {
      //   args: {
      //     orderId: payload.orderId,
      //   },
      // })
      /**
       * This is very temporary solution
       */
      let enSubject = `Your eSIM order confirmed ${appsMetadata?.isHideSubjectOrderId ? '' : order.orderId
        }`;
      let jpSubject = `【${order?.user?.source === 'airtrip' ? 'エアトリeSIM' : 'グロモバeSIM'
        }】ご注文ありがとうございます ${appsMetadata?.isHideSubjectOrderId ? '' : order.orderId
        }`;

      if (
        appsMetadata?.subject === 'order-email.airtrip.campaign-subject' ||
        appsMetadata?.template === 'airtrip-campaign-layout'
      ) {
        enSubject =
          '[AirTrip eSIM] eSIM gift! Thank you for answering the survey.';
        jpSubject =
          '【エアトリeSIM】eSIMプレゼント！アンケートのご回答ありがとうございます。';
      } else if (
        // TODO: update these values base on GM campaign apps keys
        //@ts-expect-error
        order.orderMetaData.template === 'gm-esim-campaign-layout'
      ) {
        enSubject =
          '[グロモバ eSIM] eSIM gift! Thank you for answering the survey.';
        jpSubject = `${order?.user?.source === 'airtrip'
          ? '【エアトリeSIM】'
          : '【グロモバeSIM】'
          } eSIMプレゼント！会員登録ありがとうございます。`;
      }

      const orderConfirmSubject = {
        en: enSubject,
        jp: jpSubject,
      };
      const lang = Object.keys(orderConfirmSubject).includes(
        orderInformation.language,
      )
        ? orderInformation.language
        : 'en';
      const response = await this.emailService.sendEmail({
        to: customer.email,
        message: translatedMailResponse.data,
        subject: orderConfirmSubject[lang],
        lang,
        //@ts-expect-error
        origin:
          user.userPool === LocalUserPool.AIRTRIP ||
            user.source === UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP ||
            user.source === UserRegisterSource.AIRTRIP_ESIM_IOS_APP
            ? 'GLOBAL_ESIM_AIRTRIP'
            : 'GLOBAL_ESIM_DEFAULT',
      });
      this.logger.log(response);
      this.logger.log(
        `Order confirmation email dispatched to ${customer.email} for order number ${orderId}`,
      );
      if (job) job.log(`Order confirmation email dispatched to ${customer.email} for order number ${orderId}`);
      return null;
    } catch (err) {
      this.logger.error(
        'Error while sending email to order',
        orderId,
        err?.message,
      );
      this.logger.error(err?.message);
      if (job) job.log(`Error while sending email to order ${orderId}: ${err?.message}`);
      throw err;
    }
  }
}
