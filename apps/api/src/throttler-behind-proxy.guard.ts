import { Injectable, Logger } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

@Injectable()
export class ProxyAwareThrottlerGuard extends ThrottlerGuard {
  private readonly logger = new Logger(ProxyAwareThrottlerGuard.name);

  protected getTracker(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'] as string;
    //@ts-expect-error
    const ip = forwarded ? forwarded.split(',')[0] : req.socket.remoteAddress;
    return ip;
  }


}
