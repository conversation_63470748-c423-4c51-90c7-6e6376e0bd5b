import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class KeyValueStoreService {
  constructor(private prismaService: PrismaService) {}

  set(key: string, value: any) {
    return this.prismaService.kvStore.upsert({
      where: {
        key,
      },
      create: {
        key,
        value,
      },
      update: { key, value },
    });
  }

  get(key: string) {
    return this.prismaService.kvStore.findFirst({
      where: {
        key,
      },
    });
  }
}
