import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Get,
  Inject,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UnprocessableEntityException,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExcludeEndpoint,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { Recaptcha } from '@nestlab/google-recaptcha';
import * as Sentry from '@sentry/nestjs';
import { Cache } from 'cache-manager';
import { Request, Response } from 'express';
import { isUndefined, omit, omitBy, pick } from 'lodash';
import { I18n, I18nContext } from 'nestjs-i18n';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { GetUser } from 'src/auth/get-user.decorator';
import { GetUserPool } from 'src/auth/get-userpool';
import {
  FIVE_MIN_IN_MILLISECONDS,
  INTENT_TYPE,
  PAYMENT_STATUS,
} from 'src/constants';
import { CouponsService } from 'src/coupons/coupons.service';
import { ContentConflictError } from 'src/errors/ContentConflict';
import { PriceCalculator } from 'src/esim-orders/PriceCalculator';
import {
  BulkOrdersDto,
  BulkOrdersGuestDto,
  PaymentMethodType,
} from 'src/esim-orders/dtos/BulkOrdersDto';
import { EsimOrderDto } from 'src/esim-orders/dtos/esim-order-dto';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { IOrderMetadata } from 'src/esim-orders/interfaces/IOrderMetadata';
import { IStripeMetadata } from 'src/interface/IStripeMetadata';
import { IUser } from 'src/interface/IUser';
import { PaymentService } from 'src/payment/payment.service';
import { PdfService } from 'src/pdf/pdf.service';
import { PlansQueryDto } from 'src/plans/dtos/plans-query.dto';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';
import { PlansService } from 'src/plans/plans.service';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { UsersService } from 'src/users/users.service';
import { getFormmattedOrderId, getPlanDetailsApi } from 'src/utils';
import {
  SubscribeOrderDTO,
  SubscribeOrderGuestDTO,
} from './dto/SubscribeOrderDTO';
import { UsageDto } from './dto/UsageDto';
import {
  EsimOrderResponseDto,
  SingleEsimOrderResponseDto,
} from './dto/esim-order-response.dto';
import { EsimUsageDto } from './dto/esim-usage.dto';
import { InstantEsimDto } from './dto/instant-esim.dto';
import { SubscribeResponseDto } from './dto/subscribe-response.dto';
import { EsimService } from './esim.service';
import { ESIM_PROVIDER } from './providers/EsimProvider';

@Controller('esim')
@ApiTags('Esim Purchase, orders, details')
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class EsimController {
  constructor(
    private esimService: EsimService,
    private esimOrderService: EsimOrdersService,
    private paymentService: PaymentService,
    private userService: UsersService,
    private plansService: PlansService,
    private couponService: CouponsService,
    private logger: Logger,
    private awsCognitoService: AwsCognitoService,
    private pdfService: PdfService,
    private readonly jwtService: JwtService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  @Post('/corporate/:corporateCode/:corporateId/subscribe')
  @UsePipes(ValidationPipe)
  @ApiOkResponse({ status: 201, type: SubscribeResponseDto })
  @ApiNotFoundResponse({ description: 'Invalid plan' })
  @ApiBadRequestResponse({ description: 'Invalid plan id' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  public async corporateSubscribe(
    @Body() payload: SubscribeOrderDTO,
    @GetUser() user: IUser,
    @Req() req: Request,
    @I18n() i18n: I18nContext,
    @Param('corporateCode') corporateCode: string,
    @Param('corporateId') corporateId: string,
  ) {
    try {
      if (isNaN(+payload.products.planId)) {
        throw new BadRequestException('Invalid plan id');
      }
      const localUser = await this.userService.getUserInfo(user.userId);
      if (!localUser) throw new BadRequestException('No user found');
      const userCoporate = localUser.corporates_users[0].corporate;

      if (
        !userCoporate ||
        userCoporate?.code !== corporateCode ||
        +corporateId !== userCoporate?.id
      ) {
        throw new UnauthorizedException();
      }

      const plan = await this.plansService.getPlan(+payload.products.planId);
      if (!plan || !plan.enabled) {
        throw new NotFoundException('Invalid plan');
      }
      const app =
        localUser.corporates_users?.[0].corporate?.corporate_apps?.[0];
      if (!app) {
        throw new UnauthorizedException('App not found.');
      }
      const order = await this.esimOrderService.createOrderRecord(
        {
          planId: +payload.products.planId,
          metadata: payload.metadata || {},
          appsId: app?.id,
          userId: +user.appId,
          provisionPrice: plan.provision_price,
        },
        { ignoreConfilict: true },
      );
      if (order instanceof ContentConflictError) {
        throw new ConflictException();
      }
      const planWithXE = await this.plansService.getPlanById(
        +payload.products.planId,
      );
      await this.esimOrderService.queueESIMPurchaseRequest({
        appsId: app.id,
        planId: payload.products.planId,
        lang: payload.locale,
        chargeAmount: planWithXE.xe['JPY'] + '',
        metadata: payload.metadata || {},
        orderId: order.id,
      });
      return {
        orderId: getFormmattedOrderId(order.id),
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  /**
   * @deprecated
   * @param payload
   * @param request
   * @param i18n
   * @param userpool
   * @param query
   * @returns
   */
  @Post('/subscribe/guest')
  @Recaptcha({ response: (req) => req.body.recaptcha })
  @UsePipes(ValidationPipe)
  @ApiOkResponse({ status: 201, type: SubscribeResponseDto })
  @ApiNotFoundResponse({ description: 'Invalid plan' })
  @ApiBadRequestResponse({ description: 'Invalid plan id' })
  public async subscribeGuest(
    @Body() payload: SubscribeOrderGuestDTO,
    @Req() request: Request,
    @I18n() i18n: I18nContext,
    @GetUserPool() userpool: LocalUserPool,
    @Query()
    query: Pick<
      PlansQueryDto,
      'requestOriginServiceName' | 'requestOriginServiceNameOnly'
    >,
  ) {
    const requestOriginServiceName =
      query.requestOriginServiceName ||
      request.headers?.['x-service-name'] ||
      undefined;

    try {
      const response = await this.create(
        payload,
        null,
        request,
        i18n,
        userpool,
        requestOriginServiceName as ServicePlansEnum,
        request.headers?.['x-service-name'] as ServicePlansEnum,
      );
      return response;
    } catch (err) {
      this.logger.error(err);
      this.logger.error(
        `Purchase issue ${JSON.stringify({ payload, query, userpool })}`,
      );
      throw new BadRequestException(err.message);
    }
  }

  /**
   * @deprecated
   * @param payload
   * @param user
   * @param request
   * @param i18n
   * @param userpool
   * @param query
   * @returns
   */
  @Post('/subscribe')
  @Recaptcha({ response: (req) => req.body.recaptcha })
  @UsePipes(ValidationPipe)
  @ApiOkResponse({ status: 201, type: SubscribeResponseDto })
  @ApiNotFoundResponse({ description: 'Invalid plan' })
  @ApiBadRequestResponse({ description: 'Invalid plan id' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  public async subscribe(
    @Body() payload: SubscribeOrderDTO,
    @GetUser() user: IUser,
    @Req() request: Request,
    @I18n() i18n: I18nContext,
    @GetUserPool() userpool: LocalUserPool,
    @Query()
    query: Pick<
      PlansQueryDto,
      'requestOriginServiceName' | 'requestOriginServiceNameOnly'
    >,
  ) {
    const requestOriginServiceName =
      query.requestOriginServiceName ||
      request.headers?.['x-service-name'] ||
      undefined;
    try {
      const response = await this.create(
        payload,
        user,
        request,
        i18n,
        userpool,
        requestOriginServiceName as ServicePlansEnum,
      );
      return response;
    } catch (err) {
      this.logger.error(
        `Purchase issue ${JSON.stringify({ payload, query, userpool })}`,
      );
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Post('/order')
  @UsePipes(ValidationPipe)
  @Throttle(1500, 60000)
  @UseGuards(AuthGuard())
  public async subscribeNew(
    @Body() payload: BulkOrdersDto,
    @GetUser() user: IUser,
    @Req() request: Request,
    @I18n() i18n: I18nContext,
    @GetUserPool() userpool: LocalUserPool,
    @Query()
    query: Pick<
      PlansQueryDto,
      'requestOriginServiceName' | 'requestOriginServiceNameOnly'
    >,
  ) {
    try {
      const requestOriginServiceName =
        query.requestOriginServiceName ||
        request.headers?.['x-service-name'] ||
        undefined;
      const response = await this.createEsimOrder(
        payload,
        {
          userId: user?.userId,
          lang: i18n.lang,
        },
        userpool,
        requestOriginServiceName as ServicePlansEnum,
        request.headers?.['x-service-name'] as ServicePlansEnum,
      );
      return response;
    } catch (err) {
      Sentry.captureException(err);
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Post('/order/guest')
  @UsePipes(ValidationPipe)
  @Throttle(1500, 60000)
  @Recaptcha({ response: (req) => req.body.recaptha })
  public async subscribeNewGuest(
    @Body() payload: BulkOrdersGuestDto,
    @GetUser() user: IUser,
    @I18n() i18n: I18nContext,
    @Req() request: Request,
    @GetUserPool() userpool: LocalUserPool,
    @Query()
    query: Pick<
      PlansQueryDto,
      'requestOriginServiceName' | 'requestOriginServiceNameOnly'
    >,
  ) {
    try {
      const requestOriginServiceName =
        query.requestOriginServiceName ||
        request.headers?.['x-service-name'] ||
        undefined;
      const response = await this.createEsimOrder(
        payload,
        {
          userId: user?.userId,
          lang: payload.locale,
        },
        userpool,
        requestOriginServiceName as ServicePlansEnum,
        request.headers?.['x-service-name'] as ServicePlansEnum,
      );
      return response;
    } catch (err) {
      Sentry.captureException(err);
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  /**
   * @deprecated
   * @param payload
   * @param user
   * @param req
   * @param i18n
   * @param userpool
   * @param requestOriginServiceName
   * @returns
   */
  async create(
    payload: SubscribeOrderDTO & SubscribeOrderGuestDTO,
    user: IUser | null,
    req: Request,
    i18n: I18nContext,
    userpool: LocalUserPool,
    requestOriginServiceName: ServicePlansEnum,
    fallbackRequestOriginServiceName?: ServicePlansEnum,
  ) {
    try {
      let couponDetails;
      let localUser = user
        ? await this.userService.getUserInfo(user.userId, userpool)
        : null;

      if (!localUser) {
        if (!payload?.email) {
          throw new Error(`Unable to proceed, please log in to continue.`);
        }
        try {
          const user = await this.awsCognitoService.registerUser(
            {
              referral: payload.referral,
              email: payload.email,
              firstName: payload.firstName || 'Guest',
              lastName: payload.lastName || 'Guest',
              locale: payload.locale || i18n.lang || 'en',
              password: Date.now() + '-auto',
              source: payload.source,
              profileImage: 'Guest',
            },
            userpool,
          );
        } catch (err) {
          // Email already with us but user havent logged in
          this.logger.error(err.message);
          this.logger.log(
            `${payload.email} is old customer who is already registered but is not logged in.`,
          );
        } finally {
          // Ignore corporate users
          //@ts-expect-error
          localUser = await this.userService.getUserByEmail(
            payload.email,
            userpool,
          );
        }
      }
      if (!localUser) {
        throw new Error(
          `We couldn't associate this request with any user, please log in to continue.`,
        );
      }

      let stripeId = localUser.stripeId;

      if (payload.source) {
        stripeId = await this.paymentService.getStripeIdBySource(
          payload.source as UserRegisterSource,
          localUser,
        );
        if (!stripeId) {
          stripeId = (
            await this.paymentService.createStripeCustomerWithSource(
              payload.source as UserRegisterSource,
              localUser,
            )
          ).id;
        }
      }

      localUser = {
        ...localUser,
        stripeId,
      };

      if (isNaN(+payload.products.planId)) {
        throw new BadRequestException('Invalid plan id');
      }

      // const plan = await this.plansService.getPlan(+payload.products.planId);
      const plan = await this.plansService.getPlanById(
        +payload.products.planId,
        {
          serviceProvider: {
            enabled: undefined,
          },
          includeProvisionPrice: true,
          requestOriginServiceName: requestOriginServiceName,
          fallbackRequestOriginServiceName: fallbackRequestOriginServiceName,
        },
      );
      if (!plan || !plan.enabled) {
        throw new NotFoundException('Invalid plan');
      }

      const order = await this.esimOrderService.saveOrder(
        localUser.id,
        [
          {
            qty: 1,
            optionId: plan.planId,
          },
        ],
        {},
        {
          ip: req.ip,
          piId: String(Date.now()),
          paymentStatus: PAYMENT_STATUS.PENDING,
          type: INTENT_TYPE.SUBSCRIBE,
          planId: plan.id,
          source: payload.source,
        },
      );
      let planFinalPrice = plan.prices?.[plan?.defaultCurrency] || plan.price;
      const priceCalculator = new PriceCalculator(planFinalPrice, {
        calculateTaxOnBasePrice: false,
      });

      if (payload.couponId) {
        try {
          const reference = await this.couponService.redeem(
            payload.couponId,
            localUser.userId,
            plan,
          );
          couponDetails = reference;
          payload.couponReferenceId = reference.referenceId;
        } catch (err) {
          if (!err.message.includes('CountrySpecificCoupon')) throw err;
        }
      }

      if (payload.couponReferenceId) {
        const couponReference = await this.couponService.getCouponByReference(
          payload.couponReferenceId,
        );

        if (couponReference) {
          try {
            const coupon = await this.couponService.validate(
              Object.assign({}, couponReference.coupon),
              localUser.userId,
              plan,
              {
                source: payload.source,
              },
            );

            if (coupon) {
              priceCalculator.applyDiscount({
                discount: couponReference.coupon.discount,
                type: couponReference.coupon.type,
              });
            }
            if (couponReference) {
              await this.couponService.updateCouponReference(
                couponReference.id,
                {
                  state: planFinalPrice > 0 ? 'PROCESSING' : 'COMPLETED',
                },
              );
            }
          } catch (err) {
            this.logger.log('Unable to apply code', err.message);
            this.logger.log(err);
            if (!err.message.includes('CountrySpecificCoupon'))
              throw new Error(`Problem with coupon code. ${err.message}`);
          }
        }
      }

      planFinalPrice = parseFloat(
        priceCalculator.calculateFinalPrice().toFixed(2),
      );

      let paymentResponse;
      if (planFinalPrice > 0) {
        paymentResponse = await this.paymentService.createPaymentIntent(
          payload.products.planId,
          localUser.stripeId,
          {
            payment_method: payload.paymentMethod,
            automatic_payment_methods: { enabled: true },
            metadata: {
              orderId: order.id,
              type: INTENT_TYPE.SUBSCRIBE,
              userId: localUser.userId,
              dataPlan: plan.dataId,
              usageDays: plan.name,
              email: localUser.email,
              language: payload.locale || i18n.lang || localUser.locale || 'en',
              sessionSecret: payload.sessionSecret || '',
              couponReferenceId: payload.couponReferenceId || 'N/A',
              discount: couponDetails ? couponDetails.discount : 'N/A',
              discountType: couponDetails?.type || 'N/A',
            } as IStripeMetadata,
          },
          {
            ...plan,
            price: planFinalPrice,
          },
          {
            ...localUser,
            source: payload.source ? payload.source : localUser.source,
          },
        );
        if (!paymentResponse.id) throw new InternalServerErrorException();
      }
      this.logger.log('Payment intent created for: ' + localUser.email);
      //Save the data only when code is 0000
      const metadata = payload.metadata || {};
      const updatedOrder = await this.esimOrderService.updateOrder(
        {
          provisionPrice: plan.provision_price,
          price: planFinalPrice,
          pi_id: paymentResponse?.id,
          jpyPrice: paymentResponse?.amount || 0,
          orderId: getFormmattedOrderId(order.id),
          //@ts-ignore
          lang: payload.locale || i18n.lang || 'en',
          orderMetaData: omitBy(
            {
              firstName: metadata.firstName || localUser.firstName,
              lastName: metadata.lastName || localUser.lastName,
              email: metadata.email || localUser.email,
              bookingNo: getFormmattedOrderId(order.id),
              //@ts-expect-error
              dob: metadata.dob || metadata.birthdate,
              phone: metadata.phone,
              gender: metadata.gender,
              passportNo: metadata.passportNo,
              channelName: metadata.channelName,
              //@ts-expect-error
              countryCode: metadata.country || metadata.countryCode,
            },
            isUndefined,
          ),
        },
        {
          id: order.id,
        },
      );

      this.logger.log('Pending order created for ' + localUser.email);

      let secrentAccessToken = null;
      try {
        secrentAccessToken = this.jwtService.sign(
          {
            orderId: updatedOrder.orderId,
          },
          {
            secret: EsimService.getInstantEsimSecret(),
            expiresIn: '1h',
          },
        );
      } catch {}
      if (planFinalPrice < 1) {
        await this.esimOrderService.queueESIMPurchaseRequest({
          planId: updatedOrder.planId + '',
          lang: updatedOrder.lang,
          chargeAmount: updatedOrder.jpyPrice + '',
          metadata: (updatedOrder.orderMetaData || {}) as IOrderMetadata,
          orderId: updatedOrder.id,
          planUrl: getPlanDetailsApi(updatedOrder.planId + ''),
          stripeChargeId: '',
        });
      }

      return {
        orderId: getFormmattedOrderId(order.id),
        instantEsimSecret: secrentAccessToken,

        //Sent from usimsa
        // order: orderResponse,

        //Sent from Stripe
        payment: pick(paymentResponse, ['client_secret', 'amount']),
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Post()
  @ApiExcludeEndpoint()
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  async updateUsageTest(@Body() usage: any) {
    return this.cacheManager.set('usage' + usage.topup.topupId, usage);
  }

  @Post('/usage')
  @UsePipes(ValidationPipe)
  @ApiOkResponse({
    type: EsimUsageDto,
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  public async usage(
    @Body() usage: UsageDto,
    @GetUser() user: IUser,
    @Query('force') force: string,
  ) {
    try {
      if (!force) {
        const cached = await this.cacheManager.get(
          `${usage.topupid}-${user.appId}`,
        );

        if (cached) return cached;
      }

      const esim = await this.esimOrderService.get({
        userId: +user.appId,
        topupId: usage.topupid,
      });

      if (!esim) throw new UnauthorizedException();
      usage.externalBookingNo = esim.externalBookingNo;
      const usageResult = await this.esimService.usage(
        usage,
        esim.plan.serviceProvider.name as ESIM_PROVIDER,
      );

      if (usageResult) {
        usageResult.lastCheck = Date.now();
        usageResult.nextCheck = Date.now() + FIVE_MIN_IN_MILLISECONDS;
      }
      this.cacheManager.set(
        `${usage.topupid}-${user.appId}`,
        usageResult,
        FIVE_MIN_IN_MILLISECONDS,
      );
      return usageResult;
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Post('/usageByTopup')
  @UsePipes(ValidationPipe)
  @ApiOkResponse({
    type: EsimUsageDto,
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  public async usageByTopup(@Body() usage: UsageDto, @GetUser() user: IUser) {
    try {
      const usageResult = await this.esimService.usage(
        usage,
        ESIM_PROVIDER.UROCOMM,
      );
      this.cacheManager.set(
        `${usage.topupid}-${user.appId}`,
        usageResult,
        FIVE_MIN_IN_MILLISECONDS,
      );
      return usageResult;
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Get('/query/:orderId')
  @UsePipes(ValidationPipe)
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  public async query(
    @Param() params: { orderId: string },
    @GetUser() user: IUser,
  ) {
    const esim = await this.esimOrderService.get({
      userId: +user.appId,
      orderId: params.orderId,
    });
    if (!esim) throw new UnauthorizedException();
    return this.esimService.query(
      params.orderId,
      esim.plan.serviceProvider.name as ESIM_PROVIDER,
    );
  }

  @Get('/orders')
  @ApiOkResponse({
    type: EsimOrderResponseDto,
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  public async indexOrders(
    @GetUser() user: IUser,
    @Query() requestParams?: EsimOrderDto,
  ) {
    const { status, ...query } = requestParams;

    try {
      const { page, search, limit, sort_by, booked_date, ...filters } = query;
      const orderBy = {};
      if (sort_by) {
        const [column, sort] = sort_by.split(':');
        orderBy[column] = sort || 'asc';
      }
      const items = await this.esimOrderService.index(
        {
          jpyPrice: query.jpy_price,
          orderId: query.order_id,
          activateCode: query.activate_code,
          createdAt: query.created_at,
          iccid: query.iccid,
          userId: user.appId,
          updatedAt: query.updated_at,
          orderCreatedAt: query.order_created_at,
          planId: +query.plan_id || undefined,
          topupId: query.topup_id,
          type: query.type || 'SUBSCRIBE',
          paymentStatus: query.payment_status,
        },
        {
          page: Number(page || 1),
          sort: orderBy,
          search,
          limit: +limit,
          bookedDate: booked_date,
          status: status,
          removeUser: true,
        },
      );

      return {
        total: items.total,
        data: items.data?.map((item) =>
          omit(
            {
              status,
              ...item,
              stripeLatestChargeId: null,
              provisionPrice: null,
              price: null,
              response: null,
              topupId: null,
              orders: null,
              plan: {
                ...item.plan,
                provision_price: null,
                price: null,
                pi_id: null,
              },
            },
            'jpyPrice',
            'jpyExchangeUSD',
            'externalBookingNo',
            'countBatchLGUPlus',
            'orderMetaData',
            'stripeLatestChargeId',
          ),
        ),
      };
    } catch (err) {
      throw new UnprocessableEntityException();
    }
  }

  @Get('/orders/:orderId')
  @ApiOkResponse({
    type: SingleEsimOrderResponseDto,
  })
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  public async orderDetails(
    @Query() query: { type: 'usage' },
    @Param() params: { orderId: string },
    @GetUser() user: IUser,
  ) {
    const result = await this.esimOrderService.orderDetailAndUsage(
      params.orderId,
      {
        ...query,
        userId: user.appId,
      },
    );
    if (!result) throw new NotFoundException();
    return {
      ...result,
      order: omit(
        {
          ...result.order,
          user: null,
          pi_id: null,
          plan: {
            ...result.order.plan,
            provision_price: null,
          },
          stripeLatestChargeId: null,
        },
        'jpyPrice',
        'jpyExchangeUSD',
        'externalBookingNo',
        'countBatchLGUPlus',
        'orderMetaData',
        'stripeLatestChargeId',
      ),
    };
  }

  @Post('/order/quotation')
  @UsePipes(ValidationPipe)
  public async orderQuotation(
    @Body() payload: Pick<BulkOrdersDto, 'products'>,
    @Req() req: Request,
    @Query()
    query: Pick<
      PlansQueryDto,
      'requestOriginServiceName' | 'requestOriginServiceNameOnly'
    >,
    @Req() request: Request,
  ) {
    const requestOriginServiceName =
      query.requestOriginServiceName ||
      request.headers?.['x-service-name'] ||
      undefined;

    try {
      const response = await this.esimOrderService.createOrderQuotation(
        payload,
        {
          ignoreValidation: !true,
          requestOriginServiceName:
            requestOriginServiceName as ServicePlansEnum,
          fallbackRequestOriginServiceName: request.headers?.[
            'x-service-name'
          ] as ServicePlansEnum,
        },
      );

      return response?.quotation;
    } catch (err) {
      this.logger.error(err);
      this.logger.error(
        `Quotation issue ${JSON.stringify({ payload, query })}`,
      );
      throw new BadRequestException();
    }
  }

  @Post('/order/quotation-new')
  @Throttle(500, 60000)
  @UsePipes(ValidationPipe)
  public async orderQuotationNew(
    @Body() payload: Pick<BulkOrdersDto, 'products'>,
    @Req() req: Request,
    @Query()
    query: Pick<
      PlansQueryDto,
      'requestOriginServiceName' | 'requestOriginServiceNameOnly'
    >,
    @Req() request: Request,
  ) {
    const requestOriginServiceName =
      query.requestOriginServiceName ||
      request.headers?.['x-service-name'] ||
      undefined;

    try {
      const response = await this.esimOrderService.createOrderQuotationNew(
        payload,
        {
          ignoreValidation: false,
          requestOriginServiceName:
            requestOriginServiceName as ServicePlansEnum,
          fallbackRequestOriginServiceName: request.headers?.[
            'x-service-name'
          ] as ServicePlansEnum,
        },
      );

      return response?.quotation;
    } catch (err) {
      this.logger.error(err);
      this.logger.error(
        `Quotation issue ${JSON.stringify({ payload, query })}`,
      );
      throw new BadRequestException();
    }
  }

  /**
   * @deprecated
   * @param body
   * @returns
   */
  @Post('/instant-esim')
  @UsePipes(ValidationPipe)
  async getInstantEsim(@Body() body: InstantEsimDto) {
    try {
      const response = await this.esimOrderService.instantEsim(
        body.secret,
        body.orderId,
      );
      this.logger.log(
        `Instant esim log ${body.orderId} and ${
          response.qrCodeImgUrl ? 'served' : 'not served'
        }`,
      );

      return {
        order: response.order,
        qrCodeImgUrl: response.qrCodeImgUrl,
      };
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }

  private async createEsimOrder(
    orderDto: BulkOrdersDto & BulkOrdersGuestDto,
    params: { userId?: string; lang: string },
    userpool: LocalUserPool,
    requestOriginServiceName: ServicePlansEnum,
    fallbackRequestOriginServiceName: ServicePlansEnum,
  ) {
    let user = await this.awsCognitoService.createOrGetUser(
      {
        firstName: orderDto.firstName,
        lastName: orderDto.lastName,
        referral: orderDto.referral,
        email: orderDto.email,
        locale: params.lang,
        password: Date.now() + '',
        source: orderDto.source,
      },
      params.userId,
      userpool,
    );
    let stripeId = user.stripeId;
    if (orderDto.source) {
      stripeId = await this.paymentService.getStripeIdBySource(
        orderDto.source as UserRegisterSource,
        user,
      );
      if (!stripeId) {
        stripeId = (
          await this.paymentService.createStripeCustomerWithSource(
            orderDto.source as UserRegisterSource,
            user,
          )
        )?.id;
      }
    }
    user = {
      ...user,
      stripeId,
    };

    const { quotation, coupon, order, couponReference } =
      await this.esimOrderService.createOrderQuotationNew(orderDto, {
        ...params,
        userId: user.userId,
        requestOriginServiceName,
        fallbackRequestOriginServiceName,
        includeProvisionPrice: true,
        ignoreValidation: true,
      });
    const parentOrder = await order.commitQuotation(quotation, {
      userId: +user.id,
      user,
      orderDto,
    });
    const metadata = {
      couponReferenceId: couponReference?.referenceId || 'N/A',
      orderId: parentOrder.model.id,
      userId: user.userId,
      type: orderDto.topupOrderId ? 'TOPUP' : 'SUBSCRIBE',
      email: user.email,
      language: params.lang || 'en',
      orders: parentOrder.orders
        .map((item) => getFormmattedOrderId(item.model.id))
        .join(','),
      insured: parentOrder.orders
        .map((item) =>
          item.insured ? getFormmattedOrderId(item.model.id) : undefined,
        )
        .filter(Boolean)
        .join(','),
    } as any;
    const planFinalPrice = quotation.xe['USD'];
    const planFinalPriceJPY = quotation.xe['JPY'];
    let paymentResponse;
    if (
      planFinalPriceJPY > 0 &&
      ![PaymentMethodType.INVOICE, PaymentMethodType.PAYPAY].includes(
        orderDto.paymentMethod,
      )
    ) {
      paymentResponse = await this.paymentService.createPaymentIntentNew(
        quotation.xe['JPY'],
        user,
        {
          source: orderDto.source,
          stripeOptions: {
            automatic_payment_methods: { enabled: true },
            metadata,
          },
        },
      );
      if (!paymentResponse.id) throw new Error();
      this.logger.log('Payment intent created for: ' + user.email);
    }

    if (orderDto.paymentMethod === PaymentMethodType.PAYPAY) {
      paymentResponse = {
        amount: planFinalPriceJPY,
        client_secret: '',
      };
    }

    const isPaymentInvoice = orderDto.paymentMethod === 'invoice';

    let invoice;
    if (isPaymentInvoice && user.corporates_users?.length) {
      invoice = await this.paymentService.createInvoice(parentOrder, user, {
        stripeOptions: {
          source: orderDto.source,
          metadata,
        },
      });
      this.logger.log(
        'Invoice created for: ' + user.email + ' invoice id:' + invoice.id,
      );
    }
    //Save the data only when code is 0000
    const updatedOrder = await this.esimOrderService.updateOrder(
      {
        price: planFinalPrice,
        pi_id: paymentResponse?.id || '',
        jpyPrice: isPaymentInvoice
          ? planFinalPriceJPY
          : paymentResponse?.amount || 0,
        orderId: getFormmattedOrderId(parentOrder.model.id),
        userId: +user.id,
        source: orderDto.source,
        lang: orderDto.locale,
        orderMetaData: {
          invoiceId: invoice?.id,
          //@ts-expect-error
          ...(parentOrder.model?.orderMetaData
            ? //@ts-expect-error
              parentOrder.model.orderMetaData
            : {}),
        },
      },
      {
        id: parentOrder.model.id,
      },
    );
    this.logger.log('Pending order created for ' + user.email);

    let secrentAccessToken = null;
    try {
      secrentAccessToken = this.jwtService.sign(
        {
          orderId: updatedOrder.orderId,
        },
        {
          secret: EsimService.getInstantEsimSecret(),
          expiresIn: '1h',
        },
      );
    } catch {}
    this.logger.log(
      `Instant eSIM secret for ${parentOrder.model.id} ${secrentAccessToken}`,
    );
    if (
      planFinalPriceJPY < 1 ||
      (orderDto.paymentMethod === 'invoice' && user.corporates_users?.length)
    ) {
      const noPaymentOrders =
        planFinalPriceJPY < 1
          ? [updatedOrder.childOrders[0]]
          : updatedOrder.childOrders;
      for (const order of noPaymentOrders) {
        if (!order) throw new Error('Some thing went wrong');
        await this.esimOrderService.queueESIMPurchaseRequest({
          planId: order.planId + '',
          lang: updatedOrder.lang,
          chargeAmount: updatedOrder.jpyPrice + '',
          metadata: (order.orderMetaData || {}) as IOrderMetadata,
          orderId: order.id,
          planUrl: getPlanDetailsApi(order.planId + ''),
          stripeChargeId: '',
        });
      }
    }

    return {
      instantEsimSecret: secrentAccessToken,
      orderId: getFormmattedOrderId(parentOrder.model.id),
      payment: pick(paymentResponse, ['client_secret', 'amount']),
      userId: updatedOrder.user.userId,
      ...(quotation?.errors.length > 0 ? { errors: quotation?.errors } : {}),
    };
  }

  @Post('/instant-esim-new')
  @UsePipes(ValidationPipe)
  async getInstantEsimNew(@Body() body: InstantEsimDto) {
    try {
      const response = await this.esimOrderService.instantEsimNew(
        body.secret,
        body.orderId,
        'PING',
      );
      this.logger.log(
        `Instant esim log ${body.orderId} and ${
          response.qrCodeImgUrl ? 'served' : 'not served'
        }`,
      );

      return {
        orders: response.esims,
        qrCodeImgUrl: response.qrCodeImgUrl,
      };
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }

  @Get('/orders/:orderId/download')
  @UsePipes(ValidationPipe)
  @Throttle(100, 60000)
  async downloadEsimfile(
    @Param('orderId') orderId: string,
    @GetUser() user: IUser,
    @Query() query: InstantEsimDto,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      this.logger.log(
        `Generating pdf for order  ${JSON.stringify(query)} ${orderId}`,
      );
      const response = await this.esimOrderService.instantEsimNew(
        query.secret,
        orderId,
        'DOWNLOAD',
      );
      const pdfStream = await this.pdfService.generateEsimPdf(response.order);
      this.logger.log(
        `Generated pdf for order ${JSON.stringify(query)} ${
          response.order.orderId
        }`,
      );
      this.logger.log(`PDF ${response.order.orderId} ${req.url}`);
      res.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename=${response.order.plan.country.name}-${response.order.orderId}.pdf`,
      });
      //
      pdfStream.pipe(res);
      return response;
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException('Invalid or expired download url.');
    }
  }
}
