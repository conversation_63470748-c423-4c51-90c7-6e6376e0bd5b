import { HttpModule } from '@nestjs/axios';
import { <PERSON><PERSON>, Module } from '@nestjs/common';
import { EsimService } from './esim.service';
import { EsimController } from './esim.controller';
import { PassportModule } from '@nestjs/passport';
import { EsimOrdersModule } from 'src/esim-orders/esim-orders.module';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { PrismaService } from 'src/prisma.service';
import { PaymentService } from 'src/payment/payment.service';
import { PlansService } from 'src/plans/plans.service';
import { UsersService } from 'src/users/users.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EsimProviderBuilder } from './providers/EsimProviderBuilder';
import { EmailsService } from 'src/emails/emails.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { JwtService } from '@nestjs/jwt';
import { PdfService } from 'src/pdf/pdf.service';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 60000,
        maxRedirects: 5,
        baseURL: configService.get('USIMSA_HOST_NAME'),
      }),
      inject: [ConfigService],
    }),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    EsimOrdersModule,
  ],
  providers: [
    EsimService,
    EsimOrdersService,
    PrismaService,
    PaymentService,
    PlansService,
    UsersService,
    Logger,
    XchangeService,
    KeyValueStoreService,
    NotificationsService,
    EmailTemplateService,
    EsimProviderBuilder,
    EmailsService,
    JwtService,
    PdfService,
    AwsCognitoService,
    EsimStocksService,
    CouponsService,
    ReferralsService,
  ],
  controllers: [EsimController],
})
export class EsimModule {}
