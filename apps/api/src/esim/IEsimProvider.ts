import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { ValidationError } from 'class-validator';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';
import { SubscribeOrderDTO } from './dto/SubscribeOrderDTO';
import { TopupDto } from './dto/TopupDto.dto';
import { UsageDto } from './dto/UsageDto';

export interface ISignaterGeneratorPayload {
  timestamp?: number;
  httpMethod?: string;
  pathAndQuery?: string;
  payload?: object;
}
export interface IEsimProvider {
  supportsWebhook(): boolean;

  setup(
    configService: ConfigService,
    httpService: HttpService,
    cacheService: Cache,
    esimStocksService: EsimStocksService,
  ): Promise<IEsimProvider>;

  generateSignature(
    payload: ISignaterGeneratorPayload,
  ): Promise<string | Error>;

  subscribe(
    payload: Omit<SubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): Promise<IEsimPurchaseResponse | Error>;

  topup(payload: TopupDto);

  usage(payload: UsageDto): Promise<any>;

  query(orderId: string);

  validatePayload(payload: unknown): Promise<ValidationError[]>;

  getSMDP(): string;
}
