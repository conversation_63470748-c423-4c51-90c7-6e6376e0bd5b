import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { orders, plans } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { IOrderMetadata } from 'src/esim-orders/interfaces/IOrderMetadata';
import { OrderType } from './OrderType.dto';

export class SubscribeOrderDTO {
  @IsString()
  @IsOptional()
  orderId?: string;

  @IsString()
  @IsOptional()
  recaptcha?: string;

  @ApiProperty()
  @Type(() => OrderType)
  products: OrderType;

  @IsOptional()
  @ApiPropertyOptional()
  locale?: string;

  @IsOptional()
  @ApiPropertyOptional()
  sessionSecret?: string;

  @IsOptional()
  @ApiPropertyOptional()
  metadata?: IOrderMetadata;

  @IsOptional()
  @ApiPropertyOptional()
  paymentMethod?: string;

  @IsString()
  @IsOptional()
  couponReferenceId?: string;

  @IsString()
  @IsOptional()
  couponId?: string;

  @ApiProperty()
  @MinLength(2)
  @IsOptional()
  @IsEnum([
    UserRegisterSource.AIRTRIP,
    UserRegisterSource.GLOBAL_ESIM,
    UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
    UserRegisterSource.GLOBAL_ESIM_IOS_APP,
    UserRegisterSource.GLOBAL_ESIM_JP,
    UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP,
    UserRegisterSource.AIRTRIP_ESIM_IOS_APP,
  ])
  source?: string;
}

export class InternalSubscribeOrderDTO extends SubscribeOrderDTO {
  plan: plans;
  iccid?: string;
  parentOrder?: orders;
}

export class SubscribeOrderGuestDTO extends SubscribeOrderDTO {
  @IsEmail()
  email?: string;

  @IsString()
  @MinLength(1)
  firstName?: string;

  @IsString()
  @MinLength(1)
  lastName?: string;

  @ApiProperty()
  @IsString()
  @MinLength(2)
  @IsOptional()
  referral?: string;
}

export class GuestOrderDto {
  @IsString()
  recaptha?: string;

  @IsEmail()
  email?: string;

  @IsString()
  @MinLength(1)
  firstName?: string;

  @IsString()
  @MinLength(1)
  lastName?: string;

  @IsString()
  @IsOptional()
  couponId?: string;

  @IsString()
  @IsOptional()
  referral?: string;
}
