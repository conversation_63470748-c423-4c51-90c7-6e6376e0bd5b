import { ApiProperty } from '@nestjs/swagger';

const OrderBody = {
  id: 66,
  userId: 3,
  response: null,
  orders: null,
  createdAt: '2024-01-26T09:05:03.066Z',
  updatedAt: '2024-01-29T06:18:40.666Z',
  pi_id: 'pi_3OclLPFxfnAZvAy7083Ym4s6',
  paymentStatus: 'SUCCESS',
  txnId: '',
  type: 'SUBSCRIBE',
  planId: 2688,
  iccid: '89852290010420875847',
  topupId: null,
  activateCode: '1$ecprsp.eastcompeace.com$07F3B6F2E6384B85AD1A14F237483A52',
  qrCodeImgUrl:
    'https://mcrmgr.umacaroon.com//data/upload/qrcode/52988d080de1e099c624db8e8b72f6b5_.png',
  stripeLatestChargeId: null,
  jpyPrice: 148,
  orderId: 'GEsimLocal66',
  orderCreatedAt: '2024-01-26T09:05:17.124Z',
  downloadLink:
    'LPA:1$ecprsp.eastcompeace.com$07F3B6F2E6384B85AD1A14F237483A52',
  isActivated: true,
  activateDate: '2024-01-26T09:10:35.000Z',
  expireTime: '2024-02-06T14:59:59.000Z',
  provisionPrice: null,
  price: null,
  lang: 'en',
  appsId: null,
  orderMetaData: null,
  plan: {
    id: 2688,
    name: '30 days',
    planId: '14816',
    price: null,
    dataId: '3 GB',
    description: '',
    createdAt: '2024-01-31T01:36:10.807Z',
    updatedAt: '2024-03-14T10:12:21.190Z',
    provision_price: null,
    validityDays: 30,
    dataVolume: '3',
    dataUnit: 'GB',
    validityDaysCycle: 'days',
    metadata: {},
    networkId: 102,
    enabled: true,
    packageType: 'PER_DAY',
    countryId: 3,
    serviceProviderId: 2370,
    network: {
      id: 102,
      name: 'japan wireless esim',
      enabled: true,
      code: 'www.dtac.co.th-japan wireless esim-4G-256KB (Single day)-LOCAL',
      apn: 'www.dtac.co.th',
      qos: '256KB (Single day)',
      type: 'LOCAL',
      networkGeneration: '4G',
      countryId: 3,
      createdAt: '2024-01-26T04:11:01.374Z',
      updatedAt: '2024-03-14T10:12:21.189Z',
    },
    country: {
      id: 3,
      name: 'japan',
      enabled: true,
      code: null,
      createdAt: '2023-10-05T06:38:26.143Z',
      updatedAt: '2024-03-14T10:12:21.945Z',
      subCountries: null,
    },
    pi_id: null,
  },
};
export class EsimOrderResponseDto {
  @ApiProperty({
    example: {
      total: 10,
      data: [OrderBody],
    },
  })
  data: object;
}

export class SingleEsimOrderResponseDto {
  @ApiProperty({
    example: {
      order: OrderBody,
    },
  })
  data: object;
}
