import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { Cache } from 'cache-manager';
import { plainToInstance } from 'class-transformer';
import { validate, ValidationError } from 'class-validator';
import {
  IEsimProvider,
  ISignaterGeneratorPayload,
} from 'src/esim/IEsimProvider';
import { SubscribeOrderDTO } from 'src/esim/dto/SubscribeOrderDTO';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';
import { isAppEnvDev } from 'src/utils';
import { LGUSubscribeOrderDTO } from '../LGUProvider/LGUSubscribeDto';

export class EsimtaProvider implements IEsimProvider {
  private appId: string;
  private appSecret: string;
  private accessToken: string | null = null;
  private httpService: AxiosInstance;
  private logger = new Logger('EsimtaProvider');
  private configService: ConfigService;
  private cacheService: Cache;

  supportsWebhook(): boolean {
    return false;
  }

  getSMDP(): string {
    // Return the SMDP address if known, or blank/constant
    return '';
  }

  async setup(
    configService: ConfigService<Record<string, unknown>, false>,
    data: any,
    cacheService: Cache,
  ) {
    this.configService = configService;
    this.cacheService = cacheService;

    this.appId = configService.getOrThrow('ESIMTA_APP_ID');
    this.appSecret = configService.getOrThrow('ESIMTA_APP_SECRET');

    this.httpService = axios.create({
      baseURL: configService.getOrThrow('ESIMTA_API_HOST'), // e.g. https://api.esimsta.com
      timeout: 60000,
      headers: {
        Accept: 'application/json',
        'X-APP-ID': this.appId,
        'X-APP-SECRET': this.appSecret,
      },
    });

    // Try to get or refresh token on setup
    await this.getAuthToken();

    // Add interceptor to inject X-APP-AUTHORIZATION for all requests except auth
    this.httpService.interceptors.request.use((config) => {
      if (!config.url?.includes('/auth/authorize')) {
        config.headers['X-APP-AUTHORIZATION'] = this.accessToken;
      }
      return config;
    });

    return this;
  }

  private async getAuthToken() {
    // Check cache first
    const cachedToken = await this.cacheService.get('EsimtaProvider-token');
    if (cachedToken) {
      this.accessToken = cachedToken as string;
      return this.accessToken;
    }

    try {
      const response = await this.httpService.post(
        '/external/partner/v1/auth/authorize',
      );
      if (response.data.code !== 2000) {
        throw new Error(`Auth failed: ${response.data.message}`);
      }
      const token = response.data.data?.[0]?.accessToken;
      if (!token) {
        throw new Error('No access token received from Esimta');
      }
      this.accessToken = token;

      // Cache token, you might want to set expiration based on token TTL if provided
      await this.cacheService.set('EsimtaProvider-token', token);
      return token;
    } catch (error) {
      this.logger.error('Failed to get Esimta auth token', error);
      throw error;
    }
  }

  async generateSignature(_: ISignaterGeneratorPayload) {
    // Esimta uses access token directly for authorization header
    if (!this.accessToken) {
      await this.getAuthToken();
    }
    return this.accessToken;
  }

  async validatePayload(
    payload: LGUSubscribeOrderDTO,
  ): Promise<ValidationError[]> {
    const dto = plainToInstance(LGUSubscribeOrderDTO, payload);
    const errors = await validate(dto);
    return errors;
  }

  private parsePlanId(planId: string) {
    // Implement parsing logic if needed
    return planId.split?.('_').pop?.() || planId;
  }

  async subscribe(
    payload: Omit<SubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): Promise<IEsimPurchaseResponse | Error> {
    try {
      const qrHost = isAppEnvDev()
        ? 'https://esim-dev.gmobile.biz'
        : 'https://esim.gmobile.biz';
      const cleanPlanId = this.parsePlanId(payload.products.optionId);
      // Prepare order payload based on spec
      const customerDetails = [];

      const orderPayload = {
        productCode: cleanPlanId,
        quantity: 1,
        customerDetails,
      };

      this.logger.log(
        `Sending order request to Esimta: ${JSON.stringify(orderPayload)}`,
      );

      const response = await this.httpService.post(
        '/external/partner/v1/orders',
        orderPayload,
      );

      if (response.data.code !== 2000) {
        throw new Error(`Esimta order failed: ${response.data.message}`);
      }

      const esimData = response.data.data?.[0]?.esims?.[0];
      if (!esimData) {
        throw new Error('No esim data returned from Esimta order');
      }

      const [version, smdp, activationCode] = esimData.lpaUrl.split?.('$');

      return {
        code: '0000',
        message: response.data.message,
        name: 'EsimtaProvider',
        products: [
          {
            activateCode: activationCode,
            downloadLink: esimData.lpaUrl,
            iccid: esimData.iccid,
            qrCodeImgUrl: `${qrHost}/api/v1/qr/generate/?data=${esimData.lpaUrl}`,
            qrcodeImgUrl: `${qrHost}/api/v1/qr/generate/?data=${esimData.lpaUrl}`,
            smdp, // unknown or set default
            topupId: esimData.imsi + ':' + esimData.msisdn,
            optionId: payload.products.optionId,
            esimVendorUniqueReference: esimData.pin + ':' + esimData.puk,
          },
        ],
      };
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 401) {
        await this.cacheService.del('EsimtaProvider-token');
      }
      this.logger.error(error);
      throw error;
    }
  }

  topup(payload: any) {
    throw new Error('Method not applicable.');
  }

  async usage(payload: any) {
    throw new Error('Method not applicable.');
  }

  query(orderId: string) {
    throw new Error('Method not applicable.');
  }
}
