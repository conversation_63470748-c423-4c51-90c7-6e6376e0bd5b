import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { IEsimProvider } from '../IEsimProvider';
import { ESIM_PROVIDER } from './EsimProvider';
import { EsimtaProvider } from './EsimtaProvider/EsimtaProvider';
import { KDDIProvider } from './KDDIProvider/KDDIProvider';
import { LGUProvider } from './LGUProvider/LGUProvider';
import { LinkKoreaProvider } from './LinkKoreaProvider/LinkKoreaProvider';
import { UroCommCampaign } from './UroComm/UroCommCampaign';
import { UroCommProvider } from './UroComm/UroCommProvider';
import { UsimsaProvider } from './UsimsaProvider';

@Injectable()
export class EsimProviderBuilder {
  static readonly PROVIDER_USIMSA = ESIM_PROVIDER.USIMSA;
  static readonly PROVIDER_URO = ESIM_PROVIDER.UROCOMM;
  static readonly PROVIDER_LGUPLUS = ESIM_PROVIDER.LGUPLUS;
  static readonly PROVIDER_UROCOMM_CAMPAIGN = ESIM_PROVIDER.UROCOMM_CAMPAIGN;
  static readonly PROVIDER_LGUPLUS_AIRTRIP_CAMPAIGN =
    ESIM_PROVIDER.LGUPLUS_AIRTRIP_CAMPAIGN;
  static readonly PROVIDER_KDDI = ESIM_PROVIDER.KDDI;
  static readonly PROVIDER_LINK_KOREA = ESIM_PROVIDER.LINK_KOREA;
  static readonly PROVIDER_ESIMTA = ESIM_PROVIDER.ESIMTA;

  constructor(
    private logger: Logger,
    private configService: ConfigService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private esimStocksService: EsimStocksService,
  ) {}

  build(className: ESIM_PROVIDER): Promise<IEsimProvider> {
    if (className === EsimProviderBuilder.PROVIDER_LINK_KOREA) {
      return new LinkKoreaProvider().setup(
        this.configService,
        null,
        this.cacheManager,
        this.esimStocksService,
      );
    }

    if (className === EsimProviderBuilder.PROVIDER_KDDI) {
      return new KDDIProvider().setup(
        this.configService,
        null,
        this.cacheManager,
        this.esimStocksService,
      );
    }
    if (className === EsimProviderBuilder.PROVIDER_UROCOMM_CAMPAIGN) {
      return new UroCommCampaign().setup(
        this.configService,
        null,
        this.cacheManager,
      );
    }
    if (className === EsimProviderBuilder.PROVIDER_USIMSA) {
      return new UsimsaProvider(this.logger).setup(this.configService);
    }
    if (className === EsimProviderBuilder.PROVIDER_URO) {
      return new UroCommProvider().setup(
        this.configService,
        null,
        this.cacheManager,
      );
    }

    if (
      className === EsimProviderBuilder.PROVIDER_LGUPLUS ||
      className === EsimProviderBuilder.PROVIDER_LGUPLUS_AIRTRIP_CAMPAIGN
    ) {
      return new LGUProvider().setup(
        this.configService,
        null,
        this.cacheManager,
      );
    }
    if (className === EsimProviderBuilder.PROVIDER_ESIMTA) {
      return new EsimtaProvider().setup(
        this.configService,
        null,
        this.cacheManager,
      );
    }
    return null;
  }
}
