import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { ValidationError } from 'class-validator';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { SubscribeOrderDTO } from 'src/esim/dto/SubscribeOrderDTO';
import { TopupDto } from 'src/esim/dto/TopupDto.dto';
import { UsageDto } from 'src/esim/dto/UsageDto';
import {
  IEsimProvider,
  ISignaterGeneratorPayload,
} from 'src/esim/IEsimProvider';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';

import { StockEsimProvider } from '../StockEsimProvider/StockEsimProvider';

export class KDDIProvider extends StockEsimProvider {
  supportsWebhook(): boolean {
    return false;
  }
  async setup(
    configService: ConfigService,
    httpService: HttpService,
    cacheService: Cache,
    esimStocksService: EsimStocksService,
  ): Promise<IEsimProvider> {
    this.esimStocksService = esimStocksService;
    return this;
  }
  generateSignature(
    payload: ISignaterGeneratorPayload,
  ): Promise<string | Error> {
    return;
  }
  parsePlanId(rawPlanId: string) {
    return rawPlanId.split?.('_').pop?.() || rawPlanId;
  }

  async subscribe(
    payload: Omit<SubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): Promise<IEsimPurchaseResponse | Error> {
    const esim = await this.esimStocksService.getEsimsByOptionId(
      payload.products.optionId,
    );

    return {
      code: '0000',
      name: '',
      products: [
        {
          activateCode: esim.activateCode,
          downloadLink: esim.downloadLink,
          iccid: esim.iccid,
          qrCodeImgUrl: esim.qrCodeImgUrl,
          qrcodeImgUrl: esim.qrCodeImgUrl,
          smdp: esim.smdp,
          topupId: esim.topupId,
        },
      ],
    };
  }
  topup(payload: TopupDto) {
    throw new Error('Method not implemented.1');
  }
  usage(payload: UsageDto): Promise<any> {
    throw new Error('Service not available');
  }
  query(orderId: string) {
    throw new Error('Method not implemented.3');
  }
  async validatePayload(payload: unknown): Promise<ValidationError[]> {
    return [];
  }
  getSMDP(): string {
    return 'https://www.google.com';
  }
}
