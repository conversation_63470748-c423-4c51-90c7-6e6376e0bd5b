export type LinkKoreaAPIErrorResultCodeType =
    | ""
    | "API_ERR_E001" // Response error
    | "API_ERR_E003" // System error
    | "API_ERR_E005" // Missing required fields
    | "API_ERR_E006" // Invalid access path
    | "API_ERR_E007" // Non-existent type
    | "API_ERR_ESIM" // [ESIM order error] Error due to ESIM
    | "API_ERR_E008" // [ESIM lookup error] Error during ESIM lookup
    | "API_ERR_E009" // [TYPE error] Type change from G01 to G03
    | "API_ERR_E010" // Duplicate vendor reservation number error
    | "API_ERR_AUTH"; // Authorization fail

export const LinkKoreaAPIErrorResultCodeTypeMessages: Record<LinkKoreaAPIErrorResultCodeType, string> = {
    "": "",
    API_ERR_E001: "Response error",
    API_ERR_E003: "System error",
    API_ERR_E005: "Missing required fields",
    API_ERR_E006: "Invalid access path",
    API_ERR_E007: "Non-existent type",
    API_ERR_ESIM: "[ESIM order error] Error due to ESIM",
    API_ERR_E008: "[ESIM lookup error] Error during ESIM lookup",
    API_ERR_E009: "[TYPE error] Type change from G01 to G03",
    API_ERR_E010: "Duplicate vendor reservation number error",
    API_ERR_AUTH: "Authorization fail",
};

export type LinkKoreaAPIErrorResultCodeTypeMessage = typeof LinkKoreaAPIErrorResultCodeTypeMessages[LinkKoreaAPIErrorResultCodeType];

export enum LinkKoreaAPIErrorResultCodeTypeEnum {
    RESPONSE_ERROR = "API_ERR_E001",
    SYSTEM_ERROR = "API_ERR_E003",
    MISSING_REQUIRED_FIELDS = "API_ERR_E005",
    INVALID_ACCESS_PATH = "API_ERR_E006",
    NON_EXISTENT_TYPE = "API_ERR_E007",
    ESIM_ORDER_ERROR = "API_ERR_ESIM",
    ESIM_LOOKUP_ERROR = "API_ERR_E008",
    TYPE_ERROR = "API_ERR_E009",
    DUPLICATE_VENDOR_RESERVATION_NUMBER_ERROR = "API_ERR_E010",
    AUTHORIZATION_FAIL = "API_ERR_AUTH",
}
