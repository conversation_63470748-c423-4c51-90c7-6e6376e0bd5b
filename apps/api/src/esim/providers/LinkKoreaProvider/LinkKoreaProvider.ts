import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Cache } from 'cache-manager';
import { ValidationError } from 'class-validator';
import { format } from 'date-fns';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { InternalSubscribeOrderDTO } from 'src/esim/dto/SubscribeOrderDTO';
import { TopupDto } from 'src/esim/dto/TopupDto.dto';
import { UsageDto } from 'src/esim/dto/UsageDto';
import {
  IEsimProvider,
  ISignaterGeneratorPayload,
} from 'src/esim/IEsimProvider';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';
import { QrService } from 'src/qr/qr.service';
import { isAppEnvDev } from 'src/utils';
import {
  LinkKoreaAPIErrorResultCodeTypeEnum,
  LinkKoreaAPIErrorResultCodeTypeMessages,
} from './IAPIError';
import { ILinkKoreaEsimG02Request } from './ILinkKoreaEsimG02Request';
import { ILinkKoreaEsimG02Response } from './ILinkKoreaEsimG02Response';
import { ILinkKoreaEsimG03Request } from './ILinkKoreaEsimG03Request';
import { ILinkKoreaEsimG03Response } from './ILinkKoreaEsimG03Response';

export class LinkKoreaProvider implements IEsimProvider {
  private accessKey: string = 'test';
  private secretKey: String = 'EA7414608E812A221921661BAD45C18B';
  private httpService: AxiosInstance = null;
  private logger = new Logger('LinkKoreaProvider');
  private configService: ConfigService;
  private cacheService: Cache;

  supportsWebhook(): boolean {
    return false;
  }
  async setup(
    configService: ConfigService,
    httpService: HttpService,
    cacheService: Cache,
    esimStocksService: EsimStocksService,
  ): Promise<IEsimProvider> {
    this.configService = configService;
    this.cacheService = cacheService;

    this.accessKey = configService.getOrThrow('LINK_KOREA_CLIENT_ID'); // Refactored
    this.secretKey = configService.getOrThrow('LINK_KOREA_CLIENT_SECRET'); // Refactored

    this.httpService = axios.create({
      timeout: 60000,
      maxRedirects: 5,
      baseURL: configService.getOrThrow('LINK_KOREA_PRROVIDER_HOST'), // Refactored
    });

    this.httpService.interceptors.request.use((config) => {
      config.headers['X-LINK-CLIENT-ID'] = this.accessKey;
      config.headers['X-LINK-CLIENT-SECRET'] = this.secretKey;

      return config;
    });

    return this;
  }
  generateSignature(
    payload: ISignaterGeneratorPayload,
  ): Promise<string | Error> {
    throw new Error('Method not implemented.');
  }

  parsePlanId(rawPlanId: string): {
    planDivision: string;
    planValidityDays: number;
  } {
    const planIdLayer1 = rawPlanId.split?.('_').pop?.() || rawPlanId;
    const [planDivision, planValidityDays] = planIdLayer1.split(':');
    return {
      planDivision,
      planValidityDays: +planValidityDays,
    };
  }

  async subscribe(
    payload: Omit<InternalSubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): Promise<IEsimPurchaseResponse | Error> {
    try {
      // common/api/Goods/AjaxG01_T.aspx
      const { planDivision, planValidityDays } = this.parsePlanId(
        payload.products.optionId,
      );
      const ESIM_PATH_LK = isAppEnvDev()
        ? '/common/api/Goods/AjaxG01_T.aspx'
        : '/common/api/Goods/AjaxG01.aspx';
      this.logger.log(
        `Sending purchase request to LinkKorea ${
          payload.products.optionId
        } ${JSON.stringify({
          order_number: payload.orderId,
          goods_id: payload.products.optionId,
        })}`,
      );
      const nextPayload: ILinkKoreaEsimG03Request = {
        header: [{ type: 'G03' }],
        body: [
          {
            traceno: 'IPC-CHANNEL-' + payload.orderId,
            GoodsDiv: planDivision,
            AgCd: '10066',
            AgRsvNumber: payload.orderId,
            Email: payload.metadata.email,
            LeasePlace: '',
            ReturnPlace: '',
            LeaseDt: format(new Date(), 'yyyyMMdd'),
            ReturnDt: '',
            LeaseQty: '1',
            UseDt: planValidityDays + '',
            ParcelYN: '',
            ParcelZipCd: '',
            ParcelAddr1: '',
            ParcelAddr2: '',
            ParcelTel: '',
            nation: '',
            mdn: '',
            DAYNUMBER: '',
            GDCD: '',
            CustNm: `${payload.metadata.firstName || 'N/A'} ${
              payload.metadata.lastName || 'N/A'
            }`,
            Sex: (payload.metadata.gender as 'M' | 'F') || 'M',
            GuideLang: 'JPN',
            Tel: '',
            PlusLeaseQty: [],
            PlusGDCD: [],
            PlusUseDt: [],
            user_id: 'linkkt',
          },
        ],
      };

      // First API call (G03)
      const response = await this.httpService.post<ILinkKoreaEsimG03Response>(
        ESIM_PATH_LK,
        nextPayload,
      );

      const { body } = response.data;
      const {
        traceno,
        RsvNumber,
        result: resultG03,
        RESULT,
        resultcd: resultcdGO3,
        RESULTCD: RESULTCDGO3,
        RESULTMSG: RESULTMSGGO3,
        resultMsg: resultMsgG03,
      } = body?.[0] || body;

      // Handle errors based on the G03 response
      if (RESULT === 'N' || resultG03 === 'N') {
        // Constructing a meaningful error message from the result
        const errorMessage =
          LinkKoreaAPIErrorResultCodeTypeMessages[resultcdGO3 || RESULTCDGO3] ||
          RESULTMSGGO3 ||
          resultMsgG03 ||
          'Unknown error in G02 request';
        this.logger.error(
          `G03 Request Failed: ${JSON.stringify(
            payload,
          )} message: ${errorMessage} ${JSON.stringify(response.data)} `,
        );
        if (
          resultcdGO3 !==
          LinkKoreaAPIErrorResultCodeTypeEnum.DUPLICATE_VENDOR_RESERVATION_NUMBER_ERROR
        ) {
          throw new Error(
            `G03 Request Failed: ${resultcdGO3 || RESULTCDGO3} ${
              payload.orderId
            } ${payload.products.optionId} ${errorMessage}`,
          );
        }
      }

      // Second API call (G02)
      const go2EsimPayload: ILinkKoreaEsimG02Request = {
        header: [{ type: 'G02' }],
        body: [
          {
            AgRsvNumber: payload.orderId,
            traceno: 'IPC-CHANNEL-' + payload.orderId,
          },
        ],
      };
      // Adding a delay of a few seconds before making the second API call
      await new Promise((resolve) => setTimeout(resolve, 10000));
      const go2esimResponse =
        await this.httpService.post<ILinkKoreaEsimG02Response>(
          ESIM_PATH_LK,
          go2EsimPayload,
        );

      const { body: esimPayloadBody } = go2esimResponse.data;

      // Handle errors based on the G02 response
      const {
        result,
        resultCd,
        resultMsg,
        RESULT: resultG02,
        RESULTMSG,
        RESULTCD,
      } = esimPayloadBody[0];
      if (result === 'N' || resultG02 === 'N') {
        const errorMessage =
          LinkKoreaAPIErrorResultCodeTypeMessages[resultCd || RESULTCD] ||
          resultMsg ||
          RESULTMSG ||
          'Unknown error in G02 request';
        this.logger.error(
          `G02 Request Failed: ${JSON.stringify(
            go2EsimPayload,
          )} message: ${errorMessage} ${JSON.stringify(response.data)}`,
        );
        // Constructing a meaningful error message from the result
        throw new Error(
          `G02 Request Failed: ${resultCd} ${payload.orderId} ${payload.products.optionId} ${errorMessage}`,
        );
      }
      const [data] = esimPayloadBody;
      const [esim] = data.data;
      // Generate the QR code URL
      const qrCodeUrl = QrService.getUrlOfQRByCodeAndSMDP(
        esim.smdp,
        esim.active_cd,
      );

      return {
        code: '0000',
        name: '',
        products: [
          {
            activateCode: esim.active_cd,
            downloadLink: `LPA:1$${esim.smdp}$${esim.active_cd}`,
            iccid: esim.iccid,
            qrCodeImgUrl: qrCodeUrl,
            smdp: esim.smdp,
            topupId: esim.mdn.replace(/[()|+]/g, ''),
            qrcodeImgUrl: qrCodeUrl,
          },
        ],
        message: 'eSIM purchase is successful',
      };
    } catch (err) {
      // Log and rethrow any error encountered during the process
      this.logger.error(err);
      throw err;
    }
  }

  topup(payload: TopupDto) {
    throw new Error('Method not implemented.');
  }
  usage(payload: UsageDto): Promise<any> {
    throw new Error('Method not implemented.');
  }
  query(orderId: string) {
    throw new Error('Method not implemented.');
  }
  async validatePayload(payload: unknown): Promise<ValidationError[]> {
    return [];
  }
  getSMDP(): string {
    throw null;
  }
}
