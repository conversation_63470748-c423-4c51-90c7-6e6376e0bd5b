import { LinkKoreaAPIErrorResultCodeType, LinkKoreaAPIErrorResultCodeTypeMessage } from "./IAPIError";

export interface ILinkKoreaEsimG02Response {
    header: { type: string }[];
    body: {
        traceno: string;
        result: string;
        data: {
            smdp: string;
            active_cd: string;
            mdn: string;
            rental_mgmt_num: string;
            iccid: string;
        }[];
        order_id: string;
        resultCd: LinkKoreaAPIErrorResultCodeType;
        resultMsg: LinkKoreaAPIErrorResultCodeTypeMessage;
        RESULT: "Y" | "N"; // Success or Failure
        RESULTCD?: LinkKoreaAPIErrorResultCodeType; // Optional result code
        RESULTMSG?: LinkKoreaAPIErrorResultCodeTypeMessage; // Optional result message
    }[];
}
