import { LinkKoreaAPIErrorResultCodeType, LinkKoreaAPIErrorResultCodeTypeMessage } from "./IAPIError";

export interface ILinkKoreaEsimG03Response {
    header: { type: string }[];
    body: {
        traceno: string;
        RsvNumber: string;
        result: "Y" | "N"; // Success or Failure
        resultcd?: LinkKoreaAPIErrorResultCodeType; // Optional result code
        resultMsg?: LinkKoreaAPIErrorResultCodeTypeMessage; // Optional result message
        RESULT: "Y" | "N"; // Success or Failure
        RESULTCD?: LinkKoreaAPIErrorResultCodeType; // Optional result code
        RESULTMSG?: LinkKoreaAPIErrorResultCodeTypeMessage; // Optional result message
    };
}

