import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { AbstractUroComm } from './AbstractUroComm';

export class UroCommCampaign extends AbstractUroComm {
  protected accessKey: string;
  protected secretKey: string;
  protected httpService = null;
  protected logger = new Logger('UroCommCampaign');

  async setup(
    configService: ConfigService<Record<string, unknown>, false>,
    httpService: HttpService,
    cacheService: Cache,
  ) {
    this.accessKey = configService.getOrThrow('UROCOM_CAMPAIGN_ACCESS_KEY'); // Refactored
    this.secretKey = configService.getOrThrow('UROCOM_CAMPAIGN_SECRET_KEY'); // Refactored

    this.setupHttpService(
      configService.getOrThrow('UROCOM_API_HOST'), // Refactored
    );

    return this;
  }
}
