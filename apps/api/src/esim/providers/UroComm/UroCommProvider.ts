import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { AbstractUroComm } from './AbstractUroComm';

export class UroCommProvider extends AbstractUroComm {
  protected accessKey: string;
  protected secretKey: string;
  protected httpService = null;
  protected logger = new Logger('UrocommProvider');

  async setup(
    configService: ConfigService<Record<string, unknown>, false>,
    httpService: HttpService,
    cacheService: Cache,
  ) {
    this.accessKey = configService.getOrThrow('UROCOM_ACCESS_KEY'); // Refactored
    this.secretKey = configService.getOrThrow('UROCOM_SECRET_KEY'); // Refactored

    this.setupHttpService(
      configService.getOrThrow('UROCOM_API_HOST'), // Refactored
    );

    return this;
  }
}
