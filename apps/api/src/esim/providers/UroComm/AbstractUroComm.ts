import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Sentry from '@sentry/nestjs';
import axios from 'axios';
import { Cache } from 'cache-manager';
import MD5 from 'crypto-js/md5';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';
import { IUrocomOrderResponse } from 'src/interface/IUrocomOrderResponse';
import { convertJSTtoUTC, isAppEnvDev } from 'src/utils';
import { v4 as uuidv4 } from 'uuid';
import { InternalSubscribeOrderDTO } from '../../dto/SubscribeOrderDTO';
import { TopupDto } from '../../dto/TopupDto.dto';
import { UsageDto } from '../../dto/UsageDto';
import { IEsimProvider, ISignaterGeneratorPayload } from '../../IEsimProvider';
export abstract class AbstractUroComm implements IEsimProvider {
  protected accessKey: string = 'test';
  protected secretKey: string = 'EA7414608E812A221921661BAD45C18B';
  protected httpService = null;
  protected logger = new Logger('UrocommProvider');

  supportsWebhook() {
    return false;
  }
  async validatePayload(payload) {
    return [];
  }
  getSMDP(): string {
    return 'sm-v4-081-a-gtm.pr.go-esim.com';
  }

  async setup(
    configService: ConfigService<Record<string, unknown>, false>,
    httpService: HttpService,
    cacheService: Cache,
  ) {
    return this;
  }

  protected setupHttpService(baseURL: string) {
    this.httpService = axios.create({
      timeout: 60000 * 2,
      maxRedirects: 5,
      baseURL,
    });
    this.httpService.interceptors.request.use(async (config) => {
      let timestamp = new Date().getTime();
      timestamp = parseInt(timestamp / 1000 + '');
      const url = new URL(config.baseURL + config.url);
      config.data = {
        ...config.data,
        timestamp,
      };
      const signature = await this.generateSignature({
        timestamp: timestamp,
        httpMethod: config.method.toUpperCase(),
        pathAndQuery: url.pathname + url.search,
        payload: config.data,
      });
      config.headers['x-sign-method'] = 'md5';
      config.headers['x-sign-lang'] = 'en';
      config.headers['x-sign-value'] = signature;
      config.headers['x-channel-id'] = this.accessKey;
      this.logger.log({
        signature,
        access: this.accessKey,
        payload: config.data,
      });
      this.logger.log(config);
      return config;
    });
  }
  async generateSignature(options: ISignaterGeneratorPayload) {
    this.logger.log(
      `Environment variable ${process.env['UROCOM_ACCESS_KEY']} ${process.env['UROCOM_SECRET_KEY']} ${process.env['UROCOM_API_HOST']}`,
    );
    this.logger.log(
      `Creating signaure ${JSON.stringify(options.payload)} ${this.secretKey}`,
    );
    const sign = MD5(JSON.stringify(options.payload) + this.secretKey);
    return sign.toString();
  }
  parsePlanId(rawPlanId: string) {
    return rawPlanId.split?.('_').pop?.() || rawPlanId;
  }
  async subscribe(
    payload: Omit<InternalSubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): Promise<IEsimPurchaseResponse | Error> {
    try {
      const cleanPlanId = this.parsePlanId(payload.products.optionId);
      const goodsId = cleanPlanId;

      this.logger.log(
        `Sending purchase request to urocomm ${cleanPlanId} ${JSON.stringify({
          order_number: payload.orderId,
          goods_id: goodsId,
          iccid: payload.iccid,
        })}`,
      );
      if (isAppEnvDev()) {
        return {
          code: '0000',
          products: [
            {
              activateCode: uuidv4(),
              downloadLink: 'LPA:$usimsa.com$452accad179547f4995a0efb74f2f099',
              iccid: uuidv4(),
              qrCodeImgUrl:
                'https://issue.usimsa.com/api/iccid/qrcode/LPA:$usimsa.com$452accad179547f4995a0efb74f2f099/120',
              qrcodeImgUrl:
                'https://issue.usimsa.com/api/iccid/qrcode/LPA:$usimsa.com$452accad179547f4995a0efb74f2f099/120',
              smdp: 'usimsa.com',
              topupId: uuidv4(),
              optionId: payload.products.optionId,
              esimVendorUniqueReference: `sam-sung`,
            },
          ],
        };
      }
      const nextPayload = {
        order_number: payload.orderId,
        goods_id: goodsId,
        iccid: payload.iccid,
      };
      if (!nextPayload.iccid) {
        delete nextPayload.iccid;
      }
      const response = await this.httpService.post(
        '/coope/order/esimOrder',
        nextPayload,
      );
      const data = response.data as IUrocomOrderResponse;
      this.logger.log(
        `Response from, ${JSON.stringify({
          order_number: payload.orderId,
          goods_id: goodsId,
          data,
        })}`,
      );
      if (data.tradeCode !== 1000) {
        this.logger.log(
          `Failed to receive esim for order ${payload.orderId}`,
          data,
        );
        this.logger.error(data);
        this.logger.log(process.env);
        this.logger.log(
          `${process.env['UROCOM_SECRET_KEY']} ${
            process.env[`UROCOM_SECRET_KEY`]
          }`,
        );

        try {
          this.logger.log(process.env.UROCOM_SECRET_KEY);
        } catch (err) {
          this.logger.error(`error while accessing data`);
          Sentry.captureException(err.stack);
        }
        throw new Error(
          `Unable to get esim from vendor. ${JSON.stringify(data)}`,
        );
      }

      return {
        code: '0000',
        products: [
          {
            activateCode: data?.tradeData?.matching_id,
            downloadLink: data?.tradeData?.qrCodeContent,
            iccid: data?.tradeData?.iccid + '',
            qrCodeImgUrl: data?.tradeData?.qrcode,
            qrcodeImgUrl: data?.tradeData?.qrcode,
            smdp: data?.tradeData?.sm_dp,
            topupId: data?.tradeData?.iccid + '',
            optionId: payload.products.optionId,
          },
        ],
      };
    } catch (err) {
      this.logger.log(payload, err?.response);
      this.logger.log(err);
      throw err;
    }
  }
  topup(payload: TopupDto) {
    throw new Error('Method not implemented.');
  }
  async usage(payload: UsageDto) {
    try {
      const response = await this.httpService.post(
        '/coope/order/esimUsedFlow',
        {
          iccid: payload.topupid,
        },
      );
      const data = response.data as IUrocomOrderResponse;
      /**
       * We convert JST to UTC because
       * the function which consumes it outside expects it to be in UTC
       * as the previous service we were using used to send it in UTC.
       * and to make it compatible with exisitng flow we convert it to UTC
       * even though it looks redundent.
       */
      const orderStartTime = data?.tradeData?.orderStart
        ? convertJSTtoUTC(data.tradeData.orderStart)
        : null;

      const orderEndTime = data?.tradeData?.orderEnd
        ? convertJSTtoUTC(data.tradeData.orderEnd)
        : null;

      //@ts-expect-error
      const flow = data?.tradeData?.flow?.map((item) => {
        try {
          const [day, timerange] = item.day.split(' ');
          const [startTime, endTime] = timerange.split('~');
          return {
            day,
            startTime: startTime,
            endTime: endTime,
            used: item.used,
          };
        } catch {
          return {
            day: null,
            startTime: null,
            endTime: null,
            used: item.used,
          };
        }
      });
      return {
        code: data.tradeCode,
        topup: {
          topupId: data.tradeData.iccid,
          createTime: orderStartTime,
          expireTime: orderEndTime,
          activeTime: data.tradeData?.traffic ? orderStartTime : null,
          totalUsage: data?.tradeData?.traffic,
          //@ts-expect-error
          usage: data?.tradeData?.flow?.[0]?.used,
          flow,
        },
      };
    } catch (err) {
      this.logger.log(err);
      return {
        code: 500,
        topup: {
          topupId: payload.topupid,
          createTime: null,
          expireTime: null,
          activeTime: null,
          usage: null,
        },
      };
    }
  }
  query(orderId: string) {
    throw new Error('Method not implemented.');
  }
}
