import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as CryptoJS from 'crypto-js';
import axios from 'axios';
import { SubscribeOrderDTO } from '../dto/SubscribeOrderDTO';
import { UsageDto } from '../dto/UsageDto';
import { TopupDto } from '../dto/TopupDto.dto';
import { USIMSA_ACCESS_KEY, USIMSA_SECRET_KEY } from 'src/constants';
import { IEsimProvider, ISignaterGeneratorPayload } from '../IEsimProvider';

export class UsimsaProvider implements IEsimProvider {
  private accessKey: string = '';
  private secretKey: String = '';
  private httpService = axios.create({
    timeout: 60000,
    maxRedirects: 5,
    baseURL: process.env['USIMSA_HOST_NAME'],
  });
  constructor(private logger: Logger) {}
  supportsWebhook() {
    return true;
  }
  async validatePayload(payload) {
    return [];
  }
  async setup(configService: ConfigService) {
    this.accessKey = configService.get(USIMSA_ACCESS_KEY);
    this.secretKey = configService.get(USIMSA_SECRET_KEY);
    this.httpService.interceptors.request.use(async (config) => {
      const currentDate = new Date();
      const timestamp = currentDate.valueOf(); // current timestamp (epoch)
      const url = new URL(config.baseURL + config.url);
      const signature = this.generateSignature({
        timestamp,
        httpMethod: config.method.toUpperCase(),
        pathAndQuery: url.pathname + url.search,
      });
      config.headers['x-gat-timestamp'] = timestamp;
      config.headers['x-gat-access-key'] = this.accessKey;
      config.headers['x-gat-signature'] = signature;

      return config;
    });
    return this;
  }
  /**
   *
   * @param timestamp current timestamp
   * @param httpMethod http method used for calling api
   * @param pathAndQuery path and query excluding hostname of the api
   * @returns Signuature
   */
  async generateSignature({
    timestamp,
    httpMethod,
    pathAndQuery,
  }: ISignaterGeneratorPayload) {
    const stringToSigh = `${httpMethod} ${pathAndQuery}\n${timestamp}\n${this.accessKey}`;
    const base64Key = CryptoJS.enc.Base64.parse(this.secretKey + '');
    const hashedString = CryptoJS.enc.Utf8.parse(stringToSigh);
    const hash = CryptoJS.HmacSHA256(hashedString, base64Key);
    return hash.toString(CryptoJS.enc.Base64);
  }

  async subscribe(
    payload: Omit<SubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ) {
    const response = await this.httpService
      .post('order', {
        orderId: payload.orderId,
        products: [payload.products],
      })
      .catch((err) => {
        this.logger.error(err);
        throw err;
      });
    return response.data;
  }

  async topup(payload: TopupDto) {
    const response = await this.httpService
      .post('charge', payload)
      .catch((err) => {
        this.logger.error(err);
        throw err;
      });
    return response.data;
  }

  async usage(payload: UsageDto) {
    const response = await this.httpService
      .get('topup', {
        data: {
          topupId: payload.topupid,
        },
      })
      .catch((err) => {
        this.logger.error(err.message);
        throw err;
      });
    return response.data;
  }

  async query(orderId: string) {
    const response = await this.httpService
      .get(`order/${orderId}`)
      .catch((err) => {
        this.logger.error(err);
        throw err;
      });
    return response.data;
  }
  getSMDP(): string {
    return 'usimsa.com';
  }
}
