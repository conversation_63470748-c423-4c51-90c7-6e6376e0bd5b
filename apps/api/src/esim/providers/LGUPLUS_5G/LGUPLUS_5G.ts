import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { ValidationError } from 'class-validator';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { SubscribeOrderDTO } from 'src/esim/dto/SubscribeOrderDTO';
import { TopupDto } from 'src/esim/dto/TopupDto.dto';
import { UsageDto } from 'src/esim/dto/UsageDto';
import { ISignaterGeneratorPayload } from 'src/esim/IEsimProvider';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';
import { LGUProvider } from '../LGUProvider/LGUProvider';
import { LGUSubscribeOrderDTO } from '../LGUProvider/LGUSubscribeDto';

export class LGUPLUS_5G extends LGUProvider {
  private esimStocksService: EsimStocksService;
  supportsWebhook(): boolean {
    return false;
  }
  async setup(
    configService: ConfigService,
    data: any,
    cacheService: Cache,
    esimStocksService?: EsimStocksService,
  ): Promise<this> {
    if (esimStocksService) {
      this.esimStocksService = esimStocksService;
    }
    return this;
  }
  generateSignature(
    payload: ISignaterGeneratorPayload,
  ): Promise<string | Error> {
    return;
  }
  parsePlanId(rawPlanId: string) {
    return rawPlanId.split?.('_').pop?.() || rawPlanId;
  }

  async subscribe(
    payload: Omit<SubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): Promise<IEsimPurchaseResponse | Error> {
    const esim = await this.esimStocksService.getEsimsByOptionId(
      payload.products.optionId,
    );

    return {
      code: '0000',
      name: 'LGUPLUS_5G',
      products: [
        {
          activateCode: esim.activateCode,
          downloadLink: esim.downloadLink,
          iccid: esim.iccid,
          qrCodeImgUrl: esim.qrCodeImgUrl,
          qrcodeImgUrl: esim.qrCodeImgUrl,
          smdp: esim.smdp,
          topupId: esim.topupId,
        },
      ],
    };
  }
  topup(payload: TopupDto) {
    return super.topup(payload);
  }
  usage(payload: UsageDto): Promise<any> {
    return super.usage(payload);
  }
  query(orderId: string) {
    return super.query(orderId);
  }
  async validatePayload(payload: LGUSubscribeOrderDTO): Promise<ValidationError[]> {
    return super.validatePayload(payload as LGUSubscribeOrderDTO);
  }
  getSMDP(): string {
    return super.getSMDP();
  }
}
