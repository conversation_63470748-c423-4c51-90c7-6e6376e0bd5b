import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { Cache } from 'cache-manager';
import { plainToInstance } from 'class-transformer';
import { ValidationError, validate } from 'class-validator';
import { addDays, addHours, format, isAfter, isBefore } from 'date-fns';
import { startCase } from 'lodash';
import {
  IEsimProvider,
  ISignaterGeneratorPayload,
} from 'src/esim/IEsimProvider';
import { InternalSubscribeOrderDTO } from 'src/esim/dto/SubscribeOrderDTO';
import { TopupDto } from 'src/esim/dto/TopupDto.dto';
import { UsageDto } from 'src/esim/dto/UsageDto';
import { IEsimPurchaseResponse } from 'src/interface/IEsimPurchaseResponse';
import {
  convertBytesToMB,
  convertKSTtoUTC,
  decodeFromBase64,
  encodeToBase64,
  encryptAppStandard,
  isAppEnvDev,
  parseLGUTime,
} from 'src/utils';
import { v4 as uuidv4 } from 'uuid';
import {
  ILGUProviderSubscribeResponse,
  ILGUProviderTopupResponse,
} from './ILGUProviderSubscribeResponse';
import { ILGUProviderUsageResponse } from './ILGUProviderUsageResponse';
import { LGUSubscribeOrderDTO } from './LGUSubscribeDto';
export class LGUProvider implements IEsimProvider {
  private accessKey = 'test';
  private secretKey = 'EA7414608E812A221921661BAD45C18B';
  private httpService: AxiosInstance = null;
  private logger = new Logger('LGUProvider');
  private configService: ConfigService;
  private cacheService: Cache;

  supportsWebhook(): boolean {
    return false;
  }

  getSMDP(): string {
    return 'ecprsp.eastcompeace.com';
  }

  parsePlanId(rawPlanId: string) {
    return rawPlanId.split?.('_').pop?.() || rawPlanId;
  }

  async setup(
    configService: ConfigService<Record<string, unknown>, false>,
    data: any,
    cacheService: Cache,
  ) {
    this.configService = configService;
    this.cacheService = cacheService;

    this.accessKey = configService.getOrThrow('LGU_CLIENT_ID'); // Refactored
    this.secretKey = configService.getOrThrow('LGU_CLIENT_SECRET'); // Refactored

    this.httpService = axios.create({
      timeout: 60000,
      maxRedirects: 5,
      baseURL: configService.getOrThrow('LGU_API_HOST'), // Refactored
    });

    const timestamp = Math.floor(Date.now() / 1000); // Changed to use Math.floor for better readability

    const signature = await this.generateSignature({});
    this.httpService.interceptors.request.use((config) => {
      config.headers['X-IBM-Client-Id'] = this.accessKey;
      config.headers['X-IBM-Client-Secret'] = this.secretKey;
      config.headers['Authorization'] = `Bearer ${signature}`;

      return config;
    });

    return this;
  }

  async getOAuthToken() {
    // const cachedToken = await this.cacheService.get('LGUProvider-token');
    // if (cachedToken) {
    //   try {
    //     const token = JSON.parse(decryptAppStandard(cachedToken as string)) as {
    //       accessToken: string;
    //     };
    //     return token;
    //   } catch (err) {
    //     this.logger.log('Corrupt token', cachedToken);
    //     this.logger.error(err);
    //   }
    // }

    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: this.accessKey,
      client_secret: this.secretKey + '',
      scope: 'RC',
    });
    try {
      const response = await this.httpService.post(
        '/uplus/extuser/oauth2/token',
        params,
      );
      const token = {
        accessToken: response.data.access_token,
        expiresIn: response.data.expires_in,
        consentedOn: response.data.consented_on,
      };
      await this.cacheService.set(
        'LGUProvider-token',
        encryptAppStandard(JSON.stringify(token)),
        response.data.expires_in - 1000,
      );
      return token;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }
  async generateSignature(options: ISignaterGeneratorPayload) {
    const { accessToken } = await this.getOAuthToken();
    return accessToken;
  }

  async validatePayload(
    payload: LGUSubscribeOrderDTO,
  ): Promise<ValidationError[]> {
    const dto = plainToInstance(LGUSubscribeOrderDTO, payload);
    const errors = await validate(dto);
    return errors;
  }

  mockDevSubscribeResponse(
    payload: Omit<InternalSubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): IEsimPurchaseResponse | Error {
    const qrHost = 'https://esim-dev.gmobile.biz';

    if (payload.iccid) {
      const { parentOrder } = payload;
      const esimVendorUniqueReference: string = (parentOrder.response as any)
        .products[0].esimVendorUniqueReference;
      const smdp: string = (parentOrder.response as any).products[0].smdp;

      return {
        code: '0000',
        message: 'SUCCESS',
        name: 'LGUProvider',
        products: [
          {
            activateCode: parentOrder.activateCode,
            downloadLink: parentOrder.downloadLink,
            iccid: parentOrder.iccid,
            qrCodeImgUrl: parentOrder.qrCodeImgUrl,
            qrcodeImgUrl: parentOrder.qrCodeImgUrl,
            smdp: smdp,
            topupId: parentOrder.topupId,
            optionId: payload.products.optionId,
            esimVendorUniqueReference: esimVendorUniqueReference,
          },
        ],
        lguTopup: {
          startDate: new Date(),
          endDate: addDays(new Date(), 7),
        },
      };
    } else {
      // for live data,
      // esimVendorUniqueReference = saleNo + '-' + subSaleNo
      // so, mock data follows the same format
      const mockSaleNo = uuidv4().replace(/-/g, '');
      const mockSubSaleNo = uuidv4().replace(/-/g, '');
      const mockEsimVendorUniqueReference = `${mockSaleNo}-${mockSubSaleNo}`;

      return {
        code: '0000',
        products: [
          {
            activateCode: uuidv4(),
            downloadLink: 'LPA:$lgu.com$452accad179547f4995a0efb74f2f099',
            iccid: uuidv4(),
            qrCodeImgUrl: `${qrHost}/api/v1/qr/generate/?data=LPA:1$SM-V4-024-A-GTM.PR.GO-ESIM.COM$B91F7606246C00916235C79625D0D5F5&size=350x350`,
            qrcodeImgUrl: `${qrHost}/api/v1/qr/generate/?data=LPA:1$SM-V4-024-A-GTM.PR.GO-ESIM.COM$B91F7606246C00916235C79625D0D5F5&size=350x350`,
            smdp: 'usimsa.com',
            topupId: uuidv4(),
            optionId: payload.products.optionId,
            esimVendorUniqueReference: mockEsimVendorUniqueReference,
          },
        ],
      };
    }
  }

  async subscribe(
    payload: Omit<InternalSubscribeOrderDTO, 'locale' | 'sessionSecret'>,
  ): Promise<IEsimPurchaseResponse | Error> {
    const qrHost = isAppEnvDev()
      ? 'https://esim-dev.gmobile.biz'
      : 'https://esim.gmobile.biz';
    try {
      // in dev env, return mocked response
      if (isAppEnvDev()) {
        return this.mockDevSubscribeResponse(payload);
      }

      const cleanPlanId = this.parsePlanId(payload.products.optionId);
      const goodsId = cleanPlanId;

      // if there is iccid, then it is topup request
      if (payload.iccid) {
        const { parentOrder } = payload;

        // Check if elligible for topup
        if (!parentOrder.activateDate || !parentOrder.expireTime) {
          throw new Error('Failed to topup');
        }

        const activatedTimestamp = new Date(parentOrder.activateDate);
        const expiryTimestamp = new Date(parentOrder.expireTime);
        const gracePeriodHours = 48;
        const finalTimestamp = addHours(expiryTimestamp, gracePeriodHours);

        const currentTime = new Date();

        const isCurrentAfterActivate = isAfter(currentTime, activatedTimestamp);
        const isCurrentBeforeFinal = isBefore(currentTime, finalTimestamp);

        if (!isCurrentAfterActivate || !isCurrentBeforeFinal) {
          throw new Error('Failed to topup');
        }

        // Start gathering data for topup request
        const esimVendorUniqueReference: string = (parentOrder.response as any)
          .products[0].esimVendorUniqueReference;

        const saleNo = esimVendorUniqueReference.split('-')[0];

        const bodyForTopup = {
          userId: encodeToBase64(
            this.configService.getOrThrow('LGU_USER_ID'), // Refactored
          ),
          telNo: encodeToBase64(parentOrder.topupId.replace(/-/g, '')),
          jncoCd: this.configService.getOrThrow('LGU_JNCO_AFFILIATE_CODE'),
          deskId: this.configService.getOrThrow('LGU_DESK_ID'),
          saleNo: saleNo,
          saleSubNo: '',
          devPpCd: goodsId,
        };

        this.logger.log(
          `Sending Topup request to LGUProvider. Request body: ${JSON.stringify(
            bodyForTopup,
          )}`,
        );

        const topupResponse =
          await this.httpService.post<ILGUProviderTopupResponse>(
            '/uplus/extuser/pv/rc/ss/pr/jncoSaleMgmt/v1/useTermExtend',
            bodyForTopup,
          );

        const topupResponseData = topupResponse.data;

        this.logger.log(
          `Response from lguprovider ${JSON.stringify({
            order_number: payload.orderId,
            topupResponseData,
          })}`,
        );

        if (topupResponseData.dsRsltInfo.rtnCd !== 'SUCCESS') {
          throw new Error(
            `Unable to get esim ${payload.orderId
            } from vendor LGUProvider.  ${JSON.stringify(topupResponseData)}`,
          );
        }

        const smdp: string = (parentOrder.response as any).products[0].smdp;

        // termExtnStartDttm is in this format: 20240314140918
        const startDate = parseLGUTime(topupResponseData.termExtnStartDttm);
        const endDate = parseLGUTime(topupResponseData.termExtnEndDttm);

        return {
          code: '0000',
          message: topupResponseData.dsRsltInfo.rtnMsg,
          name: 'LGUProvider',
          products: [
            {
              activateCode: parentOrder.activateCode,
              downloadLink: parentOrder.downloadLink,
              iccid: parentOrder.iccid,
              qrCodeImgUrl: parentOrder.qrCodeImgUrl,
              qrcodeImgUrl: parentOrder.qrCodeImgUrl,
              smdp: smdp,
              topupId: parentOrder.topupId,
              optionId: payload.products.optionId,
              esimVendorUniqueReference: esimVendorUniqueReference,
            },
          ],
          lguTopup: {
            startDate,
            endDate,
          },
        };
      }

      const nextPayload = {
        userId: encodeToBase64(
          this.configService.getOrThrow('LGU_USER_ID'), // Refactored
        ),
        psno: encodeToBase64(payload.metadata?.passportNo || 'A123456789'),
        jncoCd: this.configService.getOrThrow('LGU_JNCO_AFFILIATE_CODE'), // Refactored
        deskId: this.configService.getOrThrow('LGU_DESK_ID'), // Refactored
        custNm: encodeToBase64('Inbound' + ' ' + 'Platform'),
        countryCd: payload.metadata?.countryCode || 'JPN',
        bday: encodeToBase64(
          format(new Date(payload.metadata?.dob || '2002/05/13'), 'yyyyMMdd'),
        ),
        sex: payload.metadata?.gender || 'M',
        loclCtplc: encodeToBase64(payload.metadata?.phone || '+819012345678'),
        cprtChnlNm: `${startCase(
          payload?.metadata?.channelName || 'Global Mobile',
        )}_${payload.orderId}`,
        dsReSaleProdInfo: [
          {
            devPpCd: goodsId,
            saleCnt: 1,
          },
        ],
      };
      this.logger.log(
        `Sending purchase request to lguprovider ${payload.products.optionId
        } ${JSON.stringify({
          order_number: payload.orderId,
          goods_id: payload.products.optionId,
        })}`,
      );
      const response =
        await this.httpService.post<ILGUProviderSubscribeResponse>(
          '/uplus/extuser/pv/rc/ss/pr/jncoSaleMgmt/v1/rtmSale',
          nextPayload,
        );
      const esimResponse = response.data;
      this.logger.log(
        `Response from lguprovider ${JSON.stringify({
          order_number: payload.orderId,
          esimResponse,
        })}`,
      );

      if (
        esimResponse.dsRsltInfo.rtnCd !== 'SUCCESS' ||
        esimResponse.ErrorCode === 'FAIL'
      ) {
        throw new Error(
          `Unable to get esim ${payload.orderId
          } from vendor LGUProvider.  ${JSON.stringify(esimResponse)}`,
        );
      }
      const esim = esimResponse.dsSaleProdInfo[0];
      const qrCodeUrl = `${qrHost}/api/v1/qr/generate?data=${decodeFromBase64(
        esim.actFullPath,
      )}&size=350x350`;
      return {
        code: '0000',
        message: esimResponse.dsRsltInfo.rtnMsg,
        name: 'LGUProvider',
        products: [
          {
            activateCode: decodeFromBase64(esim.actCd),
            downloadLink: `$${decodeFromBase64(esim.actFullPath)}`,
            iccid: esim.iccid,
            qrCodeImgUrl: qrCodeUrl,
            qrcodeImgUrl: qrCodeUrl,
            smdp: decodeFromBase64(esim.smDpAddress),
            topupId: decodeFromBase64(esim.telNo),
            optionId: payload.products.optionId,
            esimVendorUniqueReference: `${esimResponse?.saleNo}-${esim?.subSaleNo}`,
          },
        ],
      };
    } catch (err: unknown | AxiosError) {
      if (err instanceof AxiosError) {
        if (err?.response?.status === 401) {
          await this.cacheService.del('LGUProvider-token');
        }
      }
      this.logger.error(err);
      throw err;
    }
  }
  topup(payload: TopupDto) {
    throw new Error('Method not implemented.');
  }
  async usage(payload: UsageDto) {
    try {
      const [saleNo, subSaleNo] = payload.externalBookingNo?.split?.('-') || '';
      if (!saleNo) {
        throw new Error('No sale no found!');
      }
      const nextPayload = {
        saleNo: saleNo,
        jncoCd: this.configService.getOrThrow('LGU_JNCO_AFFILIATE_CODE'),
      };

      const response = await this.httpService.get<ILGUProviderUsageResponse>(
        '/uplus/extuser/pv/rc/ss/pr/jncoSaleMgmt/v1/usageOfqnList',

        {
          params: nextPayload,
          headers: {
            userid: encodeToBase64(
              this.configService.getOrThrow('LGU_USER_ID'),
            ),
            telno: encodeToBase64(payload.topupid),
          },
        },
      );
      const { data } = response;
      const esim = data.dsUsageOfqnList?.[0];
      if (!esim) {
        return {
          code: 500,
          topup: {
            topupId: payload.topupid,
            createTime: null,
            expireTime: null,
            activeTime: null,
            usage: null,
          },
        };
      }
      return {
        code: data?.dsRsltInfo?.rtnCd,
        topup: {
          topupId: payload.topupid,
          createTime: esim.usedStDt ? convertKSTtoUTC(esim.usedStDt) : null,
          expireTime:
            esim.svcEdDt && esim.usedQty ? convertKSTtoUTC(esim.svcEdDt) : null,
          activeTime:
            esim.svcStDt && esim.usedQty ? convertKSTtoUTC(esim.svcStDt) : null,
          total: esim.supportQty ? convertBytesToMB(esim.supportQty) : null,
          usage: esim.usedQty ? convertBytesToMB(esim.usedQty) : null,
          remaining: esim.remainQty ? convertBytesToMB(esim.remainQty) : null,
        },
      };
    } catch (err) {
      this.logger.error(err);
      return {
        code: 500,
        topup: {
          topupId: payload.topupid,
          createTime: null,
          expireTime: null,
          activeTime: null,
          usage: null,
        },
      };
    }
  }
  query(orderId: string) {
    throw new Error('Method not implemented.');
  }
}
