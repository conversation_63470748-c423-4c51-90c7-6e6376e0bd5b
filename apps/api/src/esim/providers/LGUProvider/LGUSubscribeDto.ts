import {
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderMetadataDto } from 'src/esim-orders/interfaces/IOrderMetadata';
import { OrderType } from 'src/esim/dto/OrderType.dto';

export class LGUSubscribeOrderDTO {
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  orderId?: string;

  @ApiProperty()
  @Type(() => OrderType)
  products: OrderType;

  @IsOptional()
  @ApiPropertyOptional()
  locale?: string;

  @ApiProperty()
  @ValidateNested()
  @Type(() => OrderMetadataDto)
  metadata: OrderMetadataDto;
}
