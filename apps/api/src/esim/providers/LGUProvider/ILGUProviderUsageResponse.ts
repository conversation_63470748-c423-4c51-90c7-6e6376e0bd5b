export interface ILGUProviderUsageResponse {
  dsUsageOfqnList: DsUsageOfqnList[];
  dsRsltInfo: DsRsltInfo;
}

export interface DsUsageOfqnList {
  usedEdDt: string;
  usedStDt: string;
  svcEdDt: string;
  svcCdType: string;
  supportQty: string;
  remainQty: string;
  svcQos: string;
  expireYn: string;
  svcType: string;
  usedQty: string;
  svcStDt: string;
  svcAddType: string;
  svcCdnm: string;
}

export interface DsRsltInfo {
  rtnMsg: string;
  rtnCd: string;
}
