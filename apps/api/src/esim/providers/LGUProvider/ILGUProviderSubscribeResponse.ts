export interface ILGUProviderSubscribeResponse {
  saleNo: string;
  saleDttm: string;
  dsReSaleProdInfo: DsReSaleProdInfo[];
  dsSaleProdInfo: DsSaleProdInfo[];
  dsRsltInfo: DsRsltInfo;
  ErrorCode?: string;
}

export interface ILGUProviderTopupResponse {
  dsRsltInfo: {
    rtnMsg: string;
    rtnCd: 'SUCCESS' | 'FAIL';
  };
  termExtnStartDttm: string;
  termExtnEndDttm: string;
}

export interface DsRsltInfo {
  rtnCd: string;
  rtnMsg: string;
}

export interface DsSaleProdInfo {
  devPpCd: string;
  devPpNm: string;
  productNm: string;
  subSaleNo: string;
  telNo: string;
  barcodeNo: string;
  useTerm: string;
  valdEndDttm: string;
  iccid: string;
  smDpAddress: string;
  actCd: null;
  actFullPath: string;
}

export interface DsReSaleProdInfo {
  barcodeNo: string;
  actCd: string;
  smDpAddress: string;
  devPpCd: string;
  valdEndDttm: string;
  iccid: string;
  devPpNm: string;
  useTerm: number;
  subSaleNo: string;
  actFullPath: string;
  telNo: string;
  productNm: string;
}
