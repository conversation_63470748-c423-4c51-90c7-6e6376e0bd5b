import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { IEsimProvider, ISignaterGeneratorPayload } from '../IEsimProvider';
import { SubscribeOrderDTO } from '../dto/SubscribeOrderDTO';
import { TopupDto } from '../dto/TopupDto.dto';
import { UsageDto } from '../dto/UsageDto';

export enum ESIM_PROVIDER {
  USIMSA = 'USIMSA',
  UROCOMM = 'UROCOMM',
  LGUPLUS = 'LGUPLUS',
  UROCOMM_CAMPAIGN = 'UROCOMM_CAMPAIGN',
  LGUPLUS_AIRTRIP_CAMPAIGN = 'LGUPLUS_AIRTRIP_CAMPAIGN',
  KDDI = 'KDDI',
  LINK_KOREA = 'LINK_KOREA',
  ESIMTA = 'ESIMTA',
  STOCK_ESIM = 'STOCK_ESIM',
  LGUPLUS_5G = 'LGUPLUS_5G',
}
export class EsimProvider implements IEsimProvider {
  constructor(private service: IEsimProvider) {}
  getSMDP(): string {
    return this.service.getSMDP();
  }

  validatePayload(payload: unknown) {
    return this.service.validatePayload(payload);
  }

  supportsWebhook(): boolean {
    return this.service.supportsWebhook();
  }
  setup(
    configService: ConfigService<Record<string, unknown>, false>,
    httpService: HttpService,
    cacheService: Cache,
    esimStocksService: EsimStocksService,
  ) {
    return this.service.setup(
      configService,
      httpService,
      cacheService,
      esimStocksService,
    );
  }
  generateSignature({
    timestamp,
    httpMethod,
    pathAndQuery,
  }: ISignaterGeneratorPayload) {
    return this.service.generateSignature({
      timestamp,
      httpMethod,
      pathAndQuery,
    });
  }
  subscribe(payload: Omit<SubscribeOrderDTO, 'locale' | 'sessionSecret'>) {
    return this.service.subscribe(payload);
  }
  topup(payload: TopupDto) {
    return this.service.topup(payload);
  }
  usage(payload: UsageDto) {
    return this.service.usage(payload);
  }
  query(orderId: string) {
    return this.service.query(orderId);
  }
}
