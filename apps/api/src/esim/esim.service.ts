import { Injectable, Logger } from '@nestjs/common';
import { InternalSubscribeOrderDTO, SubscribeOrderDTO } from './dto/SubscribeOrderDTO';
import { TopupDto } from './dto/TopupDto.dto';
import { UsageDto } from './dto/UsageDto';
import { ESIM_PROVIDER } from './providers/EsimProvider';
import { EsimProviderBuilder } from './providers/EsimProviderBuilder';

@Injectable()
export class EsimService {
  constructor(
    private logger: Logger,
    private esimProvider: EsimProviderBuilder,
  ) { }

  async supportsWebhook(provider): Promise<boolean> {
    const esim = await this.esimProvider.build(provider);
    return esim.supportsWebhook();
  }

  async validatePayload(
    payload: Omit<SubscribeOrderDTO, 'locale' | 'sessionSecret'>,
    provider: ESIM_PROVIDER,
  ) {
    const esim = await this.esimProvider.build(provider);
    return esim.validatePayload(payload);
  }

  async getSMDP(provider: ESIM_PROVIDER) {
    const esim = await this.esimProvider.build(provider);
    return esim.getSMDP();
  }
  async subscribe(
    payload: Omit<SubscribeOrderDTO & InternalSubscribeOrderDTO, 'locale' | 'sessionSecret'>,
    provider: ESIM_PROVIDER,
  ) {
    this.logger.log(
      `Subscribe calling esim provider for ${provider} ${JSON.stringify(
        payload,
      )} `,
    );
    const esim = await this.esimProvider.build(provider);
    return esim.subscribe(payload);
  }

  async topup(payload: TopupDto, provider: ESIM_PROVIDER) {
    const esim = await this.esimProvider.build(provider);
    return esim.topup(payload);
  }

  async usage(payload: UsageDto, provider: ESIM_PROVIDER) {
    const esim = await this.esimProvider.build(provider);
    return esim.usage(payload);
  }

  async query(orderId: string, provider: ESIM_PROVIDER) {
    const esim = await this.esimProvider.build(provider);
    return esim.query(orderId);
  }

  static getInstantEsimSecret() {
    return process.env.PKCE_CODE_VERIFIER + process.env.PKCE_CODE_CHALLENGE;
  }
}
