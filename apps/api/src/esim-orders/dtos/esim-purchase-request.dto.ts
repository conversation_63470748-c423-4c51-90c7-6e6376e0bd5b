import { plans } from '@prisma/client';
import { IOrderMetadata } from '../interfaces/IOrderMetadata';

export class EsimPurchaseRequestDto {
  iccid?: string;
  lang?: string;
  userId?: number;
  appsId?: number;
  planId: string;
  chargeAmount: string;
  metadata?: IOrderMetadata;
  plan?: plans;
  orderId?: number;
  planUrl?: string;
  stripeChargeId?: string;
  parentResponse?: string;
  retryKey?: string;
}
