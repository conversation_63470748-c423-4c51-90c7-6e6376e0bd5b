import { ApiPropertyOptional } from '@nestjs/swagger';
import { INTENT_TYPE, PAYMENT_STATUS } from '@prisma/client';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class EsimOrderDto {
  // @IsOptional()
  // id: number;


  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  user_id: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  created_at: Date;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  updated_at: Date;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  plan_id: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  iccid: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  topup_id: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  activate_code: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  jpy_price: number;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  order_id: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  order_created_at: Date;

  @IsEnum(INTENT_TYPE)
  @IsOptional()
  @ApiPropertyOptional({
    enum: INTENT_TYPE,
  })
  type: INTENT_TYPE;

  @IsEnum(PAYMENT_STATUS)
  @IsOptional()
  @ApiPropertyOptional({
    enum: PAYMENT_STATUS,
  })
  payment_status: PAYMENT_STATUS;

  @IsOptional()
  @ApiPropertyOptional({
    description: 'Page number for pagination',
  })
  page: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: 'Search by iccid, topupId, activateCode, orderId',
  })
  search: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description:
      'sorts the result, provide in format like "id:desc" or "id:asc"',
  })
  sort_by: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: 'Number of result to be fetched, default is 10',
  })
  limit: string;

  @IsOptional()
  @IsString()
  booked_date: string;

  @IsOptional()
  @IsString()
  app_id: number;

  @IsOptional()
  @IsString()
  status?: "expired" | "active";
}
