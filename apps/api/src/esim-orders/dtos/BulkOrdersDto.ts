import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CURRENCY } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { OrderType } from 'src/esim/dto/OrderType.dto';
import { GuestOrderDto } from 'src/esim/dto/SubscribeOrderDTO';
import { IOrderMetadata } from '../interfaces/IOrderMetadata';

export enum PaymentMethodType {
  INVOICE = 'invoice',
  CREDIT = 'credit',
  NONE = 'none',
  GOOGLEPAY = 'google-pay',
  APPLEPAY = 'apple-pay',
  APPLE = 'apple',
  GOOGLE = 'google',
  PAYPAY = 'paypay',
}

export class BulkOrdersDto {
  @ApiProperty({
    description: 'List of products for the bulk order',
    type: [OrderType], // Array of OrderType
  })
  @Type(() => OrderType)
  @IsArray()
  @ValidateNested({ each: true })
  products: OrderType[];

  @IsString()
  @IsOptional()
  couponId?: string;

  @ApiProperty()
  @MinLength(2)
  @IsOptional()
  @IsEnum([
    UserRegisterSource.AIRTRIP,
    UserRegisterSource.GLOBAL_ESIM,
    UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
    UserRegisterSource.GLOBAL_ESIM_IOS_APP,
    UserRegisterSource.GLOBAL_ESIM_JP,
    UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP,
    UserRegisterSource.AIRTRIP_ESIM_IOS_APP,
  ])
  source?: string;

  @ApiProperty({
    description: 'Affiliate of the order',
  })
  @IsString()
  @IsOptional()
  affiliate?: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(CURRENCY)
  currency?: CURRENCY;
}

export class BulkOrdersGuestDto extends GuestOrderDto {
  @ApiProperty({
    description: 'List of products for the bulk order',
    type: [OrderType], // Array of OrderType
  })
  @Type(() => OrderType)
  @IsArray()
  @ValidateNested({ each: true })
  products: OrderType[];

  @IsString()
  @IsOptional()
  couponReferenceId?: string;

  @IsString()
  @IsOptional()
  referral?: string;
  source?: string;

  @IsOptional()
  @ApiPropertyOptional()
  metadata?: IOrderMetadata;

  @IsOptional()
  @ApiPropertyOptional({
    description:
      'Language for this order, user will recieve confirmation email in this language if provided',
  })
  locale?: string;

  topupOrderId?: string;

  @IsEnum(PaymentMethodType)
  @IsOptional()
  paymentMethod?: PaymentMethodType;

  @ApiProperty({
    description: 'Affiliate of the order',
  })
  @IsString()
  @IsOptional()
  affiliate?: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(CURRENCY)
  currency?: CURRENCY;
}
