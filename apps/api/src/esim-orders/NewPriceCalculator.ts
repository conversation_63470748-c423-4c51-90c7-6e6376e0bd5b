import { CURRENCY } from '@prisma/client';

type Insurance = 'insured';

export type Discount = {
  discount: number;
  type: 'PERCENTAGE' | 'AMOUNT';
  currency: CURRENCY;
};

export class NewPriceCalculator {
  private priceBeforeTax: number;
  private discountAmount: number = 0;
  private insuranceAmount: number = 0;
  public discount?: Discount;

  constructor(
    public readonly basePrice: number,
    private readonly options: { calculateTaxOnBasePrice: boolean },
  ) {
    this.priceBeforeTax = options.calculateTaxOnBasePrice
      ? this.calculatePriceBeforeTax()
      : this.basePrice;
  }

  private calculatePriceBeforeTax(): number {
    return +(this.basePrice - this.basePrice * (10 / 100)).toFixed(2); // Deduct 10% from the base price
  }

  public getPriceBeforeTax(): number {
    return this.priceBeforeTax;
  }

  public getDiscountAmount(): number {
    return this.discountAmount;
  }

  public getInsuranceAmount(): number {
    return this.insuranceAmount;
  }

  public applyDiscount(discount: Discount): void {
    this.discount = discount;
    if (discount.type === 'PERCENTAGE') {
      this.discountAmount = +(
        this.priceBeforeTax *
        (discount.discount / 100)
      ).toFixed(2);
    } else if (discount.type === 'AMOUNT') {
      this.discountAmount = discount.discount;
    }
  }

  public addInsurance(): void {
    this.insuranceAmount = +(this.priceBeforeTax * 0.2).toFixed(2); // Add 20% for insurance
  }

  private calculateSubtotal(): number {
    return this.priceBeforeTax - this.discountAmount + this.insuranceAmount;
  }

  private calculateTax(subtotal: number): number {
    return subtotal * (10 / 100); // Add 10% tax
  }

  public calculateFinalPrice(params?: { round?: boolean }): number {
    if (!this.discountAmount && !this.insuranceAmount)
      return +this.basePrice.toFixed(2);

    const subtotal = this.calculateSubtotal();
    let finalPrice = subtotal;

    if (this.options.calculateTaxOnBasePrice) {
      const tax = this.calculateTax(subtotal);
      finalPrice += tax;
    }

    if (params?.round) {
      finalPrice = +finalPrice.toFixed(2);
    }

    return finalPrice;
  }
}
