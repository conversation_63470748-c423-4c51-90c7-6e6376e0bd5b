import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { EsimOrdersService } from '../esim-orders.service';
import { FREE_ESIM_EVENT } from '../events';
import { FreeEsimDto } from '../events/free-esim.event';

@Injectable()
export class FreeEsimListener {
  private logger: Logger = new Logger('FreeEsimListener');
  constructor(
    private esimOrderService: EsimOrdersService,
    private kvStore: KeyValueStoreService,
  ) {}

  @OnEvent(FREE_ESIM_EVENT)
  async handleFreeEsimEvent({ data }: { data: FreeEsimDto }) {
    await this.dispatchFreeEsim({ data });
  }

  async dispatchFreeEsim({ data }: { data: FreeEsimDto }) {
    try {
      let campaignPlan = await this.kvStore.get(
        `campaign-${data.campaignName}`,
      );

      if (!campaignPlan) {
        campaignPlan = await this.getCampaignPlanByServiceName(data);
      }

      this.logger.log(
        'Free eSIM Campaign request:' + JSON.stringify({ campaignPlan, data }),
      );

      if (!campaignPlan || !campaignPlan?.value) return;

      const campaignValue = campaignPlan.value as {
        planId: number;
        lang: 'jp';
        template?: string;
        campaignSuccessURL?: string;
      };

      const order = await this.esimOrderService.createEsim(
        +campaignValue.planId,
        data.user,
        {
          lang: campaignValue.lang,
          template: campaignValue.template,
        },
      );

      this.logger.log(
        `A free esim for plan ${+campaignValue.planId} dispatched to user  ${
          data.user.id
        }.`,
      );
      return order;
    } catch (err) {
      this.logger.error(err);
    }
  }
  private async getCampaignPlanByServiceName(data: FreeEsimDto) {
    this.logger.log(
      `Campaign: ${data.campaignName} not found, trying with service name: ${data.serviceName}`,
    );
    const serviceName =
      data.serviceName === 'GLOBAL_ESIM_JAPANESE'
        ? 'gmnikkeitrendi'
        : 'airtrip';
    return this.kvStore.get(`campaign-${serviceName}`);
  }
}
