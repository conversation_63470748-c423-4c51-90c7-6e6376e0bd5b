import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import axios from 'axios';
import { Queue } from 'bullmq';
import FormData from 'form-data';
import { USAGE_QUEUE } from 'src/constants';
import { isAppEnvDev } from 'src/utils';
import * as countries from '../../i18n/jp/countries.json';
import { EsimOrdersService } from '../esim-orders.service';
import { NEW_SUCCESSFUL_ORDER } from '../events';

@Injectable()
export class OrderUpdatesListener {
  private lambdaUrl = `https://qy3gfemup46v3ewyywgyxmaiue0isbpb.lambda-url.ap-northeast-1.on.aws/?source=${process.env.FRONTEND_HOST}&event=`;
  private isDev =
    !!process.env.FRONTEND_HOST?.includes?.('esim-dev') ||
    !!process.env.FRONTEND_HOST?.includes?.('localhost');

  constructor(
    private logger: Logger,
    @InjectQueue(USAGE_QUEUE) private usageQueue: Queue,
    private esimOrderService: EsimOrdersService,
    private configService: ConfigService,
  ) { }

  @OnEvent(NEW_SUCCESSFUL_ORDER)
  async handleOrderSuccessful({
    data: { order, orderInformation, user, plan },
  }) {
    const loyaltyFeatureEnabled =
      this.configService.get('loyalty')?.featureEnabled;
    const isThirdPartyPurchase =
      order?.paymentStatus === 'THIRD_PARTY_PURCHASE';

    if (loyaltyFeatureEnabled) {
      if (!isThirdPartyPurchase) {
        await new Promise((resolve) => setTimeout(resolve, 10000));
        await this.esimOrderService.allocateCoupons(user.userId, order);
      }
    }

    if (isThirdPartyPurchase) {
      this.logger.log(
        'Skipping Ukomi order creation: order is a third-party purchase',
      );
      return;
    }
    // if (order.userId) {
    //   this.usageQueue.add(
    //     USAGE_QUEUE,
    //     {
    //       ...orderInformation,
    //       topupId: order.topupId,
    //     },
    //     {
    //       removeOnComplete: false,
    //       repeatJobKey: order.id,
    //       jobId: order.id,
    //       repeat: {
    //         every: 60 * 60000,
    //         limit: 500,
    //       },
    //     },
    //   );
    // }

    // if (this.isDev) return;

    try {
      const accessToken = await this.getUkomiAccessToken();
      if (!accessToken) {
        throw new Error('Failed to get Ukomi access token');
      }

      const ukomiPayload = this.createUkomiOrderPayload(accessToken, {
        order,
        orderInformation,
        user,
        plan,
      });

      if (!ukomiPayload) {
        return;
      }

      this.logger.log('Sending order update to Ukomi');
      const apiKey = this.configService.get<string>('UKOMI_API_KEY');
      const response = await axios.post(
        `https://api.u-komi.com/orders/${apiKey}/create`,
        ukomiPayload,
      );
      this.logger.log(`Ukomi response: ${JSON.stringify(response.data)}`);
    } catch (error: any) {
      this.logger.error(error);
    }
  }

  createUkomiOrderPayload(
    accessToken: string,
    { order, orderInformation, user, plan },
  ) {
    if (order?.paymentStatus === 'THIRD_PARTY_PURCHASE') {
      this.logger.log(
        'Skipping Ukomi order creation: order is a third-party purchase',
      );
      return;
    }
    if (!plan.country) {
      this.logger.log('Skipping Ukomi order creation: plan has no country');
      return;
    }

    const envPrefix = !isAppEnvDev() ? '' : 'TEST-';
    const countryName = plan.country.name.toLowerCase().trim();
    const jpCountryName = countries[countryName] || countryName;

    if (!order.source) {
      this.logger.error(
        'Order source not provided in the order details, unable to send order request to Ukomi.',
      );
      return;
    }
    const orderSource = (order.source as string).toLowerCase();

    const productIdPrefix =
      orderSource === 'airtrip' ? 'AIRTRIP-ESIM' : 'GLOBAL-MOBILE-ESIM';

    const productNamePrefixJP =
      orderSource === 'airtrip' ? '【エアトリeSIM】' : '【グロモバeSIM】';

    const planNameJP = plan.name.toLowerCase().includes('unlimited')
      ? '無制限プラン'
      : `${plan.name.split(' ')[0]}日`;

    const isUnlimited =
      plan.name.toLowerCase().includes('unlimited') ||
      plan.packageType === 'PER_DAY';

    // Format data volume differently for unlimited and fixed plans
    const dataFormat = isUnlimited
      ? `${planNameJP} - ${plan.validityDays}日`
      : `${plan.dataVolume}${plan.dataUnit} - ${planNameJP}`;

    const name = `${envPrefix}${productNamePrefixJP} ${jpCountryName}, ${dataFormat}`;

    const productUrl =
      orderSource === 'airtrip'
        ? `https://esim.airtrip.jp/region/${countryName}`
        : order.source === 'global-esim-jp'
          ? `https://www.gmobile.biz/esim/region/${countryName}`
          : `https://esim.gmobile.biz/region/${countryName}`;

    const payload = {
      access_token: accessToken,
      orders: [
        {
          customer_email: user.email,
          customer_name: user.firstName + ' ' + user.lastName,
          customer_id: user.id,
          customer_nickname: user.firstName + ' ' + user.lastName,
          order_id: order.orderId,
          order_date: order.createdAt,
          currency_iso: 'JPY',
          products: {
            [`${envPrefix}${productIdPrefix}-${order.planId}`]: {
              url: productUrl,
              name: name,
              image: `https://esim.gmobile.biz/api/v1/plans/${order.planId}/cover-image`,
              description: `海外旅行用 ${jpCountryName}向け eSIM`,
              group_name: 'gmobile-esim-' + countryName,
              price: order.jpyPrice,
            },
          },
        },
      ],
    };
    return payload;
  }
  async getUkomiAccessToken(): Promise<string> {
    try {
      const formData = new FormData();
      formData.append(
        'api_key',
        this.configService.get<string>('UKOMI_API_KEY'),
      );
      formData.append(
        'api_secret',
        this.configService.get<string>('UKOMI_API_SECRET'),
      );

      const authResponse = await axios({
        method: 'post',
        url: 'https://api.u-komi.com/auth/access_token',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      const responseData = authResponse.data;

      if (responseData?.status !== 'OK' || !responseData?.data?.access_token) {
        throw new Error(
          `Invalid response from Ukomi: ${responseData?.message || 'Unknown error'
          }`,
        );
      }
      return responseData?.data?.access_token;
    } catch (error) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        JSON.stringify(error);
      throw new Error(
        `Authentication with Ukomi failed. Reason: ${errorMessage}`,
      );
    }
  }
}
