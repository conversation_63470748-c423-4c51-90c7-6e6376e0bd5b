import { HttpModule } from '@nestjs/axios';
import { Logger, MiddlewareConsumer, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AppsService } from 'src/apps/apps.service';
import { AuthModule } from 'src/auth/auth.module';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EmailsService } from 'src/emails/emails.service';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { EsimService } from 'src/esim/esim.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { BasicAuthMiddleware } from 'src/middleware/BasicAuthMiddleware';
import { NotificationsService } from 'src/notifications/notifications.service';
import { PaymentModule } from 'src/payment/payment.module';
import { PaymentService } from 'src/payment/payment.service';
import { PlansService } from 'src/plans/plans.service';
import { PrismaService } from 'src/prisma.service';
import { EsimPurchaseQueueWorker } from 'src/processors/esim-create-buy.worker';
import { RateService } from 'src/rate/rate.service';
import { UsersService } from 'src/users/users.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { EsimOrdersController } from './esim-orders.controller';
import { EsimOrdersService } from './esim-orders.service';
import { OrderUpdatesListener } from './listeners/order-updates.listener';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    AuthModule,
    PaymentModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 5000,
        maxRedirects: 5,
        baseURL: configService.get('USIMSA_HOST_NAME'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    EsimOrdersService,
    PrismaService,
    EsimService,
    RateService,
    Logger,
    XchangeService,
    OrderUpdatesListener,
    KeyValueStoreService,
    EsimProviderBuilder,
    ConfigService,
    PlansService,
    CouponsService,
    JwtService,
    EsimStocksService,
    UsersService,
    EmailsService,
    NotificationsService,
    EmailTemplateService,
    AwsCognitoService,
    PaymentService,
    UsersService,
    EsimPurchaseQueueWorker,
    AppsService,
    ReferralsService
  ],
  controllers: [EsimOrdersController],
  exports: [EsimOrdersService],
})
export class EsimOrdersModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(BasicAuthMiddleware)
      .forRoutes('esim-orders/:orderId', 'health');
  }
}
