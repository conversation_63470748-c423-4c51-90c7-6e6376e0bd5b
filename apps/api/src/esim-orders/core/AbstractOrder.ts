import { Injectable } from '@nestjs/common';
import { CouponsService } from 'src/coupons/coupons.service';
import { PlansService } from 'src/plans/plans.service';
import { EsimOrdersService } from '../esim-orders.service';
import { PriceCalculator } from '../PriceCalculator';
import { plans, orders } from '@prisma/client';

export abstract class AbstractOrder {
  orderId: string;
  planId: number;
  userId: string;
  insurace?: string;
  calculator: PriceCalculator;
  plan: plans;
  model: orders & { childOrders?: orders[] };

  constructor(
    protected planService: PlansService,
    protected couponsService: CouponsService,
    protected esimOrderService: EsimOrdersService,
  ) {}

  abstract process();

  getTotalPrice() {
    return this.calculator.calculateFinalPrice({ round: true });
  }
}
