import { plans } from '@prisma/client';
import { CouponsService } from 'src/coupons/coupons.service';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';
import { PlansService } from 'src/plans/plans.service';
import { EsimOrdersService } from '../esim-orders.service';
import { PriceCalculator } from '../PriceCalculator';
import { AbstractOrder } from './AbstractOrder';

export class SubOrder extends AbstractOrder {
  orderId: string;
  planId: number;
  userId: string;
  insurace?: string;
  calculator: PriceCalculator;
  plan: plans;

  constructor(
    protected planService: PlansService,
    protected couponsService: CouponsService,
    protected esimOrderService: EsimOrdersService,
  ) {
    super(planService, couponsService, esimOrderService);
  }

  addInsurance(insurance: string) {
    this.insurace = insurance;
  }

  getPlan() {
    return this.plan;
  }

  async process(
    options: {
      requestOriginServiceName?: ServicePlansEnum;
      fallbackRequestOriginServiceName?: ServicePlansEnum;
    } = {},
  ) {
    this.plan = await this.planService.getPlanById(this.planId, {
      requestOriginServiceName: options.requestOriginServiceName,
      serviceProvider: {
        enabled: undefined,
      },
      fallbackRequestOriginServiceName:
        options.fallbackRequestOriginServiceName,
    });
    this.calculator = new PriceCalculator(
      this.plan.prices?.[this.plan?.defaultCurrency] || this.plan.price,
      {
        calculateTaxOnBasePrice: true,
      },
    );
    if (this.insurace === 'insured') {
      this.calculator.addInsurance();
    }
  }
}
