import {
  PAYMENT_STATUS,
  coupons,
  coupons_orders,
  plans
} from '@prisma/client';
import { sumBy } from 'lodash';
import { CurrencyList } from 'src/constants';
import { CouponsService } from 'src/coupons/coupons.service';
import { PlansService } from 'src/plans/plans.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { EsimOrdersService } from '../esim-orders.service';
import { PriceCalculator } from '../PriceCalculator';
import { AbstractOrder } from './AbstractOrder';
import { IQuotation } from './IQuotation';
import { SubOrder } from './SubOrder';

export class Order extends AbstractOrder {
  orderId: string;
  planId: number;
  insurace?: string;
  orders: SubOrder[];
  coupon?: coupons;
  calculator: PriceCalculator;
  plan: plans;

  constructor(
    protected planService: PlansService,
    protected couponsService: CouponsService,
    protected esimOrderService: EsimOrdersService,
    private xe: XchangeService,
  ) {
    super(planService, couponsService, esimOrderService);
    this.orders = [];
  }

  addSubOrder(order: SubOrder) {
    this.orders.push(order);
  }

  addInsurance(insurance: string) {
    this.insurace = insurance;
  }

  async process() {
    const price = this.orders.reduce(
      (acc, cul) =>
        (acc += cul.calculator.calculateFinalPrice({ round: true })),
      0,
    );

    this.calculator = new PriceCalculator(price, {
      calculateTaxOnBasePrice: false,
    });
  }

  async applyCoupon(
    coupon: { referenceId?: string; couponId?: string },
    userId: string | undefined,
    options?: { ignoreValidation?: boolean, source?: string, usersId?: number },
  ): Promise<{ reference?: coupons_orders; coupon: coupons }> {
    let couponModel = coupon.referenceId
      ? (
        await this.couponsService.getCouponByReference(coupon.referenceId, {
          coupons_orders: false,
          referrals: false,
        })
      ).coupon
      : await this.couponsService.getCouponByCode(coupon.couponId, {
        coupons_orders: false,
        referrals: false,
      });

    if (!options?.ignoreValidation) {
      for (const order of this.orders) {
        await this.couponsService.validate(couponModel, userId, order.plan, {
          source: options?.source
        });
      }
    }

    this.calculator.applyDiscount({
      discount: couponModel.discount,
      type: couponModel.type,
    });
    this.coupon = couponModel;
    if (!userId) return { coupon: couponModel };
    const reference =
      await this.couponsService.getCouponReferenceByReferenceOrCode({
        userId,
        coupon: couponModel,
        usersId: options?.usersId,
      });
    return { reference, coupon: couponModel };
  }

  async commitQuotation(quotation: IQuotation, params: { userId: number }) {
    const parentOrder = await this.esimOrderService.createOrder({
      requiredParams: {
        paymentStatus: PAYMENT_STATUS.SUCCESS,
      },
      optionalParams: {
        price: quotation.total,
        userId: params.userId,
        provisionPrice: 0,
        orderCreatedAt: new Date(),
      },
    });

    const subOrders = [];
    // Save all sub orders
    for (const order of quotation.orders) {
      const subOrder = await this.esimOrderService.createOrder({
        requiredParams: {
          paymentStatus: PAYMENT_STATUS.PENDING,
        },

        optionalParams: {
          price: order.plan.price,
          provisionPrice: isNaN(order.plan.provision_price)
            ? 0
            : order.plan.provision_price,
          userId: params.userId,
          jpyPrice: order.xe['JPY'],
          orderCreatedAt: new Date(),
          planId: parseInt(order.plan.planId),
        },
      });
      order.model = subOrder;
      subOrders.push(subOrder);
    }

    // const order = await this.esimOrderService.

    quotation.model = parentOrder;
    return quotation;
  }

  async commit(params: { userId: number }) {
    const cal = this.calculator;
    const parentOrder = await this.esimOrderService.createOrder({
      requiredParams: {
        paymentStatus: PAYMENT_STATUS.SUCCESS,
      },
      optionalParams: {
        price: cal.calculateFinalPrice(),
        userId: params.userId,
        provisionPrice: 0,
        orderCreatedAt: new Date(),
      },
    });

    const subOrders = [];
    // Save all sub orders
    for (const order of this.orders) {
      const xeRate = await this.xe.getRateOfCurrencyAmount(
        '1',
        CurrencyList.JPY,
      );
      const subOrder = await this.esimOrderService.createOrder({
        requiredParams: {
          paymentStatus: PAYMENT_STATUS.PENDING,
        },
        optionalParams: {
          price: order.plan.price,
          provisionPrice: isNaN(order.plan.provision_price)
            ? 0
            : order.plan.provision_price,
          userId: params.userId,
          jpyPrice: Math.ceil(order.getTotalPrice() * xeRate),
          orderCreatedAt: new Date(),
          planId: parseInt(order.plan.planId),
        },
      });
      order.model = subOrder;
      subOrders.push(subOrder);
    }

    // const order = await this.esimOrderService.

    this.model = parentOrder;
    return this;
  }

  async serialize(): Promise<IQuotation> {
    let baseCurrency = 'USD';
    const xe = await this.xe.getRateOfCurrency('1', { baseCurrency });
    const orders = this.orders.map((item) => {
      const insured = !!item.insurace?.length;
      const totalPrice = item.getTotalPrice();
      const insurancePrice = insured ? item.calculator.getInsuranceAmount() : 0;
      const priceWithoutInsurance = totalPrice - insurancePrice;

      if (item.plan.prices?.[item.plan.defaultCurrency]) {
        baseCurrency = 'JPY';
      }

      return {
        insured,
        price: totalPrice,
        plan: item.plan,
        xe: this.xe.getXchanges(item.getTotalPrice(), xe, {
          prices: item.plan.prices as object,
          defaultCurrency: baseCurrency,
        }),
        insurance: insurancePrice,
        priceWithoutInsurance,
        taxableAmount: item.calculator.getPriceBeforeTax(),
        tax: item.plan.price - item.calculator.getPriceBeforeTax(),
      };
    });
    const xes = orders.map((item) => item.xe).flat();
    let cumulativeXe = {
      [CurrencyList.JPY]: sumBy(xes, CurrencyList.JPY),
      [CurrencyList.EUR]: +sumBy(xes, CurrencyList.EUR).toFixed(2),
      [CurrencyList.AUD]: +sumBy(xes, CurrencyList.AUD).toFixed(2),
      [CurrencyList.CAD]: +sumBy(xes, CurrencyList.CAD).toFixed(2),
      [CurrencyList.TWD]: +sumBy(xes, CurrencyList.TWD).toFixed(2),
      [CurrencyList.HKD]: +sumBy(xes, CurrencyList.HKD).toFixed(2),
      [CurrencyList.CNY]: Math.ceil(sumBy(xes, CurrencyList.CNY)),
      [CurrencyList.KRW]: Math.ceil(sumBy(xes, CurrencyList.KRW)),
      [CurrencyList.USD]: +sumBy(xes, CurrencyList.USD).toFixed(2),
      [CurrencyList.GBP]: +sumBy(xes, CurrencyList.GBP).toFixed(2),
    };
    const intialCumulativeXe = { ...cumulativeXe };

    const withoutDiscountTotal = cumulativeXe[baseCurrency];
    let totalPrice = withoutDiscountTotal;

    if (this.calculator.discount) {
      totalPrice = +(
        withoutDiscountTotal - this.calculator.getDiscountAmount()
      ).toFixed(2);
    }

    const discount = +Number(withoutDiscountTotal - totalPrice).toFixed(2);
    const discountXe = discount
      ? this.xe.getXchanges(discount, xe, { defaultCurrency: baseCurrency })
      : null;
    if (this.calculator.discount) {
      cumulativeXe = Object.keys(cumulativeXe).reduce((acc, cum) => {
        return {
          ...acc,
          [cum]:
            cumulativeXe[cum] -
            (cum === 'JPY' ? Math.ceil(discountXe[cum]) : discountXe[cum]),
        };
      }, cumulativeXe);
    }

    return {
      total: totalPrice,
      totalWithoutDiscount: withoutDiscountTotal,
      totalWithoutDiscountXe: withoutDiscountTotal ? intialCumulativeXe : null,
      discount,
      discountXe,
      xe: cumulativeXe,
      coupon: this.coupon
        ? {
          code: this.coupon.code,
          discount: this.coupon.discount,
          type: this.coupon.type,
          validFrom: this.coupon.validFrom,
          validTill: this.coupon.validTill,
        }
        : undefined,
      orders: orders,
    };
  }
}
