import { plans } from '@prisma/client';

export interface IQuotation {
  total: number;
  totalWithoutDiscount: number;
  totalWithoutDiscountXe: DiscountXe;
  discount: number;
  discountXe: DiscountXe;
  xe: DiscountXe;
  coupon: Coupon;
  orders: Order[];
  model?: { id: number };
}

interface Coupon {
  code: string;
  discount: number;
  type: string;
  validFrom: Date;
  validTill: Date;
}

interface DiscountXe {
  JPY: number;
  EUR: number;
  AUD: number;
  CAD: number;
  GBP: number;
  TWD: number;
  HKD: number;
  CNY: number;
  KRW: number;
  USD: number;
}

interface Order {
  insured: boolean;
  price: number;
  plan: plans;
  xe: DiscountXe;
  model?: { id: number };
}
