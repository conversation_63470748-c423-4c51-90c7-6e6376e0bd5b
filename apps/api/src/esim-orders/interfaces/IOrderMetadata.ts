import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsDateString,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';

export interface IOrderMetadata {
  firstName?: string;
  lastName?: string;
  email?: string;
  bookingNo?: string;
  costPrice?: string;
  passportNo?: string;
  dob?: string;
  phone?: string;
  gender?: string;
  countryCode?: string;
  sendEmail?: string;
  channelName?: string;
  templat?: string;
}

export class OrderMetadataDto implements IOrderMetadata {
  @IsString()
  @ApiProperty()
  @IsNotEmpty()
  firstName?: string;

  @ApiProperty()
  @IsString()
  lastName?: string;

  @ApiProperty()
  @IsEmail()
  email?: string;

  @ApiProperty()
  @IsString()
  bookingNo?: string;

  @IsNumber()
  costPrice?: string;

  @ApiProperty()
  @IsString()
  passportNo?: string;

  @ApiProperty()
  @IsDateString()
  dob?: string;

  @ApiProperty()
  @IsString()
  phone?: string;

  @ApiProperty()
  @IsEnum(['M', 'F'])
  gender?: string;

  @IsString()
  @IsOptional()
  countryCode?: string;
}
