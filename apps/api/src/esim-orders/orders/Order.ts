import { plans } from '@prisma/client';
import { CouponsService } from 'src/coupons/coupons.service';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';
import { PlansService } from 'src/plans/plans.service';
import { EsimOrdersService } from '../esim-orders.service';
import { NewPriceCalculator } from '../NewPriceCalculator';
import { AbstractOrder } from './AbstractOrder';

export class Order extends AbstractOrder {
  orderId: string;
  planId: number;
  userId: string;
  insurace?: string;
  NewPriceCalculator: NewPriceCalculator;
  plan: plans;

  constructor(
    protected planService: PlansService,
    protected couponsService: CouponsService,
    protected esimOrderService: EsimOrdersService,
  ) {
    super(planService, couponsService, esimOrderService);
  }

  addInsurance(insurance: string) {
    this.insurace = insurance;
  }

  getPlan() {
    return this.plan;
  }

  async process(
    options: {
      requestOriginServiceName?: ServicePlansEnum;
      fallbackRequestOriginServiceName?: ServicePlansEnum;
      includeProvisionPrice?: boolean;
    } = {},
  ) {
    this.plan = await this.planService.getPlanById(this.planId, {
      requestOriginServiceName: options.requestOriginServiceName,
      fallbackRequestOriginServiceName:
        options.fallbackRequestOriginServiceName,
      includeProvisionPrice: options.includeProvisionPrice,
      serviceProvider: {
        enabled: undefined,
      },
    });
    //@ts-expect-error
    this.calculator = new NewPriceCalculator(this.plan.xe['USD'], {
      calculateTaxOnBasePrice: false,
    });
    if (this.insurace === 'insured') {
      this.calculator.addInsurance();
    }
  }
}
