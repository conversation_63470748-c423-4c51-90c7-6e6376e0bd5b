import {
  CURRENCY,
  PAYMENT_STATUS,
  coupons,
  coupons_orders,
  plans,
  users,
} from '@prisma/client';
import { CurrencyList } from 'src/constants';
import { CouponsService } from 'src/coupons/coupons.service';
import { PlansService } from 'src/plans/plans.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { EsimOrdersService } from '../esim-orders.service';
import { NewPriceCalculator } from '../NewPriceCalculator';
import { AbstractOrder } from './AbstractOrder';
// import { InsuranceService } from 'src/insurance/insurance.service';
import { isUndefined, omitBy, round, sumBy } from 'lodash';
import { sanitizeCurrencyCode } from 'src/utils';
import { BulkOrdersDto, BulkOrdersGuestDto } from '../dtos/BulkOrdersDto';
import { IQuotation, IQuotationError, OrderWithXe } from './IQuotation';
import { Order } from './Order';

export class Quotation extends AbstractOrder {
  orderId: string;
  planId: number;
  insurace?: string;
  orders: Order[];
  coupon?: coupons;
  calculator: NewPriceCalculator;
  plan: plans;

  constructor(
    protected planService: PlansService,
    protected couponsService: CouponsService,
    protected esimOrderService: EsimOrdersService,
    private xe: XchangeService, // private insuranceService: InsuranceService,
  ) {
    super(planService, couponsService, esimOrderService);
    this.orders = [];
  }

  addSubOrder(order: Order) {
    this.orders.push(order);
  }

  addInsurance(insurance: string) {
    this.insurace = insurance;
  }

  async process() {
    const price = this.orders.reduce(
      (acc, cul) =>
        (acc += cul.calculator.calculateFinalPrice({ round: true })),
      0,
    );

    this.calculator = new NewPriceCalculator(price, {
      calculateTaxOnBasePrice: false,
    });
  }

  async applyCoupon(
    coupon: { referenceId?: string; couponId?: string },
    userId?: string,
    options?: { ignoreValidation?: boolean; source?: string, usersId?: number },
  ): Promise<{ reference?: coupons_orders; coupon: coupons }> {
    const couponModel = coupon.referenceId
      ? (await this.couponsService.getCouponByReference(coupon.referenceId))
        .coupon
      : await this.couponsService.getCouponByCode(coupon.couponId);

    if (!options?.ignoreValidation) {
      for (const order of this.orders) {
        await this.couponsService.validate(couponModel, userId, order.plan, {
          source: options?.source,
        });
      }
    }

    this.calculator.applyDiscount({
      discount: couponModel.discount,
      type: couponModel.type,
      currency: couponModel.currency,
    });
    this.coupon = couponModel;
    if (!userId) return { coupon: couponModel };
    const reference =
      await this.couponsService.getCouponReferenceByReferenceOrCode({
        userId,
        coupon: couponModel,
        usersId: options?.usersId,
      });
    return { reference, coupon: couponModel };
  }

  private calculateDiscountedPrice(
    quotation: IQuotation,
    order: OrderWithXe,
    currencyCode: CURRENCY,
  ) {
    if (!quotation.discount) {
      return order.xe[currencyCode];
    }

    if (quotation.orders.length <= 1) {
      return quotation.xe[currencyCode];
    }

    const totalOriginalValue = quotation.totalWithoutDiscountXe[currencyCode];

    const orderProportion = order.xe[currencyCode] / totalOriginalValue;

    // Apply proportion to discounted total
    const discountedPrice = quotation.xe[currencyCode] * orderProportion;

    return currencyCode !== 'JPY'
      ? Math.round(discountedPrice * 100) / 100
      : Math.round(discountedPrice);
  }

  async commitQuotation(
    quotation: IQuotation,
    params: {
      userId: number;
      user: users;
      orderDto: BulkOrdersDto & BulkOrdersGuestDto;
    },
  ) {
    const { orderDto, user } = params;
    orderDto.currency = sanitizeCurrencyCode(orderDto?.currency);

    const parentOrder = !orderDto.topupOrderId
      ? await this.esimOrderService.createOrder({
        requiredParams: {
          paymentStatus: PAYMENT_STATUS.PARENT_ORDER,
        },
        optionalParams: {
          price: quotation.xe['USD'],
          paymentMethod: orderDto.paymentMethod,
          userId: params.userId,
          provisionPrice: 0,
          orderCreatedAt: new Date(),
        },
      })
      : await this.esimOrderService.get({ orderId: orderDto.topupOrderId });

    const subOrders = [];
    const xeRateJPY = await this.xe.getRateOfCurrencyAmount(
      '1',
      CurrencyList.JPY,
    );
    const xeRateCurrency = orderDto?.currency
      ? await this.xe.getRateOfCurrencyAmount(
        '1',
        CurrencyList[orderDto.currency],
      )
      : 0;

    // Save all sub orders
    for (const order of quotation.orders) {
      if (
        params.orderDto.topupOrderId &&
        parentOrder &&
        order?.plan?.countryId !== parentOrder?.plan?.countryId
      ) {
        throw new Error('Topup works only with same region`s eSIM');
      }

      const subOrder = await this.esimOrderService.createOrder({
        requiredParams: {
          paymentStatus: PAYMENT_STATUS.PENDING,
        },
        optionalParams: {
          paymentMethod: orderDto.paymentMethod,
          type: orderDto.topupOrderId ? 'TOPUP' : 'SUBSCRIBE',
          lang: orderDto.locale,
          parentOrderId: parentOrder.id,
          price: order.xe['USD'],
          provisionPrice: isNaN(order.plan.provision_price)
            ? 0
            : order.plan.provision_price,
          userId: params.userId,
          markedJPYPrice: quotation?.discount
            ? order.xe['JPY']
            : quotation.xe['JPY'],
          jpyPrice: this.calculateDiscountedPrice(
            quotation,
            order,
            CURRENCY.JPY,
          ),
          orderCreatedAt: new Date(),
          planId: order.plan.id,
          jpyExchangeUSD: Math.ceil(xeRateJPY),
          source: orderDto.source || user.source,
          affiliate: orderDto?.affiliate,
          currency: orderDto?.currency,
          exchangeRate: xeRateCurrency,
          finalPrice: this.calculateDiscountedPrice(
            quotation,
            order,
            orderDto.currency,
          ),
          orderMetaData: omitBy(
            {
              firstName: orderDto?.metadata?.firstName || user.firstName,
              lastName: orderDto?.metadata?.lastName || user.lastName,
              email:
                orderDto?.email || orderDto?.metadata?.lastName || user.email,
              // bookingNo: getFormmattedOrderId(order.id),
              //@ts-expect-error
              dob: orderDto?.metadata?.dob || orderDto?.metadata?.birthdate,
              phone: orderDto?.metadata?.phone,
              gender: orderDto?.metadata?.gender,
              passportNo: orderDto?.metadata?.passportNo,
              channelName: orderDto?.metadata?.channelName,
              countryCode:
                //@ts-expect-error
                orderDto?.metadata?.country || orderDto?.metadata?.countryCode,
            },
            isUndefined,
          ),
        },
      });
      // if (order.insured) {
      //   await this.insuranceService.insureOrder(
      //     subOrder.id,
      //     subOrder.jpyPrice,
      //     subOrder.price,
      //   );
      // }
      order.model = subOrder;
      subOrders.push(subOrder);
    }

    // const order = await this.esimOrderService.

    quotation.model = parentOrder;
    return quotation;
  }

  async commit(params: { userId: number }) {
    const cal = this.calculator;
    const parentOrder = await this.esimOrderService.createOrder({
      requiredParams: {
        paymentStatus: PAYMENT_STATUS.PARENT_ORDER,
      },
      optionalParams: {
        price: cal.calculateFinalPrice(),
        userId: params.userId,
        provisionPrice: 0,
        orderCreatedAt: new Date(),
      },
    });

    const subOrders = [];
    // Save all sub orders
    for (const order of this.orders) {
      const xeRate = await this.xe.getRateOfCurrencyAmount(
        '1',
        CurrencyList.JPY,
      );
      const subOrder = await this.esimOrderService.createOrder({
        requiredParams: {
          paymentStatus: PAYMENT_STATUS.PENDING,
        },
        optionalParams: {
          parentOrderId: parentOrder.id,
          price: order.plan.price,
          provisionPrice: isNaN(order.plan.provision_price)
            ? 0
            : order.plan.provision_price,
          userId: params.userId,
          jpyPrice: Math.ceil(order.getTotalPrice() * xeRate),
          orderCreatedAt: new Date(),
          planId: order.plan.id,
          jpyExchangeUSD: xeRate,
        },
      });
      if (order.insurace) {
        // await this.insuranceService.insureOrder(
        //   subOrder.id,
        //   subOrder.jpyPrice,
        //   subOrder.price,
        // );
      }
      order.model = subOrder;
      subOrders.push(subOrder);
    }

    // const order = await this.esimOrderService.

    this.model = parentOrder;
    return this;
  }

  async serialize(options?: {
    errors?: IQuotationError[];
  }): Promise<IQuotation> {
    // const xe = await this.xe.getRateOfCurrency('1', { baseCurrency });

    const orders = this.orders.map((item) => {
      const insured = !!item.insurace?.length;
      const totalPrice = item.getTotalPrice();
      const insurancePrice = insured ? item.calculator.getInsuranceAmount() : 0;
      const priceWithoutInsurance = totalPrice - insurancePrice;

      return {
        insured,
        price: totalPrice,
        plan: item.plan,
        //@ts-expect-error
        xe: item.plan.xe,
        insurance: insurancePrice,
        priceWithoutInsurance,
        taxableAmount: item.calculator.getPriceBeforeTax(),
        tax: item.plan.price - item.calculator.getPriceBeforeTax(),
      };
    });
    const xes = orders.map((item) => item.xe).flat();
    let cumulativeXe = {
      [CurrencyList.JPY]: sumBy(xes, CurrencyList.JPY),
      [CurrencyList.EUR]: +sumBy(xes, CurrencyList.EUR).toFixed(2),
      [CurrencyList.AUD]: +sumBy(xes, CurrencyList.AUD).toFixed(2),
      [CurrencyList.CAD]: +sumBy(xes, CurrencyList.CAD).toFixed(2),
      [CurrencyList.TWD]: +sumBy(xes, CurrencyList.TWD).toFixed(2),
      [CurrencyList.HKD]: +sumBy(xes, CurrencyList.HKD).toFixed(2),
      [CurrencyList.CNY]: Math.ceil(sumBy(xes, CurrencyList.CNY)),
      [CurrencyList.KRW]: Math.ceil(sumBy(xes, CurrencyList.KRW)),
      [CurrencyList.USD]: +sumBy(xes, CurrencyList.USD).toFixed(2),
      [CurrencyList.GBP]: +sumBy(xes, CurrencyList.GBP).toFixed(2),
      // [CurrencyList.PHP]: +sumBy(xes, CurrencyList.PHP).toFixed(2),
    };
    const intialCumulativeXe = { ...cumulativeXe };

    const withoutDiscountTotal = cumulativeXe['USD'];
    let totalPrice = withoutDiscountTotal;
    if (this.calculator.discount) {
      totalPrice = +(
        withoutDiscountTotal - this.calculator.getDiscountAmount()
      ).toFixed(2);
    }

    const discount = +Number(withoutDiscountTotal - totalPrice).toFixed(2);
    const discountXeRates = await this.xe.getRateOfCurrency('1', {
      baseCurrency: this.calculator.discount?.currency || 'USD',
    });
    const discountXe = discount
      ? this.xe.getXchangesNew(discount, discountXeRates, {
        type: 'DISCOUNT',
        defaultCurrency: this.calculator.discount?.currency || 'USD',
      })
      : null;
    // const discountXe = discount
    //   ? this.xe.getXchanges(discount, xe, { defaultCurrency: baseCurrency })

    if (this.calculator.discount) {
      cumulativeXe = Object.keys(cumulativeXe).reduce((acc, cum) => {
        const params = {
          ...acc,
          [cum]:
            cumulativeXe[cum] -
            (cum === 'JPY'
              ? Math.round(discountXe?.[cum] || 0)
              : discountXe?.[cum] || 0),
        };
        if (this.calculator.discount.type === 'PERCENTAGE') {
          params[cum] = Math.round(
            cumulativeXe[cum] -
            cumulativeXe[cum] * (this.calculator.discount.discount / 100),
          );
          params[cum] = [
            CurrencyList.JPY,
            CurrencyList.CNY,
            CurrencyList.KRW,
          ].includes(cum as CurrencyList)
            ? Math.round(
              cumulativeXe[cum] -
              cumulativeXe[cum] * (this.calculator.discount.discount / 100),
            )
            : round(
              cumulativeXe[cum] -
              cumulativeXe[cum] * (this.calculator.discount.discount / 100),
              2,
            );
          discountXe[cum] = cumulativeXe[cum] - params[cum];
        }
        return params;
      }, cumulativeXe);
    }

    return {
      totalWithoutDiscount: !discount ? null : withoutDiscountTotal,
      totalWithoutDiscountXe: !discount
        ? null
        : withoutDiscountTotal
          ? intialCumulativeXe
          : null,
      discount: !discount ? null : discount,
      discountXe,
      xe: cumulativeXe,
      coupon: this.coupon
        ? {
          code: this.coupon.code,
          discount: this.coupon.discount,
          type: this.coupon.type,
          validFrom: this.coupon.validFrom,
          validTill: this.coupon.validTill,
        }
        : undefined,
      orders: orders,
      errors: [...(options?.errors || [])],
    };
  }
}
