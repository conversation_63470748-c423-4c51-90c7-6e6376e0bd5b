import { plans } from '@prisma/client';

export interface IQuotationError {
  message: string;
  code: string;
}
export interface IQuotation {
  totalWithoutDiscount: number;
  totalWithoutDiscountXe: DiscountXe;
  discount: number;
  discountXe: DiscountXe;
  xe: DiscountXe;
  coupon: Coupon;
  orders: OrderWithXe[];
  model?: { id: number; response?: any };
  errors: IQuotationError[];
}

interface Coupon {
  code: string;
  discount: number;
  type: string;
  validFrom: Date;
  validTill: Date;
}

interface DiscountXe {
  JPY: number;
  EUR: number;
  AUD: number;
  CAD: number;
  GBP: number;
  TWD: number;
  HKD: number;
  CNY: number;
  KRW: number;
  USD: number;
}

export interface OrderWithXe {
  insured: boolean;
  price: number;
  plan: plans;
  xe: DiscountXe;
  model?: { id: number; jpyPrice: number };
}
