import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  COUPON_ORDER_STATE,
  INTENT_TYPE,
  PAYMENT_STATUS,
  Prisma,
  coupons,
  coupons_orders,
  orders,
  plans,
  users,
} from '@prisma/client';
import { Queue } from 'bullmq';
import { differenceInDays, isPast } from 'date-fns';
import { zonedTimeToUtc } from 'date-fns-tz';
import { isEmpty } from 'lodash';
import * as os from 'os';
import pretty from 'prettysize';
import {
  ESIM_EXPIRE_TIME_IF_NOT_ACTIVATED_DAYS,
  QUEUE_ESIM_CREATE_BUY,
} from 'src/constants';
import { CouponsService } from 'src/coupons/coupons.service';
import { OrderType } from 'src/esim/dto/OrderType.dto';
import { UsageDto } from 'src/esim/dto/UsageDto';
import { EsimService } from 'src/esim/esim.service';
import { ESIM_PROVIDER } from 'src/esim/providers/EsimProvider';
import { IStripeMetadata } from 'src/interface/IStripeMetadata';
import { PlansService } from 'src/plans/plans.service';
import { PrismaService } from 'src/prisma.service';
import {
  formatUTCtoJST,
  getFormmattedOrderId,
  getPlanDetailsApi,
  ordinal,
} from 'src/utils';
import { XchangeService } from 'src/xchange/xchange.service';
import { Order } from './core/Order';
import { SubOrder } from './core/SubOrder';
import { BulkOrdersDto, BulkOrdersGuestDto } from './dtos/BulkOrdersDto';
import { EsimPurchaseRequestDto } from './dtos/esim-purchase-request.dto';
import { IOrderMetadata } from './interfaces/IOrderMetadata';

import { CouponErrors } from 'src/coupons/core/CouponErrors';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';
import { ReferralsService } from 'src/referrals/referrals.service';
import { UsersService } from 'src/users/users.service';
import { Order as BulkOrder } from './orders/Order';
import { Quotation } from './orders/Quotation';

@Injectable()
export class EsimOrdersService {
  constructor(
    private prismaService: PrismaService,
    private esimService: EsimService,
    private logger: Logger,
    @InjectQueue(QUEUE_ESIM_CREATE_BUY)
    private webhookQueue: Queue,
    private configService: ConfigService,
    private plansService: PlansService,
    private couponService: CouponsService,
    private xe: XchangeService,
    private readonly jwtService: JwtService,
    private readonly referralService: ReferralsService,
    private readonly userService: UsersService,
  ) { }

  async createOrder({
    requiredParams,
    optionalParams,
  }: {
    requiredParams: Pick<orders, 'paymentStatus'>;
    optionalParams?: Partial<orders>;
  }) {
    return this.prismaService.orders.create({
      //@ts-ignore
      data: {
        paymentStatus: requiredParams.paymentStatus,
        response: {},
        ...requiredParams,
        ...(optionalParams || {}),
        type: optionalParams.type || 'SUBSCRIBE',
        orders: [],
      },
      include: {
        childOrders: true,
        plan: {
          include: {
            serviceProvider: true,
          },
        },
      },
    });
  }

  saveOrder(
    userId: number,
    orders: {}[],
    response: object,
    extra: Partial<{
      ip?: string;
      piId: string;
      paymentStatus: PAYMENT_STATUS;
      type: INTENT_TYPE;
      planId: number;
      orderId: string;
      lang?: string;
      source?: string;
    }>,
  ) {
    return this.prismaService.orders.create({
      data: {
        lang: extra.lang,
        userId: +userId,
        response,
        orders,
        pi_id: extra.piId,
        paymentStatus: extra.paymentStatus,
        type: extra.type,
        planId: extra.planId,
        source: extra.source,
      },
      include: {
        plan: {
          include: {
            serviceProvider: true,
          },
        },
      },
    });
  }

  getTotalOrdersCount() {
    return this.prismaService.orders.count();
  }

  getLastOrderOfUser(userId: string) {
    return this.prismaService.orders.findFirst({
      where: {
        userId: +userId,
      },
    });
  }

  async updateOrder(data: Partial<orders>, filter: Partial<orders>) {
    const update = await this.prismaService.orders.update({
      where: filter,
      data,
      include: {
        childOrders: true,
        user: true,
        apps: true,
        plan: {
          include: {
            country: true,
          },
        },
      },
    });
    return update;
  }

  async all(
    filter: Partial<Omit<orders, 'response' | 'orders' | 'orderMetaData'>>,
  ) {
    return await this.prismaService.orders.findMany({
      where: filter,
      include: {
        plan: true,
        user: !true,
      },
    });
  }

  get(filter: Partial<Omit<orders, 'response' | 'orders' | 'orderMetaData'>>) {
    return this.prismaService.orders.findFirstOrThrow({
      where: filter,
      include: {
        childOrders: {
          include: {
            plan: true,
          },
        },
        apps: true,
        plan: {
          include: {
            country: true,
            network: true,
            serviceProvider: {
              select: {
                name: true,
                enabled: true,
              },
            },
          },
        },
        user: true,
      },
    });
  }
  async export(params: {
    filter?: {
      orderId?: string;
      limit?: number; // limit and offset as numbers for pagination
      offset?: number;
      startDate?: string;
      endDate?: string;
    };
    usageLinkHost: string;
  }) {
    const query = Prisma.sql`
    SELECT 
      orders.orderId, 
      DATE_FORMAT(CONVERT_TZ(orders.orderCreatedAt,'+00:00', '+09:00'), "%Y-%m-%d") AS orderCreatedDate, 
      DATE_FORMAT(CONVERT_TZ(orders.orderCreatedAt,'+00:00', '+09:00'), "%H:%i:%s") AS orderCreatedTime, 
      orders.type AS orderType, 
      users.firstName, 
      users.lastName, 
      users.email, 
      plans.dataId, 
      plans.name, 
      countries.name AS country, 
      orders.iccid, 
      orders.topupId, 
      plans.planId AS optionId, 
      orders.activateCode, 
      orders.downloadLink, 
      orders.qrCodeImgUrl, 
      orders.jpyPrice, 
  
      plans.price,
      orders.provisionPrice AS provision_price,
      orders.stripeLatestChargeId AS stripeChargeId, 
      orders.pi_id AS paymentIntention,
      orders.paymentStatus AS orderStatus,
      apps.name AS appName,
      service_providers.name AS serviceProvider,
      plans.validityDays AS validityDays,
      LOWER(COALESCE(NULLIF(users.source, ''), NULLIF(users.userPool, ''))) AS userSource,
      orders.orderMetaData AS orderMetaData,
      CONCAT(${params.usageLinkHost
      }, orders.orderId, "?type=usage") AS usageCheckLink,
  
      -- Newly added fields
      orders.affiliate,
      orders.paymentMethod,
      orders.currency,
      orders.exchangeRate,
      orders.markedJPYPrice AS markedPrice,
      orders.finalPrice,
  
      -- Coupon information (assuming one coupon per order)
      coupons.code AS couponCode,
      coupons.discount AS couponDiscount,
      coupons.type AS couponType
  
    FROM orders 
    LEFT JOIN users ON users.id = orders.userId 
    LEFT JOIN apps ON apps.id = orders.appsId 
    LEFT JOIN plans ON orders.planId = plans.id 
    LEFT JOIN countries ON plans.countryId = countries.id 
    LEFT JOIN service_providers ON plans.serviceProviderId = service_providers.id
    LEFT JOIN coupons_orders ON coupons_orders.orderId = orders.parentOrderId
    LEFT JOIN coupons ON coupons.id = coupons_orders.couponsId
  
    WHERE (orders.paymentStatus = ${PAYMENT_STATUS.SUCCESS} 
      OR orders.paymentStatus = ${PAYMENT_STATUS.THIRD_PARTY_PURCHASE}
      OR orders.paymentStatus = ${PAYMENT_STATUS.PAYMENT_SUCCESS_ESIM_FAILED})
      ${params?.filter?.orderId
        ? Prisma.sql`AND orders.orderId = ${params.filter.orderId}`
        : Prisma.empty
      }
      ${params?.filter?.startDate && params?.filter?.endDate
        ? Prisma.sql`AND orders.orderCreatedAt BETWEEN ${params.filter.startDate} AND ${params.filter.endDate}`
        : Prisma.empty
      }
    ORDER BY orders.orderCreatedAt  
    ${params?.filter?.limit
        ? Prisma.sql`LIMIT ${params.filter.limit}`
        : Prisma.sql`LIMIT 10`
      }
    ${params?.filter?.offset
        ? Prisma.sql`OFFSET ${params.filter.offset}`
        : Prisma.empty
      }
  `;

    return this.prismaService.$queryRaw(query);
  }

  async index(
    filter: Partial<Omit<orders, 'response' | 'orders' | 'orderMetaData'>>,
    options?: {
      removeUser?: boolean;
      status?: string;
      page?: number;
      search?: string;
      sort?: {
        [key: string]: string;
      };
      limit?: number;
      bookedDate: string;
    },
  ) {
    const { page } = options || { page: 1 };
    const perPage = options.limit || 10;
    let skip, take;
    if (page) {
      skip = page === 1 || !page ? 0 : perPage * (Number(page || 1) - 1);
      take = perPage;
    }

    let nextFilter: Parameters<
      typeof this.prismaService.orders.findMany
    >['0']['where'] = filter;

    if (options.search) {
      const orFields = ['iccid', 'topupId', 'activateCode', 'orderId'];
      nextFilter = {
        OR: orFields.map((item) => ({
          [item]: {
            startsWith: options.search,
          },
        })),
      };
    }
    if (options.bookedDate) {
      const [startDate, endDate] = options.bookedDate.split?.('-');
      nextFilter.createdAt = {
        gte: zonedTimeToUtc(new Date(startDate), 'asia/tokyo'),
        lte: zonedTimeToUtc(new Date(endDate), 'asia/tokyo'),
      };
    }
    nextFilter.expireTime = {
      [options.status === 'expired' ? 'lte' : 'gte']:
        options.status === 'expired' ? new Date() : undefined,
    };
    const result = await this.prismaService.$transaction([
      this.prismaService.orders.count({
        where: {
          ...nextFilter,
          paymentStatus: {
            in: [PAYMENT_STATUS.SUCCESS, PAYMENT_STATUS.THIRD_PARTY_PURCHASE],
          },
        },
      }),
      this.prismaService.orders.findMany({
        where: {
          ...nextFilter,
          paymentStatus: {
            in: [PAYMENT_STATUS.SUCCESS, PAYMENT_STATUS.THIRD_PARTY_PURCHASE],
          },
        },
        orderBy: options.sort,
        include: {
          plan: {
            include: {
              network: true,
              country: true,
            },
          },
          user: options.removeUser
            ? false
            : {
              select: {
                firstName: true,
                lastName: true,
                profileImage: true,
                email: true,
                userId: true,
                createdAt: true,
                stripeId: true,
              },
            },
        },
        skip,
        take,
      }),
    ]);

    return {
      total: result[0] ?? 0,
      data: result[1],
    };
  }

  async superAccess() {
    const result = await this.export({
      usageLinkHost: process.env['FRONTEND_HOST'] + 'esim-orders/',
    });
    //@ts-ignore
    return result.map((order) => ({
      id: order.orderId,
      userId: '76bc4b77-f573-4794-80b0-4ba70905852f',
      ip: '::ffff:127.0.0.1',
      response: {
        code: '0000',
        message: 'success',
        products: [
          {
            smdp: 'ecprsp.eastcompeace.com',
            iccid: order.iccid,
            topupId: order.topupId,
            optionId: order.optionId,
            activateCode: order.activateCode,
            downloadLink: order.downloadLink,
            qrCodeImgUrl: order.qrCodeImgUrl,
          },
        ],
      },
      orders: [
        {
          qty: 1,
          optionId: order.optionId,
        },
      ],
      createdAt: order.orderCreatedDate,
      updatedAt: '2023-02-20T06:25:49.145Z',
      pi_id: 'pi_3MdSonHeVP5r75gK1FftNJhe',
      paymentStatus: 'SUCCESS',
      txnId: '',
      type: order.orderType,
      planId: order.optionId,
      iccid: order.iccid,
      topupId: order.topupId,
      activateCode: order.activateCode,
      qrCodeImgUrl: order.qrCode,
      stripeLatestChargeId: order.stripeChargeId,
      jpyPrice: order.jpyPrice,
      orderId: order.orderId,
      orderCreatedAt: order.orderCreatedAt,
      downloadLink: order.downloadLink,
      provision_price: order.provision_price,
      plan: {
        id: 157,
        name: order.name,
        country: 'Japan',
        planId: order.optionId,
        price: order.price,
        dataId: order.dataId,
        description: '',
        createdAt: '2023-01-30T08:42:12.913Z',
        updatedAt: '2023-02-22T09:56:39.238Z',
        provision_price: order.provision_price,
      },
      user: {
        id: 40,
        userId: '76bc4b77-f573-4794-80b0-4ba70905852f',
        stripeId: order.stripeId,
        firstName: order.firstName,
        lastName: order.lastName,
        email: order.email,
        createdAt: '2023-02-20T06:13:26.772Z',
        updatedAt: '2023-02-22T09:05:39.853Z',
        profileImage: null,
      },
    }));
  }

  orderStatus(data: {
    activateDate?: Date;
    expiredDate?: Date;
    createdDate?: Date;
  }) {
    if (
      !data.activateDate &&
      differenceInDays(new Date(), data.createdDate) >
      ESIM_EXPIRE_TIME_IF_NOT_ACTIVATED_DAYS
    )
      return 'Not Activated and Expired';

    if (!data.activateDate) return 'Not Activated';

    if (data.activateDate && isPast(data.expiredDate))
      return 'Used and Expired';

    if (data.activateDate && !isPast(data.expiredDate)) return 'RUNNING';

    return 'Unknown';
  }

  async orderDetailAndUsage(
    orderId: string,
    query: {
      userId?: number;
      type: 'usage' | 'none';
    },
  ) {
    let order = await this.get({
      orderId,
      userId: query.userId,
    });
    if (!order) return null;
    let usageInfo = {};
    if (query.type === 'usage') {
      try {
        const usage = await this.esimService.usage(
          {
            topupid: order.topupId,
            externalBookingNo: order.externalBookingNo,
          },
          order.plan.serviceProvider.name as ESIM_PROVIDER,
        );
        if (usage.code === '1002' || !usage?.topup) {
          return {
            order,
            usageInfo: {},
          };
        }
        const createTime =
          usage.topup.createTime &&
          formatUTCtoJST(usage.topup.createTime + 'Z');
        const expireTime =
          usage.topup.expireTime &&
          formatUTCtoJST(usage.topup.expireTime + 'Z');
        const activeTime =
          usage.topup.activeTime &&
          formatUTCtoJST(usage.topup.activeTime + 'Z');
        usageInfo = {
          ...usage,
          topup: {
            topupId: usage.topup.topupId,
            createTime,
            expireTime,
            activeTime,
            usage: pretty(Number(usage?.topup?.usage || 0) * 1024 * 1024),
          },
          originalResponse: usage.topup,
        };
        //@ts-expect-error
        order.orderStatus = this.orderStatus({
          createdDate: order.createdAt,
          activateDate: activeTime && new Date(activeTime),
          expiredDate: expireTime && new Date(expireTime),
        });

        if (order && !order.isActivated) {
          if (usage.topup.activeTime) {
            const nextOrder = await this.updateOrder(
              {
                activateDate:
                  usage.topup.activeTime &&
                  new Date(usage.topup.activeTime + 'Z'),
                expireTime:
                  usage.topup.expireTime &&
                  new Date(usage.topup.expireTime + 'Z'),
                isActivated: usage.topup.activeTime
                  ? !!usage.topup.activeTime
                  : false,
              },
              {
                id: order.id,
              },
            );
            order = {
              ...order,
              activateDate: nextOrder.activateDate,
              expireTime: nextOrder.expireTime,
              isActivated: nextOrder.isActivated,
            };
          }
        }
      } catch (err) {
        this.logger.error(err);
        return {
          order,
          usage: {},
        };
      }
    }
    return {
      order,
      usage: usageInfo,
    };
  }
  async createOrderRecord(
    data: {
      planId: number;
      metadata: object;
      bookingNo?: string;
      appsId?: number;
      userId?: number;
      provisionPrice?: number;
      language?: string;
    },
    options?: { ignoreConfilict?: boolean },
  ) {
    try {
      if (options?.ignoreConfilict) {
        throw new Error('Ignore conflict.');
      }
      const oldOrder = await this.prismaService.orders.findFirstOrThrow({
        where: {
          bookingNo: data.bookingNo,
          appsId: data.appsId,
        },
      });
      return oldOrder;
      // return new ContentConflictError(
      //   `An order ${oldOrder.orderId} has been already created for booking no ${data.bookingNo}`,
      // );
    } catch (err) {
      const order = await this.prismaService.orders.create({
        data: {
          lang: data.language,
          userId: data.userId,
          provisionPrice: data.provisionPrice,
          orderMetaData: data.metadata || {},
          bookingNo: data.bookingNo,
          paymentStatus: PAYMENT_STATUS.PENDING,
          type: INTENT_TYPE.SUBSCRIBE,
          orders: [],
          response: {},
          appsId: data.appsId,
          planId: data.planId || -1,
        },
      });
      await this.prismaService.orders.update({
        data: {
          orderId: getFormmattedOrderId(order.id),
        },
        where: {
          id: order.id,
        },
      });
      const orderId = getFormmattedOrderId(order.id);
      return { ...order, orderId };
    }
  }
  async initiateOrder(payload: EsimPurchaseRequestDto) {
    const plan = await this.prismaService.plans.findFirstOrThrow({
      where: {
        id: +payload.planId,
      },
      include: {
        serviceProvider: {
          select: {
            name: true,
          },
        },
      },
    });

    let parentOrder: orders;

    if (plan.serviceProvider.name === 'LGUPLUS' && payload.iccid) {
      const order = await this.prismaService.orders.findFirstOrThrow({
        where: {
          id: payload.orderId,
        },
        include: {
          parentOrder: true,
        },
      });

      parentOrder = order.parentOrder;
    }

    const orderInformation = {
      paymentStatus: PAYMENT_STATUS.PENDING,
      planId: +payload.planId,
      type: payload.iccid ? INTENT_TYPE.TOPUP : INTENT_TYPE.SUBSCRIBE,
      orders: [],
      response: {},
      [payload.appsId ? 'appsId' : 'userId']: payload.appsId || payload.userId,
      jpyPrice: isNaN(+payload.chargeAmount) ? 0 : +payload.chargeAmount,
      orderMetaData: payload.metadata as object,
    };
    const newOrder = await this.prismaService.orders.upsert({
      where: {
        id: payload.orderId,
      },
      create: orderInformation,
      update: orderInformation,
    });
    const orderId = getFormmattedOrderId(newOrder.id);

    let orderResponse;
    if (!newOrder.activateCode && !newOrder.iccid) {
      this.logger.log(
        `Building esim provider for  ${JSON.stringify(plan.serviceProvider)} `,
      );
      orderResponse = await this.esimService.subscribe(
        {
          orderId: orderId as unknown as string,
          products: {
            optionId: plan.planId,
            qty: 1,
          } as unknown as OrderType,
          metadata: payload.metadata,
          plan,
          iccid: payload.iccid,
          // Include parent order only for LGU topup
          ...(parentOrder ? { parentOrder } : {}),
        },
        plan.serviceProvider.name as ESIM_PROVIDER,
      );
      this.logger.log(
        `Request for new esim as we already dont have ${JSON.stringify(
          payload,
        )}`,
      );
      this.logger.log(orderResponse);
    } else {
      this.logger.log(
        `Using from existing saved esim ${JSON.stringify(payload)} `,
      );
      this.logger.log(newOrder);
      const smdp = await this.esimService.getSMDP(
        plan.serviceProvider.name as ESIM_PROVIDER,
      );
      orderResponse = !isEmpty(newOrder?.response || {})
        ? newOrder?.response
        : {
          code: '0000',
          products: [
            {
              //@ts-expect-error
              smdp: newOrder.response?.products?.[0]?.smdp || smdp,
              iccid: newOrder.iccid,
              topupId: newOrder.topupId,
              optionId: newOrder.planId,
              activateCode: newOrder.activateCode,
              downloadLink: newOrder.downloadLink,
              qrCodeImgUrl: newOrder.qrCodeImgUrl,
              qrcodeImgUrl: newOrder.qrCodeImgUrl,
              esimVendorUniqueReference: newOrder.externalBookingNo,
            },
          ],
        };
    }

    const esim = orderResponse?.products?.[0];
    if (!esim) {
      throw new Error('Unable to get esim for this order');
    }

    let failedMessage = null;

    if (!orderResponse || orderResponse instanceof Error) {
      failedMessage = `Charge was successfull but couldnt subscribe/topup for order ${orderId}, type SUBSCRIBE`;
    }
    if (orderResponse?.code != '0000' || !orderResponse?.products?.length) {
      failedMessage = `Failed to find products ${orderId}, code ${orderResponse?.code}`;
    }

    let lguTopupInfo: Partial<orders>;

    // For LGU topup order, there is start and end of new topup order
    if (
      orderResponse.lguTopup &&
      orderResponse.lguTopup.startDate &&
      orderResponse.lguTopup.endDate
    ) {
      lguTopupInfo = {
        activateDate: orderResponse.lguTopup.startDate,
        expireTime: orderResponse.lguTopup.endDate,
      };
    }

    const order = await this.updateOrder(
      {
        ...orderInformation,
        paymentStatus: failedMessage
          ? PAYMENT_STATUS.FAILED
          : PAYMENT_STATUS.PAYMENT_SUCCESS_ESIM_PENDING,
        txnId: '',
        response: orderResponse,
        orderId,
        topupId: esim?.topupId || '',
        lang: payload.lang,
        stripeLatestChargeId: payload.stripeChargeId || '',
        externalBookingNo:
          orderResponse?.products?.[0]?.esimVendorUniqueReference ||
          newOrder.externalBookingNo,
        ...lguTopupInfo,
      },
      {
        id: newOrder.id,
      },
    );
    if (failedMessage) throw new Error(failedMessage);
    const supportwebhook = await this.esimService.supportsWebhook(
      plan.serviceProvider.name,
    );
    if (!supportwebhook) {
      // Here handle esim reception from services which doesnt have webhook
      // for e.g usimsa has webhook and urocomm, lgu they dont have.
      // await this.newEsimQueue.add(
      //   `${order.orderId}:${esim.iccid}`,
      //   {
      //     orderId: order.id,
      //     appsId: order.appsId,
      //     userId: order.userId,
      //     plan: order.planId,
      //     orderResponse,
      //   },
      //   {
      //     jobId: `${order.orderId}:${esim.iccid}`,
      //     ...(this.configService.get('redis').queue || {}),
      //   },
      // );
    }
    return { order, orderResponse };
  }

  /**
   * Allows to query usage of any orders, for now this method is invoked
   * only by apps (api id/key) users
   * @param orderId string
   */
  async orderUsage(payload: UsageDto) {
    const order = await this.get({
      topupId: payload.topupid,
    });
    if (!order?.plan?.serviceProvider?.name)
      throw new Error('Unable to get usage information.');

    const usageResult = await this.esimService.usage(
      {
        topupid: payload.topupid,
      },
      order.plan?.serviceProvider?.name as ESIM_PROVIDER,
    );
    return usageResult;
  }

  async addOrderNotificationEntry(usersId: number, orderId: number) {
    const notification = await this.getNotificationMetadata(orderId);
    if (notification) return notification;
    return this.prismaService.order_notifications.create({
      data: {
        usersId,
        orderId,
        metadata: {},
      },
    });
  }

  getNotificationMetadata(orderId: number) {
    return this.prismaService.order_notifications.findFirst({
      where: {
        orderId,
      },
    });
  }

  async queueESIMPurchaseRequest(product: EsimPurchaseRequestDto) {
    const jobId = `${getFormmattedOrderId(product.orderId)}-${product?.metadata?.bookingNo || ""
      }-${os.hostname()}-${product.stripeChargeId || 'none'}${product?.retryKey ? `-${product.retryKey}` : ''}`;
    this.logger.log(
      `Added new job for queue purchase ${jobId}: ${JSON.stringify(
        product || {},
      )}`,
    );
    const queue = await this.webhookQueue
      .add(
        jobId,
        {
          payload: {
            ...product,
          },
        },
        {
          jobId: jobId,
          ...(this.configService.get('redis').queue || {}),
          attempts: 2,
        },
      )
      .catch((e) => {
        this.logger.error(e);
        throw new Error('Unable to process and add information to our system.');
      });
    return queue;
  }

  /**
   *
   * @param planId id of plan
   * @param userId user id
   * @returns
   */
  async createEsim(
    planId: number,
    user: users,
    metadata?: {
      lang?: string;
      template?: string;
    },
  ) {
    const plan = await this.plansService.getPlan(planId);
    if (!plan) throw new Error('Plan not found');
    const order = await this.saveOrder(
      user.id,
      [
        {
          qty: 1,
          optionId: plan.planId,
        },
      ],
      {},
      {
        piId: String(Date.now()),
        paymentStatus: PAYMENT_STATUS.PENDING,
        type: INTENT_TYPE.SUBSCRIBE,
        planId: planId,
        lang: metadata?.lang,
      },
    );

    // const response = await this.initiateOrder({
    //   chargeAmount: order.jpyPrice + '',
    //   planId: planId + '',
    //   lang: metadata.lang,
    //   orderId: order.id,
    // });
    const response = await this.queueESIMPurchaseRequest({
      planId: planId + '',
      lang: metadata.lang,
      chargeAmount: '0',
      metadata: {
        ...metadata,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        bookingNo: getFormmattedOrderId(order.id),
        phone: user.phone_number,
        template: metadata?.template,
      } as IOrderMetadata,
      orderId: order.id,
      planUrl: getPlanDetailsApi(order.planId + ''),
    });

    return { ...order, queue: response };
  }

  /*
   * @deprecated
   */
  async createOrderQuotation(
    orderDto: BulkOrdersDto & BulkOrdersGuestDto,
    params: {
      userId?: string;
      ignoreValidation?: boolean;
      fallbackRequestOriginServiceName: ServicePlansEnum;
      requestOriginServiceName: ServicePlansEnum;
    },
  ) {
    const order = new Order(
      this.plansService,
      this.couponService,
      this,
      this.xe,
    );
    for (const item of orderDto.products) {
      try {
        const subOrder = new SubOrder(
          this.plansService,
          this.couponService,
          this,
        );
        subOrder.planId = +item.optionId;
        await subOrder.process({
          requestOriginServiceName: params.requestOriginServiceName,
          fallbackRequestOriginServiceName:
            params.fallbackRequestOriginServiceName,
        });
        order.addSubOrder(subOrder);
      } catch (err) {
        this.logger.log(`Cart add error ${JSON.stringify(orderDto || {})}`);
        this.logger.error(err);
      }
    }
    await order.process();
    let couponReference:
      | { reference?: coupons_orders; coupon: coupons }
      | undefined;
    if (orderDto.couponId || orderDto?.couponReferenceId) {
      couponReference = await order.applyCoupon(
        {
          couponId: orderDto.couponId,
          referenceId: orderDto.couponReferenceId,
        },
        params.userId,
        {
          ignoreValidation: params.ignoreValidation,
        },
      );
    }
    const quotation = await order.serialize();
    return {
      order,
      coupon: couponReference?.coupon,
      couponReference: couponReference?.reference,
      quotation,
    };
  }

  async createOrderQuotationNew(
    orderDto: BulkOrdersDto & BulkOrdersGuestDto,
    params: {
      userId?: string;
      ignoreValidation?: boolean;
      requestOriginServiceName: ServicePlansEnum;
      fallbackRequestOriginServiceName: ServicePlansEnum;
      includeProvisionPrice?: boolean;
    },
  ) {
    const order = new Quotation(
      this.plansService,
      this.couponService,
      this,
      this.xe,
    );
    for (const item of orderDto.products) {
      try {
        const subOrder = new BulkOrder(
          this.plansService,
          this.couponService,
          this,
        );
        subOrder.planId = +item.optionId;
        await subOrder.process({
          includeProvisionPrice: params.includeProvisionPrice,
          requestOriginServiceName: params.requestOriginServiceName,
          fallbackRequestOriginServiceName:
            params.fallbackRequestOriginServiceName,
        });
        order.addSubOrder(subOrder);
      } catch (err) {
        this.logger.log(`Cart add error ${JSON.stringify(orderDto || {})}`);
        this.logger.error(err);
      }
    }
    await order.process();
    let couponCodeError = [];
    let couponReference:
      | { reference?: coupons_orders; coupon: coupons }
      | undefined;
    if (orderDto.couponId || orderDto?.couponReferenceId) {
      try {
        couponReference = await order.applyCoupon(
          {
            couponId: orderDto.couponId,
            referenceId: orderDto.couponReferenceId,
          },
          params.userId,
          {
            ignoreValidation: params.ignoreValidation,
            source: orderDto.source || params.fallbackRequestOriginServiceName,
          },
        );
      } catch (err) {
        this.logger.error(err);
        if (err instanceof CouponErrors) {
          couponCodeError = err.errors.map((err) => ({
            message: err.message,
            code: err.error,
          }));
        } else {
          couponCodeError = [
            {
              message: 'Something went wrong.',
              code: 'unknown',
            },
          ];
        }
      }
    }
    const quotation = await order.serialize({
      errors: couponCodeError,
    });
    return {
      order,
      coupon: couponReference?.coupon,
      couponReference: couponReference?.reference,
      quotation,
    };
  }

  /**
   * @deprecated
   * @param secret
   * @param requestedOrderId
   * @returns
   */
  async instantEsim(secret: string, requestedOrderId: string) {
    let orderId = requestedOrderId;
    const token = this.jwtService.verify(secret, {
      secret: EsimService.getInstantEsimSecret(),
    });

    const order = await this.prismaService.orders.findFirstOrThrow({
      where: {
        orderId,
      },
      include: {
        plan: true,
      },
    });

    orderId = token.orderId;
    return {
      qrCodeImgUrl: order.qrCodeImgUrl,
      order: {
        orderId: getFormmattedOrderId(order.id),
        qr: order.qrCodeImgUrl,
        iccid: order.iccid,
        plan: order.plan,
      },
    };
  }

  async instantEsimNew(
    secret: string,
    requestedOrderId: string,
    type: 'DOWNLOAD' | 'PING',
  ) {
    let orderId = requestedOrderId;
    let token;
    token = this.jwtService.verify(secret, {
      secret: EsimService.getInstantEsimSecret(),
    });
    const order = await this.prismaService.orders.findFirstOrThrow({
      where: {
        orderId,
      },
      include: {
        plan: { include: { country: true, network: true } },
        childOrders: {
          include: {
            plan: { include: { country: true, network: true } },
          },
        },
      },
    });
    if (token.orderId !== requestedOrderId && type === 'PING') {
      throw new Error('Wrong order id information used for polling.');
    }
    if (
      token.orderId !== getFormmattedOrderId(order.parentOrderId) &&
      type === 'DOWNLOAD'
    ) {
      throw new Error('Wrong order id information used for download.');
    }

    orderId = token.orderId;
    return {
      qrCodeImgUrl: order.qrCodeImgUrl,
      esims: order.childOrders?.map((item) => ({
        orderId: getFormmattedOrderId(item.id),
        qr: item.qrCodeImgUrl,
        plan: { ...item.plan, provision_price: null },
      })),
      order,
    };
  }
  async processEsimOrder(
    orderId: number,
    /**
     * @deprecated
     */
    userId?: string,
    stripePayload?: {
      amount: string;
      payment_intent?: string;
      latest_charge?: string;
      invoiceId?: string;
    },
    metadata?: IStripeMetadata,
  ) {
    const referralConfig = this.configService.get('referral');
    const esimOrder = await this.get({
      id: +orderId,
    });
    if (!esimOrder) throw new Error('No order found!');

    const profile = esimOrder.user;
    const processedOrders = [];
    // Please dont user plan of order for any other thing than taking provision price.
    for (const order of esimOrder.childOrders) {
      try {
        if (
          order.activateCode &&
          order.topupId &&
          order.orderCreatedAt &&
          esimOrder.paymentMethod !== 'invoice'
        ) {
          this.logger.log(`Order ${order.id} is already processed, skipping!`);
          continue;
        }

        let orderResponse;
        order.orderId = getFormmattedOrderId(order.id);
        await this.updatePaymentOrder(
          {
            paymentIntentId: stripePayload?.payment_intent,
            paymentStatus:
              esimOrder.paymentMethod === 'invoice'
                ? PAYMENT_STATUS.INVOICE_PAYMENT_SUCCESS
                : PAYMENT_STATUS.PAYMENT_SUCCESS_ESIM_PENDING,
            txnId: '',
            response: orderResponse,
            orderId: order.id,
          },
          {
            stripeLatestChargeId: stripePayload?.latest_charge || '',
            orderCreatedAt: new Date(),
            provisionPrice: isNaN(order.plan.provision_price)
              ? 0
              : order.plan.provision_price,
            orderId: getFormmattedOrderId(order.id),
            orderMetaData: {
              ...(typeof order.orderMetaData === 'object'
                ? order.orderMetaData
                : {}),
              bookingNo:
                (order.orderMetaData as IOrderMetadata)?.bookingNo ||
                getFormmattedOrderId(order.id),
            },
          },
        );
        if (esimOrder.paymentMethod !== 'invoice') {
          const response = await this.queueESIMPurchaseRequest({
            planId: order.planId + '',
            lang: order.lang,
            chargeAmount: order.jpyPrice + '',
            metadata: (order.orderMetaData || {}) as IOrderMetadata,
            orderId: order.id,
            planUrl: getPlanDetailsApi(order.planId + ''),
            stripeChargeId: stripePayload?.latest_charge,
            iccid: esimOrder.iccid,
          });
        }
        processedOrders.push(order.id);
      } catch (err) {
        this.logger.error(err);
      }
    }

    const couponOrder = await this.updateCouponState(
      metadata?.couponReferenceId,
      COUPON_ORDER_STATE.COMPLETED,
      esimOrder.id,
    );

    if (
      referralConfig.featureEnabled &&
      couponOrder?.coupon.referrals?.length
    ) {
      try {
        await this.referralService.acknowledgeReferral(
          couponOrder.coupon.referrals,
        );
        await this.referralService.notifyReferrer(
          couponOrder.coupon.referrals[0].user.email,
        );
        this.logger.log(
          `Referrer notified for successful referral ${couponOrder.coupon.referrals[0].user.email}`,
        );
      } catch (err) {
        this.logger.error(err);
      }
    }

    this.logger.log(
      `After successful charge for order ${esimOrder.id}: ${processedOrders}`,
    );
    return {
      order: esimOrder,
      profile,
      processedOrders,
    };
  }

  async updatePaymentOrder(
    {
      paymentStatus,
      txnId,
      response,
      orderId,
    }: {
      paymentIntentId: string;
      txnId: string;
      paymentStatus: PAYMENT_STATUS;
      response?: any;
      orderId: any;
    },
    rest: Partial<orders>,
  ) {
    const data = { paymentStatus, txnId };
    if (response) {
      //@ts-ignore
      data.response = response;
    }

    return this.updateOrder(
      { ...data, ...rest },
      {
        id: orderId,
      },
    );
  }

  async updateCouponState(
    coupondReferenceId: string,
    state: coupons_orders['state'],
    orderId: number,
  ) {
    const couponReference = await this.couponService.getCouponByReference(
      coupondReferenceId,
    );
    if (!couponReference) return;
    return this.couponService.updateCouponReference(couponReference.id, {
      state,
      orderId,
    });
  }

  async getSuccessfulParentOrdersCount(userId: number) {
    return await this.prismaService.orders.count({
      where: {
        userId,
        parentOrderId: null,
        childOrders: {
          some: {
            paymentStatus: PAYMENT_STATUS.SUCCESS,
          },
        },
      },
    });
  }

  async allocateCoupons(
    userId: string,
    order: orders & { plan: plans; user: users },
  ) {
    try {
      const existingLoyaltyCoupon =
        await this.prismaService.orders_loyalty_coupons.findFirst({
          where: {
            orderId: order.parentOrderId,
          },
        });

      if (existingLoyaltyCoupon) {
        this.logger.log(
          `Loyalty coupon already exists for this parent order id: ${order.parentOrderId}, skipping creation for this order`,
        );
        return null;
      }

      const cfg = this.configService.get<any>('loyalty');
      const user = await this.userService.getUserInfo(userId);
      const conversions = await this.getSuccessfulParentOrdersCount(user.id);

      const rules = cfg.discountByConversion;
      const discount =
        rules[conversions] !== undefined ? rules[conversions] : rules.default;

      const {
        reference,
        code: couponCode,
        couponDiscount,
        discountType,
      } = await this.couponService.createCouponForLoyalCustomers({
        userId,
        discount,
        discountType: 'PERCENTAGE',
        orderCount: conversions,
        order,
      });
      this.logger.log(
        `Generated ${discount}% coupon (${reference.couponId}) for user ${userId}`,
      );

      await this.couponService.notifyNewCoupon(user, {
        couponCode,
        couponDiscount,
        discountType,
        order,
        orderCount: ordinal(conversions),
      });
      this.logger.log(`Sent coupon email to ${user.email}`);

      return reference;
    } catch (err) {
      this.logger.log(`Unable to create coupon`);
      this.logger.error(err);
    }
  }
}
