import {
  Controller,
  Get,
  Logger,
  NotFoundException,
  Param,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { ApiExcludeEndpoint } from '@nestjs/swagger';
import { EsimPurchaseQueueWorker } from 'src/processors/esim-create-buy.worker';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { EsimOrdersService } from './esim-orders.service';

@Controller('')
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class EsimOrdersController {
  constructor(
    private esimOrderService: EsimOrdersService,
    private logger: Logger,
    private esimPurchaseQueueWorker: EsimPurchaseQueueWorker,
  ) {}

  @ApiExcludeEndpoint()
  @Get('/esim-orders/:orderId')
  public async onlyUsage(
    @Query() query: { type: 'usage'; resendemail?: boolean },
    @Param() params: { orderId: string },
  ) {
    if (query.resendemail) {
      return await this.esimPurchaseQueueWorker.emailSend(params.orderId, null);
    }
    return this.details(query, params);
  }

  private async details(
    @Query() query: { type: 'usage' },
    @Param() params: { orderId: string },
  ) {
    try {
      const usageOrder = this.esimOrderService.orderDetailAndUsage(
        params.orderId,
        query,
      );
      return usageOrder;
    } catch (err) {
      throw new NotFoundException(err.message);
    }
  }
}
