import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Res,
  Logger,
  UsePipes,
  ValidationPipe,
  UseInterceptors,
} from '@nestjs/common';
import { EmailTemplateService } from './email-template.service';
import { CreateEmailTemplateDto } from './dto/create-email-template.dto';
import { UpdateEmailTemplateDto } from './dto/update-email-template.dto';
import { I18n, I18nContext } from 'nestjs-i18n';
import { Request, Response } from 'express';
import { ApiExcludeEndpoint } from '@nestjs/swagger';
import { SentryInterceptor } from 'src/SentryInterceptor';

@Controller('email-template')
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class EmailTemplateController {
  private logger = new Logger(EmailTemplateController.name);

  constructor(private readonly emailTemplateService: EmailTemplateService) {}

  @Post()
  @ApiExcludeEndpoint()
  @UsePipes(ValidationPipe)
  create(
    @Body() payload: CreateEmailTemplateDto,
    @Req() request: Request,
    @Res() res: Response,
    @I18n() i18n: I18nContext,
  ) {
    //@ts-ignore
    request.app.render(payload.templateName, payload, (err, content) => {
      if (err) {
        this.logger.error(err);
        return res.sendStatus(500);
      }

      res.send(content);
    });
  }
}
