import { Injectable } from '@nestjs/common';
import { CreateEmailTemplateDto } from './dto/create-email-template.dto';
import axios from 'axios';

@Injectable()
export class EmailTemplateService {

  async getEmailTemplate(createEmailTemplateDto: CreateEmailTemplateDto) {
    const translatedMailResponse = await axios.post(
      `http://127.0.0.1:${process.env.APP_PORT || 3001}/api/v1/email-template`,
      createEmailTemplateDto,
    );
    return translatedMailResponse.data
  }

}
