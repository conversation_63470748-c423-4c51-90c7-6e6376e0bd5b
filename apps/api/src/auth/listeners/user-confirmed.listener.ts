import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { coupons } from '@prisma/client';
import axios from 'axios';
import { addDays } from 'date-fns';
import { CouponsService } from 'src/coupons/coupons.service';
import { EmailsService } from 'src/emails/emails.service';
import { FREE_ESIM_EVENT } from 'src/esim-orders/events';
import { FreeEsimEvent } from 'src/esim-orders/events/free-esim.event';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { isAppEnvDev } from 'src/utils';
import { UserRegisterSource } from '../dtos/auth-register-user';
import { USER_CONFIRMED_EVENT } from '../events';
import { IUserConfirmedEvent } from '../events/user-confirmed.event';

@Injectable()
export class UserConfirmedEventListener {
  private logger: Logger = new Logger('UserConfirmedEventListener');
  constructor(
    private eventEmitter: EventEmitter2,
    private couponService: CouponsService,
    private emailService: EmailsService,
  ) {}
  @OnEvent(USER_CONFIRMED_EVENT)
  async handleOrderSuccessful({ data }: { data: IUserConfirmedEvent }) {
    try {
      const { user } = data;
      this.logger.log(`A user ${data.user.id} confirmed their account.`);
      if (data.campaign === 'KOREAFREECAMPAIGN') {
        const coupon = await this.couponService.getCouponByCode(
          'KOREAFREECAMPAIGN',
        );
        // const constraintsTwo = await this.couponService.createConstraint({
        //   name: 'Self_' + data.uniqueCode,
        //   rule: {
        //     value: [data.user.userId],
        //   },
        // });
        await this.couponService.create(
          {
            code: data.uniqueCode,
            discount: coupon.discount,
            totalUsage: 5,
            usagePerPerson: 5,
            validFrom: new Date(),
            validTill: addDays(new Date(), 30),
            type: 'AMOUNT',
            banners: [],
            currency: coupon.currency || 'JPY',
          } as coupons,
          coupon.coupons_constraints?.map?.((item) => ({
            ...item.constraints,
            rule: {},
          })),
          // .concat([constraintsTwo]),
        );

        // Send mail as well
        // campaignLink
        const translatedMailResponse = await axios.post(
          `http://127.0.0.1:${
            process.env.APP_PORT || 3001
          }/api/v1/payment/esim/mail/translate`,
          {
            campaignCode: data.uniqueCode,
            template: 'post-campaign-sms-mail',
            campaignLink: isAppEnvDev()
              ? `https://esim-dev.airtrip.jp/region/korea?via=${data.uniqueCode}`
              : `https://esim.airtrip.jp/region/korea?via=${data.uniqueCode}`,
          },
        );
        await this.emailService.sendEmail({
          to: user.email,
          bcc: ['<EMAIL>'],
          message: translatedMailResponse.data,
          subject:
            '【エアトリeSIM】SMS認証完了 / 次のステップに進んでください。',
          lang: 'jp',
          // @ts-expect-error
          origin:
            user.userPool === LocalUserPool.AIRTRIP ||
            user.source === UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP ||
            user.source === UserRegisterSource.AIRTRIP_ESIM_IOS_APP
              ? 'GLOBAL_ESIM_AIRTRIP'
              : 'GLOBAL_ESIM_DEFAULT',
        });
        return;
      }

      // This means we have already dispatched the free esim, so no need to dispatch it again
      if (data.uniqueCode?.toLowerCase?.().includes?.('gesim')) return;

      this.logger.log(`Free eSIM event dispatched.`),
        this.eventEmitter.emit(
          FREE_ESIM_EVENT,
          new FreeEsimEvent({
            user: user,
            campaignName: data.campaign,
            serviceName: data.serviceName,
          }),
        );
    } catch (err) {
      this.logger.error(err);
    }
  }
}
