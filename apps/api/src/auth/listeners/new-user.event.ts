import { Injectable, Logger } from '@nestjs/common';
import { EVENT_NEW_USER_REGISTERED } from '../events';
import { OnEvent } from '@nestjs/event-emitter';
import { users } from '@prisma/client';
import { ReferralsService } from 'src/referrals/referrals.service';

@Injectable()
export class NewUserEvent {
  private logger: Logger = new Logger('NewUserEvent');
  constructor(private referralService: ReferralsService) {}

  @OnEvent(EVENT_NEW_USER_REGISTERED)
  async handleOrderSuccessful({ user }: { user: users }) {
    try {
      const referral = await this.referralService.createReferralCode(user);
      this.logger.log(
        `New referral code generated for user ${user.userId} - ${referral.id}`,
      );
    } catch (err) {
      this.logger.error(err);
    }
  }
}
