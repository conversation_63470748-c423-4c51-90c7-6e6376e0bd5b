import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { IDENTITY_PROVIDER, users } from '@prisma/client';
import * as jwt from 'jsonwebtoken';
import { EmailNotVerifiedError } from 'src/errors/EmailNotVerifiedError';
import { InvalidCredentialsError } from 'src/errors/InvalidCredentialsError';
import { PaymentService } from 'src/payment/payment.service';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { UsersService } from 'src/users/users.service';
import { AuthChangePasswordUserDto } from './dtos/auth-change-password-user.dto';
import { AuthConfirmPasswordUserDto } from './dtos/auth-confirm-password-user.dto';
import { AuthForgotPasswordUserDto } from './dtos/auth-forgot-password-user.dto';
import { AuthLoginUserDto } from './dtos/auth-login-user';
import { AuthRegisterUserDto } from './dtos/auth-register-user';
import { RefreshTokenDto } from './dtos/refresh-token-dto';
import { SocialLoginEmailDto } from './dtos/social-login-email';
import { VerifyEmailDto } from './dtos/verify-email-dto';
import { LatestUserPool } from './pools/LatestUserPool';
import { LegacyUserPool } from './pools/LegacyUserPool';
import { UserPool } from './pools/UserPool';

@Injectable()
export class AwsCognitoService {
  private userPool: UserPool;
  private legacyUserPool: UserPool;

  constructor(
    protected paymentService: PaymentService,
    private userService: UsersService,
    protected configService: ConfigService,
    private readonly logger: Logger,
    protected eventEmitter: EventEmitter2,
  ) {
    this.legacyUserPool = new LegacyUserPool(
      paymentService,
      userService,
      configService,
      logger,
      eventEmitter,
    );
    this.userPool = new LatestUserPool(
      paymentService,
      userService,
      configService,
      logger,
      eventEmitter,
    );
  }

  getSecretHash(username: string) {
    return this.userPool.getSecretHash(username);
  }
  async registerUser(
    authRegisterUserDto: AuthRegisterUserDto,
    userPool: LocalUserPool = LocalUserPool.GLOBAL,
    options?: { supressVerificationEmail?: boolean },
  ) {
    const user = await this.userPool.registerUser(authRegisterUserDto, userPool, options);
    //@ts-expect-error
    if (!options?.supressVerificationEmail && user.profileImage !== 'Guest') {
      await this.resendVerificationEmail(
        authRegisterUserDto.email,
        null,
        userPool,
      );
    }
    return user;
  }

  async createLocalUser(
    data: {
      firstName: string;
      lastName: string;
      email: string;
      id: string;
      username: string;
      isVerified?: true;
      locale?: string;
      source?: string;
      phoneNumber?: string;
      userPool?: LocalUserPool;
      idpProvider?: IDENTITY_PROVIDER;
    },
    metadata: {} = {},
  ) {
    return this.userPool.createLocalUser(data, metadata);
  }

  async updateLastLogin(userId: number) {
    try {
      await this.userService.updateUser({ id: userId }, { lastLogin: new Date(), });
    } catch (err) {
      this.logger.error(err);
    }
  }

  async updateLastPasswordChange(filter: {
    id?: number;
    username?: string;
    userId?: string;
  }) {
    try {
      await this.userService.updateUser(filter, { lastPasswordChange: new Date(), });
    } catch (err) {
      this.logger.error(err);
    }
  }

  async authenticateUser(
    authLoginUserDto: AuthLoginUserDto,
    userpool: LocalUserPool = LocalUserPool.GLOBAL,
  ) {
    const user = await this.userService.getUserByEmail(
      authLoginUserDto.email,
      userpool,
      { idpProvider: 'IDP_MANUAL' },
    );
    if (!user) throw new Error('No user found');

    if (user && !user.isVerified && user.profileImage === 'Guest') {
      throw new Error('No user found');
    }

    try {
      if (!user.isVerified) {
        throw new EmailNotVerifiedError();
      }

      const authUser = await this.userPool.authenticateUser(
        authLoginUserDto,
        userpool,
        { idpProvider: 'IDP_MANUAL' },
      );

      await this.updateLastLogin(user.id);

      return authUser;
    } catch (err) {
      if (err?.code === 'NotAuthorizedException') {
        // This user is not in our new system, lets check if we have this user already

        await this.legacyUserPool.authenticateUserOnly(
          { ...authLoginUserDto, username: user.username },
          // userpool,
        );
        if (!user.isVerified) {
          throw new EmailNotVerifiedError();
        }
        // Migrate user to new DB
        // First we update user info to point to new User
        await this.userPool.adminRegisterUser(
          {
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            password: authLoginUserDto.password,
            locale: user.locale,
            phone: user.phone_number,
            source: user.source,
            username: user.username,
          },
          userpool,
          { supressVerificationEmail: true }
        );

        const authResponse = await this.userPool.authenticateUserOnly({
          ...authLoginUserDto,
          username: user.username,
        });
        const token = jwt.decode(authResponse.accessToken);

        await this.userService.updateUser(
          {
            username: user.username,
          },
          {
            userId: token.sub as string,
          },
        );
        await this.updateLastLogin(user.id);
        return authResponse;
        // And we create user
      }
      throw err;
    }
  }

  async refreshToken(
    refreshTokenDto: RefreshTokenDto,
    userpool: LocalUserPool = LocalUserPool.GLOBAL,
  ) {
    const user = await this.userService.getUserByEmail(
      refreshTokenDto.email,
      userpool,
    );

    try {
      if (!user) throw new InvalidCredentialsError();

      if (!user.isVerified) {
        throw new EmailNotVerifiedError();
      }

      const newAccessToken = await this.userPool.refreshToken(
        user.username,
        refreshTokenDto.refreshToken,
      );

      return newAccessToken;
    } catch (err) {
      throw err;
    }
  }

  /**
   * @todo something to be done here
   * @param payload
   * @param metadata
   */
  async confirmUser(
    payload: VerifyEmailDto,
    userpool: LocalUserPool | null,
    metadata?: { campaign?: string },
  ): Promise<users> {
    let awaitConfirm;
    let nextPayload = { ...payload };
    if (!nextPayload.username) {
      const userByEmail = await this.userService.getUserByEmail(
        payload.email,
        userpool,
      );
      nextPayload.username = userByEmail.username;
    }
    try {
      awaitConfirm = await this.userPool.confirmUser(nextPayload, metadata);
    } catch (err) {
      if (
        err.code !== 'NotAuthorizedException' &&
        !err.message.includes('CONFIRMED')
      )
        throw err;
    }
    const user = await this.userService.getUserByUsername(nextPayload.username);
    if (!user) throw new InvalidCredentialsError();

    if (user.isVerified) {
      throw new Error('Account already verified.');
    }
    const cognitoUser = await this.getUserAttributes(nextPayload.username);

    const isPhoneVerfied =
      cognitoUser.UserAttributes.find(
        (item) => item.Name === 'phone_number_verified',
      )?.Value === 'true';
    const phoneNumber = user.phone_number;
    // NOTE: verify email and phone number if it exists
    await this.userService.updateUser(
      {
        id: user.id,
      },
      {
        isVerified: true,
        isPhoneVerified: phoneNumber && isPhoneVerfied,
      },
    );
    await this.updateUserAttributes(
      {
        email_verified: 'true',
      },
      user.username,
    );

    await this.updateUserAttributes(
      {
        phone_number: '',
        phone_number_verified: 'false',
      },
      user.username,
    );
    return user;
  }

  async changeUserPassword(
    authChangePasswordUserDto: AuthChangePasswordUserDto,
    userPool?: LocalUserPool,
  ) {
    const user = await this.userPool.changeUserPassword(
      authChangePasswordUserDto,
      userPool,
    );

    const userData = await this.userService.getUserByEmail(authChangePasswordUserDto.email, userPool);
    await this.updateLastPasswordChange({
      username: userData.username || userData.userId,
    });

    return user;
  }
  async forgotUserPassword(
    authForgotPasswordUserDto: AuthForgotPasswordUserDto,
    userpool?: LocalUserPool,
  ) {
    return this.userPool.forgotUserPassword(
      authForgotPasswordUserDto,
      userpool,
    );
  }
  async socialLoginEmail(socialLoginEmail: SocialLoginEmailDto) {
    return this.userPool.socialLoginEmail(socialLoginEmail);
  }

  async confirmUserPassword(
    authConfirmPasswordUserDto: AuthConfirmPasswordUserDto,
    userpool?: LocalUserPool,
  ) {
    return this.userPool.confirmUserPassword(
      authConfirmPasswordUserDto,
      userpool,
    );
  }

  async getUserData(email: string) {
    return this.userPool.getUserData(email);
  }

  async getAccessTokenByAuthorizationCode(code: string, redirectUri?: string) {
    return this.userPool.getAccessTokenByAuthorizationCode(code, redirectUri);
  }

  async getUserInfoByToken(accessToken: string) {
    return this.userPool.getUserInfoByToken(accessToken);
  }

  async resendVerificationEmail(
    email: string,
    phone?: string,
    userPool?: LocalUserPool,
  ): Promise<users | Error> {
    // const user = await this.userService.getUserByEmail(email, userPool);
    // const cognitoUser = await this.getUserAttributes(user.username);

    // let isPhoneVerfied, phoneNumber;
    // if (user.phone_number) {
    //   isPhoneVerfied =
    //     cognitoUser.UserAttributes.find(
    //       (item) => item.Name === 'phone_number_verified',
    //     ) === 'true';
    //   phoneNumber = user.phone_number;
    //   await this.updateUserAttributes(
    //     {
    //       phone_number: '',
    //       phone_number_verified: 'false',
    //     },
    //     user.username,
    //   );
    // }

    const resend = await this.userPool.resendVerificationEmail(
      email,
      phone,
      userPool,
    );
    // await this.updateUserAttributes(
    //   {
    //     phone_number: '',
    //     phone_number_verified: 'false',
    //   },
    //   user.username,
    // );
    return resend;
  }

  async resendVerification(
    email: string,
    phone?: string,
    userPool?: LocalUserPool,
  ): Promise<users | Error> {
    return this.userPool.resendVerificationEmail(email, phone, userPool);
  }

  updateUserAttributes(
    data: Parameters<UserPool['updateUserAttributes']>[0],
    username: string,
  ) {
    return this.userPool.updateUserAttributes(data, username);
  }

  async getUserAttributes(username: string) {
    const user = await this.userPool.getUserAttributes(username);
    return user;
  }

  async createOrGetUser(
    register: AuthRegisterUserDto,
    userId: string,
    userpool: LocalUserPool,
  ) {
    let localUser = userId
      ? await this.userService.getUserInfo(userId, userpool)
      : null;
    if (!localUser) {
      if (!register?.email) {
        throw new Error(`Unable to proceed, please log in to continue.`);
      }
      try {
        await this.registerUser(
          {
            referral: register.referral,
            profileImage: 'Guest',
            email: register.email,
            firstName: register.firstName || 'Guest',
            lastName: register.lastName || 'Guest',
            locale: register.locale,
            password: Date.now() + '-auto',
            source: register.source,
          },
          userpool,
          { supressVerificationEmail: true }
        );
      } catch (err) {
        // Email already with us but user havent logged in
        this.logger.log(err.message);
        this.logger.log(
          `${register.email} is old customer who is already logged in but is not logged in.`,
        );
      } finally {
        // Ignore corporate users
        //@ts-expect-error
        localUser = await this.userService.getUserByEmail(
          register.email,
          userpool,
        );
      }
    }
    if (!localUser) {
      throw new Error(
        `We couldn't associate this request with any user, please log in to continue.`,
      );
    }

    return localUser;
  }

  async forceVerify(username: string, userPool?: LocalUserPool) {
    return this.userPool.forceVerify(username, userPool);
  }


}
