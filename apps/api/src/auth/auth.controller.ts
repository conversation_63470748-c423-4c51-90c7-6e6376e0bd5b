import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Get,
  Headers,
  Logger,
  Post,
  Query,
  Res,
  UnauthorizedException,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { I18n, I18nContext } from 'nestjs-i18n';
import { IUser } from 'src/interface/IUser';
import { NotificationChannelEnum } from 'src/notifications/NotificationChannelEnum';
import { NotificationsService } from 'src/notifications/notifications.service';
import { CustomValidationPipe } from 'src/pipes/custom-validation.pipe';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { UsersService } from 'src/users/users.service';
import {
  decryptAppStandard,
  decryptFromJWT,
  getFormmattedOrderId,
  getIdentityProvider,
} from 'src/utils';
import { AwsCognitoService } from './aws-cognito.service';
import { AuthChangePasswordUserDto } from './dtos/auth-change-password-user.dto';
import { AuthConfirmPasswordUserDto } from './dtos/auth-confirm-password-user.dto';
import { AuthForgotPasswordUserDto } from './dtos/auth-forgot-password-user.dto';
import { AuthLoginUserDto } from './dtos/auth-login-user';
import { AuthProfileUpdateDto } from './dtos/auth-profile-update.dto';
import { AuthRegisterUserDto } from './dtos/auth-register-user';
import { ProfileQueryDto } from './dtos/profile-query.dto';
import { RefreshTokenDto } from './dtos/refresh-token-dto';
import { ResendVerificationEmailDto } from './dtos/resend-verification-email.dto';
import { SocialLoginAppDto } from './dtos/social-login-app.dto';
import { SocialLoginEmailDto } from './dtos/social-login-email';
import { VerifyEmailDto } from './dtos/verify-email-dto';
import { USER_CONFIRMED_EVENT } from './events';
import { UserConfirmedEvent } from './events/user-confirmed.event';
import { GetUser } from './get-user.decorator';
import { GetUserPool } from './get-userpool';
import { IRegisterSuccessresponse } from './response/IRegisterSuccessResponse';
import { RegisterResponseFailed } from './response/RegisterResponseFailed';
// TODO: remove when recaptcha code generation is implemented on mobile app
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  GoogleRecaptchaException,
  GoogleRecaptchaValidator,
} from '@nestlab/google-recaptcha';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { FreeEsimListener } from 'src/esim-orders/listeners/free-esim.listener';
import * as Voucher from 'voucher-code-generator';

const MOBILE_SOURCE = [
  UserRegisterSource.GLOBAL_ESIM_IOS_APP,
  UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
  UserRegisterSource.AIRTRIP_ESIM_IOS_APP,
  UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP,
];

@Controller('auth')
@ApiTags('Authentication')
export class AuthController {
  constructor(
    private awsCognitoService: AwsCognitoService,
    private userService: UsersService,
    private readonly logger: Logger,
    private notificationService: NotificationsService,
    private eventEmitter: EventEmitter2,
    private readonly recaptchaValidator: GoogleRecaptchaValidator,
    private configService: ConfigService,
    private freeEsimListener: FreeEsimListener,
  ) {}

  @Get('/test-notification')
  @ApiExcludeEndpoint()
  testNotification(@Query() query: { id: number }) {
    return this.userService.subscribeUserEmailToNotification(+query.id);
  }

  @Post('/register')
  // TODO: re-add when recaptcha code generation is implemented on mobile app
  // @Recaptcha({ response: (req) => req.body.recaptcha })
  @UsePipes(ValidationPipe)
  @ApiOkResponse({
    status: 201,
    type: IRegisterSuccessresponse,
  })
  @ApiBadRequestResponse({
    description: 'Error message can be either string or an array of string',
    type: RegisterResponseFailed,
  })
  async register(
    @Body() authRegisterUserDto: AuthRegisterUserDto,
    @I18n() i18n: I18nContext,
    @GetUserPool() userpool: LocalUserPool,
  ) {
    try {
      // TODO: remove when recaptcha code generation is implemented on mobile app
      const result = await this.recaptchaValidator.validate({
        response: authRegisterUserDto.recaptcha,
      });

      // TODO: remove when recaptcha code generation is implemented on mobile app
      if (
        !MOBILE_SOURCE.includes(
          authRegisterUserDto.source as UserRegisterSource,
        )
      ) {
        if (!result.success) {
          throw new GoogleRecaptchaException(result.errors);
        }
      }

      authRegisterUserDto.locale = authRegisterUserDto.locale || i18n.lang;
      authRegisterUserDto.source =
        authRegisterUserDto.source || authRegisterUserDto.website;
      const user = await this.awsCognitoService.registerUser(
        authRegisterUserDto,
        userpool,
      );
      return user;
    } catch (err) {
      delete authRegisterUserDto.password;
      this.logger.error(
        `Sign up issue with ${JSON.stringify(
          authRegisterUserDto,
        )}: ${JSON.stringify({ userpool })}`,
      );
      this.logger.error(err);
      if (err.code === 'P2002') {
        throw new ConflictException('Email Already exists');
      }
      throw new BadRequestException({
        message: err.message,
        errorCode: err.code,
      });
    }
  }

  @Post('/login')
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        expiresIn: { type: 'number' },
        accessToken: { type: 'string' },
        refreshToken: { type: 'string' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },

        message: {
          type: 'string',
          example: "The username and password combination didn't match.",
        },
        error: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  @UsePipes(CustomValidationPipe)
  async login(
    @Body() authLoginUserDto: AuthLoginUserDto,
    @GetUserPool() userpool: LocalUserPool,
  ) {
    try {
      const login = await this.awsCognitoService.authenticateUser(
        authLoginUserDto,
        userpool,
      );
      this.logger.log(
        `A user logged in ${authLoginUserDto.email} - ${userpool}`,
      );
      return login;
    } catch (err) {
      const user = await this.userService.getUserByEmail(
        authLoginUserDto.email,
        userpool,
        { idpProvider: 'IDP_MANUAL' },
      );
      const isSendUsername = ['UserNotConfirmedException', 4007].includes(
        err.code,
      );
      const errorBody = {
        message: err.message,
        errorCode: err.code,
        username: isSendUsername ? user.username : null,
      };

      if (!isSendUsername) {
        delete errorBody.username;
      }
      delete authLoginUserDto.password;
      this.logger.error(
        `Sign in issue with ${JSON.stringify(
          authLoginUserDto,
        )}:${JSON.stringify({ userpool })}`,
      );
      this.logger.error(err);
      throw new BadRequestException(errorBody);
    }
  }

  @Post('/refresh-token')
  // @UsePipes(CustomValidationPipe)
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    @GetUserPool() userpool: LocalUserPool,
  ) {
    try {
      const token = await this.awsCognitoService.refreshToken(
        refreshTokenDto,
        userpool,
      );
      return token;
    } catch (err) {
      throw new BadRequestException({
        message: err.message,
        errorCode: err.code,
      });
    }
  }

  @Post('/token')
  async token(@Body('code') code: string) {
    try {
      if (!code) throw new UnauthorizedException();
      const jwt = decryptFromJWT(code) as { payload: string };
      const creds = decryptAppStandard(jwt?.payload);
      return JSON.parse(creds);
    } catch (err) {
      this.logger.error(err);
      throw new UnauthorizedException();
    }
  }

  @Post('confirm')
  @UsePipes(ValidationPipe)
  async confirm(
    @Query() query: { campaign: 'airtrip' },
    @Body() confirmUserPayload: VerifyEmailDto,
    @GetUserPool() userpool: LocalUserPool,
    @Headers('x-service-name') serviceName: string,
  ) {
    if (!serviceName) {
      throw new BadRequestException('Header "x-service-name" is required');
    }

    try {
      const user = await this.awsCognitoService.confirmUser(
        confirmUserPayload,
        userpool,
        {
          campaign: query.campaign,
        },
      );
      let uniqueCode = Voucher.generate({
        prefix: query?.campaign + '_',
        length: 5,
        count: 1,
        charset: Voucher.charset('alphabetic'),
      })[0].toUpperCase();

      const allowedCampaigns = ['gmo', 'airtrip', 'gmnikkeitrendi'];

      if (
        query?.campaign &&
        allowedCampaigns.includes(query.campaign.toLowerCase() as string)
      ) {
        const order = await this.freeEsimListener.dispatchFreeEsim({
          data: {
            user,
            campaignName: query.campaign,
            serviceName,
          },
        });
        uniqueCode = order.orderId || getFormmattedOrderId(order.id);
      }

      this.logger.log(
        `User confirmed: ${JSON.stringify({
          user,
          campaign: query?.campaign,
          uniqueCode,
        })}`,
      );
      this.eventEmitter.emit(
        USER_CONFIRMED_EVENT,
        new UserConfirmedEvent({
          user: user,
          campaign: query?.campaign,
          uniqueCode,
          serviceName,
        }),
      );
      return {
        ...user,
        type: query?.campaign?.toLowerCase?.().includes('koreafree')
          ? 'REDIRECT'
          : undefined,
        // type: query?.campaign === 'airtrip' ? undefined : 'REDIRECT',
        campaignCode: uniqueCode,
      };
    } catch (err) {
      this.logger.error(
        `Confirm failed: ${JSON.stringify({ query, confirmUserPayload })}`,
      );
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Post('change-password')
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  @UsePipes(ValidationPipe)
  async changePassword(
    @GetUser() user: IUser,
    @GetUserPool() userpool: LocalUserPool,
    @Body() changePasswordDto: AuthChangePasswordUserDto,
  ) {
    if (changePasswordDto.currentPassword === changePasswordDto.newPassword) {
      throw new BadRequestException(
        'New password should be different than old password',
      );
    }
    try {
      return await this.awsCognitoService.changeUserPassword(
        {
          email: user.username,
          ...changePasswordDto,
        },
        userpool,
      );
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }
  @Post('forgot-password')
  @UsePipes(ValidationPipe)
  async forgotPassword(
    @Body() authForgotPasswordUserDto: AuthForgotPasswordUserDto,
    @GetUserPool() userpool: LocalUserPool,
  ) {
    try {
      const response = await this.awsCognitoService.forgotUserPassword(
        authForgotPasswordUserDto,
        userpool,
      );
      return response;
    } catch (err) {
      throw new BadRequestException({
        message: err.message,
        errorCode: err.code,
      });
    }
  }

  @Post('/confirm-password')
  @UsePipes(ValidationPipe)
  async confirmPassword(
    @Body() authConfirmPasswordUserDto: AuthConfirmPasswordUserDto,
    @GetUserPool() userpool: LocalUserPool,
  ) {
    return await this.awsCognitoService.confirmUserPassword(
      authConfirmPasswordUserDto,
      userpool,
    );
  }
  @Post('/social/login/email')
  @UsePipes(ValidationPipe)
  async checkSocialLoginEmail(
    @Body() authConfirmPasswordUserDto: SocialLoginEmailDto,
  ) {
    try {
      const data = await this.awsCognitoService.socialLoginEmail(
        authConfirmPasswordUserDto,
      );
      return data;
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Post('/social/login/app')
  async socialLoginCallbackApp(
    @Body()
    query: SocialLoginAppDto,
  ) {
    let user;
    try {
      user = await this.socialLoginFunc(query?.idToken, query);
      return user;
    } catch (err) {
      this.logger.error(
        `Social sign up issue with from app ${user?.email} -  ${
          query?.userPool
        }: ${JSON.stringify(query)}`,
      );
      throw new BadRequestException(err.message);
    }
  }

  @Get('/social/login')
  async socialLoginCallback(
    @Query()
    query: {
      referral?: string;
      code: string;
      redirectUri?: string;
      userPool?: LocalUserPool;
      source?: string;
    },
  ) {
    let user;
    try {
      const { access_token, refresh_token, id_token } =
        await this.awsCognitoService.getAccessTokenByAuthorizationCode(
          query.code,
          query.redirectUri,
        );
      user = await this.socialLoginFunc(id_token, query);
      return {
        access_token,
        refresh_token,
      };
    } catch (err) {
      this.logger.error(
        `Social sign up issue with ${user?.email} -  ${query.userPool}:
        ${JSON.stringify(query)}`,
      );

      this.logger.error(err);
      if (err.code === 'P2002') {
        throw new ConflictException('Email already exists.');
      }
      throw new BadRequestException(err.message);
    }
  }

  private async socialLoginFunc(
    id_token: string,
    query: { referral?: string; userPool?: string; source?: string },
  ) {
    let user;
    try {
      user = await this.awsCognitoService.getUserInfoByToken(id_token);
      const metadata = {};
      if (query.referral) {
        //@ts-ignore
        metadata.referral = query.referral;
      }
      const userpool =
        query.userPool === LocalUserPool.AIRTRIP ||
        query.source === UserRegisterSource.AIRTRIP_ESIM_IOS_APP ||
        query.source === UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP
          ? LocalUserPool.AIRTRIP
          : LocalUserPool.GLOBAL;
      const uniqueUsername = user['cognito:username'] + ':' + userpool;
      if (!user.email) {
        throw new BadRequestException(
          'Sorry! Email address is required to create an account.',
        );
      }
      await this.awsCognitoService.createLocalUser(
        {
          email: user.email,
          firstName: user.given_name,
          source: query.source,
          lastName: user.family_name,
          id: user.sub + ':' + userpool,
          username: uniqueUsername,
          isVerified: true,
          userPool: userpool,
          idpProvider: getIdentityProvider(user['cognito:username']),
        },
        metadata,
      );
      await this.awsCognitoService.updateUserAttributes(
        { preferred_username: uniqueUsername },
        user['cognito:username'] || '',
      );
      return user;
    } catch (err) {
      this.logger.log(
        `Social sign up issue with ${user?.email} -  ${query.userPool}`,
      );

      this.logger.error(err);
      if (err.code === 'P2002') {
        throw new ConflictException('Email already exists.');
      }
      throw new BadRequestException(err.message);
    }
  }

  @Get('social/login/links')
  async socialLogin(
    @Res() res: Response,
    @Query() query: { platform: 'Google' | 'Facebook'; redirectUri?: string },
  ) {
    const getLink = (platform) =>
      `${this.configService.getOrThrow(
        'AWS_COGNITO_DOMAIN',
      )}oauth2/authorize?identity_provider=${platform}&redirect_uri=${
        query.redirectUri ||
        this.configService.getOrThrow('COGNITO_REDIRECT_URI')
      }&response_type=code&client_id=${this.configService.getOrThrow(
        'AWS_COGNITO_CLIENT_ID',
      )}&scope=openid&code_challenge_method=S256&code_challenge=${this.configService.getOrThrow(
        'PKCE_CODE_CHALLENGE',
      )}`;

    return res.redirect(getLink(query.platform));
  }

  @Get('verify/email')
  @UsePipes(ValidationPipe)
  async verifyEmail(@Res() res: Response, @Query() query: VerifyEmailDto) {
    if (!query.username) {
      throw new Error('Required parameters missing');
    }
    const host = query.website?.includes('airtrip')
      ? this.configService.getOrThrow('FRONTEND_HOST_AIRTRIP')
      : query.locale === 'jp'
      ? this.configService.get('FRONTEND_HOST_JP') ||
        'https://www.gmobile.biz/esim/'
      : this.configService.getOrThrow('FRONTEND_HOST');

    try {
      const [userPool, source] = query.website?.split?.(':') || [null, null];
      await this.awsCognitoService.confirmUser(
        query,
        userPool as LocalUserPool,
      );
      const user = await this.userService.updateUser(
        {
          username: query.username,
        },
        {
          isVerified: true,
        },
      );
      if (!user) {
        throw new BadRequestException('User not found.');
      }
      if (query.source === 'mobile-app') {
        res.send({
          statusCode: 200,
          data: {
            success: 'true',
          },
        });
      } else {
        let guestRedirect = '';
        // if (user.profileImage === 'Guest') {
        //   await this.awsCognitoService.forgotUserPassword(
        //     {
        //       email: user.email,
        //     },
        //     user.userPool as LocalUserPool,
        //   );
        // guestRedirect = '?guest=true';
        // }
        return res.redirect(
          host +
            (query.locale || 'en') +
            '/' +
            'verify/success/' +
            guestRedirect,
        );
      }
    } catch (err) {
      this.logger.error(err);
      if (query.source === 'mobile-app') {
        throw new BadRequestException(err.message || 'Failed to verify email.');
      } else {
        return res.redirect(
          host + (query.locale || 'en') + '/' + 'verify/fail',
        );
      }
    }
  }

  @Get('resend-verification')
  @Post('resend-verification')
  @UsePipes(ValidationPipe)
  async resendVerificationEmail(
    @Query() query: ResendVerificationEmailDto,
    @Body() body: ResendVerificationEmailDto,
    @GetUserPool() userpool: LocalUserPool,
  ) {
    try {
      await this.awsCognitoService.resendVerificationEmail(
        query.email || body.email,
        query.phone_number || body.phone_number,
        userpool,
      );
      return {
        success: 'true',
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException('Unable to resend verification email.');
    }
  }
  @Post('resend-phone-verification')
  @UsePipes(ValidationPipe)
  async resendPhoneVerificationEmail(
    @Body() body: ResendVerificationEmailDto,
    @GetUserPool() userpool: LocalUserPool,
  ) {
    try {
      const user = await this.awsCognitoService.resendVerification(
        body.email,
        body.phone_number,
        userpool || LocalUserPool.GLOBAL,
      );

      return {
        success: 'true',
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException('Unable to resend verification code.');
    }
  }

  @Get('profile')
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  async profile(
    @GetUser() user: IUser,
    @Query()
    query: ProfileQueryDto,
  ) {
    await ConfigModule.envVariablesLoaded;

    const profile = await this.userService.getUserInfo(user.userId);
    if (!profile) throw new UnauthorizedException();

    try {
      const audience = await this.notificationService.createAudience(
        user.appId,
      );
      if (audience) {
        if (query.token) {
          this.notificationService.subscribeAudienceToChannel(
            audience.audienceId,
            NotificationChannelEnum.PUSH_NOTIFICATION,
            query.token,
          );
        }
        this.notificationService.subscribeAudienceToChannel(
          audience.audienceId,
          NotificationChannelEnum.EMAIL,
          profile.email,
        );
      }
    } catch (err) {
      this.logger.error(err);
      // return null
    }

    return {
      email: profile.email,
      firstName: profile.firstName,
      lastName: profile.lastName,
      id: profile.id,
      createdAt: profile.createdAt,
      profileImage: profile.profileImage,
      defaultPaymentMethodId: profile.defaultPaymentMethodId,
      identityProvider:
        profile.idpProvider || getIdentityProvider(user.username),
      isVerified: profile.isVerified,
      locale: profile.locale,
      userPool: profile.userPool,
      corporate: profile.corporates_users?.length
        ? {
            id: profile.corporates_users?.[0].corporate.id,
            name: profile.corporates_users?.[0].corporate.name,
            code: profile.corporates_users?.[0].corporate.code,
            emailAddress: profile.corporates_users?.[0].corporate.emailAddress,
            enabled: profile.corporates_users?.[0].enabled,
          }
        : null,
    };
  }

  @Post('profile')
  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UsePipes(ValidationPipe)
  async updateProfile(
    @GetUser() user: IUser,
    @Body() userPayload: AuthProfileUpdateDto,
  ) {
    // await this.awsCognitoService.updateUserAttributes({
    //   email: user.username,
    //   firstName: userPayload.firstName,
    //   lastName: userPayload.lastName,
    // });
    const profile = await this.userService.updateUser(
      {
        userId: user.userId,
      },
      {
        locale: userPayload.locale,
        firstName: userPayload.firstName,
        lastName: userPayload.lastName,
        profileImage: userPayload.profileImage,
        defaultPaymentMethodId: userPayload.defaultPaymentMethodId,
      },
    );
    return {
      email: profile.email,
      firstName: profile.firstName,
      lastName: profile.lastName,
      id: profile.id,
      defaultPaymentMethodId: userPayload.defaultPaymentMethodId,
      createdAt: profile.createdAt,
      locale: userPayload.locale,
    };
  }

  @Post('change-email')
  @ApiBearerAuth()
  @UseGuards(AuthGuard())
  @UsePipes(ValidationPipe)
  async changeEmail(
    @GetUser() user: IUser,
    @Body() userPayload: AuthForgotPasswordUserDto,
  ) {
    return {};
  }
}
