import { HttpModule } from '@nestjs/axios';
import { CacheModule } from '@nestjs/cache-manager';
import { Logger, Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AppsService } from 'src/apps/apps.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EmailsService } from 'src/emails/emails.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { FreeEsimListener } from 'src/esim-orders/listeners/free-esim.listener';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { EsimService } from 'src/esim/esim.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { PaymentService } from 'src/payment/payment.service';
import { PlansService } from 'src/plans/plans.service';
import { PrismaService } from 'src/prisma.service';
import { UsersService } from 'src/users/users.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { APIKeyStrategy } from './api-key.strategy';
import { APIRequestCheckStrategy } from './api-request-check.strategy';
import { AuthController } from './auth.controller';
import { AwsCognitoService } from './aws-cognito.service';
import { JwtStrategy } from './jwt.strategy';
import { UserConfirmedEventListener } from './listeners/user-confirmed.listener';
import { NewUserEvent } from './listeners/new-user.event';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    HttpModule.register({
      baseURL: process.env['USIMSA_HOST_NAME'],
    }),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    CacheModule.register(),
  ],
  controllers: [AuthController],
  providers: [
    JwtStrategy,
    PaymentService,
    PlansService,
    PrismaService,
    UsersService,
    EsimOrdersService,
    Logger,
    XchangeService,
    KeyValueStoreService,
    UsersService,
    APIKeyStrategy,
    EsimService,
    AppsService,
    NotificationsService,
    EmailTemplateService,
    EsimProviderBuilder,
    EmailsService,
    APIRequestCheckStrategy,
    UserConfirmedEventListener,
    CouponsService,
    JwtService,
    AwsCognitoService,
    EsimStocksService,
    UsersService,
    NewUserEvent,
    ReferralsService,
    FreeEsimListener,
  ],
  exports: [AwsCognitoService],
})
export class AuthModule {}
