import {
  BadRequestException,
  createParamDecorator,
  ExecutionContext,
} from '@nestjs/common';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';

export const GetUserPool = createParamDecorator(
  (_, ctx: ExecutionContext): { name: string } => {
    const req = ctx.switchToHttp().getRequest();
    let userPool = req.headers['x-user-pool'] || LocalUserPool.GLOBAL;
    const allowedPools = [LocalUserPool.GLOBAL, LocalUserPool.AIRTRIP].includes(
      userPool,
    );
    if (!allowedPools) {
      userPool = LocalUserPool.GLOBAL;
    }

    return userPool;
  },
);
