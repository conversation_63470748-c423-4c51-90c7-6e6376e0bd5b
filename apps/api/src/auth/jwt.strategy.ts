import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Cache } from 'cache-manager';
import { Request } from 'express';
import { passportJwtSecret } from 'jwks-rsa';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AN_HOUR_IN_MILLISECONDS } from 'src/constants';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private userService: UsersService,
    @Inject(CACHE_MANAGER)
    private cacheService: Cache,
    private configService: ConfigService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      _audience: configService.getOrThrow('AWS_COGNITO_CLIENT_ID'),
      issuer: configService.getOrThrow('AWS_COGNITO_AUTHORITY'),
      algorithms: ['RS256'],
      passReqToCallback: true,
      secretOrKeyProvider: passportJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri:
          configService.getOrThrow('AWS_COGNITO_AUTHORITY') +
          '/.well-known/jwks.json',
      }),
    });
  }
  async getUser(sub: string) {
    const cachedUser = await this.cacheService.get(sub);
    if (cachedUser) return cachedUser;
    const user = await this.userService.getUserInfo(sub);
    if (!user) return null;
    await this.cacheService.set(sub, user, AN_HOUR_IN_MILLISECONDS);
    return user;
  }
  async validate(request: Request, payload: any) {
    const userpool =
      request.headers['x-user-pool'] === LocalUserPool.AIRTRIP
        ? LocalUserPool.AIRTRIP
        : LocalUserPool.GLOBAL;
    let user = await this.userService.getUserInfo(payload.sub, userpool);
    if (!user) {
      const subAccounts = await this.userService.getUserInfoUserAccounts({ cognitoUserSub: payload.sub + ":" + userpool, cognitoUserName: payload.username + ":" + userpool }, userpool)
      if (!subAccounts) return null;
      //@ts-expect-error
      user = subAccounts.user
    };


    return {
      appId: user?.id,
      userId: user.userId,
      username: user.username,
      corporateId: user.corporates_users?.[0]?.id,
    };
  }
}
