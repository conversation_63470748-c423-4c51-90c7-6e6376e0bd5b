import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';

export class ResendVerificationEmailDto {
  @ApiProperty()
  @ApiPropertyOptional()
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional()
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  phone_number?: string;
}
