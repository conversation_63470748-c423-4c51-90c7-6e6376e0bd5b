import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, Matches } from 'class-validator';
import { PASSWORD_REGEX, PASSWORD_WRONG_MESSAGE } from './auth-register-user';

export class AuthChangePasswordUserDto {
  @ApiProperty()
  @IsEmail()
  @IsOptional()
  email?: string;

  /* Minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character */

  @ApiProperty()
  @Matches(PASSWORD_REGEX, {
    message: PASSWORD_WRONG_MESSAGE,
  })
  currentPassword: string;

  @ApiProperty()
  @Matches(PASSWORD_REGEX, {
    message: PASSWORD_WRONG_MESSAGE,
  })
  newPassword: string;
}
