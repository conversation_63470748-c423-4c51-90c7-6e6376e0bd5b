import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Matches } from 'class-validator';
import { PASSWORD_REGEX, PASSWORD_WRONG_MESSAGE } from './auth-register-user';

export class AuthConfirmPasswordUserDto {
  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  confirmationCode: string;

  @Matches(PASSWORD_REGEX, {
    message: PASSWORD_WRONG_MESSAGE,
  })
  @ApiProperty()
  newPassword: string;
}
