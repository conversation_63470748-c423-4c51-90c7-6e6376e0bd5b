import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsUrl,
  Matches,
  MinLength,
} from 'class-validator';
import { PASSWORD_REGEX, PASSWORD_WRONG_MESSAGE } from './auth-register-user';

export class AuthProfileUpdateDto {
  @IsString()
  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  firstName?: string;

  @ApiProperty()
  @IsString()
  @MinLength(1)
  @IsOptional()
  lastName?: string;

  @IsString()
  @ApiProperty()
  @IsUrl()
  @MinLength(2)
  @IsOptional()
  profileImage?: string;

  @MinLength(2)
  @IsOptional()
  defaultPaymentMethodId?: string;

  @IsString()
  @IsString()
  @MinLength(2)
  @IsOptional()
  locale?: string;
}
