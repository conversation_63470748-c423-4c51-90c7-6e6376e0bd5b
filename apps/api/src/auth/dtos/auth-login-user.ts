import { ApiProperty } from '@nestjs/swagger';
import {
  IsAlphanumeric,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  Matches,
} from 'class-validator';
import { PASSWORD_REGEX, PASSWORD_WRONG_MESSAGE } from './auth-register-user';
import { InvalidCredentialsError } from 'src/errors/InvalidCredentialsError';
import { InvalidEmailError } from 'src/errors/InvalidEmailError';

export class AuthLoginUserDto {
  @ApiProperty()
  @IsEmail(
    {},
    {
      message: new InvalidEmailError().message,
    },
  )
  email: string;

  @ApiProperty()
  @IsString()
  password: string;

  @IsString()
  @IsOptional()
  @IsEnum(['authorization_code'])
  grant_type: string;
}
