import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  Matches,
  MinLength,
} from 'class-validator';

export const PASSWORD_REGEX = /^(?=.*).{8,}$/;

export const PASSWORD_WRONG_MESSAGE =
  'Password must have minimum eight characters.';

export enum UserRegisterSource {
  GLOBAL_ESIM_AIRTRIP = 'global_esim_airtrip',
  AIRTRIP = 'airtrip',
  GLOBAL_ESIM_JP = 'global-esim-jp',
  GLOBAL_ESIM = 'global-esim',
  GLOBAL_ESIM_ANDROID_APP = 'global-esim-android-app',
  GLOBAL_ESIM_IOS_APP = 'global-esim-ios-app',
  AIRTRIP_ESIM_ANDROID_APP = 'airtrip-esim-android-app',
  AIRTRIP_ESIM_IOS_APP = 'airtrip-esim-ios-app',
}

/** Keys of this enum is NEXT_PUBLIC_SERVICE_NAME used on frontend */
export enum ServiceName {
  GLOBAL_ESIM_AIRTRIP = 'global_esim_airtrip',
  GLOBAL_ESIM_JAPANESE = 'global_esim_japanese',
  GLOBAL_ESIM = 'global_esim',
  GLOBAL_ESIM_ANDROID_APP = 'global_esim_android_app',
  GLOBAL_ESIM_IOS_APP = 'global_esim_ios_app',
  AIRTRIP_ESIM_ANDROID_APP = 'airtrip_esim_android_app',
  AIRTRIP_ESIM_IOS_APP = 'airtrip_esim_ios_app',
}

export class AuthRegisterUserDto {
  @ApiProperty()
  @IsEmail(
    {},
    {
      message: 'Please provide valid email.',
    },
  )
  email: string;

  @IsString()
  @IsOptional()
  recaptcha?: string;

  /* Minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character */

  @ApiProperty()
  @Matches(PASSWORD_REGEX, {
    message: PASSWORD_WRONG_MESSAGE,
  })
  password: string;

  @IsString()
  @ApiProperty()
  @MinLength(1)
  firstName: string;

  @ApiProperty()
  @IsString()
  @MinLength(1)
  lastName: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @MinLength(2)
  @IsEnum(['en', 'jp', 'zh-cn', 'zh-tw', 'es', 'fr', 'ph', 'vi'])
  locale?: string;

  @ApiProperty()
  @IsString()
  @MinLength(2)
  @IsOptional()
  referral?: string;

  @ApiProperty()
  @IsString()
  @MinLength(2)
  @IsOptional()
  profileImage?: string;

  @ApiProperty()
  @IsOptional()
  @Matches(/^\d{9,12}$/, {
    message: '電話番号が無効です。正しい番号を再入力してください。',
  })
  phone?: string;

  @ApiProperty()
  @MinLength(2)
  @IsOptional()
  @IsEnum([
    UserRegisterSource.AIRTRIP,
    UserRegisterSource.GLOBAL_ESIM,
    UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
    UserRegisterSource.GLOBAL_ESIM_IOS_APP,
    UserRegisterSource.GLOBAL_ESIM_JP,
    UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP,
    UserRegisterSource.AIRTRIP_ESIM_IOS_APP,
  ])
  /**
   * @deprecated
   * Used for backward compatibility.
   * For any new integration prefer "source"
   */
  website?: string;

  @ApiProperty()
  @MinLength(2)
  @IsOptional()
  @IsEnum([
    UserRegisterSource.AIRTRIP,
    UserRegisterSource.GLOBAL_ESIM,
    UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
    UserRegisterSource.GLOBAL_ESIM_IOS_APP,
    UserRegisterSource.GLOBAL_ESIM_JP,
    UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP,
    UserRegisterSource.AIRTRIP_ESIM_IOS_APP,
  ])
  source?: string;
}
