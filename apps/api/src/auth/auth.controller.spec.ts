import * as request from 'supertest';
import { ApiErrorCodes } from 'src/constants/errors';

const HOST = 'https://esim-dev.gmobile.biz/api/v1';
const LOGIN_PAYLOAD_GLOBAL = {
  email: '<EMAIL>',
  password: 'Br0adlink',
};
const LOGIN_PAYLOAD_AIRTRIP = {
  email: '<EMAIL>',
  password: 'Br0adlink',
};
describe('Authentication', () => {
  let app: request.SuperTest<request.Test>;
  let airtripLogin: any;
  let globalLogin: any;

  beforeEach(async () => {
    app = request(HOST);
    airtripLogin = app.post('/auth/login').set('x-user-pool', 'AIRTRIP');
    globalLogin = app.post('/auth/login').set('x-user-pool', 'GLOBAL');
  });

  describe('Airtrip Login', () => {
    const creds = LOGIN_PAYLOAD_AIRTRIP;

    it('should return status code 201', () => {
      return airtripLogin.expect(400);
    });

    it('should return response time less than 200ms', async () => {
      const startTime = Date.now();
      await airtripLogin.send(creds);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(800);
    });

    it('should validate the response schema', () => {
      return airtripLogin
        .send(creds)
        .expect(201)
        .expect((res) => {
          const responseData = res.body;
          expect(responseData).toBeInstanceOf(Object);
          expect(responseData).toHaveProperty('statusCode');
          expect(responseData).toHaveProperty('data');
          expect(responseData.data).toBeInstanceOf(Object);
          expect(responseData.data).toHaveProperty('tokenType');
          expect(responseData.data).toHaveProperty('expiresIn');
          expect(responseData.data).toHaveProperty('accessToken');
        });
    });

    it('should throw wrong email', () => {
      return airtripLogin
        .send({
          email: '<EMAIL>',
          password: ' wrong',
        })
        .expect(400)
        .expect((res) => {
          const responseData = res.body;
          expect(responseData).toBeInstanceOf(Object);
          expect(responseData).toHaveProperty('errorCode');
          expect(responseData).toHaveProperty('message');
          expect(responseData.message).toBe(
            ApiErrorCodes.ERR_400_INVALID_USERNAME_PASSWORD.message,
          );
          expect(responseData.errorCode).toBe(
            ApiErrorCodes.ERR_400_INVALID_USERNAME_PASSWORD.errorCode,
          );
        });
    });
    it('should fail the login with creds of global', () => {
      return airtripLogin.send(LOGIN_PAYLOAD_GLOBAL).expect(400);
    });

    it('should throw wrong password/email', () => {
      return airtripLogin
        .send({
          email: creds.email,
          password: ' wrong',
        })
        .expect(400)
        .expect((res) => {
          const responseData = res.body;
          expect(responseData).toBeInstanceOf(Object);
          expect(responseData).toHaveProperty('errorCode');
          expect(responseData).toHaveProperty('message');
          expect(responseData.message).toBe('Incorrect username or password.');
          expect(responseData.errorCode).toBe('NotAuthorizedException');
        });
    });
  });

  describe('Global Login', () => {
    const creds = LOGIN_PAYLOAD_GLOBAL;

    it('should return status code 201', () => {
      return globalLogin.expect(400);
    });

    it('should return response time less than 200ms', async () => {
      const startTime = Date.now();
      await globalLogin.send(creds);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(800);
    });

    it('should globalLogin the response schema', () => {
      return globalLogin
        .send(creds)
        .expect(201)
        .expect((res) => {
          const responseData = res.body;
          expect(responseData).toBeInstanceOf(Object);
          expect(responseData).toHaveProperty('statusCode');
          expect(responseData).toHaveProperty('data');
          expect(responseData.data).toBeInstanceOf(Object);
          expect(responseData.data).toHaveProperty('tokenType');
          expect(responseData.data).toHaveProperty('expiresIn');
          expect(responseData.data).toHaveProperty('accessToken');
        });
    });
    it('should fail the login with creds of airtrip', () => {
      return globalLogin.send(LOGIN_PAYLOAD_AIRTRIP).expect(400);
    });

    it('should throw wrong email', () => {
      return globalLogin
        .send({
          email: '<EMAIL>',
          password: ' wrong',
        })
        .expect(400)
        .expect((res) => {
          const responseData = res.body;
          expect(responseData).toBeInstanceOf(Object);
          expect(responseData).toHaveProperty('errorCode');
          expect(responseData).toHaveProperty('message');
          expect(responseData.message).toBe(
            ApiErrorCodes.ERR_400_INVALID_USERNAME_PASSWORD.message,
          );
          expect(responseData.errorCode).toBe(
            ApiErrorCodes.ERR_400_INVALID_USERNAME_PASSWORD.errorCode,
          );
        });
    });

    it('should throw wrong password/email', () => {
      return globalLogin
        .send({
          email: creds.email,
          password: ' wrong',
        })
        .expect(400)
        .expect((res) => {
          const responseData = res.body;
          expect(responseData).toBeInstanceOf(Object);
          expect(responseData).toHaveProperty('errorCode');
          expect(responseData).toHaveProperty('message');
          expect(responseData.message).toBe('Incorrect username or password.');
          expect(responseData.errorCode).toBe('NotAuthorizedException');
        });
    });
  });

  // describe('Register', () => {
  //   it('should return an array of cats', async () => {
  //     return app.get('/env').expect(200).expect('development');
  //   });
  // });

  // describe('Forgot Password', () => {
  //   it('should return an array of cats', async () => {
  //     return app.get('/env').expect(200).expect('development');
  //   });
  // });

  // describe('Reset Password', () => {
  //   it('should return an array of cats', async () => {
  //     return app.get('/env').expect(200).expect('development');
  //   });
  // });

  afterAll(async () => {});
});
