import { PassportStrategy } from '@nestjs/passport';
import { HeaderAPIKeyStrategy } from 'passport-headerapikey';
import { Request } from 'express';
import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import * as crypto from 'crypto-js';
import { Cache } from 'cache-manager';
import { AppsService } from 'src/apps/apps.service';
import { apiIdWrapper } from 'src/utils';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

@Injectable()
export class APIRequestCheckStrategy extends PassportStrategy(
  HeaderAPIKeyStrategy,
  'APIRequestCheckStrategy',
) {
  constructor(
    private appsService: AppsService, // private kvStore: KeyValueStoreService, // @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    super(
      {
        prefix: '',
      },
      true,
      async (apiKey, done, req: Request) => {
        try {
          const apiId = req.headers['x-api-id'] as string;
          const receivedSignature = req.headers['x-gat-signature'] as string;
          const timestamp = req.headers['x-gat-timestamp'] as string;
          if (!receivedSignature || !timestamp) {
            done(new UnauthorizedException('Missing signature and timestamp.'));
            return;
          }
          const method = req.method;
          const url = req.originalUrl;

          let app;
          app = await this.cacheManager.get(`${apiId}-${apiKey}`);
          if (!app) {
            app = await this.appsService.findOne(
              apiIdWrapper(apiId as string) as string,
            );
          }
          if (
            this.verifySignature(
              method,
              url,
              timestamp,
              apiId,
              receivedSignature,
              app.signatureKey,
              req.body,
            )
          ) {
            done(null, app);
            return;
          }
          done(new UnauthorizedException('Invalid signature.'));
        } catch (err) {
          console.log(err);
          done(new UnauthorizedException('Invalid signature.'));
        }
      },
    );
  }

  verifySignature(
    httpMethod: string,
    pathAndQuery: string,
    timestamp: string,
    apiId: string,
    receivedSignature: string,
    secretKey: string,
    payload: object,
  ) {
    const stringToSigh = `${httpMethod} ${pathAndQuery}\n${timestamp}\n${apiId}\n${JSON.stringify(
      payload,
    )}`;
    const base64Key = crypto.enc.Base64.parse(secretKey);
    const hashedString = crypto.enc.Utf8.parse(stringToSigh);
    const hash = crypto.HmacSHA256(hashedString, base64Key);
    const signature = hash.toString(crypto.enc.Base64);
    return signature === receivedSignature;
  }
}
