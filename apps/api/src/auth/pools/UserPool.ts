import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { IDENTITY_PROVIDER, users } from '@prisma/client';
import {
  AuthenticationDetails,
  CognitoUser,
  CognitoUserAttribute,
  CognitoUserPool,
} from 'amazon-cognito-identity-js';
import * as AWS from 'aws-sdk';
import axios from 'axios';
import * as crypto from 'crypto';
import jwt_decode from 'jwt-decode';
import { AccountNotIDPManual } from 'src/errors/AccountNotIDPManual';
import { EmailConflictWithSocilaLogin } from 'src/errors/EmailConflictWithSocialLogin';
import { EmailFoundError } from 'src/errors/EmailFoundError';
import { InvalidCredentialsError } from 'src/errors/InvalidCredentialsError';
import { InvalidEmailError } from 'src/errors/InvalidEmailError';
import { PhoneFoundError } from 'src/errors/PhoneFoundError';
import { PaymentService } from 'src/payment/payment.service';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { UsersService } from 'src/users/users.service';
import {
  encryptAppStandard,
  encryptToJWT,
  getIdentityProvider,
} from 'src/utils';
import * as util from 'util';
import { v4 as uuidv4 } from 'uuid';
import { AuthChangePasswordUserDto } from '../dtos/auth-change-password-user.dto';
import { AuthConfirmPasswordUserDto } from '../dtos/auth-confirm-password-user.dto';
import { AuthForgotPasswordUserDto } from '../dtos/auth-forgot-password-user.dto';
import { AuthLoginUserDto } from '../dtos/auth-login-user';
import { AuthRegisterUserDto } from '../dtos/auth-register-user';
import { SocialLoginEmailDto } from '../dtos/social-login-email';
import { VerifyEmailDto } from '../dtos/verify-email-dto';

export abstract class UserPool {
  protected userPool: CognitoUserPool;
  protected cognito: AWS.CognitoIdentityServiceProvider;
  protected paymentService: PaymentService;
  protected userService: UsersService;
  protected configService: ConfigService;
  protected readonly logger: Logger;
  protected eventEmitter: EventEmitter2;

  constructor(
    protected awsConfig: {
      region: string;
      accessKeyId: string;
      secretAccessKey: string;
      userpoolId: string;
      clientId: string;
      cognitoSecretKey: string;
    },
  ) {
    AWS.config.update({
      region: awsConfig.region,
      accessKeyId: awsConfig.accessKeyId,
      secretAccessKey: awsConfig.secretAccessKey,
    });
    this.cognito = new AWS.CognitoIdentityServiceProvider();
    this.userPool = new CognitoUserPool({
      UserPoolId: awsConfig.userpoolId,
      ClientId: awsConfig.clientId,
    });
  }

  private cleanUsername(username: string) {
    const splitBySeperator = username.split(':');
    return splitBySeperator[0];
  }
  getSecretHash(username: string) {
    return crypto
      .createHmac('sha256', this.awsConfig.cognitoSecretKey)
      .update(username + this.awsConfig.clientId)
      .digest('base64');
  }

  async adminRegisterUser(
    authRegisterUserDto: AuthRegisterUserDto & { username?: string },
    userPool: LocalUserPool = LocalUserPool.GLOBAL,
    options?: {
      supressVerificationEmail?: boolean;
      attributes?: CognitoUserAttribute[];
    },
  ) {
    try {
      const { firstName, lastName, email, password, username } =
        authRegisterUserDto;
      const attributes = [
        new CognitoUserAttribute({
          Name: 'given_name',
          Value: firstName,
        }),
        new CognitoUserAttribute({
          Name: 'family_name',
          Value: lastName,
        }),
        new CognitoUserAttribute({
          Name: 'email',
          Value: email,
        }),
        new CognitoUserAttribute({
          Name: 'locale',
          Value: authRegisterUserDto.locale,
        }),
        new CognitoUserAttribute({
          Name: 'website',
          Value: `${userPool}:${authRegisterUserDto.website || authRegisterUserDto.source || 'N/A'
            }`,
        }),
        // new CognitoUserAttribute({
        //   Name: 'email_verified',
        //   Value: 'true',
        // }),
      ];

      const phoneNumber = authRegisterUserDto.phone
        ? '+' + authRegisterUserDto.phone
        : undefined;
      if (authRegisterUserDto.phone) {
        attributes.push(
          new CognitoUserAttribute({
            Name: 'phone_number',
            Value: phoneNumber,
          }),
        );
      }

      const adminSignup = util
        .promisify<
          AWS.CognitoIdentityServiceProvider.Types.AdminCreateUserRequest,
          any
        >(this.cognito.adminCreateUser)
        .bind(this.cognito);


      let signupResponse;
      signupResponse = await this.cognito
        .adminCreateUser({
          Username: username,
          UserPoolId: this.awsConfig.userpoolId,
          DesiredDeliveryMediums: [],
          TemporaryPassword: password,
          //@ts-expect-error
          UserAttributes: [...attributes, ...(options?.attributes || {})],
          MessageAction: 'SUPPRESS'
        })
        .promise();

      // handle signupResponse if needed

      const adminSetUserPassword = util
        .promisify<
          AWS.CognitoIdentityServiceProvider.Types.AdminSetUserPasswordRequest,
          any
        >(this.cognito.adminSetUserPassword)
        .bind(this.cognito);


      await adminSetUserPassword({
        Password: password,
        Username: username,
        UserPoolId: this.awsConfig.userpoolId,
        Permanent: true,
      });

      return signupResponse;
    } catch (err) {
      throw err;
    }
  }
  /**
   * Registers a new user in the system.
   * - Checks if the user already exists by email or phone.
   * - If the user is a guest, updates their information.
   * - Otherwise, creates a new user in AWS Cognito.
   *
   * @param authRegisterUserDto - The DTO containing user registration details.
   * @param userPool - The user pool to register the user in (default: LocalUserPool.GLOBAL).
   * @returns A promise resolving to the newly created user or an error.
   */
  async registerUser(
    authRegisterUserDto: AuthRegisterUserDto,
    userPool: LocalUserPool,
    options?: { supressVerificationEmail?: boolean },
  ) {
    const { firstName, lastName, email, password, profileImage } =
      authRegisterUserDto;

    // Format phone number if provided
    const phoneNumber = authRegisterUserDto.phone
      ? '+' + authRegisterUserDto.phone
      : undefined;

    const [userByEmail, userByPhone] = await Promise.all([
      // Check if the user already exists by email
      this.userService.findUserByEmailIsolatedSource({
        email,
        userPool,
        idpProvider: 'IDP_MANUAL',
      }),
      // Check if the user already exists by phone number
      this.userService.findUserByEmailIsolatedSource({
        phone: phoneNumber,
        userPool,
      }),
    ]);

    // Determine if the existing user is a guest
    const isGuestUser =
      userByEmail?.email === email &&
      userByEmail.isVerified === false &&
      userByEmail.profileImage === 'Guest';

    // Throw error if a non-guest user already exists with the same email
    if (
      !isGuestUser &&
      userByEmail?.email === email &&
      userByEmail.idpProvider === 'IDP_MANUAL'
    ) {
      throw new EmailFoundError();
    }


    // In production, throw error if the phone number is already in use
    if (
      this.configService.getOrThrow('APP_ENV') === 'production' &&
      phoneNumber &&
      userByPhone?.phone_number &&
      userByPhone?.phone_number === phoneNumber
    ) {
      throw new PhoneFoundError();
    }
    const userInSamePool = await this.userService.findUserByEmailIsolatedSource(
      {
        email,
        userPool: userPool,
        idpProvider: ['IDP_GOOGLE', 'IDP_FACEBOOK', 'IDP_APPLE'],
      },
    );

    if (userInSamePool) throw new EmailConflictWithSocilaLogin();

    const username = uuidv4();

    // Define user attributes for Cognito
    const attributes = [
      new CognitoUserAttribute({ Name: 'given_name', Value: firstName }),
      new CognitoUserAttribute({ Name: 'family_name', Value: lastName }),
      new CognitoUserAttribute({ Name: 'email', Value: email }),
      new CognitoUserAttribute({
        Name: 'locale',
        Value: authRegisterUserDto.locale,
      }),
      new CognitoUserAttribute({
        Name: 'picture',
        Value: authRegisterUserDto.profileImage,
      }),
      new CognitoUserAttribute({
        Name: 'website',
        Value: `${userPool}:${authRegisterUserDto.website || authRegisterUserDto.source || 'N/A'
          }`,
      }),
    ];

    if (authRegisterUserDto.phone) {
      attributes.push(
        new CognitoUserAttribute({ Name: 'phone_number', Value: phoneNumber }),
      );
    }

    if (isGuestUser) {
      await this.userService.updateUser(
        { userId: userByEmail.userId },
        { profileImage: '' },
      );
    }
    return new Promise(async (resolve, reject) => {
      if (isGuestUser) {
        return this.cognito.adminSetUserPassword(
          {
            UserPoolId: this.configService.getOrThrow(
              'AWS_COGNITO_USER_POOL_ID',
            ),
            Username: userByEmail.username,
            Password: password,
            Permanent: true,
          },
          (err, result) => {
            if (err) {
              this.logger.error(err);
              return reject(new InvalidEmailError());
            }

            // Update user attributes
            return this.cognito.adminUpdateUserAttributes(
              {
                UserAttributes: attributes,
                Username: userByEmail.username,
                UserPoolId: this.configService.getOrThrow(
                  'AWS_COGNITO_USER_POOL_ID',
                ),
              },
              async (err, result) => {
                if (err) {
                  this.logger.error(err);
                  return reject(new Error('Something went wrong.'));
                }

                try {
                  await this.resendVerificationEmail(
                    userByEmail.email,
                    null,
                    userPool,
                  );
                  await this.userService.updateUser(
                    { userId: userByEmail.userId },
                    { profileImage: '' },
                  );
                  resolve(userByEmail);
                } catch (error) {
                  this.logger.error(error);
                  return reject(new Error('Something went wrong.'));
                }
              },
            );
          },
        );
      }

      const params = {
        ClientId: this.awsConfig.clientId,
        SecretHash: this.getSecretHash(username),
        Username: username,
        Password: password,
        UserAttributes: attributes,
      };

      try {
        const cognitoUser = await this.adminRegisterUser(
          {
            ...authRegisterUserDto,
            username: username,
          },
          userPool,
          {
            attributes,
            supressVerificationEmail: options?.supressVerificationEmail,
          },
        );
        const metadata = authRegisterUserDto.referral
          ? { referral: authRegisterUserDto.referral }
          : {};

        const user = await this.createLocalUser(
          {
            firstName,
            lastName,
            email,
            id: cognitoUser.User.Attributes.find((user) => user.Name === 'sub')
              ?.Value,
            username,
            locale: authRegisterUserDto.locale,
            source: authRegisterUserDto.source,
            phoneNumber,
            userPool,
            profileImage,
          },
          metadata,
        );
        this.logger.log('New user created: ' + email);
        resolve({ ...user, isGuestUser });
      } catch (error) {
        this.logger.error(error);
        reject('Unable to create user');
      }

      // Register a new user in Cognito
      // return this.cognito.signUp(params, async (err, result) => {
      //   if (err) {
      //     return reject(err);
      //   }

      // });
    });
  }

  async createLocalUser(
    data: {
      firstName: string;
      lastName: string;
      email: string;
      id: string;
      username: string;
      isVerified?: true;
      locale?: string;
      source?: string;
      phoneNumber?: string;
      profileImage?: string;
      userPool?: LocalUserPool;
      idpProvider?: IDENTITY_PROVIDER;
    },
    metadata: {} = {},
  ) {
    const { firstName, lastName, email, id, profileImage } = data;
    // const user = await this.userService.hasUser(id);
    // if (user) return null;
    if (email) {
      // Check if a user with the same email exists in the same pool
      this.logger.log(
        `Checking if user with email ${email} exists in the same pool.`,
      );
      const userInSamePool = await this.userService.getUserByEmail(
        email,
        data.userPool,
      );

      // If the user exists and both are manual IDP users, throw an EmailFoundError
      if (
        userInSamePool &&
        data.idpProvider == 'IDP_MANUAL' &&
        userInSamePool.idpProvider === 'IDP_MANUAL'
      ) {
        this.logger.error(
          `Email conflict detected for manual IDP user with email: ${email}`,
        );
        throw new EmailFoundError();
      }

      // If the user exists and the IDP provider is one of the social providers
      if (
        userInSamePool &&
        ['IDP_GOOGLE', 'IDP_FACEBOOK', 'IDP_APPLE'].includes(data.idpProvider)
      ) {
        this.logger.log(
          `User with email ${email} exists with social IDP provider: ${userInSamePool.idpProvider}`,
        );
        // If the IDP provider matches, return the existing user
        if (data.idpProvider === userInSamePool.idpProvider)
          return userInSamePool;

        // Otherwise, create a linked account for the user and return the existing user
        this.logger.log(
          `Creating linked account for user with email ${email} and IDP provider ${data.idpProvider}`,
        );
        await this.userService.createUserAccounts({
          cognitoUserName: data.username,
          cognitoUserSub: data.id,
          idpProvider: data.idpProvider,
          usersId: userInSamePool.id,
        });
        return userInSamePool;
      }

      // If no IDP provider is specified or it is a manual IDP, check for conflicts with social providers
      if (!data.idpProvider || data.idpProvider === 'IDP_MANUAL') {
        this.logger.log(
          `Checking for email conflicts in the same pool for email: ${email}`,
        );
        const userInSamePool =
          await this.userService.findUserByEmailIsolatedSource({
            email,
            userPool: data.userPool,
            idpProvider: ['IDP_GOOGLE', 'IDP_FACEBOOK', 'IDP_APPLE'],
          });

        // If a conflict is found with a social provider, throw an EmailFoundError
        if (userInSamePool) {
          this.logger.error(
            `Email conflict detected with social provider for email: ${email}`,
          );
          throw new EmailFoundError();
        }
      }
    }

    try {
      const user = await this.userService.upsertUser({
        firstName,
        lastName,
        email,
        profileImage,
        userId: id,
        username: data.username,
        source: data.source,
        phone_number: data.phoneNumber,
        locale: data.locale,
        userPool: data.userPool,
      });
      const customer = await this.paymentService.createCustomer(user, metadata);
      /**Turned this off for now, will turn on when the flow is finalized */
      // this.eventEmitter.emit(EVENT_NEW_USER_REGISTERED, { user });
      return await this.userService.updateUser(
        {
          userId: id,
        },
        {
          username: data.username,
          stripeId: customer.id,
          idpProvider: data.idpProvider || getIdentityProvider(data.username),
          isVerified: !!data.isVerified,
        },
      );
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }
  async authenticateUserOnly(
    authLoginUserDto: AuthLoginUserDto & { username: string },
  ) {
    const adminInitiateAuth = util
      .promisify<
        AWS.CognitoIdentityServiceProvider.Types.AdminInitiateAuthRequest,
        AWS.CognitoIdentityServiceProvider.Types.AdminInitiateAuthResponse
      >(this.cognito.adminInitiateAuth)
      .bind(this.cognito);

    const authResponse = await adminInitiateAuth({
      UserPoolId: this.awsConfig.userpoolId,
      ClientId: this.awsConfig.clientId,
      AuthFlow: 'ADMIN_USER_PASSWORD_AUTH',
      AuthParameters: {
        SECRET_HASH: this.getSecretHash(authLoginUserDto.username),
        USERNAME: authLoginUserDto.username,
        PASSWORD: authLoginUserDto.password,
      },
    });
    const loginCreds = {
      tokenType: authResponse.AuthenticationResult.TokenType,
      expiresIn: authResponse.AuthenticationResult.ExpiresIn,
      accessToken: authResponse.AuthenticationResult.AccessToken,
      refreshToken: authResponse.AuthenticationResult.RefreshToken,
    };
    const response = {
      ...loginCreds,
    };
    if (authLoginUserDto.grant_type === 'authorization_code') {
      // delete response.accessToken;
      delete response.expiresIn;
      delete response.tokenType;
      //@ts-expect-error
      response.authorization_token = encryptToJWT(
        {
          payload: encryptAppStandard(JSON.stringify(loginCreds)),
        },
        '1m',
      );
    }
    return response;
  }

  async refreshTokenOnly(refreshTokenDto: {
    username: string;
    refreshToken: string;
  }) {
    const adminInitiateAuth = util
      .promisify<
        AWS.CognitoIdentityServiceProvider.Types.AdminInitiateAuthRequest,
        AWS.CognitoIdentityServiceProvider.Types.AdminInitiateAuthResponse
      >(this.cognito.adminInitiateAuth)
      .bind(this.cognito);

    const authResponse = await adminInitiateAuth({
      UserPoolId: this.awsConfig.userpoolId,
      ClientId: this.awsConfig.clientId,
      AuthFlow: 'REFRESH_TOKEN_AUTH',
      AuthParameters: {
        SECRET_HASH: this.getSecretHash(refreshTokenDto.username),
        REFRESH_TOKEN: refreshTokenDto.refreshToken,
      },
    });

    const loginCreds = {
      tokenType: authResponse.AuthenticationResult.TokenType,
      expiresIn: authResponse.AuthenticationResult.ExpiresIn,
      accessToken: authResponse.AuthenticationResult.AccessToken,
    };

    return loginCreds;
  }

  async authenticateUser(
    authLoginUserDto: AuthLoginUserDto,
    userpool?: LocalUserPool,
    options?: { idpProvider?: IDENTITY_PROVIDER },
  ) {
    const { email, password } = authLoginUserDto;
    const user = await this.userService.getUserByEmail(email, userpool, {
      idpProvider: 'IDP_MANUAL',
    });
    if (!user) throw new InvalidCredentialsError();

    const authResponse = await this.authenticateUserOnly({
      ...authLoginUserDto,
      username: user.username,
    });

    return authResponse;
  }

  async refreshToken(username: string, refreshToken: string) {
    const refreshResponse = await this.refreshTokenOnly({
      username: username,
      refreshToken,
    });

    return refreshResponse;
  }

  /**
   * @todo something to be done here
   * @param payload
   * @param metadata
   */
  async confirmUser(
    payload: VerifyEmailDto,
    metadata?: { campaign?: string },
  ): Promise<users> {
    const user = await this.userService.getUserByUsername(payload.username);

    const params = {
      ClientId: this.awsConfig.clientId,
      SecretHash: this.getSecretHash(payload.username),
      Username: payload.username,
      ConfirmationCode: payload.code,
      ForceAliasCreation: true,

    };

    return new Promise((resolve, reject) => {
      this.cognito.confirmSignUp(params, (err, result) => {
        if (!err) return resolve(user);

        switch (err?.code) {
          case 'AliasExistsException':
            this.cognito.adminConfirmSignUp(
              {
                Username: payload.username,
                UserPoolId: this.awsConfig.userpoolId,
              },
              (err, result) => {
                if (err) reject(err);
                resolve(user);
              },
            );
            return false;
          default:
            reject(err);
        }

        return reject(err);
      });
    });
  }

  async changeUserPassword(
    authChangePasswordUserDto: AuthChangePasswordUserDto,
    userPool?: LocalUserPool,
  ) {
    const { email, currentPassword, newPassword } = authChangePasswordUserDto;

    const userData = {
      Username: email,
      Pool: this.userPool,
    };

    const authenticationDetails = new AuthenticationDetails({
      Username: email,
      Password: currentPassword,
    });

    const userCognito = new CognitoUser(userData);

    return new Promise((resolve, reject) => {
      userCognito.authenticateUser(authenticationDetails, {
        onSuccess: () => {
          userCognito.changePassword(
            currentPassword,
            newPassword,
            (err, result) => {
              if (err) {
                reject(err);
                return;
              }
              resolve({
                result,
              });
            },
          );
        },
        onFailure: (err) => {
          reject(new Error(err.message));
        },
      });
    });
  }
  async forgotUserPassword(
    authForgotPasswordUserDto: AuthForgotPasswordUserDto,
    userpool?: LocalUserPool,
  ) {
    const user = await this.userService.getUserByEmail(
      authForgotPasswordUserDto.email,
      userpool,
      { idpProvider: 'IDP_MANUAL' },
    );
    if (!user) {
      const userSocial = await this.userService.getUserByEmail(
        authForgotPasswordUserDto.email,
        userpool,
      );

      if (userSocial && userSocial?.idpProvider !== 'IDP_MANUAL') {
        throw new AccountNotIDPManual();
      }
      throw new Error('User not found.');
    }

    // This is guest user, we treat them as non existent user, so they are forced to create an account
    if (
      user &&
      user.idpProvider !== 'IDP_MANUAL' &&
      !user.isVerified &&
      user.profileImage === 'Guest'
    ) {
      throw new Error('No user found');
    }

    if (user.isVerified) {
      await this.updateUserAttributes(
        {
          email_verified: 'true',
        },
        user.username,
      );

    }

    const params = {
      ClientId: this.awsConfig.clientId,
      SecretHash: this.getSecretHash(user.username),
      Username: user.username,
    };
    return new Promise((resolve, reject) => {
      this.cognito.forgotPassword(params, (error, result) => {
        if (error) return reject(error);
        resolve(result);
      });
    });
  }
  async socialLoginEmail(socialLoginEmail: SocialLoginEmailDto) {
    const data = await this.userService.getUserByEmail(socialLoginEmail.email);
    if (data) {
      return {
        userId: data?.userId,
        email: data?.email,
        isVerified: data?.isVerified,
      };
    }

    return null;
  }

  async confirmUserPassword(
    authConfirmPasswordUserDto: AuthConfirmPasswordUserDto,
    userpool?: LocalUserPool,
  ) {
    const user = await this.userService.getUserByEmail(
      authConfirmPasswordUserDto.email,
      userpool,
      { idpProvider: 'IDP_MANUAL' },
    );
    if (!user) throw new Error('User not found.');

    const params = {
      ClientId: this.awsConfig.clientId,
      SecretHash: this.getSecretHash(user.username),
      Username: user.username,
      ConfirmationCode: authConfirmPasswordUserDto.confirmationCode,
      Password: authConfirmPasswordUserDto.newPassword,
    };

    return new Promise((resolve, reject) => {
      this.cognito.confirmForgotPassword(params, (err, result) => {
        if (err) return reject(err);
        resolve({ status: 'success' });
      });
    });
  }

  async getUserData(email: string) {
    const userCognito = new CognitoUser({
      Username: email,
      Pool: this.userPool,
    });

    return userCognito.getUserData(console.log);
  }

  async getAccessTokenByAuthorizationCode(code: string, redirectUri?: string) {
    try {
      const response = await axios.post(
        `${this.configService.get('AWS_COGNITO_DOMAIN')}oauth2/token`,
        {
          grant_type: 'authorization_code',
          client_id: this.configService.get('AWS_COGNITO_CLIENT_ID'),
          client_secret: this.configService.get('AWS_COGNITO_SECRET_KEY'),
          code,
          redirect_uri:
            redirectUri || this.configService.get('COGNITO_REDIRECT_URI'),
          code_verifier: this.configService.get('PKCE_CODE_VERIFIER'),
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      return response.data as {
        access_token: string;
        refresh_token: string;
        id_token: string;
      };
    } catch (err) {
      this.logger.error(err);
      throw new Error('Invalid grant or unauthorized client.');
    }
  }

  async getUserInfoByToken(accessToken: string) {
    return jwt_decode(accessToken) as {
      sub: string;
      name: string;
      given_name: string;
      family_name: string;
      preferred_username: string;
      email: string;
      'cognito:username': string;
    };
  }

  async resendVerificationEmail(
    email: string,
    phone?: string,
    userPool?: LocalUserPool,
  ): Promise<users | Error> {
    const user = await this.userService.getUserByEmail(email, userPool, {
      idpProvider: 'IDP_MANUAL',
    });
    if (!user) {
      throw new Error('User not found');
    }
    if (phone && user.phone_number !== '+' + phone) {
      throw new Error('Invalid phone number and user info.');
    }
    if (user.isVerified) {
      throw new Error('User is already verified');
    }

    const params = {
      ClientId: this.awsConfig.clientId,
      SecretHash: this.getSecretHash(user.username),
      Username: user.username,
      // UserAttributes: attributes,
    };

    return new Promise((resolve, reject) => {
      this.cognito.resendConfirmationCode(params, (err, result) => {
        if (err) {
          return reject(err);
        }
        resolve(user);
      });
    });
  }

  updateUserAttributes(
    data: {
      family_name?: string;
      given_name?: string;
      email?: string;
      gender?: string;
      locale?: string;
      phone_number?: string;
      website?: string;
      email_verified?: string;
      phone_number_verified?: string;
      preferred_username?: string;
    },
    username: string,
  ) {
    const attributeList = Object.keys(data).map(
      (item) => new CognitoUserAttribute({ Name: item, Value: data[item] }),
    );

    return new Promise((resolve, reject) => {
      this.cognito.adminUpdateUserAttributes(
        {
          UserAttributes: attributeList,
          Username: username,
          UserPoolId: this.awsConfig.userpoolId,
        },
        function (err, result) {
          if (err) {
            reject(err.message || JSON.stringify(err));
            return;
          }
          resolve(result);
        },
      );
    });
  }

  async getUserAttributes(username: string) {
    const getUser = util
      .promisify<
        AWS.CognitoIdentityServiceProvider.Types.AdminCreateUserRequest,
        any
      >(this.cognito.adminGetUser)
      .bind(this.cognito);
    const user = await getUser({
      UserPoolId: this.awsConfig.userpoolId,
      Username: username,
    });
    return user;
  }

  async linkSocialToManualUser({
    userPoolId,
    username,
    socialProvider,
    socialUserId,
  }: {
    userPoolId: string;
    username: string;
    socialProvider:
    | 'Google'
    | 'Facebook'
    | 'LoginWithAmazon'
    | 'SignInWithApple';
    socialUserId: string;
  }) {
    // Fetch the existing Cognito User
    const user = await this.cognito
      .adminGetUser({
        UserPoolId: userPoolId,
        Username: username, // Cognito username (email/password user)
      })
      .promise();

    if (!user) throw new Error('User not found');

    // Link the social login to the existing user
    const response = await this.cognito
      .adminLinkProviderForUser({
        UserPoolId: userPoolId,
        DestinationUser: {
          ProviderName: 'Cognito',
          ProviderAttributeValue: username, // The existing email/password user
        },
        SourceUser: {
          ProviderAttributeName: 'Cognito_Subject',
          ProviderAttributeValue: socialUserId,
          ProviderName: socialProvider, // Example: Google, Facebook
        },
      })
      .promise();
    return 'Accounts linked successfully';
  }

  async forceVerify(username: string, userPool?: LocalUserPool) {
    const user = await this.userService.getUserByUsername(username);
    if (!user) {
      return null;
    }

    await this.userService.updateUser({ userId: user.userId }, { isVerified: true });
    return this.updateUserAttributes({
      email_verified: 'true',
    }, user.username);
  }

}
