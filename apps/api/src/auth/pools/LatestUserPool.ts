import { PaymentService } from 'src/payment/payment.service';
import { UsersService } from 'src/users/users.service';
import { UserPool } from './UserPool';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

export class LatestUserPool extends UserPool {
  constructor(
    protected paymentService: PaymentService,
    protected userService: UsersService,
    protected configService: ConfigService,
    protected readonly logger: Logger,
    protected eventEmitter: EventEmitter2,
  ) {
    const env = process.env;
    super({
      accessKeyId: env['AWS_ACCESS_KEY'],
      clientId: env['AWS_COGNITO_CLIENT_ID'],
      region: env['AWS_COGNITO_REGION'],
      secretAccessKey: env['AWS_SECRET_ACCESS_KEY'],
      userpoolId: env['AWS_COGNITO_USER_POOL_ID'],
      cognitoSecretKey: env['AWS_COGNITO_SECRET_KEY'],
    });
  }
}
