import { PassportStrategy } from '@nestjs/passport';
import { HeaderAPIKeyStrategy } from 'passport-headerapikey';
import { Request } from 'express';
import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import * as bcrypt from 'bcrypt';
import { apiIdWrapper } from 'src/utils';
import { Cache } from 'cache-manager';
import { AppsService } from 'src/apps/apps.service';
import { AN_HOUR_IN_MILLISECONDS } from 'src/constants';

const DEFAULT_FAIL_MESSAGE = 'Invalid API id and key combination.';

@Injectable()
export class APIKeyStrategy extends PassportStrategy(
  HeaderAPIKeyStrategy,
  'headerapikey',
) {
  constructor(
    private appsService: AppsService, // private kvStore: KeyValueStoreService, // @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    super(
      {
        prefix: '',
      },
      true,
      async (apiKey, done, req: Request) => {
        try {
          const apiId = req.headers['x-api-id'];

          // We dont always verify api key from database, if it is matched
          // we store it in memory so that sub sequent verification can become more fast
          const cachedvalue = await this.cacheManager.get(`${apiId}-${apiKey}`);
          if (cachedvalue) {
            return done(null, cachedvalue);
          }
          this.appsService
            .findOne(apiIdWrapper(apiId as string) as string)
            .then((result) => {
              if (!result)
                return done(new UnauthorizedException(DEFAULT_FAIL_MESSAGE));
              bcrypt
                //@ts-ignore
                .compare(apiKey, result.secret)
                .then((isMatched) => {
                  if (!isMatched)
                    done(new UnauthorizedException(DEFAULT_FAIL_MESSAGE));

                  this.cacheManager.set(
                    `${apiId}-${apiKey}`,
                    result,
                    AN_HOUR_IN_MILLISECONDS,
                  );

                  done(null, result);
                })
                .catch(() =>
                  done(new UnauthorizedException(DEFAULT_FAIL_MESSAGE)),
                );
            })
            .catch(() => done(new UnauthorizedException(DEFAULT_FAIL_MESSAGE)));
        } catch {
          done(new UnauthorizedException(DEFAULT_FAIL_MESSAGE));
        }
      },
    );
  }
}
