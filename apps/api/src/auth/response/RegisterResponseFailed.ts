import { ApiProperty } from '@nestjs/swagger';

export class RegisterResponseFailed {
  @ApiProperty({ example: 400 })
  'statusCode': 400;

  @ApiProperty({
    oneOf: [
      {
        example: 'Email address already exists.',
      },
      {
        example: ['Password must have minimum eight characters.'],
      },
    ],
  })
  message: string | string[];

  @ApiProperty({ example: 'Bad Request' })
  'error': 'Bad Request';
}
