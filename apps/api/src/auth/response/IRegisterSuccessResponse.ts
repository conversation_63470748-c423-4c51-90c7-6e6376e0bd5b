import { ApiProperty } from '@nestjs/swagger';

export class IRegisterSuccessresponse {
  @ApiProperty({ example: 201 })
  statusCode: 201;

  @ApiProperty({
    example: {
      id: 4,
      userId: '4d1777db-764e-4377-941f-2fbceed8b70c',
      stripeId: 'cus_Pu2HZCIwTw0z3P',
      firstName: 'samussndra',
      lastName: 'khatri',
      email: '<EMAIL>',
      createdAt: '2024-04-11T03:21:58.140Z',
      updatedAt: '2024-04-11T03:21:58.783Z',
      profileImage: null,
      defaultPaymentMethodId: null,
      isVerified: false,
      idpProvider: 'IDP_MANUAL',
      locale: 'en',
      username: '50838b39-e936-4088-94e0-d2547e6e617b',
    },
  })
  data: object;
}
