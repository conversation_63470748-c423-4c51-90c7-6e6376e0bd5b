import {
  CallH<PERSON>ler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { isAppEnvDev } from './utils';

export interface Response<T> {
  data: T;
}

@Injectable()
export class TransformInterceptor<T>
  implements NestInterceptor<T, Response<T>> {
  private readonly logger = new Logger(TransformInterceptor.name);
  // Array of routes that should skip transformation
  private excludedRoutes = [
    '/api/v1/metrics',
    'slack',
    'cgi',
  ];

  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    const url = context.getArgByIndex(1).req.url;

    if (this.excludedRoutes.some((route) => url.includes(route))) {
      return next.handle();
    }

    return next.handle().pipe(
      map((data) => {
        try {
          if (
            !url.includes('health') &&
            !url.includes('auth') &&
            !url.includes('admin') &&
            !url.includes('plans') &&
            !url.includes('profile') &&
            !isAppEnvDev()) {
            this.logger.log(`Response: ${url}, HTTP:${context.getArgByIndex(1).statusCode}, Method: ${context.getArgByIndex(1).method}, Body: ${(JSON.stringify(data || {}))}`);
          }
        } catch (e) {
          this.logger.error(e);
        }
        return ({
          statusCode: context.getArgByIndex(1).statusCode,
          data,
        })
      }),
    );
  }
}
