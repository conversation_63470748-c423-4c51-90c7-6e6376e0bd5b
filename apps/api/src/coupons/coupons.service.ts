import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  COUPON_DISCOUNT_TYPE,
  constraints,
  coupons,
  coupons_constraints,
  coupons_orders,
  orders,
  plans,
  referrals,
  users,
} from '@prisma/client';
import { addDays } from 'date-fns';
import * as ruleEngine from 'json-rules-engine';
import * as xlsx from 'node-xlsx';
import { PrismaService } from 'src/prisma.service';
import { v4 } from 'uuid';
import * as Voucher from 'voucher-code-generator';
import { CouponErrors } from './core/CouponErrors';
import { CSVParser } from './core/parser/CSVParser';
import { DataProcessor } from './core/parser/EsimOrderRequestParser';
import { ICoupon } from './core/parser/IParserStratefy';
import { CouponRuleFactory } from './core/rules-engine/CouponRuleFactory';
import { CountrySpecificCoupon } from './core/rules-engine/rules/CountrySpecificCoupon';
import { CouponActive } from './core/rules-engine/rules/CouponActive';
import { CouponExpired } from './core/rules-engine/rules/CouponExpired';
import { CouponPerPersonUsageExceeded } from './core/rules-engine/rules/CouponPerPersonUsageExceeded';
import { CouponUsageExceeded } from './core/rules-engine/rules/CouponUsageExceeded';
import { PlanSpecificCoupon } from './core/rules-engine/rules/PlanSpecificCoupon';
import { SelfOnlyCoupon } from './core/rules-engine/rules/SelfOnlyCoupon';
import { EmailsService } from 'src/emails/emails.service';
import { I18nService } from 'nestjs-i18n';
import { ServiceSpecificRule } from './core/rules-engine/rules/ServiceSpecificRule';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { ServicePlansEnum } from 'src/plans/enums/ServicePlansEnum';

@Injectable()
export class CouponsService {
  constructor(
    private prismaService: PrismaService,
    private configService: ConfigService,
    private logger: Logger,
    @Inject(EmailsService)
    private readonly emailService: EmailsService,
    private readonly i18nService: I18nService,
  ) {}

  async createConstraint(constraintDto: { name: string; rule: object }) {
    try {
      return await this.prismaService.constraints.upsert({
        where: { name: constraintDto.name },
        update: {},
        create: { name: constraintDto.name, rule: constraintDto.rule },
      });
    } catch (err: any) {
      if (err.code === 'P2002') {
        // Some other process just created it so — fetch the existing one
        return this.prismaService.constraints.findFirstOrThrow({
          where: { name: constraintDto.name },
        });
      }
      throw err;
    }
  }

  async create(coupon: coupons, constraints?: constraints[]) {
    const savedCoupon = await this.prismaService.coupons.create({
      data: {
        ...coupon,
        coupons_constraints: {
          create:
            constraints?.map?.((item) => ({
              constraintsId: item.id,
            })) || [],
        },
      },
    });

    return savedCoupon;
  }

  getCouponByCode(couponCode: string, include?: {}) {
    return this.prismaService.coupons.findFirstOrThrow({
      where: {
        code: couponCode,
      },
      include: {
        coupons_orders: true,
        coupons_constraints: {
          include: {
            constraints: true,
          },
        },
        referrals: {
          include: {
            user: true,
          },
        },
        ...include,
      },
    });
  }
  getCouponByReference(referenceId: string, include?: {}) {
    if (!referenceId) {
      return null;
    }

    return this.prismaService.coupons_orders.findFirst({
      where: {
        referenceId,
      },
      include: {
        coupon: {
          include: {
            referrals: {
              include: {
                user: true,
              },
            },
            coupons_orders: true,
            coupons_constraints: {
              include: {
                constraints: true,
              },
            },
            ...include,
          },
        },
      },
    });
  }
  completeCoupon(couponReferenceId: number) {
    return this.updateCouponReference(couponReferenceId, {
      state: 'COMPLETED',
    });
  }
  updateCoupon(couponId: string, payload: Partial<coupons>) {
    return this.prismaService.coupons.update({
      where: { code: couponId },
      data: payload,
    });
  }
  updateCouponReference(id: number, data: Partial<coupons_orders>) {
    return this.prismaService.coupons_orders.update({
      where: {
        id,
      },
      data: data,
      include: {
        coupon: {
          include: {
            referrals: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });
  }

  async validate(
    coupon: coupons &
      Partial<{
        coupons_constraints: Array<
          coupons_constraints & { constraints: constraints }
        >;
        coupons_orders: coupons_orders[];
        referrals: Array<referrals & { user: users }>;
      }>,
    userId: string | undefined,
    plan: plans,
    options?: {
      source?: string;
    },
  ) {
    try {
      // @todo
      // Renable it when you want to block user from applying own referral code
      if (
        coupon.referrals &&
        userId &&
        coupon.referrals?.[0]?.user.userId === userId
      ) {
        throw new Error('Invalid coupon code.');
      }

      const engine = new ruleEngine.Engine();
      const couponOrdersofUser = userId
        ? await this.prismaService.coupons_orders.findMany({
            where: {
              userId: userId,
              state: {
                in: ['COMPLETED'],
              },
            },
            include: { coupon: true },
          })
        : [];

      const internalUserId = await this.prismaService.users.findFirst({
        where: {
          userId,
        },
        select: {
          id: true,
        },
      });

      const orderOfUsers = await this.prismaService.orders.findMany({
        where: {
          userId: internalUserId.id,
          paymentStatus: 'SUCCESS',
        },
      });

      const ruleFactory = new CouponRuleFactory(
        { ...coupon, userId } as ICoupon,
        this.configService,
        coupon.coupons_constraints.map((item) => item.constraints),
        plan,
        couponOrdersofUser,
        orderOfUsers,
        options,
      );
      const rules = [
        CouponExpired.NAME,
        CouponPerPersonUsageExceeded.NAME,
        CountrySpecificCoupon.NAME,
        CouponUsageExceeded.NAME,
        ServiceSpecificRule.NAME,
        CouponActive.NAME,
        ...(coupon.coupons_constraints?.map?.((item) => {
          const name = item?.constraints?.name;
          if (name.includes('Self_')) return SelfOnlyCoupon.NAME;
          if (name.includes('CountrySpecificCoupon')) {
            return CountrySpecificCoupon.NAME;
          }
          if (name.includes('PlanSpecificCoupon')) {
            return PlanSpecificCoupon.NAME;
          }
          return name;
        }) || []),
      ];
      const facts = {};
      const ruleInstances = rules.map((ruleClass) =>
        ruleFactory.build(ruleClass),
      );

      ruleInstances.forEach((ruleInstance) => {
        if (!ruleInstance) return;
        engine.addRule(ruleInstance.getRule());
        Object.assign(facts, ruleInstance.getFactValue());
      });

      const rulesResponse = await engine.run(facts);

      if (rulesResponse.failureResults.length) {
        throw new CouponErrors(
          rulesResponse.failureEvents.map((item) => {
            return {
              error: item.type.toLowerCase(),
              message: item.params.message,
            };
          }),
        );
      }
      return coupon;
    } catch (err) {
      this.logger.log(err, coupon.code);
      throw err;
    }
  }
  async getSimilarCoupons(couponCode: string) {
    if (!couponCode) throw new Error('Unknown coupon');
    return this.prismaService.coupons.findFirstOrThrow({
      where: {
        code: { contains: couponCode },
      },
    });
  }

  async getById(couponCode: string) {
    if (!couponCode) throw new Error('Unknown coupon');
    return this.prismaService.coupons.findFirstOrThrow({
      where: {
        code: couponCode,
      },
    });
  }

  async getCouponReferenceByReferenceOrCode(params: {
    userId: string;
    coupon: coupons;
    state?: coupons_orders['state'];
  }) {
    const referenceId = v4();
    const existingCouponReference =
      await this.prismaService.coupons_orders.findFirst({
        where: {
          couponsId: params.coupon.id,
          state: params.state || 'STARTED',
          userId: params.userId,
        },
        include: {
          coupon: true,
        },
      });
    const reference = existingCouponReference
      ? existingCouponReference
      : await this.prismaService.coupons_orders.create({
          data: {
            referenceId: referenceId,
            state: params.state || 'STARTED',
            couponsId: params.coupon.id,
            userId: params.userId,
          },
          include: {
            coupon: true,
          },
        });
    return reference;
  }

  private async getCouponReference(coupon: coupons, userId: string | null) {
    return userId
      ? await this.getCouponReferenceByReferenceOrCode({
          userId,
          coupon: coupon,
        })
      : null;
  }

  async redeem(
    couponCode: string,
    userId: string | null,
    plan: plans,
    couponModel?: coupons,
  ) {
    let coupon;
    try {
      const coupon = couponModel || (await this.getCouponByCode(couponCode));
      const reference = await this.getCouponReference(coupon, userId);
      if (userId) {
        await this.validate(coupon, userId, plan);
      }
      return {
        code: coupon.code,
        discount: coupon.discount,
        type: coupon.type,
        referenceId: reference?.referenceId,
      };
    } catch (err) {
      this.logger.error(err, coupon?.code);
      throw err;
    }
  }
  attachCouponToOrderId(orderId: number, couponReferenceNumber: number) {
    // const referenced = this.
  }
  generate(options: {
    discount: number;
    type: COUPON_DISCOUNT_TYPE;
    validFrom: Date;
    validTo: Date;
    usagePerPerson: number;
    totalUsageLimit: number;
    code: string;
    services: string;
  }) {
    return {
      discount: +options.discount,
      type: COUPON_DISCOUNT_TYPE.PERCENTAGE, //options.type,
      code:
        options.code ||
        Voucher.generate({
          prefix: 'promo-',
          postfix: '-2024',
          length: 5,
          count: 1,
          charset: Voucher.charset('alphabetic'),
        })[0],
      validTill: new Date(options.validTo),
      validFrom: new Date(options.validFrom),
      totalUsage: +options.totalUsageLimit,
      usagePerPerson: +options.usagePerPerson,
      services: options?.services
        ? (options?.services || '').split(',')
        : undefined,
    };
  }

  saveVouchers(vouchers: coupons[]) {
    return this.prismaService.coupons.createMany({
      data: vouchers,
    });
  }

  processCouponSource(file: Express.Multer.File) {
    //@ts-expect-error
    const dataProcessor = new DataProcessor(new CSVParser(file));
    const esimPurchasePayloads = dataProcessor.process();
    return esimPurchasePayloads;
  }

  static getDiscountedAmount(totalPrice: number, coupon: coupons) {
    if (coupon.type === 'AMOUNT') return coupon.discount;
    return (coupon.discount / 100) * totalPrice;
  }

  static calculateExchangeRate(usdAmount: number, jpyAmount: number): number {
    // Ensure that usdAmount is not zero to avoid division by zero
    if (usdAmount !== 0) {
      const exchangeRate: number = jpyAmount / usdAmount;
      return exchangeRate;
    } else {
      throw new Error(
        'USD amount should not be zero to calculate the exchange rate.',
      );
    }
  }
  static getTotalPriceAfterDiscount(totalPrice: number, coupon: coupons) {
    return totalPrice - CouponsService.getDiscountedAmount(totalPrice, coupon);
  }

  getCoupons() {
    return this.prismaService.coupons.findMany({});
  }

  async downloadAsExcel() {
    const coupons = await this.getCoupons();
    const excelData = coupons.map((coupon) => Object.values(coupon));
    const header = Object.keys(coupons[0]);
    //@ts-expect-error
    const d = [header].concat(excelData);
    //@ts-expect-error
    return xlsx.build([{ name: 'coupons', data: d }]); // Returns a buffer
  }

  async getPromotedCoupons() {
    const currentDate = new Date();
    const coupons = await this.prismaService.coupons.findMany({
      select: {
        banners: true,
        code: true,
        discount: true,
        id: true,
        message_code: true,
        promote: true,
        validFrom: true,
        validTill: true,
      },
      where: {
        promote: true,
        validFrom: {
          lte: currentDate, // Coupon valid from this date or earlier
        },
        validTill: {
          gte: currentDate, // Coupon valid till this date or later
        },
      },
    });
    return coupons;
  }

  async getUserCoupons(userId: string) {
    const coupons = await this.prismaService.users_coupons.findMany({
      where: {
        userId: userId,
      },
      include: {
        coupon: {
          include: {
            coupons_orders: true,
          },
        },
      },
    });
    return coupons
      .filter((coupon) =>
        coupon.coupon.coupons_orders.every(
          (item) => item.state !== 'COMPLETED',
        ),
      )
      .map(({ coupon, ...order }) => {
        return {
          coupon: coupon,
          order: order,
        };
      });
  }

  private resolveServices(orderSource: string) {
    if (orderSource.includes('airtrip')) {
      return [
        UserRegisterSource.AIRTRIP,
        UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP,
        UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP,
      ];
    }

    //add global esim ecosystem for global-esim or global-esim-jp
    if (orderSource.includes('global-esim')) {
      return [
        UserRegisterSource.GLOBAL_ESIM,
        UserRegisterSource.GLOBAL_ESIM_JP,
        UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
        UserRegisterSource.GLOBAL_ESIM_IOS_APP,
      ];
    }

    return [orderSource];
  }

  async createCouponForLoyalCustomers(options: {
    userId: string;
    discount: number;
    discountType?: COUPON_DISCOUNT_TYPE;
    orderCount?: number;
    order: orders;
  }) {
    const { source: orderSource, parentOrderId } = options.order;
    const services = this.resolveServices(orderSource || 'global-esim');

    const coupon = await this.create({
      code: Voucher.generate({
        prefix: 'LY',
        length: 5,
        count: 1,
      })[0].toUpperCase(),
      discount: options.discount,
      type: options.discountType || 'PERCENTAGE',
      usagePerPerson: 1,
      validFrom: new Date(),
      validTill: null, //null means never expires
      totalUsage: 1,
      services,
    } as coupons);

    const reference = await this.prismaService.users_coupons.create({
      data: {
        couponId: coupon.id,
        userId: options.userId,
      },
    });

    //create coupon for only one order in a basket
    await this.prismaService.orders_loyalty_coupons.create({
      data: {
        couponId: coupon.id,
        orderId: parentOrderId,
      },
    });

    return {
      reference,
      code: coupon.code,
      couponDiscount: coupon.discount || options.discount,
      discountType: options.discountType as COUPON_DISCOUNT_TYPE,
    };
  }

  async notifyNewCoupon(
    user: users,
    payload: {
      orderCount: string;
      couponCode: string;
      couponDiscount: number;
      discountType: COUPON_DISCOUNT_TYPE;
      order: orders & { plan: plans; user: users };
    },
  ) {
    const template = await this.emailService.translateEmail({
      template: 'loyalty-worked',
      ...payload,
      ...payload.order,
      language: payload.order.lang || 'en',
      userSource: payload.order?.source || 'global-esim',
    });

    const subject = this.i18nService.t('loyalty-email.subject', {
      lang: payload.order.lang || 'en',
      args: {
        couponDiscount:
          payload.discountType === 'PERCENTAGE'
            ? `${payload.couponDiscount}%`
            : payload.couponDiscount,
        serviceName:
          payload.order?.source === 'global-esim-jp'
            ? '【グロモバeSIM】'
            : payload.order?.source === 'airtrip'
            ? '【エアトリeSIM】'
            : 'Global Mobile',
      },
    });

    await this.emailService.sendEmail({
      to: user.email,
      subject,
      content: template.data,
      lang: ['en', 'jp'].includes(payload.order?.lang)
        ? payload.order.lang
        : 'en',
      origin: [
        UserRegisterSource.GLOBAL_ESIM_JP,
        UserRegisterSource.GLOBAL_ESIM,
        UserRegisterSource.GLOBAL_ESIM_ANDROID_APP,
        UserRegisterSource.GLOBAL_ESIM_IOS_APP,
      ].includes(payload.order.source as UserRegisterSource)
        ? ServicePlansEnum.GLOBAL_ESIM_DEFAULT
        : ServicePlansEnum.GLOBAL_ESIM_AIRTRIP,
    });
  }

  async findManyByCode(couponCodes: string[]) {
    return this.prismaService.coupons.findMany({
      where: {
        code: { in: couponCodes },
      },
    });
  }
}
