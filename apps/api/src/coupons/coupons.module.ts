import { CacheModule, Logger, Module } from '@nestjs/common';
import { CouponsService } from './coupons.service';
import { CouponsController } from './coupons.controller';
import { PassportModule } from '@nestjs/passport';
import { PrismaService } from 'src/prisma.service';
import { PlansService } from 'src/plans/plans.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { EmailsService } from 'src/emails/emails.service';

@Module({
  imports: [
    CacheModule.register(),
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 5000,
        maxRedirects: 5,
        baseURL: configService.get('API_EXCHANGE_HOST'),
      }),
      inject: [ConfigService],
    }),
    PassportModule.register({ defaultStrategy: 'jwt' }),
  ],
  controllers: [CouponsController],
  providers: [
    CouponsService,
    PrismaService,
    Logger,
    PlansService,
    PrismaService,
    KeyValueStoreService,
    XchangeService,
    EmailsService,
  ],
  exports: [CouponsService],
})
export class CouponsModule {}
