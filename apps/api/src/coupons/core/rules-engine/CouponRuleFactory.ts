import { ConfigService } from '@nestjs/config';
import {
  constraints,
  coupons,
  coupons_orders,
  orders,
  plans,
} from '@prisma/client';
import { ICoupon } from '../parser/IParserStratefy';
import { CountrySpecificCoupon } from './rules/CountrySpecificCoupon';
import { CouponActive } from './rules/CouponActive';
import { CouponExpired } from './rules/CouponExpired';
import { CouponOnWrongPlan } from './rules/CouponOnWrongPlan';
import { CouponPerPersonUsageExceeded } from './rules/CouponPerPersonUsageExceeded';
import { CouponUsageExceeded } from './rules/CouponUsageExceeded';
import { LoyalCustomerCoupon } from './rules/LoyalCustomerCoupon';
import { OnlyOnceCampaign } from './rules/OnlyOnceCampaign';
import { PlanSpecificCoupon } from './rules/PlanSpecificCoupon';
import { SelfOnlyCoupon } from './rules/SelfOnlyCoupon';
import { ServiceSpecificRule } from './rules/ServiceSpecificRule';
import { FirstTimerCoupon } from './rules/FirstTimerCoupon';

export class CouponRuleFactory {
  constructor(
    protected data: ICoupon,
    protected configService: ConfigService,
    protected couponConstraints: constraints[],
    protected plan: plans,
    protected userCouponOrders: Array<coupons_orders & { coupon: coupons }>,
    protected orderOfUsers: orders[],
    protected options?: {
      source?: string;
    } | null,
  ) { }

  build(rule: string) {
    const couponrules = {
      [CouponActive.NAME]: CouponActive,
      [CouponUsageExceeded.NAME]: CouponUsageExceeded,
      [CouponPerPersonUsageExceeded.NAME]: CouponPerPersonUsageExceeded,
      [CouponExpired.NAME]: CouponExpired,
      [CouponOnWrongPlan.NAME]: CouponOnWrongPlan,
      [OnlyOnceCampaign.NAME]: OnlyOnceCampaign,
      [LoyalCustomerCoupon.NAME]: LoyalCustomerCoupon,
      [SelfOnlyCoupon.NAME]: SelfOnlyCoupon,
      [CountrySpecificCoupon.NAME]: CountrySpecificCoupon,
      [PlanSpecificCoupon.NAME]: PlanSpecificCoupon,
      [ServiceSpecificRule.NAME]: ServiceSpecificRule,
      [FirstTimerCoupon.NAME]: FirstTimerCoupon,
    };
    const RuleClass = couponrules[rule];
    if (!RuleClass) return null;
    const constraints = this.couponConstraints
      .filter(Boolean)
      .find((item) => SelfOnlyCoupon.constraintCheck(item, rule));
    return new RuleClass(
      this.data,
      this.configService,
      constraints,
      this.plan,
      this.userCouponOrders,
      this.orderOfUsers,
      this.options,
    );
    // if (rule === CouponActive.NAME) {
    //   return new CouponActive(
    //     this.data,
    //     this.configService,
    //     this.couponConstraints,
    //   );
    // }
    // if (rule === CouponUsageExceeded.NAME) {
    //   return new CouponUsageExceeded(
    //     this.data,
    //     this.configService,
    //     this.couponConstraints,
    //   );
    // }

    // if (rule === CouponPerPersonUsageExceeded.NAME) {
    //   return new CouponPerPersonUsageExceeded(
    //     this.data,
    //     this.configService,
    //     this.couponConstraints,
    //   );
    // }

    // if (rule === CouponExpired.NAME) {
    //   return new CouponExpired(
    //     this.data,
    //     this.configService,
    //     this.couponConstraints,
    //   );
    // }
  }
}
