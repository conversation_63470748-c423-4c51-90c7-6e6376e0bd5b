import { ConfigService } from '@nestjs/config';
import {
  constraints,
  coupons,
  coupons_orders,
  orders,
  plans
} from '@prisma/client';
import { RuleProperties } from 'json-rules-engine';
import { ICoupon } from '../parser/IParserStratefy';

export type IRulePropertiesExtend = RuleProperties & {
  event: {
    type: string;
    params: {
      message?: string;
      error?: string;
    };
  };
};
export abstract class AbstractRule {
  constructor(
    protected data: ICoupon,
    protected configService: ConfigService,
    protected couponConstraints: constraints,
    protected plan: plans,
    protected userCouponOrders: Array<coupons_orders & { coupon: coupons }>,
    protected orderOfUsers: orders[],
    protected options?: {
      source?: string;
    },
  ) { }

  abstract getFactValue(): {
    [key: string]: string | number | boolean;
  };

  abstract getRule(): IRulePropertiesExtend;

  getNotifyLimit() {
    return 1;
  }

  static constraintCheck(constraint: { name: string }, rule: string) {
    return constraint.name === rule;
  }
}
