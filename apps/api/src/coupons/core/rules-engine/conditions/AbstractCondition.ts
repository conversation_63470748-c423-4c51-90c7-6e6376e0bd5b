import { ConfigService } from '@nestjs/config';
import { ICoupon } from '../../parser/IParserStratefy';
import {
  coupons,
  coupons_constraints,
  coupons_orders,
  plans,
} from '@prisma/client';

export abstract class AbstractCondition {
  constructor(
    protected data: ICoupon,
    protected configService: ConfigService,
    protected couponConstraints: coupons_constraints[],
    protected plan: plans,
    protected userCouponOrders: Array<coupons_orders & { coupon: coupons }>,
  ) {}
}
