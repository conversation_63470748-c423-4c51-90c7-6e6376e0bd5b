import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';

export class CouponPerPersonUsageExceeded extends AbstractRule {
  static NAME = 'CouponPerPersonUsageExceeded';

  getFactValue() {
    const usedCoupons = (this.data.coupons_orders || []).filter(
      (ref) =>
        (ref.state === 'COMPLETED' || ref.state === 'PROCESSING') &&
        ref.userId == this.data.userId,
    );
    return {
      CouponPerPersonUsageExceeded:
        usedCoupons.length >= this.data.usagePerPerson,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'CouponPerPersonUsageExceeded',
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: CouponPerPersonUsageExceeded.NAME,
        params: {
          message: 'Coupon code is only usable once.',
        },
      },
    };
  }
}
