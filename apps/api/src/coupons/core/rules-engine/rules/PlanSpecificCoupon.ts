import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';

export class PlanSpecificCoupon extends AbstractRule {
  static NAME = 'PlanSpecificCoupon';

  getFactValue() {
    //@ts-expect-error
    const constraint = this.data.coupons_constraints.find((item) =>
      item.constraints.name.includes('PlanSpecificCoupon'),
    )?.constraints;

    const isAllowed =
      !constraint || constraint?.rule?.value?.includes(this.plan.id);

    return {
      PlanSpecificCoupon: isAllowed,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'PlanSpecificCoupon',
            operator: 'equal',
            value: true,
          },
        ],
      },
      event: {
        type: PlanSpecificCoupon.NAME,
        params: {
          error: 'PlanSpecificCoupon',
          message: 'Coupon not allowed for this plan.',
        },
      },
    };
  }
}
