import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { ICouponConstraintRule } from '../ICouponConstraintRule';

export class LoyalCustomerCoupon extends AbstractRule {
  static NAME = 'LoyalCustomerCoupon';

  getFactValue() {
    const rule = this.couponConstraints
      .rule as unknown as ICouponConstraintRule;

    let status = false;
    const couponValue = rule.value as unknown as {
      userId: string;
      orderCount: number;
    };
    const orders = this.userCouponOrders.find((item) =>
      item.coupon.code.endsWith(couponValue.orderCount + ''),
    );
    if (orders) {
      status = true;
    }
    return {
      LoyalCustomerCoupon: status,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'LoyalCustomerCoupon',
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: LoyalCustomerCoupon.NAME,
        params: {
          message: 'You already have used coupon for this campaign.',
        },
      },
    };
  }
}
