import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';

export class CountrySpecificCoupon extends AbstractRule {
  static NAME = 'CountrySpecificCoupon';

  getFactValue() {
    //@ts-expect-error
    const constraint = this.data.coupons_constraints.find((item) =>
      item.constraints.name.includes('CountrySpecificCoupon'),
    )?.constraints;
    return {
      CountrySpecificCoupon:
        constraint?.rule?.value?.includes(
          //@ts-expect-error
          this.plan.country.name,
        ) === false,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'CountrySpecificCoupon',
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: CountrySpecificCoupon.NAME,
        params: {
          error: 'CountrySpecificCoupon',
          message: 'Coupon not allowed for this region.',
        },
      },
    };
  }
}
