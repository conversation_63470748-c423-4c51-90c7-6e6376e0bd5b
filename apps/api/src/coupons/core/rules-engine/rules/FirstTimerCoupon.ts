import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';

export class FirstTimerCoupon extends AbstractRule {
  static NAME = 'FirstTimerCoupon';

  getFactValue() {
    return {
      [FirstTimerCoupon.NAME]: this.orderOfUsers.length > 0,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: FirstTimerCoupon.NAME,
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: FirstTimerCoupon.NAME,
        params: {
          error: FirstTimerCoupon.NAME,
          message: 'This coupon is valid only for new user',
        },
      },
    };
  }
}
