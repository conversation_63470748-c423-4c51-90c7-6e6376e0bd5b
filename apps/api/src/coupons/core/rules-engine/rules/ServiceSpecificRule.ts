import {
  ServiceName,
  UserRegisterSource,
} from 'src/auth/dtos/auth-register-user';
import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';

export class ServiceSpecificRule extends AbstractRule {
  static NAME = 'ServiceSpecificRule';
  static ServiceToUserRegisterSourcesMap = new Map<string, string[]>([
    [ServiceName.GLOBAL_ESIM_AIRTRIP, [UserRegisterSource.AIRTRIP]],
    [
      ServiceName.GLOBAL_ESIM_JAPANESE, //NEXT_PUBLIC_SERVICE_NAME: "GLOBAL_ESIM_JAPANESE" config present on client side Global esim jp in x-service-name request header
      [UserRegisterSource.GLOBAL_ESIM_JP],
    ],
    [
      ServiceName.GLOBAL_ESIM, //NEXT_PUBLIC_SERVICE_NAME: "GLOBAL_ESIM" config present on client side Global esim in x-service-name request header
      [UserRegisterSource.GLOBAL_ESIM],
    ],
    [
      ServiceName.GLOBAL_ESIM_ANDROID_APP,
      [UserRegisterSource.GLOBAL_ESIM_ANDROID_APP],
    ],
    [
      ServiceName.GLOBAL_ESIM_IOS_APP,
      [UserRegisterSource.GLOBAL_ESIM_ANDROID_APP],
    ],
    [
      ServiceName.AIRTRIP_ESIM_ANDROID_APP,
      [UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP],
    ],
    [
      ServiceName.AIRTRIP_ESIM_IOS_APP,
      [UserRegisterSource.AIRTRIP_ESIM_IOS_APP],
    ],
  ]);

  getFactValue() {
    if (!this.data.services || !this.data.services.length) {
      return {
        ServiceSpecificRule: true,
      };
    }
    if (!this.options.source) {
      return {
        ServiceSpecificRule: false,
      };
    }
    const source = this.options.source.toLowerCase();
    const mapped =
      ServiceSpecificRule.ServiceToUserRegisterSourcesMap.get(source);

    if (mapped) {
      return {
        ServiceSpecificRule:
          mapped.length > 0 &&
          mapped.some((s) => this.data.services.includes(s)),
      };
    }

    return {
      ServiceSpecificRule: this.data.services.includes(source),
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'ServiceSpecificRule',
            operator: 'equal',
            value: true,
          },
        ],
      },
      event: {
        type: ServiceSpecificRule.NAME,
        params: {
          error: 'ServiceSpecificRule',
          message: 'This coupon is not applicable for this service.',
        },
      },
    };
  }
}
