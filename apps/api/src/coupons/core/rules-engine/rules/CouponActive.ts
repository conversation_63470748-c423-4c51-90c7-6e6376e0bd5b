import { isFuture, isPast } from 'date-fns';
import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { UserPlanDateStillRemaining } from '../conditions/UserPlanDateStillRemaining';

export class CouponActive extends AbstractRule {
  static NAME = 'CouponActive';

  getFactValue() {
    return {
      CouponActive: isFuture(this.data.validFrom),
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'CouponActive',
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: CouponActive.NAME,
        params: {
          error: 'CouponActive',
          message: 'Coupon is not active',
        },
      },
    };
  }
}
