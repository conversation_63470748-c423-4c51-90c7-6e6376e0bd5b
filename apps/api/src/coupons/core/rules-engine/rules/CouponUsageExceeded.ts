import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';

export class CouponUsageExceeded extends AbstractRule {
  static NAME = 'CouponUsageExceeded';

  getFactValue() {
    const usedCoupons = (this.data.coupons_orders || []).filter(
      (ref) => ref.state === 'COMPLETED' || ref.state === 'PROCESSING',
    );
    return {
      CouponUsageExceeded: usedCoupons.length >= this.data.totalUsage,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'CouponUsageExceeded',
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: CouponUsageExceeded.NAME,
        params: {
          message: 'Coupon code usage limit exceeded.',
        },
      },
    };
  }
}
