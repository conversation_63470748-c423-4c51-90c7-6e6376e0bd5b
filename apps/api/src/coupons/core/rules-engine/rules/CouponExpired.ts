import { isFuture, isPast } from 'date-fns';
import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';

export class CouponExpired extends AbstractRule {
  static NAME = 'CouponExpired';
  
  getFactValue() {
    return {
      CouponExpired: this.data.validTill ? isPast(this.data.validTill) : false,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'CouponExpired',
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: CouponExpired.NAME,
        params: {
          error: 'CouponExpired',
          message: 'Coupon has expired.',
        },
      },
    };
  }
}
