import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { ICouponConstraintRule } from '../ICouponConstraintRule';

export class SelfOnlyCoupon extends AbstractRule {
  static NAME = 'SelfOnlyCoupon';

  getFactValue() {
    const rule = this.couponConstraints
      .rule as unknown as ICouponConstraintRule;

    const userId = rule.value[0];
    return {
      [SelfOnlyCoupon.NAME]: userId === this.data.userId,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: SelfOnlyCoupon.NAME,
            operator: 'equal',
            value: true,
          },
        ],
      },
      event: {
        type: SelfOnlyCoupon.NAME,
        params: {
          message: 'Invalid coupon.',
        },
      },
    };
  }

  static constraintCheck(constraint: { name: string }, rule: string) {
    return constraint.name.includes('Self_');
  }
}
