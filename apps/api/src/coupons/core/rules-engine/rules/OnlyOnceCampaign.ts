import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { ICouponConstraintRule } from '../ICouponConstraintRule';

export class OnlyOnceCampaign extends AbstractRule {
  static NAME = 'OnlyOnceCampaign';

  getFactValue() {
    const rule = this.couponConstraints
      .rule as unknown as ICouponConstraintRule;

    let status = false;
    const campaign = rule.value as unknown as string;
    const orders = this.userCouponOrders.find((item) =>
      item.coupon.code.startsWith(campaign),
    );
    if (orders) {
      status = true;
    }
    return {
      OnlyOnceCampaign: status,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'OnlyOnceCampaign',
            operator: 'equal',
            value: false,
          },
        ],
      },
      event: {
        type: OnlyOnceCampaign.NAME,
        params: {
          message: 'You already have used coupon for this campaign.',
        },
      },
    };
  }
}
