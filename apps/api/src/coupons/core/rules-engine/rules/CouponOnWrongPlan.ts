import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { ICouponConstraintRule } from '../ICouponConstraintRule';

export class CouponOnWrongPlan extends AbstractRule {
  static NAME = 'CouponOnWrongPlan';

  getFactValue() {
    const rule = this.couponConstraints
      .rule as unknown as ICouponConstraintRule;

    let status = false;
    if (rule?.value?.length === 1 && rule.value[0] === '*') status = true;
    else if (rule.value.includes(this.plan.id)) status = true;

    return {
      CouponOnWrongPlan: status,
    };
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          {
            fact: 'CouponOnWrongPlan',
            operator: 'equal',
            value: true,
          },
        ],
      },
      event: {
        type: CouponOnWrongPlan.NAME,
        params: {
          error: CouponOnWrongPlan.NAME,
          message: 'Coupon code is not available for this plan.',
        },
      },
    };
  }
}
