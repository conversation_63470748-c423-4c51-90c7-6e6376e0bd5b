import { COUPON_DISCOUNT_TYPE, coupons_orders } from '@prisma/client';

// Strategy interface
export interface ICoupon {
  discount: number;
  type: COUPON_DISCOUNT_TYPE;
  validFrom: Date;
  validTill: Date;
  usagePerPerson?: number;
  totalUsage: number;
  code: string;
  coupons_orders?: coupons_orders[];
  userId?: string;
  services?: string
}
export interface IParserStrategy {
  parse(): ICoupon[];
}
