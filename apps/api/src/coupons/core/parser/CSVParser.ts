import * as xlsx from 'node-xlsx';
import { ICoupon, IParserStrategy } from '../parser/IParserStratefy';

export class CSVParser implements IParserStrategy {
  constructor(private file: File) { }
  parse(): ICoupon[] {
    //@ts-expect-error
    const rawOrders = xlsx.parse(this.file.buffer)[0].data;
    const orders = rawOrders as string[][];
    // Throwing headers
    orders.shift();
    const purchasePayload = orders.map((coupon) => {
      const [
        discount,
        type,
        validFrom,
        validTo,
        usagePerPerson,
        totalUsageLimit,
        code,
        services
      ] = coupon;

      return {
        discount,
        type,
        validFrom,
        validTo,
        usagePerPerson,
        code,
        totalUsage: totalUsageLimit,
        services
      } as unknown as ICoupon;
    });

    return purchasePayload as ICoupon[];
  }
}
