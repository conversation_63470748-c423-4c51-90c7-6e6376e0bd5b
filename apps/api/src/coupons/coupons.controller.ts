import {
  BadRequestException,
  Body,
  CACHE_MANAGER,
  ConflictException,
  Controller,
  Get,
  Inject,
  Logger,
  NotFoundException,
  ParseFilePipe,
  Post,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor } from '@nestjs/platform-express';
import { coupons } from '@prisma/client';
import { Cache } from 'cache-manager';
import { isFuture, isPast } from 'date-fns';
import { Response } from 'express';
import { GetUser } from 'src/auth/get-user.decorator';
import { IUser } from 'src/interface/IUser';
import { PlansService } from 'src/plans/plans.service';
import { Readable } from 'stream';
import { ICoupon } from './core/parser/IParserStratefy';
import { CouponsService } from './coupons.service';
import { RedeemCouponDto } from './dto/redeem-coupon.dto';
@Controller('coupons')
export class CouponsController {
  private logger: Logger = new Logger();
  constructor(
    private readonly couponsService: CouponsService,
    private readonly planService: PlansService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) { }

  @Get('/')
  async getCoupon(@Query() params: { code: string }) {
    try {
      const coupon = await this.couponsService.getSimilarCoupons(params.code);
      return {
        coupon: {
          ...coupon,
          isExpired: isPast(coupon.validTill),
          isActive: isPast(coupon.validFrom) && isFuture(coupon.validTill),
        },
      };
    } catch (err) {
      this.logger.log(err);
      throw new NotFoundException(err.message);
    }
  }

  @Get('promoted')
  async getPromotedCoupons() {
    try {
      const cachedvalue = await this.cacheManager.get('campaign-promotion');
      if (cachedvalue) return cachedvalue;

      const coupons = await this.couponsService.getPromotedCoupons();
      this.cacheManager.set('campaign-promotion', coupons, 3600000);

      return coupons;
    } catch (err) {
      this.logger.error(err);
      return [];
    }
  }
  @Post('redeem')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard())
  async redeem(
    @Body() redeemCouponDto: RedeemCouponDto,
    @GetUser() user: IUser,
  ) {
    try {
      const plan = await this.planService.getPlanById(+redeemCouponDto.planId);
      const coupon = await this.couponsService.redeem(
        redeemCouponDto.couponId,
        user?.userId,
        plan,
      );
      return coupon;
    } catch (err) {
      throw new ConflictException(err.message);
    }
  }

  @Post('redeem-guest')
  @UsePipes(ValidationPipe)
  async redeemGuest(
    @Body() redeemCouponDto: RedeemCouponDto,
    @GetUser() user: IUser,
  ) {
    try {
      const plan = await this.planService.getPlanById(+redeemCouponDto.planId);
      const coupon = await this.couponsService.redeem(
        redeemCouponDto.couponId,
        user?.userId,
        plan,
      );
      return coupon;
    } catch (err) {
      throw new ConflictException(err.message);
    }
  }

  @Post('upload')
  @UseGuards(AuthGuard('headerapikey'))
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile(new ParseFilePipe({ validators: [] }))
    file: Express.Multer.File,
  ) {
    const coupons = this.couponsService.processCouponSource(file) as ICoupon[];

    const vouchers = coupons.map((coupon) =>
      this.couponsService.generate({
        code: coupon.code,
        discount: coupon.discount,
        totalUsageLimit: coupon.totalUsage,
        type: coupon.type,
        validFrom: coupon.validFrom,
        //@ts-expect-error
        validTo: coupon.validTo,
        usagePerPerson: coupon.usagePerPerson,
        services: coupon.services,
      }),
    );
    await this.couponsService.saveVouchers(vouchers as coupons[]);
    return vouchers;
  }

  @Get('download')
  @UseGuards(AuthGuard('headerapikey'))
  async generateCoupons(@Res() res: Response) {
    const buffer = await this.couponsService.downloadAsExcel();

    res.setHeader(
      'Content-Disposition',
      `attachment; filename=coupons-${Date.now()}.xlsx`,
    );
    const bufferStream = new Readable();
    bufferStream.push(buffer);
    bufferStream.push(null);

    bufferStream.pipe(res);
  }

  @Get('/my-coupons')
  @UseGuards(AuthGuard())
  async getMyCoupons(@GetUser() user: IUser) {
    try {
      const coupons = await this.couponsService.getUserCoupons(user.userId);
      return coupons;
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }
}
