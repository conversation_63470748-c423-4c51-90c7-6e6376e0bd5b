import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { coupons, referrals, orders, users } from '@prisma/client';
import { addDays } from 'date-fns';
import { FirstTimerCoupon } from 'src/coupons/core/rules-engine/rules/FirstTimerCoupon';
import { CouponsService } from 'src/coupons/coupons.service';
import { EmailsService } from 'src/emails/emails.service';
import { PrismaService } from 'src/prisma.service';
import * as Voucher from 'voucher-code-generator';

@Injectable()
export class ReferralsService {
  private readonly logger = new Logger(ReferralsService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly couponService: CouponsService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailsService,
  ) {}

  async createReferralCode(user: users) {
    const referralConfig = this.configService.get('referral');
    if (!referralConfig.featureEnabled) return;

    const [couponCode] = Voucher.generate({
      prefix: this.configService.get('referral').couponPrefix,
      length: this.configService.get('referral').couponLength,
      count: 1,
    });
    const constraint = await this.couponService.createConstraint({
      name: FirstTimerCoupon.NAME,
      rule: [],
    });
    const coupon = await this.couponService.create(
      //@ts-expect-error
      {
        code: couponCode.toUpperCase(),
        discount: this.configService.get('referral').referralDiscount,
        totalUsage: 1000,
        usagePerPerson: 1,
        type: 'PERCENTAGE',
        validFrom: new Date(),
        validTill: addDays(
          new Date(),
          this.configService.get('referral').referralCouponExpiry,
        ),
        banners: [],
        promote: false,
      },
      [constraint],
    );
    const referral = await this.prismaService.referrals.create({
      data: {
        couponsId: coupon.id,
        usersId: user.id,
      },
      include: {
        coupon: true,
      },
    });
    return referral;
  }

  async acknowledgeReferral(referrals: Array<referrals & { user: users }>) {
    const referralConfig = this.configService.get('referral');
    if (!referralConfig.featureEnabled) return;

    // This was referred coupon by someone so please
    // Assign new coupon
    const couponConstraint = await this.couponService.createConstraint({
      name: `Self_${referrals[0]?.user.userId}`,
      rule: {
        value: [referrals[0]?.user.userId],
      },
    });
    const [couponCode] = Voucher.generate({
      prefix: 'RW',
      length: this.configService.get('referral').couponLength,
      count: 1,
    });
    const coupon = await this.couponService.create(
      {
        code: couponCode.toUpperCase(),
        discount: this.configService.get('referral').referreDiscount, //+(order.price * (5 / 100)).toFixed(2),
        type: 'PERCENTAGE',
        usagePerPerson: 1,
        validFrom: new Date(),
        validTill: addDays(
          new Date(),
          this.configService.get('referral').referreCouponExpiry,
        ),
        totalUsage: 1,
      } as coupons,
      [couponConstraint],
    );
    const reference = await this.prismaService.users_coupons.create({
      data: {
        couponId: coupon.id,
        userId: referrals[0]?.user.userId,
      },
    });

    this.logger.log(
      `Coupon created for referrer with email ${referrals[0]?.user.email}`,
    );
    return reference;
  }

  async notifyReferrer(email: string) {
    /**simple email until the tempalte is finalized */
    await this.emailService.sendEmail({
      to: email,
      subject: `Your Friend Joined, and You've Earned a Reward!`,
      content:
        'Great news! One of your friends used your referral code to purchase an eSIM from Global Mobile eSIM, and as a result, you’ve earned a special coupon. Be sure to use it before it expires. You can easily find your coupon in the "Coupons" section of your profile. Enjoy your reward, and thank you for spreading the word!',
    });
  }
}
