import { Module } from '@nestjs/common';
import { ReferralsService } from './referrals.service';
import { PrismaService } from 'src/prisma.service';
import { CouponsModule } from 'src/coupons/coupons.module';
import { EmailsModule } from 'src/emails/emails.module';

@Module({
  imports: [CouponsModule, EmailsModule],
  providers: [ReferralsService, PrismaService],
  exports: [ReferralsService],
})
export class ReferralsModule {}
