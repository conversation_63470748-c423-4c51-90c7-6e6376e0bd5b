import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { PlansService } from 'src/plans/plans.service';
import { PrismaService } from 'src/prisma.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { EsimService } from 'src/esim/esim.service';
import { HttpModule } from '@nestjs/axios';
import { XchangeService } from 'src/xchange/xchange.service';
import { UsersService } from 'src/users/users.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { PassportModule } from '@nestjs/passport';
import { NotificationsService } from 'src/notifications/notifications.service';
import { SessionCartService } from 'src/session-cart/session-cart.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { EmailsService } from 'src/emails/emails.service';
import { QueueModule } from 'src/queue/queue.module';
import { CouponsService } from 'src/coupons/coupons.service';
import { EsimPurchaseQueueWorker } from 'src/processors/esim-create-buy.worker';
import { AppsService } from 'src/apps/apps.service';
import { JwtService } from '@nestjs/jwt';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    QueueModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 10000,
        maxRedirects: 5,
        baseURL: configService.get('USIMSA_HOST_NAME'),
      }),
      inject: [ConfigService],
    }),
    PassportModule.register({ defaultStrategy: 'jwt' }),
  ],
  providers: [
    PaymentService,
    PlansService,
    PrismaService,
    ConfigService,
    EsimOrdersService,
    EsimService,
    Logger,
    XchangeService,
    UsersService,
    KeyValueStoreService,
    NotificationsService,
    SessionCartService,
    EmailTemplateService,
    EsimProviderBuilder,
    EmailsService,
    CouponsService,
    EsimPurchaseQueueWorker,
    AppsService,
    JwtService,
    EsimStocksService,
    AwsCognitoService,
    ReferralsService,
  ],
  controllers: [PaymentController],
  exports: [PaymentService],
})
export class PaymentModule {}
