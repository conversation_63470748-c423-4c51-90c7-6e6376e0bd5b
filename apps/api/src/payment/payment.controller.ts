import { InjectQueue } from '@nestjs/bullmq';
import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Get,
  Headers,
  Logger,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  Res,
  UnprocessableEntityException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthGuard } from '@nestjs/passport';
import { ApiExcludeController, ApiResponse } from '@nestjs/swagger';
import { orders, plans, service_providers } from '@prisma/client';
import { Queue } from 'bullmq';
import { Request, Response } from 'express';
import { omit, uniqBy } from 'lodash';
import { I18n, I18nContext } from 'nestjs-i18n';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { GetUser } from 'src/auth/get-user.decorator';
import { QUEUE_NEW_ESIM } from 'src/constants';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { IOrderMetadata } from 'src/esim-orders/interfaces/IOrderMetadata';
import { IStripeMetadata } from 'src/interface/IStripeMetadata';
import { IUser } from 'src/interface/IUser';
import { RequestWithRawBody } from 'src/middleware/rawBody.middleware';
import { SessionCartService } from 'src/session-cart/session-cart.service';
import { UsersService } from 'src/users/users.service';
import {
  capitalizeFirstLetter,
  getPlanDetailsApi,
  isAppEnvDev,
} from 'src/utils';
import { StripeEndpointSource } from './accounts/StripePaymentBuilder';
import { EsimWebhookDto } from './dtos/EsimWebhookDto';
import { PaymentService } from './payment.service';

@Controller('payment')
@ApiExcludeController()
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class PaymentController {
  constructor(
    private paymentService: PaymentService,
    private configService: ConfigService,
    private esimOrderService: EsimOrdersService,
    private logger: Logger,
    private userService: UsersService,
    private sessionCartService: SessionCartService,
    @InjectQueue(QUEUE_NEW_ESIM)
    private newEsimQueue: Queue,
  ) {}

  // @Post('esim/translate/check')
  // async emailTranslations(@I18n() i18n: I18nContext, @Body() payload: any) {
  //   return i18n.t(
  //     `countries.${payload.countryName.replaceAll(' ', '').toLowerCase()}`,
  //   );
  // }

  @Post('esim/mail/translate')
  async emailTranslation(
    @Body() payload: any,
    @Req() request: Request,
    @Res() res: Response,
    @I18n() i18n: I18nContext,
  ) {
    const languageMap = {
      'zh-tw': 'zh',
    };
    const language = languageMap[payload.language] || payload.language || 'en';

    const isAirtrip = payload.userSource === 'airtrip';
    const isJapanese = payload.language === 'jp';
    const isDev = isAppEnvDev();

    const config = {
      airtrip: {
        feHost: this.configService.getOrThrow('FRONTEND_HOST_AIRTRIP'),
        setupPage: isDev
          ? 'https://esim-dev.airtrip.jp/setup'
          : 'https://esim.airtrip.jp/setup',
      },
      japanese: {
        feHost: isDev
          ? 'https://www-dev.gmobile.biz/esim/'
          : 'https://www.gmobile.biz/esim/',
        setupPage: isDev
          ? 'https://www-dev.gmobile.biz/esim/setup'
          : 'https://gmobile.biz/esim/setup',
      },
    };

    const feHost = isAirtrip
      ? config.airtrip.feHost
      : isJapanese
      ? config.japanese.feHost
      : payload.feHost;

    const setupPage = isAirtrip
      ? config.airtrip.setupPage
      : isJapanese
      ? config.japanese.setupPage
      : payload.feHost;

    let setupGuideLinkIos = `${feHost}${language}/app/orders/${payload.orderId}/ios/activate?source=email`;
    let setupGuideLinkAndroid = `${feHost}${language}/app/orders/${payload.orderId}/android/activate?source=email`;
    if (payload.userSource === 'airtrip' || payload.language === 'jp') {
      setupGuideLinkIos = setupPage + '/?os=ios';
      setupGuideLinkAndroid = setupPage + '?os=android';
    }

    if (
      payload.template === 'loyalty-worked' ||
      payload.template === 'loyalty-reminder'
    ) {
      const templateData = this.buildLoyaltyTemplateData(
        payload,
        i18n,
        feHost,
        setupPage,
        payload.template,
      );

      this.logger.log(
        `Translate "${payload.template}" email for ${payload.user.email} order ${payload.order.id}`,
      );

      request.app.render(
        payload.template,
        {
          layout: payload.template,
          data: templateData,
        },
        (err, content) => {
          if (err) {
            this.logger.error(err);
            return res.sendStatus(500);
          }
          res.send(content);
        },
      );
      return;
    }

    const isAsiaOrWorld =
      payload.countryName?.toLowerCase() === 'asia' ||
      payload.countryName?.toLowerCase() === 'world';

    const gmJpOrAirtrip =
      payload.userSource === 'airtrip' ||
      payload.userSource === 'global-esim-jp';

    const isNeedCmlink =
      ['hongkong', 'taiwan'].includes(payload.countryName) ||
      (isAsiaOrWorld && gmJpOrAirtrip);

    const templateData = {
      ...payload,
      feHost: feHost,
      language: language,
      setupPage: setupPage,
      esimPlan:
        payload.esimPlan === 'PER_DAY'
          ? i18n.t('order-email.daily-plan')
          : i18n.t('order-email.data-plan'),
      countryName: capitalizeFirstLetter(
        i18n.t(
          `countries.${payload.countryName
            ?.replaceAll(' ', '')
            ?.toLowerCase()}`,
        ),
      ),
      isNeedCmlink,
      isAsiaOrWorld,
      apn: payload.apn,
      activationLinkIos: payload.thirdPartyApp
        ? `${payload.feHost}${language}/activate/ios?platform=ios&activateCode=${payload.activateCode}&qrCodeImgUrl=${payload.qrCodeFormatted}&smdp=${payload.smdp}`
        : setupGuideLinkIos,
      activationLinkAndroid: payload.thirdPartyApp
        ? `${payload.feHost}${language}/activate/android?platform=android&downloadLink=${payload.downloadLink}&activateCode=${payload.activateCode}&qrCodeImgUrl=${payload.qrCodeFormatted}&smdp=${payload.smdp}`
        : setupGuideLinkAndroid,
    };

    this.logger.log(
      `Translate order confirmation email for ${payload.email} for order number ${payload.orderId}`,
    );

    //@ts-ignore
    request.app.render(
      payload.template || 'order-confirm-layout',
      {
        layout: payload.template || 'order-confirm-layout',
        data: templateData,
        language: templateData.language,
      },
      (err, content) => {
        if (err) {
          this.logger.error(err);
          return res.sendStatus(500);
        }
        res.send(content);
      },
    );
  }

  private buildLoyaltyTemplateData(
    payload: any,
    i18n: I18nContext,
    feHost: string,
    setupPage: string,
    template: 'loyalty-worked' | 'loyalty-reminder',
  ): Record<string, any> {
    const lang = i18n.lang;
    const i18nJson =
      template === 'loyalty-worked' ? 'loyalty-email' : 'loyalty-reminder';
    const renderedAppreciation = i18n.t(`${i18nJson}.appreciation-message`, {
      lang,
      args: { couponDiscount: payload.couponDiscount },
    });

    const renderedCouponCode = i18n.t(`${i18nJson}.coupon-code`, {
      lang,
      args: { couponCode: payload.couponCode },
    });

    const serviceName =
      payload.order?.source === 'global-esim-jp'
        ? '【グロモバeSIM】'
        : payload.order?.source === 'airtrip'
        ? '【エアトリeSIM】'
        : 'Global Mobile';

    const renderedThankyouTitle = i18n.t(`${i18nJson}.thank-you-title`, {
      lang,
      args: { serviceName },
    });

    const renderedTeamName = i18n.t(`${i18nJson}.team-name`, {
      lang,
      args: { serviceName },
    });

     const renderedClosingMessage = i18n.t(`${i18nJson}.closing-message`, {
      lang,
      args: { serviceName },
    });

    return {
      ...payload,
      feHost,
      language: lang,
      setupPage,
      renderedAppreciation,
      renderedCouponCode,
      renderedThankyouTitle,
      renderedTeamName,
      renderedClosingMessage
    };
  }

  @Post('/esim/webhook')
  async esimWebhook(
    @Body() payload: EsimWebhookDto,
    @Req() request: RequestWithRawBody,
    @I18n() i18n: I18nContext,
    @Query() query,
  ) {
    this.logger.log(`Webhook recieved for topup id:${payload.topupId}`);
    const esimOrder = await this.esimOrderService.get({
      topupId: payload.topupId,
    });
    if (!esimOrder) {
      throw new NotFoundException();
    }

    const { plan, user, ...order } = esimOrder;
    if (!order || !plan) {
      this.logger.error(
        `Issue on esim webhook handler, ${order.orderId}, ${JSON.stringify(
          payload,
        )}`,
      );
      return new NotFoundException('User/Order/Plan not found.');
    }
    if (this.configService.getOrThrow('NODE_ENV') !== 'development') {
      if (order.activateCode || order.iccid) {
        throw new ConflictException('Order already processed.');
      }
    }

    this.logger.log(
      `Processing Webhook recieved for topup id:${payload.topupId}`,
    );

    let previousResponse;
    try {
      previousResponse = JSON.parse(
        JSON.stringify(order.response || {}) || '{}',
      );
    } catch {
      previousResponse = {};
    }
    await this.newEsimQueue.add(
      `${order.orderId}:${order.iccid}`,
      {
        orderId: order.id,
        appsId: order.appsId,
        userId: order.userId,
        plan: order.planId,
        orderResponse: previousResponse,
      },
      {
        jobId: `${order.orderId}:${order.iccid}`,
        ...(this.configService.get('redis').queue || {}),
      },
    );
    return payload;
  }

  @Get('/customer-sheet/:source?')
  @UseGuards(AuthGuard())
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        ephemeralKey: { type: 'string' },
        customerId: { type: 'string' },
        setupIntent: { type: 'string' },
        payment_method_types: { type: 'string' },
        payment_method_options: { type: 'string' },
      },
    },
  })
  async getCustomerSheet(
    @GetUser() user: IUser,
    @Param() params: { source?: UserRegisterSource },
  ) {
    const profile = await this.userService.getUserInfo(user.userId);
    const source = params.source;
    let stripeId: string;

    if (source) {
      const stripeIdTemp = await this.paymentService.getStripeIdBySource(
        source,
        profile,
      );

      stripeId = Boolean(stripeIdTemp)
        ? stripeIdTemp
        : (
            await this.paymentService.createStripeCustomerWithSource(
              source,
              profile,
            )
          ).id;
    } else {
      stripeId = profile.stripeId;
    }

    const context = { ...(profile ?? {}), source };

    const [response, ephemeralKey] = await Promise.all([
      this.paymentService.getSetupIntent(
        stripeId,
        //@ts-ignore
        context,
      ),
      this.paymentService.getEphimeralKeys(
        stripeId,
        //@ts-ignore
        context,
      ),
    ]);

    return {
      ephemeralKey: ephemeralKey.secret,
      customerId: stripeId,
      setupIntent: response.client_secret,
      payment_method_types: response.payment_method_types,
      payment_method_options: response.payment_method_options,
    };
  }

  @Post('setup')
  // @UseGuards(AuthGuard())
  async getSetupIntent(
    @GetUser() user: IUser,
    @Body()
    body: {
      planId: number;
      source?: UserRegisterSource;
      userPool?: string;
      corporateId?: string;
    },
  ) {
    const profile = user
      ? await this.userService.getUserInfo(user?.userId)
      : null;
    try {
      const source = body.source;
      let stripeId: string;

      if (source) {
        const stripeIdTemp = await this.paymentService.getStripeIdBySource(
          source,
          //@ts-ignore
          {
            source: body.source,
            userPool: body.userPool,
          },
        );

        stripeId = Boolean(stripeIdTemp)
          ? stripeIdTemp
          : (
              await this.paymentService.createStripeCustomerWithSource(
                source,
                //@ts-ignore
                {
                  source: body.source,
                  userPool: body.userPool,
                },
              )
            ).id;
      } else {
        stripeId = profile.stripeId;
      }

      const response = await this.paymentService.getSetupIntent(
        stripeId,
        //@ts-ignore
        profile || {
          source: body.source,
          userPool: body.userPool,
        },
      );

      return {
        client_secret: response.client_secret,
        payment_method_types: response.payment_method_types,
        payment_method_options: response.payment_method_options,
      } as Partial<typeof response>;
    } catch (err) {
      // This is for users who were customer of old stripe account
      // here we create new customer
      if (err.message.includes('No such customer')) {
        const customer = await this.paymentService.createCustomer(profile);
        await this.userService.updateUser(
          { username: profile?.username },
          { stripeId: customer.id },
        );
        return this.getSetupIntent(user, body);
      }
      throw err;
    }
  }
  @Get('list')
  @UseGuards(AuthGuard())
  async listPaymentMethods(@GetUser() user: IUser) {
    try {
      const profile = await this.userService.getUserInfo(user.userId);
      const paymentMethods = await this.paymentService.listAllPaymentMethods(
        profile,
        profile.stripeId,
      );
      return {
        data: uniqBy(paymentMethods.data, 'card.fingerprint').map((method) => ({
          id: method.id,
          card: omit(method.card, 'fingerprint'),
          type: method.type,
          createdAt: method.created,
        })),
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }
  @Get('/:paymentMethodId')
  @UseGuards(AuthGuard())
  async show(
    @GetUser() user: IUser,
    @Param('paymentMethodId') paymentMethodId: string,
  ) {
    const profile = await this.userService.getUserInfo(user.userId);
    const response = await this.paymentService.getPaymentMethodDetails(
      profile,
      profile.stripeId,
      paymentMethodId,
    );
    return { data: omit(response, 'customer', 'livemode', 'card.fingerprint') };
  }

  @Post('/webhook/?:source')
  async initiateStripePaymentIntent(
    @Headers('stripe-signature') signature: string,
    @Req() request: RequestWithRawBody,
    @Param('source') source: StripeEndpointSource,
  ) {
    await ConfigModule.envVariablesLoaded;
    if (!signature) {
      throw new BadRequestException('Missing stripe-signature header');
    }
    try {
      const event = await this.processStripeWebhook(
        request.rawBody,
        signature,
        source || 'global-esim-jp',
      );
      if (
        !['payment_intent.succeeded', 'invoice.payment_succeeded'].includes(
          event.type,
        )
      )
        return;
      await this.handleWebhookEvent(event);
    } catch (error) {
      this.logger.error(error, {});
      throw new BadRequestException(error.message);
    }
  }

  private async processStripeWebhook(
    rawBody: Buffer,
    signature: string,
    source: StripeEndpointSource,
  ) {
    const event = await this.paymentService.processWebhook(
      rawBody,
      signature,
      source,
    );
    this.logger.log('New webhook received: ' + event.type);
    return event;
  }

  private async handleWebhookEvent(event: any) {
    const chargeObject = event.data.object;
    const metadata = chargeObject.metadata as IStripeMetadata;

    if (!metadata.orderId) {
      throw new BadRequestException('Invalid metadata or unknown event.');
    }

    const esimOrder = await this.validateEsimOrder(metadata);
    if (!metadata.orders) {
      //@ts-expect-error
      await this.handleEsimOrderUpdates(esimOrder, metadata, chargeObject);
      return;
    }

    if (!metadata.orderId) throw new UnprocessableEntityException();
    const { processedOrders } = await this.esimOrderService.processEsimOrder(
      metadata.orderId,
      metadata.userId,
      {
        amount: chargeObject.amount,
        latest_charge: chargeObject.latest_charge,
        payment_intent: chargeObject.payment_intent,
      },
      metadata,
    );
    return { processedOrders };
  }

  private async validateEsimOrder(metadata: IStripeMetadata) {
    const esimOrder = await this.esimOrderService.get({
      id: Number(metadata.orderId),
    });

    if (
      !esimOrder
      // esimOrder.activateCode ||
      // esimOrder.topupId ||
      // esimOrder.orderCreatedAt
    ) {
      throw new BadRequestException('Invalid or already processed order.');
    }
    return esimOrder;
  }

  /**
   * @deprecated
   * @param esimOrder
   * @param metadata
   * @param chargeObject
   * @returns
   */
  private async handleEsimOrderUpdates(
    esimOrder: orders & {
      plan: plans & { serviceProvider: service_providers };
    },
    metadata: Pick<IStripeMetadata, 'sessionSecret' | 'type'>,
    chargeObject,
  ) {
    const response = await this.esimOrderService.queueESIMPurchaseRequest({
      planId: esimOrder.planId + '',
      lang: esimOrder.lang,
      chargeAmount: esimOrder.jpyPrice + '',
      metadata: (esimOrder.orderMetaData || {}) as IOrderMetadata,
      orderId: esimOrder.id,
      planUrl: getPlanDetailsApi(esimOrder.planId + ''),
      stripeChargeId: chargeObject.latest_charge,
    });

    return response;
  }
}
