import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { orders, plans, users } from '@prisma/client';
import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { PAYMENT_STATUS } from 'src/constants';
import { EmailsService } from 'src/emails/emails.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { IQuotation as IQuotations } from 'src/esim-orders/orders/IQuotation';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { XchangeService } from 'src/xchange/xchange.service';
import {
  StripeEndpointSource,
  StripePaymentBuilder,
} from './accounts/StripePaymentBuilder';

@Injectable()
export class PaymentService {
  private stripeBuilder: StripePaymentBuilder;

  constructor(
    esimOrder: EsimOrdersService,
    configService: ConfigService,
    xe: XchangeService,
    emailService: EmailsService,
  ) {
    this.stripeBuilder = new StripePaymentBuilder(
      xe,
      configService,
      emailService,
      esimOrder,
    );
  }
  /**
   *
   * @param planId
   * @param stripeId
   * @returns
   */
  async createPaymentIntent(
    planId: string,
    stripeId: string,
    stripeOptions: any = {},
    plan: plans,
    user: users,
  ) {
    const stripe = this.stripeBuilder.build({
      source: user.source as UserRegisterSource,
      userPool: user.userPool as LocalUserPool,
    });
    return stripe.createPaymentIntent(planId, stripeId, stripeOptions, plan);
  }

  /**
   *
   * @param planId
   * @param stripeId
   * @returns
   */
  async createPaymentIntentNew(
    amount,
    user,
    options: {
      source?: string;
      stripeOptions: {
        description?: string;
        metadata: any;
        [key: string]: any;
      };
    },
  ) {
    const stripe = this.stripeBuilder.build({
      source: (options.source as UserRegisterSource) || user.source,
      userPool: user.userPool as LocalUserPool,
    });

    return stripe.createPaymentIntentNew(
      amount,
      user.stripeId,
      options.stripeOptions,
    );
  }

  createInvoice(
    quotation: IQuotations,
    user,
    options: {
      source?: string;
      stripeOptions: {
        description?: string;
        metadata: any;
        [key: string]: any;
      };
    },
  ) {
    const stripe = this.stripeBuilder.build({
      source: (options.source as UserRegisterSource) || user.source,
      userPool: user.userPool as LocalUserPool,
    });

    return stripe.createInvoice(
      quotation,
      user.stripeId,
      options.stripeOptions,
    );
  }
  async createCustomer(user: users, metadata: {} = {}) {
    const stripe = this.stripeBuilder.build({
      source: user.source as UserRegisterSource,
      userPool: user.userPool as LocalUserPool,
    });
    return await stripe.createCustomer(user, metadata);
  }

  listAllPaymentMethods(user: users, customerId: string) {
    const stripe = this.stripeBuilder.build({
      source: user.source as UserRegisterSource,
      userPool: user.userPool as LocalUserPool,
    });
    return stripe.listAllPaymentMethods(customerId);
  }
  getPaymentMethodDetails(
    user: users,
    customerId: string,
    paymentMethodId: string,
  ) {
    const stripe = this.stripeBuilder.build({
      source: user.source as UserRegisterSource,
      userPool: user.userPool as LocalUserPool,
    });
    return stripe.getPaymentMethodDetails(customerId, paymentMethodId);
  }

  detachPaymentMethod(user: users, paymentMethodId: string) {
    const stripe = this.stripeBuilder.build({
      source: user.source as UserRegisterSource,
      userPool: user.userPool as LocalUserPool,
    });
    return stripe.deleteCustomer(paymentMethodId);
  }
  async processWebhook(
    body: object,
    signature: string,
    endpointSource: StripeEndpointSource,
  ) {
    try {
      const stripe = this.stripeBuilder.buildFromStripeEndpoint(endpointSource);
      return stripe.processWebhook(body, signature);
    } catch (err) {
      throw new Error(err.message);
    }
  }

  async updatePaymentOrder(
    {
      paymentStatus,
      txnId,
      response,
      userPool,
      userSource,
      paymentIntentId,
      orderId,
    }: {
      paymentIntentId: string;
      txnId: string;
      paymentStatus: PAYMENT_STATUS;
      response?: any;
      orderId: any;
      userSource: UserRegisterSource;
      userPool: LocalUserPool;
    },
    rest: Partial<orders>,
  ) {
    const stripe = this.stripeBuilder.build({
      source: userSource as UserRegisterSource,
      userPool: userPool as LocalUserPool,
    });
    return stripe.updatePaymentOrder(
      { orderId, paymentStatus, txnId, response, paymentIntentId },
      rest,
    );
  }

  async sendPurchaseMail(
    to: string,
    content: string,
    { subject }: { subject: string },
    user: users,
  ) {
    try {
      const stripe = this.stripeBuilder.build({
        source: user.source as UserRegisterSource,
        userPool: user.userPool as LocalUserPool,
      });
      const response = await stripe.sendPurchaseMail(to, content, {
        subject,
      });
      return response;
    } catch (err) {
      throw err;
    }
  }

  getSetupIntent(customerId: string, user: users) {
    const stripe = this.stripeBuilder.build({
      source: user.source as UserRegisterSource,
      userPool: user.userPool as LocalUserPool,
    });
    return stripe.getSetupIntent(customerId);
  }

  async getEphimeralKeys(customerId: string, user: users) {
    const stripe = this.stripeBuilder.build({
      source: user.source as UserRegisterSource,
      userPool: user.userPool as LocalUserPool,
    });
    return stripe.getEphimeralKeys(customerId);
  }

  async getStripeIdBySource(source: UserRegisterSource, user: users) {
    const stripe = this.stripeBuilder.build({
      source,
      userPool: user.userPool as LocalUserPool,
    });

    const userCheck = await stripe.checkEmailExist(user.email);

    if (userCheck.data.length > 0) {
      return userCheck.data[0].id;
    }

    return null;
  }

  async createStripeCustomerWithSource(
    source: UserRegisterSource,
    user: users,
    metadata: {} = {},
  ) {
    const stripe = this.stripeBuilder.build({
      source,
      userPool: user.userPool as LocalUserPool,
    });

    return await stripe.createCustomer(user, metadata);
  }
}
