import { ConfigService } from '@nestjs/config';
import { EmailsService } from 'src/emails/emails.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { AbstractStripePayment } from './AbstractStripePayment';

export class GlobalEsimJPStripe extends AbstractStripePayment {
  constructor(
    protected xe: XchangeService,
    protected configService: ConfigService,
    protected emailService: EmailsService,
    protected esimOrder: EsimOrdersService,
  ) {
    super({
      secretKey: configService.getOrThrow('STRIPE_GLOBAL_ESIM_JP_SECRET_KEY'),
    });
  }

  getWebhookSecret(): string {
    return this.configService.getOrThrow(
      'STRIPE_GLOBAL_ESIM_JP_ENDPOINT_SECRET',
    );
  }
}
