import { UserRegisterSource } from 'src/auth/dtos/auth-register-user';
import { LocalUserPool } from 'src/users/enums/LocalUserPool';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { EmailsService } from 'src/emails/emails.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { ConfigService } from '@nestjs/config';
import { GlobalEsimStripe } from './GlobalEsimStripe';
import { GlobalEsimJPStripe } from './GlobalEsimJPStripe';
import { AirtripEsimStripe } from './AirtripEsimStripe';
import { AbstractStripePayment } from './AbstractStripePayment';

export type StripeEndpointSource = 'airtrip' | 'global-esim-jp' | 'global-esim';
export class StripePaymentBuilder {
  constructor(
    protected xe: XchangeService,
    protected configService: ConfigService,
    protected emailService: EmailsService,
    protected esimOrder: EsimOrdersService,
  ) {}
  buildFromStripeEndpoint(stripeEndpoint: StripeEndpointSource) {
    if (stripeEndpoint === 'airtrip') {
      return new AirtripEsimStripe(
        this.xe,
        this.configService,
        this.emailService,
        this.esimOrder,
      );
    }

    if (stripeEndpoint === 'global-esim') {
      return new GlobalEsimStripe(
        this.xe,
        this.configService,
        this.emailService,
        this.esimOrder,
      );
    }

    return new GlobalEsimJPStripe(
      this.xe,
      this.configService,
      this.emailService,
      this.esimOrder,
    );
  }
  build(user: {
    source: UserRegisterSource;
    userPool: LocalUserPool;
  }): AbstractStripePayment {
    if (
      user.userPool === LocalUserPool.AIRTRIP ||
      user.source === UserRegisterSource.AIRTRIP_ESIM_ANDROID_APP ||
      user.source === UserRegisterSource.AIRTRIP_ESIM_IOS_APP
    ) {
      return new AirtripEsimStripe(
        this.xe,
        this.configService,
        this.emailService,
        this.esimOrder,
      );
    }
    if (user.source === UserRegisterSource.GLOBAL_ESIM) {
      return new GlobalEsimStripe(
        this.xe,
        this.configService,
        this.emailService,
        this.esimOrder,
      );
    }

    return new GlobalEsimJPStripe(
      this.xe,
      this.configService,
      this.emailService,
      this.esimOrder,
    );
  }
}
