import { ConfigService } from '@nestjs/config';
import { orders, plans, users } from '@prisma/client';
import { startCase } from 'lodash';
import { PAYMENT_STATUS, STRIPE_CURRENCY } from 'src/constants';
import { EmailsService } from 'src/emails/emails.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { IQuotation } from 'src/esim-orders/orders/IQuotation';
import { IStripeMetadata } from 'src/interface/IStripeMetadata';
import { getFormmattedOrderId } from 'src/utils';
import { XchangeService } from 'src/xchange/xchange.service';
import Stripe from 'stripe';

export abstract class AbstractStripePayment {
  protected xe: XchangeService;
  protected configService: ConfigService;
  protected emailService: EmailsService;
  protected esimOrder: EsimOrdersService;
  protected stripe: Stripe;

  constructor(private config: { secretKey: string }) {
    this.stripe = new Stripe(config.secretKey, {
      apiVersion: '2022-11-15',
    });
  }
  /**
   *
   * @param planId
   * @param stripeId
   * @returns
   */
  async createPaymentIntent(
    planId: string,
    stripeId: string,
    stripeOptions: { [key: string]: any; metadata?: IStripeMetadata } = ({} =
      {}),
    plan: plans,
  ) {
    const xeRate = await this.xe.getRateOfCurrency('1', {
      baseCurrency: 'USD',
    });
    const xe = this.xe.getXchanges(plan.price, xeRate);
    if (!plan) null;
    const paymentIntent = await this.stripe.paymentIntents.create({
      description: `${getFormmattedOrderId(
        stripeOptions?.metadata?.orderId,
      )} / ${plan.name?.toLowerCase() === 'unlimited' ? 'Unlimited' : plan.dataId
        } / ${plan.validityDays} days`,
      amount: plan?.prices?.[plan.defaultCurrency] ? plan.price : xe['JPY'],
      currency: this.configService.get(STRIPE_CURRENCY),
      //@ts-expect-error
      metadata: {},
      customer: stripeId,
      setup_future_usage: 'off_session',
      ...stripeOptions,
    });
    return paymentIntent;
  }
  /**
   *
   * @param planId
   * @param stripeId
   * @returns
   */
  async createPaymentIntentNew(
    amount: number,
    stripeId: string,
    stripeOptions: {
      description?: string;
      metadata: any;
      [key: string]: any;
    },
  ) {
    // const couponReference = this.
    const paymentIntent = await this.stripe.paymentIntents.create({
      description: stripeOptions.description,
      amount: Math.ceil(amount),
      currency: this.configService.get(STRIPE_CURRENCY),
      metadata: {},
      customer: stripeId ? stripeId : undefined,
      setup_future_usage: 'off_session',
      ...stripeOptions,
    });
    return paymentIntent;
  }

  async createInvoice(
    quotation: IQuotation,
    stripeCustomerId: string,
    stripeOptions: {
      description?: string;
      metadata?: Record<string, any>;
      [key: string]: any;
    },
  ) {
    const { source, automatic_payment_methods, ...newStripeOptions } = stripeOptions;
    // Step 2: Create the invoice
    const invoice = await this.stripe.invoices.create({
      customer: stripeCustomerId ? stripeCustomerId : undefined,
      auto_advance: false, // Automatically finalize and attempt to collect payment
      metadata: stripeOptions.metadata || {},
      collection_method: 'send_invoice',
      currency: this.configService.get(STRIPE_CURRENCY),
      days_until_due: 30,
      ...newStripeOptions,
    });

    // Step 1: Create an invoice item (this represents the charge details)
    for (const order of quotation.orders) {

      const invoiceItem = await this.stripe.invoiceItems.create({
        invoice: invoice.id,
        customer: stripeCustomerId ? stripeCustomerId : undefined,
        amount: order.model.jpyPrice,
        currency: this.configService.get(STRIPE_CURRENCY),
        //@ts-expect-error
        description: `${startCase(order.plan.country?.name)} /  ${order.plan.packageType === "PER_DAY" ? "Unlimited" : order.plan.dataId} / ${order.plan.validityDays} days`,
        metadata: stripeOptions.metadata || {},
      });

    }


    return invoice;
  }



  async createCustomer(user: users, metadata: {} = {}) {
    return await this.stripe.customers.create({
      email: user.email,
      name: user.firstName + ' ' + user.lastName || '',
      metadata,
    });
  }

  async deleteCustomer(customerId: string) {
    return await this.stripe.customers.del(customerId);
  }
  listAllPaymentMethods(customerId: string) {
    return this.stripe.customers.listPaymentMethods(customerId, {
      limit: 100,
    });
  }
  getPaymentMethodDetails(customerId: string, paymentMethodId: string) {
    return this.stripe.customers.retrievePaymentMethod(
      customerId,
      paymentMethodId,
    );
  }

  detachPaymentMethod(paymentMethodId: string) {
    return this.stripe.paymentMethods.detach(paymentMethodId);
  }
  async processWebhook(body: object, signature: string) {
    try {
      return this.stripe.webhooks.constructEvent(
        body as unknown as string,
        signature,
        this.getWebhookSecret() as string,
      );
    } catch (err) {
      throw new Error(err.message);
    }
  }

  async updatePaymentOrder(
    {
      paymentStatus,
      txnId,
      response,
      orderId,
    }: {
      paymentIntentId: string;
      txnId: string;
      paymentStatus: PAYMENT_STATUS;
      response?: any;
      orderId: any;
    },
    rest: Partial<orders>,
  ) {
    const data = { paymentStatus, txnId };
    if (response) {
      //@ts-ignore
      data.response = response;
    }
    return this.esimOrder.updateOrder(
      { ...data, ...rest },
      {
        id: orderId,
      },
    );
  }

  async sendPurchaseMail(
    to: string,
    content: string,
    { subject }: { subject: string },
  ) {
    try {
      const response = await this.emailService.sendEmail({
        bcc:
          this.configService.getOrThrow('NODE_ENV') !== 'development'
            ? ['<EMAIL>', '<EMAIL>']
            : [],
        subject,
        to,
        message: content,
      });
      return response;
    } catch (err) {
      throw err;
    }
  }

  getSetupIntent(customerId: string) {
    return this.stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
    });
  }

  async getEphimeralKeys(customerId: string) {
    return this.stripe.ephemeralKeys.create(
      {
        customer: customerId,
      },
      {
        apiVersion: '2022-11-15',
      },
    );
  }

  async checkEmailExist(email: string) {
    return this.stripe.customers.list({
      email,
    });
  }

  public getWebhookSecret(): string | Error {
    throw new Error('Not implemented');
  }
}
