import { XchangeService } from 'src/xchange/xchange.service';
import { AbstractStripePayment } from './AbstractStripePayment';
import { ConfigService } from '@nestjs/config';
import { EmailsService } from 'src/emails/emails.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';

export class GlobalEsimStripe extends AbstractStripePayment {
  constructor(
    protected xe: XchangeService,
    protected configService: ConfigService,
    protected emailService: EmailsService,
    protected esimOrder: EsimOrdersService,
  ) {
    super({
      secretKey: configService.getOrThrow('STRIPE_GLOBAL_ESIM_API_SECRET_KEY'),
    });
  }

  getWebhookSecret(): string {
    return this.configService.getOrThrow('STRIPE_GLOBAL_ESIM_ENDPOINT_SECRET');
  }
}
