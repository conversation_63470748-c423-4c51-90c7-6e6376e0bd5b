<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
  <!--[if gte mso 15]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Global Mobile eSIM</title>
  <style>
    img {
      -ms-interpolation-mode: bicubic;
    }

    table,
    td {
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }

    .mceStandardButton,
    .mceStandardButton td,
    .mceStandardButton td a {
      mso-hide: all !important;
    }

    p,
    a,
    li,
    td,
    blockquote {
      mso-line-height-rule: exactly;
    }

    p,
    a,
    li,
    td,
    body,
    table,
    blockquote {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
    }

    @media only screen and (max-width: 480px) {

      body,
      table,
      td,
      p,
      a,
      li,
      blockquote {
        -webkit-text-size-adjust: none !important;
      }
    }

    .mcnPreviewText {
      display: none !important;
    }

    .bodyCell {
      margin: 0 auto;
      padding: 0;
      width: 100%;
    }

    .ExternalClass,
    .ExternalClass p,
    .ExternalClass td,
    .ExternalClass div,
    .ExternalClass span,
    .ExternalClass font {
      line-height: 100%;
    }

    .ReadMsgBody {
      width: 100%;
    }

    .ExternalClass {
      width: 100%;
    }

    a[x-apple-data-detectors] {
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
    }

    body {
      height: 100%;
      margin: 0;
      padding: 0;
      width: 100%;
      background: #ffffff;
    }

    p {
      margin: 0;
      padding: 0;
    }

    table {
      border-collapse: collapse;
    }

    td,
    p,
    a {
      word-break: break-word;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      display: block;
      margin: 0;
      padding: 0;
    }

    img,
    a img {
      border: 0;
      height: auto;
      outline: none;
      text-decoration: none;
    }

    a[href^="tel"],
    a[href^="sms"] {
      color: inherit;
      cursor: default;
      text-decoration: none;
    }

    li p {
      margin: 0 !important;
    }

    .ProseMirror a {
      pointer-events: none;
    }

    @media only screen and (max-width: 480px) {
      body {
        width: 100% !important;
        min-width: 100% !important;
      }

      body.mobile-native {
        -webkit-user-select: none;
        user-select: none;
        transition: transform 0.2s ease-in;
        transform-origin: top center;
      }

      body.mobile-native.selection-allowed a,
      body.mobile-native.selection-allowed .ProseMirror {
        user-select: auto;
        -webkit-user-select: auto;
      }

      colgroup {
        display: none;
      }

      img {
        height: auto !important;
      }

      .mceWidthContainer {
        max-width: 660px !important;
      }

      .mceColumn {
        display: block !important;
        width: 100% !important;
      }

      .mceColumn-forceSpan {
        display: table-cell !important;
        width: auto !important;
      }

      .mceBlockContainer {
        padding-right: 16px !important;
        padding-left: 16px !important;
      }

      .mceBlockContainerE2E {
        padding-right: 0px;
        padding-left: 0px;
      }

      .mceSpacing-24 {
        padding-right: 16px !important;
        padding-left: 16px !important;
      }

      .mceFooterSection .mceText,
      .mceFooterSection .mceText p {
        font-size: 16px !important;
        line-height: 140% !important;
      }

      .mceText,
      .mceText p {
        font-size: 16px !important;
        line-height: 140% !important;
      }

      h1 {
        font-size: 30px !important;
        line-height: 120% !important;
      }

      h2 {
        font-size: 26px !important;
        line-height: 120% !important;
      }

      h3 {
        font-size: 20px !important;
        line-height: 125% !important;
      }

      h4 {
        font-size: 18px !important;
        line-height: 125% !important;
      }
    }

    @media only screen and (max-width: 640px) {
      .mceClusterLayout td {
        padding: 4px !important;
      }
    }

    div[contenteditable="true"] {
      outline: 0;
    }

    .ProseMirror .empty-node,
    .ProseMirror:empty {
      position: relative;
    }

    .ProseMirror .empty-node::before,
    .ProseMirror:empty::before {
      position: absolute;
      left: 0;
      right: 0;
      color: rgba(0, 0, 0, 0.2);
      cursor: text;
    }

    .ProseMirror .empty-node:hover::before,
    .ProseMirror:empty:hover::before {
      color: rgba(0, 0, 0, 0.3);
    }

    .ProseMirror h1.empty-node:only-child::before,
    .ProseMirror h2.empty-node:only-child::before,
    .ProseMirror h3.empty-node:only-child::before,
    .ProseMirror h4.empty-node:only-child::before {
      content: 'Heading';
    }

    .ProseMirror p.empty-node:only-child::before,
    .ProseMirror:empty::before {
      content: 'Start typing...';
    }

    a .ProseMirror p.empty-node::before,
    a .ProseMirror:empty::before {
      content: '';
    }

    .mceText,
    .ProseMirror {
      white-space: pre-wrap;
    }

    body,
    #bodyTable {
      background-color: rgb(244, 244, 244);
    }

    .mceText,
    .mceLabel {
      font-family: "Helvetica Neue", Helvetica, Arial, Verdana, sans-serif;
    }

    .mceText,
    .mceLabel {
      color: rgb(0, 0, 0);
    }

    .mceText h1 {
      margin-bottom: 0px;
    }

    .mceText p {
      margin-bottom: 0px;
    }

    .mceText label {
      margin-bottom: 0px;
    }

    .mceText input {
      margin-bottom: 0px;
    }

    .mceSpacing-12 .mceInput+.mceErrorMessage {
      margin-top: -6px;
    }

    .mceText h1 {
      margin-bottom: 0px;
    }

    .mceText p {
      margin-bottom: 0px;
    }

    .mceText label {
      margin-bottom: 0px;
    }

    .mceText input {
      margin-bottom: 0px;
    }

    .mceSpacing-24 .mceInput+.mceErrorMessage {
      margin-top: -12px;
    }

    .mceInput {
      background-color: transparent;
      border: 2px solid rgb(208, 208, 208);
      width: 60%;
      color: rgb(77, 77, 77);
      display: block;
    }

    .mceInput[type="radio"],
    .mceInput[type="checkbox"] {
      float: left;
      margin-right: 12px;
      display: inline;
      width: auto !important;
    }

    .mceLabel>.mceInput {
      margin-bottom: 0px;
      margin-top: 2px;
    }

    .mceLabel {
      display: block;
    }

    .mceText p {
      color: rgb(0, 0, 0);
      font-family: "Helvetica Neue", Helvetica, Arial, Verdana, sans-serif;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.5;
      text-align: center;
      letter-spacing: 0px;
      direction: ltr;
    }

    .mceText h1 {
      color: rgb(0, 0, 0);
      font-family: "Helvetica Neue", Helvetica, Arial, Verdana, sans-serif;
      font-size: 31px;
      font-weight: bold;
      line-height: 1.5;
      text-align: center;
      letter-spacing: 0px;
      direction: ltr;
    }

    .mceText a {
      color: rgb(0, 0, 0);
      font-style: normal;
      font-weight: normal;
      text-decoration: underline;
      direction: ltr;
    }

    @media only screen and (max-width: 480px) {
      .mceText p {
        font-size: 16px !important;
        line-height: 1.5 !important;
      }
    }

    @media only screen and (max-width: 480px) {
      .mceText h1 {
        font-size: 31px !important;
        line-height: 1.5 !important;
      }
    }

    @media only screen and (max-width: 480px) {
      .mceBlockContainer {
        padding-left: 16px !important;
        padding-right: 16px !important;
      }
    }

    #dataBlockId-9 p,
    #dataBlockId-9 h1,
    #dataBlockId-9 h2,
    #dataBlockId-9 h3,
    #dataBlockId-9 h4,
    #dataBlockId-9 ul {
      text-align: center;
    }
  </style>
</head>

<body>
  <center>
    <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="background-color: rgb(244, 244, 244);">
      <tbody>
        <tr>
          <td class="bodyCell" align="center" valign="top">
            <table id="root" border="0" cellpadding="0" cellspacing="0" width="100%">
              <tbody data-block-id="13" class="mceWrapper">
                <tr>
                  <td align="center" valign="top" class="mceWrapperOuter">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width:660px" role="presentation">
                      <tbody>
                        <tr>
                          <td style="background-color:#ffffff;background-position:center;background-repeat:no-repeat;background-size:cover" class="mceWrapperInner" valign="top">
                            <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" role="presentation" data-block-id="12">
                              <tbody>
                                <tr class="mceRow">
                                  <td style="background-position:center;background-repeat:no-repeat;background-size:cover" valign="top">
                                    <table border="0" cellpadding="0" cellspacing="0" width="100%" role="presentation">
                                      <tbody>
                                        <tr>
                                          <td style="padding-top:0;padding-bottom:0" class="mceColumn" data-block-id="-6" valign="top" colspan="12" width="100%">
                                            <table border="0" cellpadding="0" cellspacing="0" width="100%" role="presentation">
                                              <tbody>
                                                <tr>
                                                  <td style="padding-top:40px;padding-bottom:10px;padding-right:48px;padding-left:48px" class="mceBlockContainer" align="center" valign="top"><img data-block-id="2" width="276.99999999999994" height="auto" style="width:276.99999999999994px;height:auto;max-width:100%;display:block" alt="" src="https://mcusercontent.com/6f591753d8bf77564eb1beb47/images/da67fc40-e6d6-b6ac-e1c9-8b6a5c87ef3a.png" role="presentation" class="imageDropZone"></td>
                                                </tr>{{> (whichPartial) data=data}}
                                                <tr>
                                                  <td style="background-color:transparent;padding-top:20px;padding-bottom:20px;padding-right:24px;padding-left:24px" class="mceBlockContainer" valign="top">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color:transparent" role="presentation" data-block-id="6">
                                                      <tbody>
                                                        <tr>
                                                          <td style="min-width:100%;border-top:1px solid #d0d0d0" valign="top"></td>
                                                        </tr>
                                                      </tbody>
                                                    </table>
                                                  </td>
                                                </tr>
                                                <tr>
                                                  <td style="padding-top:5px;padding-bottom:0;padding-right:0;padding-left:0" class="mceBlockContainer" align="center" valign="top"><img data-block-id="27" width="198" height="auto" style="width:198px;height:auto;max-width:100%;display:block" alt="" src="https://mcusercontent.com/6f591753d8bf77564eb1beb47/images/d21c0ab0-4af7-d156-a926-3b0d6af8d4e8.png" role="presentation" class="imageDropZone">
                                                    <br/>
                                                    <p class="last-child"><span style="color:#e40071;"><span style="text-decoration:underline;"><span style="font-size: 14px">{{#if(isAppOnlyJapanese data.language)}}https://www.gmobile.biz/esim{{else}}https://esim.gmobile.biz/esim{{/if}}</span></span></span></p>
                                                    <p class="last-child"><span style="color:#797979;"><span style="font-size: 14px">© Inbound Platform Corp. All Rights Reserved</span></span></p>
                                                    
                                                  </td>
                                                </tr>
                                                <tr>
                                                  <td style="padding-top:8px;padding-bottom:8px;padding-right:8px;padding-left:8px" class="mceLayoutContainer" valign="top">
                                                    <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" role="presentation" data-block-id="11" id="section_d36376523db498a7ea240913dee1bddd" class="mceFooterSection">
                                                      <tbody>
                                                        <tr class="mceRow">
                                                          <td style="background-position:center;background-repeat:no-repeat;background-size:cover" valign="top">
                                                            <table border="0" cellpadding="0" cellspacing="12" width="100%" role="presentation">
                                                              <tbody>
                                                                <tr>
                                                                  <td style="padding-top:0;padding-bottom:0;margin-bottom:12px" class="mceColumn" data-block-id="-3" valign="top" colspan="12" width="100%">
                                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%" role="presentation">
                                                                      <tbody>
                                                                        <tr>
                                                                          <td style="padding-top:12px;padding-bottom:12px;padding-right:16px;padding-left:16px" class="mceBlockContainer" align="center" valign="top">
                                                                            <div data-block-id="9" class="mceText" id="dataBlockId-9" style="display:inline-block;width:100%">
                                                                              <p class="last-child"><br></p>
                                                                            </div>
                                                                          </td>
                                                                        </tr>
                                                                        <tr>
                                                                          <td class="mceLayoutContainer" align="center" valign="top"></td>
                                                                        </tr>
                                                                      </tbody>
                                                                    </table>
                                                                  </td>
                                                                </tr>
                                                              </tbody>
                                                            </table>
                                                          </td>
                                                        </tr>
                                                      </tbody>
                                                    </table>
                                                  </td>
                                                </tr>
                                              </tbody>
                                            </table>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </center>
</body>

</html>