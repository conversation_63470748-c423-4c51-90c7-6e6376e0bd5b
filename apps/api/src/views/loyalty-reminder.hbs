<html >
   <plasmo-csui></plasmo-csui>
   <head>
      <meta property="og:title" content="Your eSIM order has been completed">
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
      <meta name="referrer" content="origin">
      <meta charset="UTF-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Your eSIM order has been completed</title>
      <style type="text/css">
         p{
         margin:10px 0;
         padding:0;
         }
         table{
         border-collapse:collapse;
         }
         h1,h2,h3,h4,h5,h6{
         display:block;
         margin:0;
         padding:0;
         }
         img,a img{
         border:0;
         height:auto;
         outline:none;
         text-decoration:none;
         }
         body,#bodyTable,#bodyCell{
         height:100%;
         margin:0;
         padding:0;
         width:100%;
         }
         .mcnPreviewText{
         display:none !important;
         }
         #outlook a{
         padding:0;
         }
         img{
         -ms-interpolation-mode:bicubic;
         }
         table{
         mso-table-lspace:0pt;
         mso-table-rspace:0pt;
         }
         .ReadMsgBody{
         width:100%;
         }
         .ExternalClass{
         width:100%;
         }
         p,a,li,td,blockquote{
         mso-line-height-rule:exactly;
         }
         a[href^=tel],a[href^=sms]{
         color:inherit;
         cursor:default;
         text-decoration:none;
         }
         p,a,li,td,body,table,blockquote{
         -ms-text-size-adjust:100%;
         -webkit-text-size-adjust:100%;
         }
         .ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
         line-height:100%;
         }
         a[x-apple-data-detectors]{
         color:inherit !important;
         text-decoration:none !important;
         font-size:inherit !important;
         font-family:inherit !important;
         font-weight:inherit !important;
         line-height:inherit !important;
         }
         .templateContainer{
         max-width:600px !important;
         }
         a.mcnButton{
         display:block;
         }
         .mcnImage,.mcnRetinaImage{
         vertical-align:bottom;
         }
         .mcnTextContent{
         word-break:break-word;
         }
         .mcnTextContent img{
         height:auto !important;
         }
         .mcnDividerBlock{
         table-layout:fixed !important;
         }
         h1{
         color:#222222;
         font-family:Helvetica;
         font-size:40px;
         font-style:normal;
         font-weight:bold;
         line-height:150%;
         letter-spacing:normal;
         text-align:center;
         }
         h2{
         color:#222222;
         font-family:Helvetica;
         font-size:34px;
         font-style:normal;
         font-weight:bold;
         line-height:150%;
         letter-spacing:normal;
         text-align:left;
         }
         h3{
         color:#444444;
         font-family:Helvetica;
         font-size:22px;
         font-style:normal;
         font-weight:bold;
         line-height:150%;
         letter-spacing:normal;
         text-align:left;
         }
         h4{
         color:#949494;
         font-family:Georgia;
         font-size:20px;
         font-style:italic;
         font-weight:normal;
         line-height:125%;
         letter-spacing:normal;
         text-align:left;
         }
         #templateHeader{
         background-color:#F7F7F7;
         background-image:none;
         background-repeat:no-repeat;
         background-position:center;
         background-size:cover;
         border-top:0;
         border-bottom:0;
         padding-top:0px;
         padding-bottom:0px;
         }
         .headerContainer{
         background-color:transparent;
         background-image:none;
         background-repeat:no-repeat;
         background-position:center;
         background-size:cover;
         border-top:0;
         border-bottom:0;
         padding-top:0;
         padding-bottom:0;
         }
         .headerContainer .mcnTextContent,.headerContainer .mcnTextContent p{
         color:#757575;
         font-family:Helvetica;
         font-size:16px;
         line-height:150%;
         text-align:left;
         }
         .headerContainer .mcnTextContent a,.headerContainer .mcnTextContent p a{
         color:#007C89;
         font-weight:normal;
         text-decoration:underline;
         }
         #templateBody{
         background-color:#ffffff;
         background-image:none;
         background-repeat:no-repeat;
         background-position:center;
         background-size:cover;
         border-top:0;
         border-bottom:0;
         padding-top:0px;
         padding-bottom:0px;
         }
         .bodyContainer{
         background-color:transparent;
         background-image:none;
         background-repeat:no-repeat;
         background-position:center;
         background-size:cover;
         border-top:0;
         border-bottom:0;
         padding-top:0;
         padding-bottom:0;
         }
         .bodyContainer .mcnTextContent,.bodyContainer .mcnTextContent p{
         color:#757575;
         font-family:Helvetica;
         font-size:16px;
         line-height:150%;
         text-align:left;
         }
         .bodyContainer .mcnTextContent a,.bodyContainer .mcnTextContent p a{
         color:#007C89;
         font-weight:normal;
         text-decoration:underline;
         }
         #templateFooter{
         background-color:#333333;
         background-image:none;
         background-repeat:no-repeat;
         background-position:center;
         background-size:cover;
         border-top:0;
         border-bottom:0;
         padding-top:20px;
         padding-bottom:20px;
         }
         .footerContainer{
         background-color:#transparent;
         background-image:none;
         background-repeat:no-repeat;
         background-position:center;
         background-size:cover;
         border-top:0;
         border-bottom:0;
         padding-top:0;
         padding-bottom:0;
         }
         .footerContainer .mcnTextContent,.footerContainer .mcnTextContent p{
         color:#FFFFFF;
         font-family:Helvetica;
         font-size:12px;
         line-height:150%;
         text-align:center;
         }
         .footerContainer .mcnTextContent a,.footerContainer .mcnTextContent p a{
         color:#FFFFFF;
         font-weight:normal;
         text-decoration:underline;
         }
         @media only screen and (min-width:768px){
         .templateContainer{
         width:600px !important;
         }
         }	@media only screen and (max-width: 480px){
         body,table,td,p,a,li,blockquote{
         -webkit-text-size-adjust:none !important;
         }
         }	@media only screen and (max-width: 480px){
         body{
         width:100% !important;
         min-width:100% !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnRetinaImage{
         max-width:100% !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnImage{
         width:100% !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnCartContainer,.mcnCaptionTopContent,.mcnRecContentContainer,.mcnCaptionBottomContent,.mcnTextContentContainer,.mcnBoxedTextContentContainer,.mcnImageGroupContentContainer,.mcnCaptionLeftTextContentContainer,.mcnCaptionRightTextContentContainer,.mcnCaptionLeftImageContentContainer,.mcnCaptionRightImageContentContainer,.mcnImageCardLeftTextContentContainer,.mcnImageCardRightTextContentContainer,.mcnImageCardLeftImageContentContainer,.mcnImageCardRightImageContentContainer{
         max-width:100% !important;
         width:100% !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnBoxedTextContentContainer{
         min-width:100% !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnImageGroupContent{
         padding:9px !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnCaptionLeftContentOuter .mcnTextContent,.mcnCaptionRightContentOuter .mcnTextContent{
         padding-top:9px !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnImageCardTopImageContent,.mcnCaptionBottomContent:last-child .mcnCaptionBottomImageContent,.mcnCaptionBlockInner .mcnCaptionTopContent:last-child .mcnTextContent{
         padding-top:18px !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnImageCardBottomImageContent{
         padding-bottom:9px !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnImageGroupBlockInner{
         padding-top:0 !important;
         padding-bottom:0 !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnImageGroupBlockOuter{
         padding-top:9px !important;
         padding-bottom:9px !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnTextContent,.mcnBoxedTextContentColumn{
         padding-right:18px !important;
         padding-left:18px !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnImageCardLeftImageContent,.mcnImageCardRightImageContent{
         padding-right:18px !important;
         padding-bottom:0 !important;
         padding-left:18px !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcpreview-image-uploader{
         display:none !important;
         width:100% !important;
         }
         }	@media only screen and (max-width: 480px){
         h1{
         font-size:30px !important;
         line-height:125% !important;
         }
         }	@media only screen and (max-width: 480px){
         h2{
         font-size:26px !important;
         line-height:125% !important;
         }
         }	@media only screen and (max-width: 480px){
         h3{
         font-size:20px !important;
         line-height:150% !important;
         }
         }	@media only screen and (max-width: 480px){
         h4{
         font-size:18px !important;
         line-height:150% !important;
         }
         }	@media only screen and (max-width: 480px){
         .mcnBoxedTextContentContainer .mcnTextContent,.mcnBoxedTextContentContainer .mcnTextContent p{
         font-size:14px !important;
         line-height:150% !important;
         }
         }	@media only screen and (max-width: 480px){
         .headerContainer .mcnTextContent,.headerContainer .mcnTextContent p{
         font-size:16px !important;
         line-height:150% !important;
         }
         }	@media only screen and (max-width: 480px){
         .bodyContainer .mcnTextContent,.bodyContainer .mcnTextContent p{
         font-size:16px !important;
         line-height:150% !important;
         }
         }	@media only screen and (max-width: 480px){
         .footerContainer .mcnTextContent,.footerContainer .mcnTextContent p{
         font-size:14px !important;
         line-height:150% !important;
         }
         }
      </style>
      <link rel="stylesheet" href="https://us8.campaign-archive.com/css/archivebar-desktop.css" mc:nocompile="">
   </head>
   <body id="archivebody" style="height: 100%;margin: 0;padding: 0;width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
      <!---->
      <center>
         <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
            <tbody>
               <tr>
                  <td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
                     <!-- BEGIN TEMPLATE // -->
                     <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                        <tbody>
                           <tr>
                              <td align="center" valign="top" id="templateHeader" data-template-container="" style="background:#F7F7F7 none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #F7F7F7;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0px;padding-bottom: 0px;">
                                          <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
                                             <tbody>
                                                <tr>
                                                   <td valign="top" class="headerContainer" style="background:transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;"></td>
                                                </tr>
                                             </tbody>
                                          </table>
                              </td>
                           </tr>
                           <tr>
                              <td align="center" valign="top" id="templateBody" data-template-container="" style="background:#ffffff none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #ffffff;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0px;padding-bottom: 0px;">
                                          <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
                                             <tbody>
                                                <tr>
                                                   <td valign="top" class="bodyContainer" style="background:transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;">
                                                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnImageBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                         <tbody class="mcnImageBlockOuter">
                                                            <tr>
                                                               <td valign="top" style="padding: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnImageBlockInner">
                                                                  <table align="left" width="100%" border="0" cellpadding="0" cellspacing="0" class="mcnImageContentContainer" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                     <tbody>
                                                                        <tr>
                                                                          <td class="mcnImageContent"
																						            valign="top"
																						            style="padding-right: 9px;padding-left: 9px;padding-top: 0;padding-bottom: 0;text-align: center;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                              {{#if (isAirTrip data.userSource)}}
                                                                              <img align="center" alt=""
                                                                                    src="https://cdn.gmobile.biz/global-esim/assets/airtrip/logo.png"
                                                                                    width="211.64"
                                                                                    style="max-width: 286px;padding-bottom: 0;display: inline !important;vertical-align: bottom;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;"
                                                                                    class="mcnImage">
                                                                              {{else if (isAppOnlyJapanese data.language)}}
                                                                              <img align="center" alt=""
                                                                                    src="https://cdn.japan-wireless.com/global-esim/assets/gm-logo-jp.png"
                                                                                    width="211.64"
                                                                                    style="max-width: 286px;padding-bottom: 0;display: inline !important;vertical-align: bottom;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;"
                                                                                    class="mcnImage">
                                                                              {{else}}
                                                                              <img align="center" alt=""
                                                                                    src="https://cdn.japan-wireless.com/global-esim/assets/logo.png"
                                                                                    width="211.64"
                                                                                    style="max-width: 286px;padding-bottom: 0;display: inline !important;vertical-align: bottom;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;"
                                                                                    class="mcnImage">
                                                                              {{/if}}
                                                                        </tr>
                                                                     </tbody>
                                                                  </table>
                                                               </td>
                                                            </tr>
                                                         </tbody>
                                                      </table>
                                                     
                                                  
                                                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
                                                         <tbody class="mcnDividerBlockOuter">
                                                            <tr>
                                                               <td class="mcnDividerBlockInner" style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                  <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-top: 1px solid #EAEAEA;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                     <tbody>
                                                                        <tr>
                                                                           <td style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                              <span></span>
                                                                           </td>
                                                                        </tr>
                                                                     </tbody>
                                                                  </table>
                                                               </td>
                                                            </tr>
                                                         </tbody>
                                                      </table>
                                                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                         <tbody class="mcnTextBlockOuter">
                                                            <tr>
                                                               <td valign="top" class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                  <p style="margin: 10px 0 0 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: center;">
                                                                     {{{ data.renderedThankyouTitle }}}
                                                                 </p>
                                                                  <p style="margin: 0 0 20px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: center;">
                                                                     {{!-- {{t "loyalty-reminder.appreciation-message" couponDiscount=data.couponDiscount}} --}}
                                                                       {{{ data.renderedAppreciation }}}
                                                                 </p>
                                                                  <p style="margin: 10px 0 0 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: center;font-weight:bold;">
                                                                     {{!-- {{t "loyalty-reminder.coupon-code" couponCode=data.couponCode}} --}}
                                                                       {{{ data.renderedCouponCode }}}
                                                                  </p>
                                                                  <p style="margin: 0 0 20px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: center;font-weight: bold;">
                                                                     {{t "loyalty-reminder.one-time-notice"}}
                                                                  </p>
                                                                  <p style="margin: 10px 0 20px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: center;">
                                                                     {{{ data.renderedClosingMessage }}}
                                                                  </p>
                                                                  <p style="margin: 10px 0 0 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: center;">
                                                                     {{t "loyalty-reminder.sign-off"}}
                                                                  </p>
                                                                  <p style="margin: 0 0 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: center;">
                                                                     {{{ data.renderedTeamName }}}
                                                                  </p>
                                                               </td>
                                                            </tr>
                                                         </tbody>
                                                      </table>
                                                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
                                                         <tbody class="mcnDividerBlockOuter">
                                                            <tr>
                                                               <td class="mcnDividerBlockInner" style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                  <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                     <tbody>
                                                                        <tr>
                                                                           <td style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                              <span></span>
                                                                           </td>
                                                                        </tr>
                                                                     </tbody>
                                                                  </table>
                                                               </td>
                                                            </tr>
                                                         </tbody>
                                                      </table>
                                                   </td>
                                                </tr>
                                             </tbody>
                                          </table>
                                       </td>
                                    </tr>
                                 </table>
                                 <![endif]-->
                              </td>
                           </tr>
                           <tr>
                              <td align="center" valign="top" id="templateFooter" data-template-container="" style="background:#333333 none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #333333;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 20px;padding-bottom: 20px;">
                                 <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                                    <tr>
                                       <td align="center" valign="top" width="600" style="width:600px;">
                                          <![endif]-->
                                          <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
                                             <tbody>
                                                <tr>
                                                   <td valign="top" class="footerContainer" style="background:#transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;">
                                                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                         <tbody class="mcnTextBlockOuter">
                                                            <tr>
                                                               <td valign="top" class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                  <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                                                                     <tr>
                                                                        <![endif]-->
                                                                        <td valign="top" width="600" style="width:600px;">
                                                                           <![endif]-->
                                                                           <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" class="mcnTextContentContainer">
                                                                              <tbody>
                                                                              <tr>
                                                                                    <td valign="top" class="mcnTextContent" style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #FFFFFF;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">
                                                                                       <p style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #FFFFFF;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;"><span style="font-size:14px">
                                                                                          {{ t "order-email.esim-issue-contact" }} <a href="mailto:<EMAIL>" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #FFFFFF;font-weight: normal;text-decoration: underline;"><EMAIL></a>.</span>
                                                                                       </p>
                                                                                    </td>
                                                                                 </tr>
                                                                              </tbody>
                                                                           </table>
                                                                        </td>
                                                                        <![endif]-->
                                                                     </tr>
                                                                  </table>
                                                                  <![endif]-->
                                                               </td>
                                                            </tr>
                                                         </tbody>
                                                      </table>
                                                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
                                                         <tbody class="mcnDividerBlockOuter">
                                                            <tr>
                                                               <td class="mcnDividerBlockInner" style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                  <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-top: 1px solid #7D7D7D;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                     <tbody>
                                                                        <tr>
                                                                           <td style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                              <span></span>
                                                                           </td>
                                                                        </tr>
                                                                     </tbody>
                                                                  </table>
                                                               </td>
                                                            </tr>
                                                         </tbody>
                                                      </table>
                                                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                         <tbody class="mcnTextBlockOuter">
                                                            <tr>
                                                               <td valign="top" class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                                                           <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" class="mcnTextContentContainer">
                                                                              <tbody>
                                                                                 <tr>
                                                                                    <td valign="top" class="mcnTextContent" style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #FFFFFF;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">
                                                                                       ©<em> Inbound Platform Corp, All rights reserved.</em>
                                                                                    </td>
                                                                                 </tr>
                                                                              </tbody>
                                                                           </table>
                                                               </td>
                                                            </tr>
                                                         </tbody>
                                                      </table>
                                                   </td>
                                                </tr>
                                             </tbody>
                                          </table>
                              </td>
                           </tr>
                        </tbody>
                     </table>
                     <!-- // END TEMPLATE -->
                  </td>
               </tr>
            </tbody>
         </table>
      </center>
      <div class="ai-modal-container" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;Noto Sans&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;;"></div>
   </body>
</html>