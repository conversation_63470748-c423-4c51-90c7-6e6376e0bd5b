<html><plasmo-csui></plasmo-csui>

<head>

	<meta property="og:title" content="Your eSIM order has been completed">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="referrer" content="origin">
	<!-- NAME: SELL PRODUCTS -->
	<!--[if gte mso 15]>
        <xml>
            <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Your eSIM order has been completed</title>

	<style type="text/css">
		p {
			margin: 10px 0;
			padding: 0;
		}

		table {
			border-collapse: collapse;
		}

		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			display: block;
			margin: 0;
			padding: 0;
		}

		img,
		a img {
			border: 0;
			height: auto;
			outline: none;
			text-decoration: none;
		}

		body,
		#bodyTable,
		#bodyCell {
			height: 100%;
			margin: 0;
			padding: 0;
			width: 100%;
		}

		.mcnPreviewText {
			display: none !important;
		}

		#outlook a {
			padding: 0;
		}

		img {
			-ms-interpolation-mode: bicubic;
		}

		table {
			mso-table-lspace: 0pt;
			mso-table-rspace: 0pt;
		}

		.ReadMsgBody {
			width: 100%;
		}

		.ExternalClass {
			width: 100%;
		}

		p,
		a,
		li,
		td,
		blockquote {
			mso-line-height-rule: exactly;
		}

		a[href^=tel],
		a[href^=sms] {
			color: inherit;
			cursor: default;
			text-decoration: none;
		}

		p,
		a,
		li,
		td,
		body,
		table,
		blockquote {
			-ms-text-size-adjust: 100%;
			-webkit-text-size-adjust: 100%;
		}

		.ExternalClass,
		.ExternalClass p,
		.ExternalClass td,
		.ExternalClass div,
		.ExternalClass span,
		.ExternalClass font {
			line-height: 100%;
		}

		a[x-apple-data-detectors] {
			color: inherit !important;
			text-decoration: none !important;
			font-size: inherit !important;
			font-family: inherit !important;
			font-weight: inherit !important;
			line-height: inherit !important;
		}

		.templateContainer {
			max-width: 600px !important;
		}

		a.mcnButton {
			display: block;
		}

		.mcnImage,
		.mcnRetinaImage {
			vertical-align: bottom;
		}

		.mcnTextContent {
			word-break: break-word;
		}

		.mcnTextContent img {
			height: auto !important;
		}

		.mcnDividerBlock {
			table-layout: fixed !important;
		}

		h1 {
			color: #222222;
			font-family: Helvetica;
			font-size: 40px;
			font-style: normal;
			font-weight: bold;
			line-height: 150%;
			letter-spacing: normal;
			text-align: center;
		}

		h2 {
			color: #222222;
			font-family: Helvetica;
			font-size: 34px;
			font-style: normal;
			font-weight: bold;
			line-height: 150%;
			letter-spacing: normal;
			text-align: left;
		}

		h3 {
			color: #444444;
			font-family: Helvetica;
			font-size: 22px;
			font-style: normal;
			font-weight: bold;
			line-height: 150%;
			letter-spacing: normal;
			text-align: left;
		}

		h4 {
			color: #949494;
			font-family: Georgia;
			font-size: 20px;
			font-style: italic;
			font-weight: normal;
			line-height: 125%;
			letter-spacing: normal;
			text-align: left;
		}

		#templateHeader {
			background-color: #F7F7F7;
			background-image: none;
			background-repeat: no-repeat;
			background-position: center;
			background-size: cover;
			border-top: 0;
			border-bottom: 0;
			padding-top: 0px;
			padding-bottom: 0px;
		}

		.headerContainer {
			background-color: transparent;
			background-image: none;
			background-repeat: no-repeat;
			background-position: center;
			background-size: cover;
			border-top: 0;
			border-bottom: 0;
			padding-top: 0;
			padding-bottom: 0;
		}

		.headerContainer .mcnTextContent,
		.headerContainer .mcnTextContent p {
			color: #757575;
			font-family: Helvetica;
			font-size: 16px;
			line-height: 150%;
			text-align: left;
		}

		.headerContainer .mcnTextContent a,
		.headerContainer .mcnTextContent p a {
			color: #007C89;
			font-weight: normal;
			text-decoration: underline;
		}

		#templateBody {
			background-color: #ffffff;
			background-image: none;
			background-repeat: no-repeat;
			background-position: center;
			background-size: cover;
			border-top: 0;
			border-bottom: 0;
			padding-top: 0px;
			padding-bottom: 0px;
		}

		.bodyContainer {
			background-color: transparent;
			background-image: none;
			background-repeat: no-repeat;
			background-position: center;
			background-size: cover;
			border-top: 0;
			border-bottom: 0;
			padding-top: 0;
			padding-bottom: 0;
		}

		.bodyContainer .mcnTextContent,
		.bodyContainer .mcnTextContent p {
			color: #757575;
			font-family: Helvetica;
			font-size: 16px;
			line-height: 150%;
			text-align: left;
		}

		.bodyContainer .mcnTextContent a,
		.bodyContainer .mcnTextContent p a {
			color: #007C89;
			font-weight: normal;
			text-decoration: underline;
		}

		#templateFooter {
			background-color: #333333;
			background-image: none;
			background-repeat: no-repeat;
			background-position: center;
			background-size: cover;
			border-top: 0;
			border-bottom: 0;
			padding-top: 20px;
			padding-bottom: 20px;
		}

		.footerContainer {
			background-color: #transparent;
			background-image: none;
			background-repeat: no-repeat;
			background-position: center;
			background-size: cover;
			border-top: 0;
			border-bottom: 0;
			padding-top: 0;
			padding-bottom: 0;
		}

		.footerContainer .mcnTextContent,
		.footerContainer .mcnTextContent p {
			color: #FFFFFF;
			font-family: Helvetica;
			font-size: 12px;
			line-height: 150%;
			text-align: center;
		}

		.footerContainer .mcnTextContent a,
		.footerContainer .mcnTextContent p a {
			color: #FFFFFF;
			font-weight: normal;
			text-decoration: underline;
		}

		@media only screen and (min-width:768px) {
			.templateContainer {
				width: 600px !important;
			}

		}

		@media only screen and (max-width: 480px) {

			body,
			table,
			td,
			p,
			a,
			li,
			blockquote {
				-webkit-text-size-adjust: none !important;
			}

		}

		@media only screen and (max-width: 480px) {
			body {
				width: 100% !important;
				min-width: 100% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcnRetinaImage {
				max-width: 100% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcnImage {
				width: 100% !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.mcnCartContainer,
			.mcnCaptionTopContent,
			.mcnRecContentContainer,
			.mcnCaptionBottomContent,
			.mcnTextContentContainer,
			.mcnBoxedTextContentContainer,
			.mcnImageGroupContentContainer,
			.mcnCaptionLeftTextContentContainer,
			.mcnCaptionRightTextContentContainer,
			.mcnCaptionLeftImageContentContainer,
			.mcnCaptionRightImageContentContainer,
			.mcnImageCardLeftTextContentContainer,
			.mcnImageCardRightTextContentContainer,
			.mcnImageCardLeftImageContentContainer,
			.mcnImageCardRightImageContentContainer {
				max-width: 100% !important;
				width: 100% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcnBoxedTextContentContainer {
				min-width: 100% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcnImageGroupContent {
				padding: 9px !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.mcnCaptionLeftContentOuter .mcnTextContent,
			.mcnCaptionRightContentOuter .mcnTextContent {
				padding-top: 9px !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.mcnImageCardTopImageContent,
			.mcnCaptionBottomContent:last-child .mcnCaptionBottomImageContent,
			.mcnCaptionBlockInner .mcnCaptionTopContent:last-child .mcnTextContent {
				padding-top: 18px !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcnImageCardBottomImageContent {
				padding-bottom: 9px !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcnImageGroupBlockInner {
				padding-top: 0 !important;
				padding-bottom: 0 !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcnImageGroupBlockOuter {
				padding-top: 9px !important;
				padding-bottom: 9px !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.mcnTextContent,
			.mcnBoxedTextContentColumn {
				padding-right: 18px !important;
				padding-left: 18px !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.mcnImageCardLeftImageContent,
			.mcnImageCardRightImageContent {
				padding-right: 18px !important;
				padding-bottom: 0 !important;
				padding-left: 18px !important;
			}

		}

		@media only screen and (max-width: 480px) {
			.mcpreview-image-uploader {
				display: none !important;
				width: 100% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			h1 {
				font-size: 30px !important;
				line-height: 125% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			h2 {
				font-size: 26px !important;
				line-height: 125% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			h3 {
				font-size: 20px !important;
				line-height: 150% !important;
			}

		}

		@media only screen and (max-width: 480px) {
			h4 {
				font-size: 18px !important;
				line-height: 150% !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.mcnBoxedTextContentContainer .mcnTextContent,
			.mcnBoxedTextContentContainer .mcnTextContent p {
				font-size: 14px !important;
				line-height: 150% !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.headerContainer .mcnTextContent,
			.headerContainer .mcnTextContent p {
				font-size: 16px !important;
				line-height: 150% !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.bodyContainer .mcnTextContent,
			.bodyContainer .mcnTextContent p {
				font-size: 16px !important;
				line-height: 150% !important;
			}

		}

		@media only screen and (max-width: 480px) {

			.footerContainer .mcnTextContent,
			.footerContainer .mcnTextContent p {
				font-size: 14px !important;
				line-height: 150% !important;
			}

		}
	</style>
	<link rel="stylesheet" href="https://us8.campaign-archive.com/css/archivebar-desktop.css" mc:nocompile="">

</head>

<body id="archivebody"
	style="height: 100%;margin: 0;padding: 0;width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
	<!---->
	<center>
		<table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable"
			style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
			<tbody>
				<tr>
					<td align="center" valign="top" id="bodyCell"
						style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
						<!-- BEGIN TEMPLATE // -->
						<table border="0" cellpadding="0" cellspacing="0" width="100%"
							style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
							<tbody>
								<tr>
									<td align="center" valign="top" id="templateHeader" data-template-container=""
										style="background:#F7F7F7 none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #F7F7F7;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0px;padding-bottom: 0px;">
										<!--[if (gte mso 9)|(IE)]>
                                    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                                    <tr>
                                    <td align="center" valign="top" width="600" style="width:600px;">
                                    <![endif]-->
										<table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"
											class="templateContainer"
											style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
											<tbody>
												<tr>
													<td valign="top" class="headerContainer"
														style="background:transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;">
													</td>
												</tr>
											</tbody>
										</table>
										<!--[if (gte mso 9)|(IE)]>
                                    </td>
                                    </tr>
                                    </table>
                                    <![endif]-->
									</td>
								</tr>
								<tr>
									<td align="center" valign="top" id="templateBody" data-template-container=""
										style="background:#ffffff none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #ffffff;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0px;padding-bottom: 0px;">
										<!--[if (gte mso 9)|(IE)]>
                                    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                                    <tr>
                                    <td align="center" valign="top" width="600" style="width:600px;">
                                    <![endif]-->
										<table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"
											class="templateContainer"
											style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
											<tbody>
												<tr>
													<td valign="top" class="bodyContainer"
														style="background:transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;">
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnImageBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnImageBlockOuter">
																<tr>
																	<td valign="top"
																		style="padding: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
																		class="mcnImageBlockInner">
																		<table align="left" width="100%" border="0"
																			cellpadding="0" cellspacing="0"
																			class="mcnImageContentContainer"
																			style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																			<tbody>
																				<tr>
																					<td class="mcnImageContent"
																						valign="top"
																						style="padding-right: 9px;padding-left: 9px;padding-top: 0;padding-bottom: 0;text-align: center;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																						{{#if (isAirTrip data.userSource)}}
																						<img align="center" alt=""
																							src="https://cdn.gmobile.biz/global-esim/assets/airtrip/logo.png"
																							width="211.64"
																							style="max-width: 286px;padding-bottom: 0;display: inline !important;vertical-align: bottom;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;"
																							class="mcnImage">
																						{{else if (isAppOnlyJapanese data.language)}}
																						<img align="center" alt=""
																							src="https://cdn.japan-wireless.com/global-esim/assets/gm-logo-jp.png"
																							width="211.64"
																							style="max-width: 286px;padding-bottom: 0;display: inline !important;vertical-align: bottom;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;"
																							class="mcnImage">
																						{{else}}
																						<img align="center" alt=""
																							src="https://cdn.japan-wireless.com/global-esim/assets/logo.png"
																							width="211.64"
																							style="max-width: 286px;padding-bottom: 0;display: inline !important;vertical-align: bottom;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;"
																							class="mcnImage">
																						{{/if}}
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
														{{!-- start --}}

														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; padding-top:12px;padding-bottom:12px;padding-right:24px;padding-left:100px">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner"
																		style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<!--[if mso]><table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;"><tr><![endif]--><!--[if mso]><td valign="top" width="600" style="width:600px;"><![endif]-->
																		<table align="left" border="0" cellpadding="0"
																			cellspacing="0"
																			style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
																			width="100%"
																			class="mcnTextContentContainer">
																			<tbody>
																				<tr>
																					<td valign="top"
																						class="mcnTextContent"
																						style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;padding-left: 100px;">
																						<h3
																							style="display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							{{t
																							"order-email.thankyou-purchase"}}
																						</h3>
																						{{#if (isAirTrip data.userSource)}}
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.thankyou-purchase-description-airtrip" data }}
																						</p>
																						{{else}}
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.thankyou-purchase-description" data }}
																						</p>
																						{{/if}}
																					</td>
																				</tr>
																			</tbody>
																		</table>
																		<!--[if mso]></td><![endif]--><!--[if mso]></tr></table><![endif]-->
																	</td>
																</tr>
															</tbody>
														</table>
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner"
																		style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<!--[if mso]><table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;"><tr><![endif]--><!--[if mso]><td valign="top" width="600" style="width:600px;"><![endif]-->
																		<table align="left" border="0" cellpadding="0"
																			cellspacing="0"
																			style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
																			width="100%"
																			class="mcnTextContentContainer">
																			<tbody>
																				<tr>
																					<td valign="top"
																						class="mcnTextContent"
																						style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 100px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						<h3
																							style="display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							<span
																								style="border-left:4px solid #333; font-size:18px; padding-left:7px">{{t
																								"order-email.order-details"}}</span>
																						</h3>
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.esim-plan"}}: {{data.esimPlan}}
																						</p>
																						{{#if	data.isFixedPlan}}
																					  <p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.data-usage"}}: {{data.dataPlan}} 
																						</p> 
																						{{/if}}
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.usage-days"}}: {{t
																							"order-email.usageDayCount"
																							data.usageDayCount}}</p>
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.price-incl-tax"}}: {{data.formattedPrice}}
																						</p>
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.order-id"}}: {{data.orderId}}
																						</p>
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							ICCID: {{data.iccid}}
																						</p>
																						{{#if data.isLGU}}
																							{{#unless  (isAirTripOrJapanese data.userSource data.language)}}
																								<p style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																								Phone number: {{data.topupId}} *Granted after arrival in Korea
																								</p>
																							{{else}}
																								<p style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																									電話番号: {{data.topupId}} ※韓国到着後に付与
																								</p>
																							{{/unless}}
																						{{/if}}
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.country"}}/{{t
																							"order-email.region"}}: {{  data.countryName}}
																						</p>
                                            <p
																						  style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t "order-email.expiration-date.title"}}
																						</p>
                                             <p
																						  style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
                                              {{t "order-email.expiration-date.info"}}
																						</p>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																		<!--[if mso]></td><![endif]--><!--[if mso]></tr></table><![endif]-->
																	</td>
																</tr>
															</tbody>
														</table>
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnDividerBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
															<tbody class="mcnDividerBlockOuter">
																<tr>
																	<td class="mcnDividerBlockInner"
																		style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<table class="mcnDividerContent" border="0"
																			cellpadding="0" cellspacing="0" width="100%"
																			style="min-width: 100%;border-top: 1px solid #EAEAEA;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																			<tbody>
																				<tr>
																					<td
																						style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																						<span></span>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
														
													{{#if data.isLGU}}
														{{#if (isAirTripOrJapanese data.userSource data.language)}}
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" class="mcnTextContentContainer">
																			<tbody>
																				<tr>
																					<td valign="top" class="mcnTextContent" style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 100px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						<h3 style="display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							<span style="border-left:4px solid #333; font-size:18px; padding-left:7px">お得なキャンペーン情報、案内中！</span>
																						</h3>
																						<h4 style="color: #444444; font-family: Helvetica; font-size: 18px; font-weight: bold; margin-bottom: 10px; margin-top: 10px;">▼Kコスメクーポン</h4>
																						<a href="https://image.rakuten.co.jp/globalmobile/cabinet/ktsim3/10623807/11794183/imgrc0171644135.jpg" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																							<img src="https://image.rakuten.co.jp/globalmobile/cabinet/ktsim3/10623807/11794183/imgrc0171644183.jpg" alt="K-Cosme Campaign" style="width: 100%; max-width: 564px; border-radius: 8px; height: auto; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; display: block;" width="564">
																						</a>
																						<p style="margin-top: 10px; margin-bottom: 5px; padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							下記、引換用バウチャーをご提示くださいませ。
																						</p>
																						{{#if (isAirTrip data.userSource)}}
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							<a href="https://image.rakuten.co.jp/cvrmnt/cabinet/11613424/11793081/imgrc0095975525.jpg" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">クーポンクーポン引換用バウチャーはこちら</a>
																						</p>
																						{{else}}
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							<a href="https://image.rakuten.co.jp/globalmobile/cabinet/ktsim3/10623807/11794183/imgrc0171644182.jpg" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">クーポンクーポン引換用バウチャーはこちら</a>
																						</p>
																						{{/if}}
																						
																						<h4 style="color: #444444; font-family: Helvetica; font-size: 18px;font-style: normal; font-weight: bold; margin-bottom: 10px; margin-top: 20px;">▼K-POP限定ステッカー</h4>
																						<p style="margin-top: 0; margin-bottom: 5px; padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							下記、引換用バウチャーをご提示くださいませ。
																						</p>
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							<a href="https://image.rakuten.co.jp/cvrmnt/cabinet/11613424/11793081/imgrc0095827543.jpg" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">ステッカー引き換え用バウチャーはこちら</a>
																						</p>

																						<h4 style="color: #444444; font-family: Helvetica; font-size: 18px;font-style: normal; font-weight: bold; margin-bottom: 10px; margin-top: 20px;">▼Tmoney贈呈キャンペーン！</h4>
																						<p style="margin-bottom: 5px; padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							本メールをカウンターにご提示くださいませ。
																						</p>
																						<p style="margin:0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							<a href="https://image.rakuten.co.jp/globalmobile/cabinet/ktsim3/10623807/imgrc0167955281.jpg" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">キャンペーン案内はこちら</a>
																						</p>

																						<h4 style="color: #444444; font-family: Helvetica; font-size: 18px; font-weight: bold;font-style: normal; margin-bottom: 10px; margin-top: 20px;">▼交通系もお得に利用！</h4>
																						<p style="margin-top: 0; margin-bottom: 0; padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							下記公式サイトより詳細をご確認いただけます。
																						</p>
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 200%;text-align: left;">
																							<a href="https://www.airportrailroad.com/rt/mrktnDisc.do?param1=jV%2Fc3SCt3Dc6teMmbUH6wPXnB2tB7m%2FlYgjWJC9E%2FGI%3D" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline; margin: 10px 0px;">【A'REX】空港鉄道 直通列車割引の詳細はこちら</a><br>
																							<a href="https://lguklimousineen.theloungemembers.com/sale/?lang=jp" target="_blank" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;margin: 10px 0px;">仁川空港リムジンバス割引の詳細はこちら</a>
																						</p>

																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>

														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnDividerBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
															<tbody class="mcnDividerBlockOuter">
																<tr>
																	<td class="mcnDividerBlockInner"
																		style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<table class="mcnDividerContent" border="0"
																			cellpadding="0" cellspacing="0" width="100%"
																			style="min-width: 100%;border-top: 1px solid #EAEAEA;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																			<tbody>
																				<tr>
																					<td
																						style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																						<span></span>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
														{{/if}}
													{{/if}}

														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner"
																		style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<!--[if mso]><table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;"><tr><![endif]--><!--[if mso]><td valign="top" width="600" style="width:600px;"><![endif]-->
																		<table align="left" border="0" cellpadding="0"
																			cellspacing="0"
																			style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
																			width="100%"
																			class="mcnTextContentContainer">
																			<tbody>
																				<tr>
																					<td valign="top"
																						class="mcnTextContent"
																						style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 100px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						<h3
																							style="display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							<span
																								style="border-left:4px solid #333; font-size:18px; padding-left:7px">{{t
																								'order-email.activate-your-esim'}}</span>
																						</h3>
																						{{#if data.isAsiaOrWorld}}
																						<p style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							<span
																							style="-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;font-weight: bold;">{{{t "activation.info_japan_multiplecountries"}}}</span><br />
																							{{{t "activation.info_taiwan_hongkong"}}}
																							<br />
																							<br />
																						</p>
																						
																						{{/if}}

																						{{#if data.isNeedCmlink }}
																						
																						<p style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
                                            		<span
																								style="-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;font-weight: bold;">{{t
																								"order-email.hongkong-taiwan-title"}}</span><br />
																							{{t
																							"order-email.hongkong-taiwan-note"}}<br />
                                            <span
																								style="-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;font-weight: normal;">{{t
																								"order-email.hongkong-taiwan-warning"}}</span><br /><br />
																			{{#if (isAirTripOrJapanese data.userSource data.language)}}
																						•<a href="https://cdn.gmobile.biz/documents/esim/202505/identity_verification_guide_hongkong_taiwan.pdf"
																								rel="”noopener”"
																								target="”_blank”"
																								style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								{{t "order-email.hongkong-taiwan-registration"}}
																							</a><br />
																			{{/if}}

                                          • <a href="https://global.cmlink.com/store/realname?LT=en"
																								rel="”noopener”"
																								target="”_blank”"
																								style="mso-line-height-rule: exactly;margin:5px 0 0 0;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">{{t "order-email.hongkong-taiwan-guide"}}
																							</a>

																						</p>&nbsp;{{/if}}
																						
																					
                                            	<p
																							style="margin: 0;padding: 1em 0;padding-bottom: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
                                            <span
																								style="-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;font-weight: bold;color: #444444;">
                                              ※{{t 'order-email.check-in-advance'}}※</span></p>
                                             	<p
																							style="margin: 0;padding: 0em 1em;padding-bottom: 1rem;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
												{{!-- <p style="margin: 0;padding: 0em 1em;padding-bottom: 1rem;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">isLGU:{{data.isLGU}}, userSource: {{data.userSource}}, dataLang: {{data.language}}</p> --}}
                                             {{#if data.isLGU}}
											 	{{#unless (isGMEnglishOrEnglishLang data.userSource data.language)}}
													-<a href="https://cdn.gmobile.biz/documents/esim/202505/gm_esim_setup_guide_voice_plan.pdf"
																								rel="”noopener”"
																								target="”_blank”"
																								style="padding-bottom: 10px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								eSIM設定方法
																							</a><br />
													{{else}}
												-<a href="https://esim.gmobile.biz/setup?os=ios"
																								rel="”noopener”"
																								target="”_blank”"
																								style="padding-bottom: 10px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								iOS Setup guide
																							</a><br />
                                             	-<a href="https://esim.gmobile.biz/setup?os=android"
																								rel="”noopener”"
																								target="”_blank”"
																								style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								Android Setup guide
																							</a>
												{{/unless}}
                                             {{else if (isAirTripOrJapanese data.userSource data.language)}}
                                                	-<a href="https://cdn.gmobile.biz/documents/esim/202505/urocomm_data_only_simple_manual_iphone.pdf"
																								rel="”noopener”"
																								target="”_blank”"
																								style="padding-bottom: 10px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								iOS設定方法
																							</a><br />
                                             	-<a href="https://cdn.gmobile.biz/documents/esim/202505/urocomm_data_only_simple_manual_android.pdf"
																								rel="”noopener”"
																								target="”_blank”"
																								style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								Android設定方法
																							</a>
                                             {{else}}
                                                  	-<a href="https://esim.gmobile.biz/setup?os=ios"
																								rel="”noopener”"
																								target="”_blank”"
																								style="padding-bottom: 10px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								iOS Setup guide
																							</a><br />
                                             	-<a href="https://esim.gmobile.biz/setup?os=android"
																								rel="”noopener”"
																								target="”_blank”"
																								style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																								Android Setup guide
																							</a>
                                             {{/if}}
                                            
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							<span style="color:#333333">
																								<strong>{{t
																									'order-email.method-1-qr-code-setup'}}</strong></span>
																						</p>
																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							{{t
																							"order-email.scan-from-device"}}<br />
																							<a href="{{data.qrCode}}"
																								rel="”noopener”"
																								target="”_blank”"
																								style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">{{t
																								"order-email.qr-code-link-message"}}

																							</a>
																						</p>&nbsp;
                                            <p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							<span
																								style="color:#333333"><strong>{{t
																									"order-email.method-2-manual-setup"}}</strong>
																							</span>
																						</p>
                                              <p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 14px;line-height: 150%;text-align: left;">
																							<span>
                                                {{t
																									"order-email.method-2-manual-check-code"}}
																							</span>
																						</p>
																					<table style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; border: 1px solid #000;">
                                            <tr>
    <th style="border: 1px solid #000; background-color: #000; color: #fff; padding: 8px;">iOS</th>
    <th style="border: 1px solid #000; padding: 8px;background-color: #000;">&nbsp;</th>
  </tr>
  <tr>
    <td style="border: 1px solid #000; padding: 8px;">{{t "activation.smdp.address"}}</td>
    <td style="border: 1px solid #000; padding: 8px;">{{data.smdp}}</td>
  </tr>
  <tr>
    <td style="border: 1px solid #000; padding: 8px;">{{t
																							"activation.activation-code.txt"}}</td>
    <td style="border: 1px solid #000; padding: 8px;">{{data.activateCode}}</td>
  </tr>
  <tr>
    <th style="border: 1px solid #000; background-color: #000; color: #fff; padding: 8px;">AndroidOS</th>
    <th style="border: 1px solid #000; padding: 8px;background-color: #000;">&nbsp;</th>
  </tr>
  <tr>
    <td style="border: 1px solid #000; padding: 8px;">{{t "activation.downloadlink"}}</td>
    <td style="border: 1px solid #000; padding: 8px;">{{data.downloadLink}}</td>
  </tr>
	<tr>
		<th style="border: 1px solid #000; background-color: #000; color: #fff; padding: 8px;">{{t "activation.apn_info.title"}}</th>
		<th style="border: 1px solid #000; padding: 8px;background-color: #000;">&nbsp;</th>
	</tr>
	<tr>
		<td style="border: 1px solid #000; padding: 8px;">APN</td>
		<td style="border: 1px solid #000; padding: 8px;">
			{{#if data.isLGU}}
				internet.lguplus.co.kr
			{{else}}
				{{data.apn}}
			{{/if}}
		</td>
	</tr>
	{{#if data.isLGU}}
		<tr>
			<td style="border: 1px solid #000; padding: 8px;">{{t "activation.apn_info.user"}}</td>
			<td style="border: 1px solid #000; padding: 8px;">
				internet {{t "activation.apn_info.user_hint"}}
			</td>
		</tr>
	{{/if}}
</table>

																					</td>
																				</tr>
																			</tbody>
																		</table>
																		<!--[if mso]></td><![endif]--><!--[if mso]></tr></table><![endif]-->
																	</td>
																</tr>
															</tbody>
														</table>
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnDividerBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
															<tbody class="mcnDividerBlockOuter">
																<tr>
																	<td class="mcnDividerBlockInner"
																		style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<table class="mcnDividerContent" border="0"
																			cellpadding="0" cellspacing="0" width="100%"
																			style="min-width: 100%;border-top: 1px solid #EAEAEA;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																			<tbody>
																				<tr>
																					<td
																						style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																						<span></span>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner"
																		style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<!--[if mso]><table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;"><tr><![endif]--><!--[if mso]><td valign="top" width="600" style="width:600px;"><![endif]-->
																	
																		<!--[if mso]></td><![endif]--><!--[if mso]></tr></table><![endif]-->
																	</td>
																</tr>
															</tbody>
														</table>
													{{#unless data.isLGU}}
														<!-- Until the translation sheet is available -->
														{{#if (isGMEnglishOrEnglishLang data.userSource data.language)}}
														<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;">
														<tbody class="mcnTextBlockOuter">
															<tr>
															<td valign="top" class="mcnTextBlockInner" style="padding-top: 9px; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;">
																<table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width: 100%; min-width: 100%; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" width="100%" class="mcnTextContentContainer">
																<tbody>
																	<tr>
																	<td valign="top" class="mcnTextContent" style="padding-top: 0; padding-right: 18px; padding-bottom: 9px; padding-left: 100px; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word; color: #757575; font-family: Helvetica; font-size: 16px; line-height: 150%; text-align: left;">

																		<h3 style="display: block; margin: 0; padding: 0; color: #444444; font-family: Helvetica; font-size: 22px; font-style: normal; font-weight: bold; line-height: 150%; letter-spacing: normal; text-align: left;">
																		<span style="border-left: 4px solid #333; font-size: 18px; padding-left: 7px;">
																			Additional eSIM Data (Extended Use) is now available!
																		</span>
																		</h3>

																		<p style="margin: 0; padding: 0; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; color: #757575; font-family: Helvetica; font-size: 16px; line-height: 150%; text-align: left;">
																		&lt;Purchase History&gt; → &lt;Add Data&gt; button allows you to extend the process.
																		</p>

																		<h4 style="display: block; margin: 10px 0 0 0; padding: 0; color: #444444; font-family: Helvetica; font-size: 18px; font-style: normal; font-weight: bold; line-height: 150%; letter-spacing: normal; text-align: left;">
																		Attention:
																		</h4>

																		<p style="margin: 0; padding: 0; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; color: #444444; font-family: Helvetica; font-size: 16px; line-height: 150%; text-align: left; font-weight: bold;">
																		&middot; If you have any remaining data before purchasing:<br>
																		&nbsp;&nbsp;It will be deleted and overwritten.
																		</p>

																		<p style="margin: 0; padding: 0; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; color: #444444; font-family: Helvetica; font-size: 16px; line-height: 150%; text-align: left; font-weight: bold;">
																		&middot; After purchasing the plan:<br>
																		&nbsp;&nbsp;It will automatically switch to a new one.
																		</p>

																		<p style="margin: 0; padding: 0; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; color: #444444; font-family: Helvetica; font-size: 16px; line-height: 150%; text-align: left; font-weight: bold;">
																		&middot; If the plan does not switch:<br>
																		&nbsp;&nbsp;Please turn on/off the in-flight mode or restart the terminal.
																		</p>

																	</td>
																	</tr>
																</tbody>
																</table>
															</td>
															</tr>
														</tbody>
														</table>

														<!-- Until the translation sheet is available -->
														{{else if (isAppOnlyJapanese data.language)}}
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" class="mcnTextContentContainer">
																			<tbody>
																				<tr>
																					<td valign="top" class="mcnTextContent" style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 100px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						<h3 style="display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							<span style="border-left:4px solid #333; font-size:18px; padding-left:7px">eSIMのデータ追加(延長利用)が可能になりました！</span>
																						</h3>
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																							＜購入履歴＞→＜データ追加＞ボタンより、延長手続きいただけます。
																						</p>

																						<h4 style="display: block;margin: 10px 0 0 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 18px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							注意 :
																						</h4>
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #444444;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;font-weight: bold;">
																							・購入前の残りのデータがある場合は、削除され上書きされます。
																							・プランの購入後、自動的に新しいプランに切り替わります。
																						</p>
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #444444;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;font-weight: bold;">
																							・プランが切り替わらない場合：
																						</p>
																						<p style="margin: 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #444444;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;font-weight: bold;">
																							機内モードのON/OFF、または、端末の再起動を行ってください。
																						</p>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
														
														{{else}}
														<p></p>
														{{/if}}
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnDividerBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
															<tbody class="mcnDividerBlockOuter">
																<tr>
																	<td class="mcnDividerBlockInner"
																		style="min-width: 100%;padding: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<table class="mcnDividerContent" border="0"
																			cellpadding="0" cellspacing="0" width="100%"
																			style="min-width: 100%;border-top: 1px solid #EAEAEA;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																			<tbody>
																				<tr>
																					<td
																						style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																						<span></span>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
													{{/unless}}
                            
                            	<table align="left" border="0" cellpadding="0"
																			cellspacing="0"
																			style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
																			width="100%"
																			class="mcnTextContentContainer">
																			<tbody>
																				<tr>
																					<td valign="top"
																						class="mcnTextContent"
																						style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 100px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						{{#if (isAirTrip data.userSource)}}
																						<h3
																							style="display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							<span
																								style="border-left:4px solid #333; font-size:18px; padding-left:7px">お問い合わせ</span>
																						</h3>
																						<p style="margin: 0 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						  <a href="https://lin.ee/1zdD5Zn"
																						     rel="noopener"
																						     target="_blank"
																						     style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																						     LINE で友だち追加する
																						  </a>

																						</p>
																						<p style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						 ID：@980bndld<br />
																					     ※スピーディなご返答ができるようにLINEを利用しています。
																					    </p>
     																				    {{else if (isAppOnlyJapanese data.language)}}
																						<h3
																							style="display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;text-align: left;">
																							<span
																								style="border-left:4px solid #333; font-size:18px; padding-left:7px">お問い合わせ</span>
																						</h3>
																					    <p style="margin: 0 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						  <a href="https://lin.ee/r4SeJxR"
																						     rel="noopener"
																						     target="_blank"
																						     style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #007C89;font-weight: normal;text-decoration: underline;">
																					  	     LINE で友だち追加する
																						  </a>
																						  <p style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;">
																						   ID：@624ijart<br />
																					       ※スピーディなご返答ができるようにLINEを利用しています。
																					      </p>
																						{{else}}
																						<p></p>
																					    {{/if}}
																					    </p>
																					</td>
																				</tr>
																			</tbody>
																		</table>

													</td>
												</tr>
											</tbody>
										</table>
										<!--[if (gte mso 9)|(IE)]>
                                    </td>
                                    </tr>
                                    </table>
                                    <![endif]-->
									</td>
								</tr>
								<tr>
									<td align="center" valign="top" id="templateFooter" data-template-container=""
										style="background:#333333 none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #333333;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 20px;padding-bottom: 20px;">
										<!--[if (gte mso 9)|(IE)]>
                                    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                                    <tr>
                                    <td align="center" valign="top" width="600" style="width:600px;">
                                    <![endif]-->
										<table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"
											class="templateContainer"
											style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
											<tbody>
												<tr>
													<td valign="top" class="footerContainer"
														style="background:#transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;">
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner"
																		style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<!--[if mso]>
				<table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
				<tr>
				<![endif]-->

																		<!--[if mso]>
				<td valign="top" width="600" style="width:600px;">
				<![endif]-->
																		<table align="left" border="0" cellpadding="0"
																			cellspacing="0"
																			style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
																			width="100%"
																			class="mcnTextContentContainer">
																			<tbody>
																				<tr>

																					<td valign="top"
																						class="mcnTextContent"
																						style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #FFFFFF;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">

																						<p
																							style="margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #FFFFFF;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">
																							<span
																								style="font-size:14px">
																								{{ t
																								"order-email.esim-issue-contact"
																								}} <a
																									href="mailto:<EMAIL>"
																									style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #FFFFFF;font-weight: normal;text-decoration: underline;"><EMAIL></a>.</span>
																						</p>

																					</td>
																				</tr>
																			</tbody>
																		</table>
																		<!--[if mso]>
				</td>
				<![endif]-->

																		<!--[if mso]>
				</tr>
				</table>
				<![endif]-->
																	</td>
																</tr>
															</tbody>
														</table>
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnDividerBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;">
														
														</table>
														<table border="0" cellpadding="0" cellspacing="0" width="100%"
															class="mcnTextBlock"
															style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
															<tbody class="mcnTextBlockOuter">
																<tr>
																	<td valign="top" class="mcnTextBlockInner"
																		style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
																		<!--[if mso]>
				<table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
				<tr>
				<![endif]-->

																		<!--[if mso]>
				<td valign="top" width="600" style="width:600px;">
				<![endif]-->
																		<table align="left" border="0" cellpadding="0"
																			cellspacing="0"
																			style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
																			width="100%"
																			class="mcnTextContentContainer">
																			<tbody>
																				<tr>

																					<td valign="top"
																						class="mcnTextContent"
																						style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #FFFFFF;font-size: 14px;line-height: 150%;text-align: center;">

																						©<span> Inbound Platform Corp, All
																							rights reserved.</span>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																		<!--[if mso]>
				</td>
				<![endif]-->

																		<!--[if mso]>
				</tr>
				</table>
				<![endif]-->
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</tbody>
										</table>
										<!--[if (gte mso 9)|(IE)]>
                                    </td>
                                    </tr>
                                    </table>
                                    <![endif]-->
									</td>
								</tr>
							</tbody>
						</table>
						<!-- // END TEMPLATE -->
					</td>
				</tr>
			</tbody>
		</table>
	</center>

	<div class="ai-modal-container"
		style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;Noto Sans&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;;">
	</div>
</body>

</html>