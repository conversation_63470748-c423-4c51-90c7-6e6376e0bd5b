import { Injectable, Logger } from '@nestjs/common';
import { Job, Queue, Worker } from 'bullmq';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { USAGE_QUEUE } from 'src/constants';
import { EsimUsageRulesService } from 'src/esim-usage-rules/esim-usage-rules.service';
import redisConnection from 'config/redis-connection';
import { extractDigits, sizeToBytes } from 'src/utils';
import { NotificationsService } from 'src/notifications/notifications.service';
import { NotificationNotifier } from '../notifications/NotificationNotifier';
import { ConfigService } from '@nestjs/config';
import { IRulePropertiesExtend } from './core/AbstractRule';
import { NotificationChannelEnum } from 'src/notifications/NotificationChannelEnum';
import { Event } from 'json-rules-engine';
import { InjectQueue } from '@nestjs/bullmq';
import { differenceInHours, isPast } from 'date-fns';
import { DataUsageLogsService } from 'src/data-usage-logs/data-usage-logs.service';
import { countries, plans, users } from '@prisma/client';

export type IUsageNotifierJobData = Awaited<
  ReturnType<typeof EsimOrdersService.prototype.orderDetailAndUsage>
> & {
  order: {
    user: users;
    plan: plans & {
      country: countries;
    };
  };
};
@Injectable()
export class EsimUsageNotifier extends NotificationNotifier {
  private readonly logger = new Logger(EsimUsageNotifier.name);

  constructor(
    private esimOrderService: EsimOrdersService,
    private ruleEngine: EsimUsageRulesService,
    private configService: ConfigService,
    private notificationService: NotificationsService,
    @InjectQueue(USAGE_QUEUE) private usageQueue: Queue,
    private dataUsageLogsService: DataUsageLogsService,
  ) {
    super();
  }

  async processOrder(
    topupid: string,
    job: Job<{ alert: object; data: object }>,
  ) {
    try {
      // Simulate making the API request to the third-party service to fetch usage information
      // Replace this with your actual logic to fetch usage details
      const usageDetails = await this.fetchUsageDetails(topupid);
      if (!usageDetails) return;
      if (!usageDetails.order.userId) return;

      //@ts-expect-error
      if (usageDetails.order.orderStatus === 'Not Activated') return;
      //@ts-expect-error
      if (usageDetails.order.orderStatus === 'Not Activated and Expired')
        return null;

      // @ts-ignore
      if (!usageDetails?.usage?.originalResponse?.expireTime) return;
      // @ts-ignore
      if (!usageDetails?.usage?.topup) return;
      // @ts-ignore
      if (!usageDetails?.usage?.topup?.topupId) return;

      if (
        !this.notificationService.isAllowedToThisUser(usageDetails.order.userId)
      ) {
        this.logger.log('Not allowed to this user', usageDetails.order.userId);
        return null;
      }

      //@ts-ignore
      if (!usageDetails?.order?.plan?.validityDays) return;

      // Store the usage details in your database
      //@ts-ignore
      await this.storeUsageDetails(
        //@ts-expect-error
        usageDetails.usage.topup.topupId,
        //@ts-expect-error
        usageDetails.usage.originalResponse,
      );

      //@ts-ignore
      const createTime = new Date(
          //@ts-expect-error
          usageDetails.usage.originalResponse.createTime + 'Z',
        ),
        //@ts-ignore
        expireTime =
          //@ts-expect-error
          usageDetails.usage.originalResponse.expireTime &&
          //@ts-expect-error
          new Date(usageDetails.usage.originalResponse.expireTime + 'Z'),
        //@ts-ignore
        activeTime =
          //@ts-expect-error
          usageDetails.usage.originalResponse.activeTime &&
          //@ts-expect-error
          new Date(usageDetails.usage.originalResponse.activeTime + 'Z');

      if (
        isPast(expireTime) &&
        differenceInHours(new Date(), expireTime) >
          this.configService.get('notification.esimUsageExpireInHour')
      ) {
        this.logger.log('This plan expired 3 hours ago, so no need to notify');
        this.removeUsageQueue(usageDetails.order.id);
        return undefined;
      }
      const events = await this.ruleEngine.checkUsage({
        planType: usageDetails.order.planId,
        //@ts-ignore
        totalUsageDays: usageDetails.order.plan.validtyDays,
        //@ts-ignore
        userAllowedData: sizeToBytes(usageDetails.order.plan.dataId),
        //@ts-ignore
        userConsumedData: sizeToBytes(
          //@ts-expect-error
          usageDetails.usage.originalResponse.usage + 'MB',
        ),
        createTime: createTime,
        expireTime: expireTime,
        activeTime: activeTime,
        details: usageDetails as unknown as IUsageNotifierJobData,
      });
      const orderNotification =
        await this.esimOrderService.getNotificationMetadata(
          usageDetails.order.id,
        );
      const allEventsNotified =
        this.isAllEventNotified(
          events?.events || [],
          orderNotification?.metadata as object,
        ) &&
        this.isAllEventNotified(
          events.failureEvents || [],
          orderNotification?.metadata as object,
        );

      if (allEventsNotified) {
        return null;
      }
      return {
        events: events?.events,
        ...usageDetails,
        alert: orderNotification?.metadata,
      };
    } catch (err) {
      this.logger.error(err);
    }
  }

  private isAllEventNotified(events: Event[], alert: object = {}) {
    return events.every((event) => {
      const params = event.params as IRulePropertiesExtend['event']['params'];
      return ((alert || {})[event.type] || 0) >= params.notifyLimit;
    });
  }
  private async fetchUsageDetails(topupid: string) {
    return await this.esimOrderService.orderDetailAndUsage(topupid, {
      type: 'usage',
    });
  }

  private async storeUsageDetails(topupid: string, usageDetails: any) {
    await this.dataUsageLogsService.create({
      logs: usageDetails,
      topupId: topupid,
    });
    // await this.cacheManager.set("topup-" + topupid, usageDetails, 36000);
  }

  // @Cron(CronExpression.EVERY_MINUTE) // Adjust the cron expression as needed
  // async fetchUsageDetailsPeriodically() {
  //     this.logger.log('Fetching usage details for all orders.');

  // }

  async removeUsageQueue(jobId) {
    const r = await this.usageQueue.getRepeatableJobs();
    r.forEach((job) => {
      if (job.key.includes(USAGE_QUEUE + ':' + jobId)) {
        this.usageQueue.removeRepeatableByKey(job.key);
      }
    });
  }
  startWorker(notificationService: NotificationsService) {
    const worker = new Worker(
      USAGE_QUEUE,
      async (job) => {
        const { orderId } = job.data;
        const response = await this.processOrder(orderId, job);
        return response;
      },
      {
        connection: redisConnection(this.configService).connection,
        limiter: {
          max: 1,
          duration: 36000,
        },
      },
    );

    worker.on('completed', async (job, returnvalue) => {
      if (returnvalue === null) {
        this.logger.log(
          `Notification completed for ${job.id}, removing from queue now. ${job.data.orderId}`,
        );
        // All events passed so no return value.
        await this.removeUsageQueue(extractDigits(job.data.orderId));
        return;
      }

      if (!returnvalue) return null;
      if (!returnvalue?.order?.userId) return null;
      if (!returnvalue?.events?.length) return null;

      let alert = {};
      returnvalue.events.forEach((item: IRulePropertiesExtend['event']) => {
        const incrementOfEvent = +(returnvalue?.alert?.[item.type] || 0) + 1;

        if (incrementOfEvent > item.params.notifyLimit) {
          return;
        }

        alert = {
          ...alert,
          [item.type]: incrementOfEvent,
        };
        notificationService.sendNotificationToUser({
          userId: returnvalue.order.userId,
          messages: item.params.channelAndMessages.map(
            ([channel, message]: [NotificationChannelEnum, string]) => {
              return {
                channel,
                value: message,
              };
            },
          ),
          type: 'URGENT',
        });
      });
      if (Object.keys(alert).length)
        await this.notificationService.updateMetadata(
          returnvalue.order.id,
          returnvalue.order.userId,
          alert,
        );
    });
  }

  register(notifier: NotificationsService): Promise<void> {
    this.startWorker(notifier);
    return;
  }
}
