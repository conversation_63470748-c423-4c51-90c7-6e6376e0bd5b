import { ConfigService } from "@nestjs/config";
import { IUsageNotifierJobData } from "../esim-usage-notifier";
import { DataConsumedRule } from "./rules/DataConsumedRule";
import { PackageAboutToExpire } from "./rules/PackageAboutToExpire";
import { PackageExprired } from "./rules/PackageExpired";

export class RuleFactory {

    constructor(protected data: {
        userConsumedData: number,
        userAllowedData: number,
        totalUsageDays: number,
        planType: number,
        createTime: Date,
        expireTime: Date,
        activeTime: Date,
        details: IUsageNotifierJobData
    },
        protected configService: ConfigService
    ) { }

    build(rule: string,) {
        if (rule === DataConsumedRule.NAME) {
            return new DataConsumedRule(this.data, this.configService);
        }

        if (rule === PackageAboutToExpire.NAME) {
            return new PackageAboutToExpire(this.data, this.configService);
        }

        if (rule === PackageExprired.NAME) {
            return new PackageExprired(this.data, this.configService);
        }
    }
}