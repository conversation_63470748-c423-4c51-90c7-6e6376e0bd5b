import { NestedCondition } from "json-rules-engine";
import { IRule } from "../IRule";
import { EsimUsageRuleEngineRules } from "src/esim-usage-rules/rules";
import { AbstractCondition } from "./AbstractCondition";

export class UserPlanDateAboutToFinish extends AbstractCondition implements IRule {

    generate(): NestedCondition {
        return {
            fact: EsimUsageRuleEngineRules.PLAN_DATE_ABOUT_TO_FINISH,
            operator: 'lessThan',
            value: +this.configService.get("notification.planDateAboutToFinishNotifyInHour")
        }
    }

}