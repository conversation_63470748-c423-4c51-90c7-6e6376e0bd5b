import { NestedCondition } from "json-rules-engine";
import { IRule } from "../IRule";
import { EsimUsageRuleEngineRules } from "../../rules";
import { AbstractCondition } from "./AbstractCondition";

export class UserConsumedData extends AbstractCondition implements IRule {

    generate(): NestedCondition {
        return {
            fact: EsimUsageRuleEngineRules.USER_CONSUMED_THRESHOLD_DATA,
            operator: 'greaterThan',
            value: +this.configService.get("notification.userDataConsumeThreshold")
        }
    }

}