import { NestedCondition } from "json-rules-engine";
import { AbstractCondition } from "./AbstractCondition";
import { IRule } from "../IRule";
import { EsimUsageRuleEngineRules } from "src/esim-usage-rules/rules";

export class UserPlanDateStillRemaining extends AbstractCondition implements IRule {

    generate(): NestedCondition {
        return {
            fact: EsimUsageRuleEngineRules.PLAN_DATE_STILL_REMAINING,
            operator: 'equal',
            value: true
        }
    }

}