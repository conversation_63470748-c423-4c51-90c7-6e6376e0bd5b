import { NestedCondition } from "json-rules-engine";
import { IRule } from "../IRule";
import { EsimUsageRuleEngineRules } from "src/esim-usage-rules/rules";
import { AbstractCondition } from "./AbstractCondition";

export class UserHasBelowThresholdData extends AbstractCondition implements IRule {

    generate(): NestedCondition {
        return {
            fact: EsimUsageRuleEngineRules.USER_HAS_BELOW_THRESHOLD_DATA,
            operator: 'lessThan',
            value: +this.configService.get("notification.userHasBelowThreshold")
        }
    }

}