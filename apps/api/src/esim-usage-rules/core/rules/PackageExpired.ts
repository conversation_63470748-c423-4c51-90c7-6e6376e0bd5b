import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { EsimUsageRuleEngineRules } from 'src/esim-usage-rules/rules';
import { UserPlanDateExpired } from '../conditions/UserPlanDateExpired';
import { capitalizeFirstLetter } from 'src/utils';

export class PackageExprired extends AbstractRule {
  static NAME = 'PackageExpired';

  getFactValue() {
    return {
      [EsimUsageRuleEngineRules.PLAN_DATE_EXPIRED]: this.hasPackageExpired(),
    };
  }
  getNotifyLimit(): number {
    return +this.configService.get('notification.packageExpiredNotifyLimit');
  }
  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          new UserPlanDateExpired(this.data, this.configService).generate(),
        ],
      },
      event: {
        type: PackageExprired.NAME,
        params: {
          notifyLimit: this.getNotifyLimit(),
          channelAndMessages: [
            this.createEmailMessage('Your plan has expired', {
              data: {
                frontendHost: process.env['FRONTEND_HOST'],
                firstName: this.data.details.order.user.firstName,
                countryName: capitalizeFirstLetter(
                  this.data.details.order.plan.country.name,
                ),
              },
              templateName: 'plan-expired',
            }),
            // this.createPushNotificationMessage("Plan Expired", `Hello ${this.data.details.order.user.firstName}, your data plan has expired. Check out more data plans!`)
          ],
        },
      },
    };
  }
}
