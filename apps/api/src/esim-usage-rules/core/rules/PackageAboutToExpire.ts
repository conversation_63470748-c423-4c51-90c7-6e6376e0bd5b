import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { EsimUsageRuleEngineRules } from 'src/esim-usage-rules/rules';
import { UserPlanDateAboutToFinish } from '../conditions/UserPlanDateAboutToFinish';
import { UserPlanDateExpired } from '../conditions/UserPlanDateExpired';
import { formatUTCtoJST } from 'src/utils';
import { UserPlanDateStillRemaining } from '../conditions/UserPlanDateStillRemaining';

export class PackageAboutToExpire extends AbstractRule {
  static NAME = 'PackageAboutToExpire';

  getFactValue() {
    return {
      [EsimUsageRuleEngineRules.PLAN_DATE_ABOUT_TO_FINISH]:
        this.getPackageRemainingHour(),
      [EsimUsageRuleEngineRules.PLAN_DATE_STILL_REMAINING]:
        !this.hasPackageExpired(),
    };
  }
  getNotifyLimit(): number {
    return +this.configService.get(
      'notification.packageAboutToExpireNotifyLimit',
    );
  }

  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          new UserPlanDateAboutToFinish(
            this.data,
            this.configService,
          ).generate(),
          new UserPlanDateStillRemaining(
            this.data,
            this.configService,
          ).generate(),
        ],
      },
      event: {
        type: PackageAboutToExpire.NAME,
        params: {
          notifyLimit: this.getNotifyLimit(),
          channelAndMessages: [
            this.createEmailMessage('Your data plan is about to expire!', {
              templateName: 'plan-about-to-expire',
              data: {
                firstName: this.data.details.order.user.firstName,
                remainingHours: this.getPackageRemainingHour(),
                planEndDate: formatUTCtoJST(this.data.expireTime + ''),
                planId: this.data.details.order.plan.id,
                validitydays: this.data.details.order.plan.name,
                frontendHost: process.env['FRONTEND_HOST'],
                dataId: this.data.details.order.plan.dataId,
              },
            }),
            // this.createPushNotificationMessage(`Attention ${this.data.details.order.user.firstName}!`, `Your data plan is about to expire in ${this.getPackageRemainingHour()} hours`)
          ],
        },
      },
    };
  }
}
