import { AbstractRule, IRulePropertiesExtend } from '../AbstractRule';
import { UserConsumedData } from '../conditions/UserConsumedData';
import { UserPlanDateStillRemaining } from '../conditions/UserPlanDateStillRemaining';
import { EsimUsageRuleEngineRules } from 'src/esim-usage-rules/rules';
import { isPast } from 'date-fns';

export class DataConsumedRule extends AbstractRule {
  static NAME = 'DataConsumedRule';

  getFactValue() {
    return {
      [EsimUsageRuleEngineRules.USER_CONSUMED_THRESHOLD_DATA]:
        this.getConsumedDataPercentage(),
      [EsimUsageRuleEngineRules.PLAN_DATE_STILL_REMAINING]: !isPast(
        this.data.expireTime,
      ),
    };
  }

  getNotifyLimit(): number {
    return +this.configService.get('notification.dataConsumedRuleNotifyLimit');
  }
  getRule(): IRulePropertiesExtend {
    return {
      conditions: {
        all: [
          new UserConsumedData(this.data, this.configService).generate(),
          new UserPlanDateStillRemaining(
            this.data,
            this.configService,
          ).generate(),
        ],
      },
      event: {
        type: DataConsumedRule.NAME,
        params: {
          notifyLimit: this.getNotifyLimit(),
          channelAndMessages: [
            this.createEmailMessage('Your data is almost finished', {
              templateName: 'data-about-to-expire',
              data: {
                firstName: this.data.details.order.user.firstName,
                lastName: this.data.details.order.user.lastName,
                planId: this.data.details.order.plan.id,
                usedPercent:
                  this.getConsumedDataPercentage()?.toFixed?.(2) || 80,
                validitydays: this.data.details.order.plan.name,
                dataId: this.data.details.order.plan.dataId,
                countryName: this.data.details.order.plan.country.name,
                frontendHost: process.env['FRONTEND_HOST'],
                // Please add all dara here
              },
            }),
            // this.createPushNotificationMessage(`Your data is ${this.configService.get("notification.userDataConsumeThreshold")}% used.`, "Check out more data plans!")
          ],
        },
      },
    };
  }
}
