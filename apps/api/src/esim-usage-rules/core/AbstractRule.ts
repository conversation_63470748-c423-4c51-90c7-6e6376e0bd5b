import { differenceInHours, isPast } from 'date-fns';
import { RuleProperties } from 'json-rules-engine';
import { NotificationChannelEnum } from 'src/notifications/NotificationChannelEnum';
import { IUsageNotifierJobData } from '../esim-usage-notifier';
import { ConfigService } from '@nestjs/config';

type MessageParam = [keyof typeof NotificationChannelEnum, string];
export type IRulePropertiesExtend = RuleProperties & {
  event: {
    type: string;
    params: {
      notifyLimit: number;
      channelAndMessages: MessageParam[];
    };
  };
};
export abstract class AbstractRule {
  constructor(
    protected data: {
      userConsumedData: number;
      userAllowedData: number;
      totalUsageDays: number;
      planType: number;
      createTime: Date;
      expireTime: Date;
      activeTime: Date;
      details: IUsageNotifierJobData;
    },
    protected configService: ConfigService,
  ) {}

  abstract getFactValue(): {
    [key: string]: string | number | boolean;
  };

  abstract getRule(): IRulePropertiesExtend;

  getNotifyLimit() {
    return 1;
  }

  protected getConsumedDataPercentage() {
    const consumed = this.data.userConsumedData / this.data.userAllowedData;
    return isNaN(consumed) ? 0 : consumed * 100;
  }

  protected hasPackageExpired() {
    return isPast(this.data.expireTime);
  }

  protected getPackageRemainingHour() {
    return differenceInHours(this.data.expireTime, new Date());
  }

  protected createEmailMessage(
    title: string,
    value:
      | string
      | {
          templateName: string;
          data: object;
        },
  ): MessageParam {
    return this.createNotificationMessage(
      NotificationChannelEnum.EMAIL,
      title,
      value,
    );
  }

  protected createPushNotificationMessage(
    title: string,
    value: string,
  ): MessageParam {
    return this.createNotificationMessage(
      NotificationChannelEnum.PUSH_NOTIFICATION,
      title,
      value,
    );
  }

  private createNotificationMessage(
    channel: NotificationChannelEnum,
    title: string,
    value:
      | string
      | {
          templateName: string;
          data: object;
        },
  ): MessageParam {
    return [
      channel,
      JSON.stringify({
        title,
        value,
      }),
    ];
  }
}
