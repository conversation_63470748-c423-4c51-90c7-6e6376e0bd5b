export enum EsimUsageRuleEngineRules {
    USER_CONSUMED_THRESHOLD_DATA = "USER_CONSUMED_THRESHOLD_DATA",
    PLAN_DATE_STILL_REMAINING = "PLAN_DATE_STILL_REMAINING",
    PLAN_DATE_EXPIRED = "PLAN_DATE_EXPIRED",
    PLAN_DATE_ABOUT_TO_FINISH = "PLAN_DATE_ABOUT_TO_FINISH",
    USER_HAS_BELOW_THRESHOLD_DATA = "USER_HAS_BELOW_THRESHOLD_DATA"
}


export const Facts = {
    // User has consumed 80% of their data
    [EsimUsageRuleEngineRules.USER_HAS_BELOW_THRESHOLD_DATA]: {
        fact: EsimUsageRuleEngineRules.USER_HAS_BELOW_THRESHOLD_DATA,
        operator: 'lessThan',
        value: "80 percent of userAllowedData"
    },
    // User has consumed 80% of their data
    [EsimUsageRuleEngineRules.USER_CONSUMED_THRESHOLD_DATA]: {
        fact: EsimUsageRuleEngineRules.USER_CONSUMED_THRESHOLD_DATA,
        operator: 'greaterThan',
        value: "80 percent of userAllowedData"
    },
    // User plan date is still not expired
    [EsimUsageRuleEngineRules.PLAN_DATE_STILL_REMAINING]: {
        fact: EsimUsageRuleEngineRules.PLAN_DATE_STILL_REMAINING,
        operator: "lessThan",
        value: Date.now()
    },
    // Plan expired
    [EsimUsageRuleEngineRules.PLAN_DATE_EXPIRED]: {
        fact: EsimUsageRuleEngineRules.PLAN_DATE_EXPIRED,
        operator: "greaterThan",
        value: Date.now()
    },
    // Plan date about to expire
    [EsimUsageRuleEngineRules.PLAN_DATE_ABOUT_TO_FINISH]: {
        fact: EsimUsageRuleEngineRules.PLAN_DATE_ABOUT_TO_FINISH,
        operator: "greaterThan",
        value: Date.now()
    }
}
