import { Injectable } from '@nestjs/common';
import * as ruleEngine from "json-rules-engine";
import { RuleFactory } from './core/RuleFactory';
import { DataConsumedRule } from './core/rules/DataConsumedRule';
import { PackageAboutToExpire } from './core/rules/PackageAboutToExpire';
import { PackageExprired } from './core/rules/PackageExpired';
import { IUsageNotifierJobData } from './esim-usage-notifier';
import { ConfigService } from '@nestjs/config';

interface IEsimUsageRulesServiceConstructor {
    userConsumedData: number,
    userAllowedData: number,
    totalUsageDays: number,
    planType: number,
    createTime: Date
    expireTime: Date,
    activeTime: Date,
    details: IUsageNotifierJobData
}
@Injectable()
export class EsimUsageRulesService {

    constructor(
        private configService: ConfigService
    ) { }
    checkUsage({
        userConsumedData,
        userAllowedData,
        totalUsageDays,
        planType,
        createTime,
        expireTime,
        activeTime,
        details
    }: IEsimUsageRulesServiceConstructor) {
        let engine = new ruleEngine.Engine();
        const ruleFactory = new RuleFactory({
            userConsumedData,
            userAllowedData,
            totalUsageDays,
            planType,
            createTime,
            expireTime,
            activeTime,
            details
        }, this.configService)
        const rules = this.configService.get("notification.usageEnabledRules") || [DataConsumedRule.NAME, PackageAboutToExpire.NAME, PackageExprired.NAME]
        let facts = {}
        try {
            rules.forEach((ruleClass) => {
                engine.addRule(ruleFactory.build(ruleClass).getRule())
                //@ts-ignore
                Object.assign(facts, ruleFactory.build(ruleClass).getFactValue())
            })
        } catch (err) {
            console.log(err)
        }
        return engine
            .run(facts)
    }
}
