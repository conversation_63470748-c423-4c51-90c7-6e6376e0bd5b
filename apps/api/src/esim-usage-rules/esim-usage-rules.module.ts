import { EsimUsageRulesService } from './esim-usage-rules.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QUEUE_NEW_ESIM, QUEUE_ESIM_CREATE_BUY } from 'src/constants';
import { Logger, Module } from '@nestjs/common';
import { EsimUsageNotifier } from './esim-usage-notifier';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { PrismaService } from 'src/prisma.service';
import { EsimService } from 'src/esim/esim.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { DataUsageLogsService } from 'src/data-usage-logs/data-usage-logs.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { EsimProviderBuilder } from 'src/esim/providers/EsimProviderBuilder';
import { PlansService } from 'src/plans/plans.service';
import { CouponsService } from 'src/coupons/coupons.service';
import { JwtService } from '@nestjs/jwt';
import { EsimStocksService } from 'src/esim-stocks/esim-stocks.service';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { PaymentService } from 'src/payment/payment.service';
import { UsersService } from 'src/users/users.service';
import { EmailsService } from 'src/emails/emails.service';
import { ReferralsService } from 'src/referrals/referrals.service';

@Module({
  imports: [
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 5000,
        maxRedirects: 5,
        baseURL: configService.get('USIMSA_HOST_NAME'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    EsimUsageRulesService,
    EsimUsageNotifier,
    PrismaService,
    EsimOrdersService,
    EsimService,
    Logger,
    XchangeService,
    KeyValueStoreService,
    NotificationsService,
    DataUsageLogsService,
    EmailTemplateService,
    EsimProviderBuilder,
    PlansService,
    CouponsService,
    JwtService,
    EsimStocksService,
    AwsCognitoService,
    PaymentService,
    UsersService,
    EmailsService,
    ReferralsService,
  ],
})
export class EsimUsageRulesModule {}
