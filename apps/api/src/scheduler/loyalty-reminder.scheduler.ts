import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, RepeatableJob } from 'bullmq';
import { QUEUE_LOYALTY_REMINDER } from 'src/constants';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class LoyaltyReminderScheduler implements OnModuleInit {
  private readonly logger = new Logger(LoyaltyReminderScheduler.name);

  constructor(
    @InjectQueue(QUEUE_LOYALTY_REMINDER)
    private readonly loyaltyReminderQueue: Queue,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    await this.scheduleRepeatableReminder();
  }

  private async removeAllRepeatableJobsByKey(
    jobs: RepeatableJob[],
  ) {
    if (!jobs.length) return;
    await Promise.all(
      jobs.map((job) =>
        this.loyaltyReminderQueue.removeRepeatableBy<PERSON>ey(job.key),
      ),
    );
  }

  private async scheduleRepeatableReminder() {
    const jobName = 'send-loyalty-reminder';
    const jobId = 'loyalty-reminder-job';

    let existing = await this.loyaltyReminderQueue.getRepeatableJobs();
    this.logger.log(`Existing repeatable jobs: ${existing}`);

    const cfg = this.configService.get('loyalty-reminder');
    const isTest = cfg.testMode;

    const cronPattern = isTest ? cfg.cron.test : cfg.cron.prod;

    if (existing.length > 0 && isTest) {
      this.logger.log(
        'Test mode enabled. Removing existing repeatable jobs...',
      );
      await this.removeAllRepeatableJobsByKey(existing);
      // Re-fetch the jobs list to get the current state
      existing = await this.loyaltyReminderQueue.getRepeatableJobs();
    }

    const alreadyScheduled = existing.some(
      (job) => job.name === jobName && job.pattern === cronPattern,
    );
    if (!alreadyScheduled) {
      await this.removeAllRepeatableJobsByKey(existing);

      this.logger.log(`Scheduling new loyalty reminder job at ${cronPattern}`);
      await this.loyaltyReminderQueue.add(
        jobName,
        {},
        {
          repeat: {
            pattern: cronPattern,
            tz: 'Asia/Tokyo',
          },
          jobId,
        },
      );
    }

    const finalJobs = await this.loyaltyReminderQueue.getRepeatableJobs();
    this.logger.log(
      `Repeatable job status after scheduling: ${JSON.stringify(
        finalJobs,
        null,
        2,
      )}`,
    );
  }
}
