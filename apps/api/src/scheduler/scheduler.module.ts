import { BullModule } from '@nestjs/bullmq';
import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ConfigService } from '@nestjs/config';
import redisConnection from 'config/redis-connection';
import { QUEUE_LOYALTY_REMINDER } from 'src/constants';
import { LoyaltyReminderScheduler } from './loyalty-reminder.scheduler';

@Module({
  imports: [
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_LOYALTY_REMINDER,
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: QUEUE_LOYALTY_REMINDER,
        };
      },
    }),
  ],
  providers: [LoyaltyReminderScheduler],
})
export class SchedulerModule {}
