import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { PrismaService } from 'src/prisma.service';
import { XchangeService } from './xchange.service';
import { XchangeController } from './xchange.controller';

@Module({
  imports: [
    XchangeModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 5000,
        maxRedirects: 5,
        baseURL: configService.get('API_EXCHANGE_HOST'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [XchangeService, KeyValueStoreService, PrismaService],
  exports: [],
  controllers: [XchangeController],
})
export class XchangeModule {}
