import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Cache } from 'cache-manager';
import { isUndefined, omitBy } from 'lodash';
import { AN_HOUR_IN_MILLISECONDS, CurrencyList } from 'src/constants';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';

@Injectable()
export class XchangeService {
  constructor(
    private config: ConfigService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private kv: KeyValueStoreService,
  ) {}

  async getRateOfCurrency(
    amount: string,
    options: {
      baseCurrency?: string;
      forceUpdate?: boolean;
    } = {},
  ) {
    const key = 'xchangeRate' + amount + (options.baseCurrency || 'USD');

    // If it is force update then ignore cache
    if (!options?.forceUpdate) {
      // First see if the value is stored in in memory cache, if yes then return
      const value = await this.cacheManager.get(key);
      if (value) return value;

      // if its in database then get it from there
      const rate = await this.kv.get(key);
      if (rate) {
        // set the value in cache for next 1 hour
        await this.cacheManager.set(key, rate.value, AN_HOUR_IN_MILLISECONDS);
        return rate.value;
      }
    }

    // Get the data from Exchange server
    const response = await axios.get(
      this.config.get('API_EXCHANGE_HOST') + 'exchangerates_data/latest',
      {
        headers: {
          'Accept-Encoding': 'gzip,deflate,compress',
          apikey: this.config.getOrThrow('API_LAYER_EXCHANGE_KEY'),
        },
        params: {
          amount,
          places: '2',
          base: options?.baseCurrency || this.config.get('EXCHANGE_BASE_RATE'),
        },
      },
    );
    if (!response.data.success) throw new Error('Unable to convert currency.');
    // Store it in cache
    await this.cacheManager.set(key, response.data, 3600);
    await this.kv.set(key, response.data);
    return response.data;
  }

  async getRateOfCurrencyAmount(
    amount: string,
    toCurrency: CurrencyList,
    options?: { forceUpdate?: boolean },
  ) {
    const key = 'xchangeRate' + toCurrency + amount;

    if (!options?.forceUpdate) {
      const value = await this.cacheManager.get(key);
      if (value) return value;

      const rate = await this.kv.get(key);
      if (rate) {
        await this.cacheManager.set(key, rate.value, AN_HOUR_IN_MILLISECONDS);
        return rate.value;
      }
    }

    const response = await axios.get(
      this.config.get('API_EXCHANGE_HOST') + 'exchangerates_data/convert',
      {
        headers: {
          'Accept-Encoding': 'gzip,deflate,compress',
          apikey: this.config.getOrThrow('API_LAYER_EXCHANGE_KEY'),
        },
        params: {
          amount,
          places: '2',
          from: this.config.getOrThrow('EXCHANGE_BASE_RATE'),
          to: toCurrency,
        },
      },
    );
    if (!response.data.success) throw new Error('Unable to convert currency.');
    await this.cacheManager.set(
      key,
      response.data.result,
      AN_HOUR_IN_MILLISECONDS,
    );
    await this.kv.set(key, response.data.result);
    return response.data.result;
  }

  getXchanges(
    price: number,
    xeRate,
    {
      defaultCurrency,
      prices,
      type,
    }: {
      type?: 'DISCOUNT' | 'TOTAL';
      defaultCurrency?: string;
      prices?: object;
    } = {},
  ) {
    const roundBy = 10;
    const isDiscount = type === 'DISCOUNT';
    return {
      JPY:
        defaultCurrency === 'JPY'
          ? Math.ceil(price)
          : Math.ceil(Number(price * Math.ceil(xeRate.rates.JPY))),
      EUR: +Number(price * xeRate.rates.EUR).toFixed(2),
      AUD: +Number(price * xeRate.rates.AUD).toFixed(2),
      CAD: +Number(price * xeRate.rates.CAD).toFixed(2),
      GBP: +Number(price * xeRate.rates.GBP).toFixed(2),
      TWD: +Number(price * xeRate.rates.TWD).toFixed(2),
      HKD: +Number(price * xeRate.rates.HKD).toFixed(2),
      CNY: +Math.ceil(price * xeRate.rates.CNY),
      KRW: +Math.ceil(price * xeRate.rates.KRW),
      ...omitBy(prices || {}, isUndefined),
      USD:
        defaultCurrency === 'USD'
          ? price
          : +Number(price * xeRate.rates.USD).toFixed(2),
    };
  }

  getXchangesNew(
    price: number,
    xeRate,
    {
      defaultCurrency,
      type,
      prices,
    }: {
      type?: 'DISCOUNT' | 'TOTAL';
      defaultCurrency?: string;
      prices?: object;
    } = {},
  ) {
    return {
      JPY:
        defaultCurrency === 'JPY'
          ? Math.round(price)
          : Math.round(Number(price * xeRate.rates.JPY)),
      EUR:
        defaultCurrency === 'EUR'
          ? Math.round(price)
          : +Number(price * xeRate.rates.EUR).toFixed(2),
      AUD:
        defaultCurrency === 'AUD'
          ? Math.round(price)
          : +Number(price * xeRate.rates.AUD).toFixed(2),
      CAD:
        defaultCurrency === 'CAD'
          ? Math.round(price)
          : +Number(price * xeRate.rates.CAD).toFixed(2),
      GBP:
        defaultCurrency === 'GBP'
          ? Math.round(price)
          : +Number(price * xeRate.rates.GBP).toFixed(2),
      TWD:
        defaultCurrency === 'TWD'
          ? Math.round(price)
          : +Number(price * xeRate.rates.TWD).toFixed(2),
      HKD:
        defaultCurrency === 'HKD'
          ? Math.round(price)
          : +Number(price * xeRate.rates.HKD).toFixed(2),
      CNY:
        defaultCurrency === 'CNY'
          ? Math.round(price)
          : +Math.round(price * xeRate.rates.CNY),
      KRW:
        defaultCurrency === 'KRW'
          ? Math.round(price)
          : +Math.round(price * xeRate.rates.KRW),
      USD:
        defaultCurrency === 'USD'
          ? price
          : +Number(price * xeRate.rates.USD).toFixed(2),
      ...omitBy(prices || {}, isUndefined),
    };
  }
}
