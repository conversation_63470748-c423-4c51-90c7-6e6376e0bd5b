import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { CurrencyList } from 'src/constants';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { XchangeService } from './xchange.service';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

@Controller('xchange')
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class XchangeController {
  constructor(private xe: XchangeService) {}
  @Get('exchange/update')
  @ApiExcludeEndpoint()
  public async updateExchangeRate() {
    return; // remove later
    try {
      await this.xe.getRateOfCurrencyAmount('1', CurrencyList.JPY, {
        forceUpdate: true,
      });
      await this.xe.getRateOfCurrency('1', {
        forceUpdate: true,
      });
    } catch (err) {
      console.log(err);
    }
  }
}
