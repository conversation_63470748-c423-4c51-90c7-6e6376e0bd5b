import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class HTTPHeaderInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const response = context.switchToHttp().getResponse();

        // Set Cache-Control header for one hour (3600 seconds)
        response.setHeader('Cache-Control', 'max-age=3600');

        return next.handle();
    }
}
