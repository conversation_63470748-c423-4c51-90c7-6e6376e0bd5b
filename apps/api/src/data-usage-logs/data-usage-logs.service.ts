import { Injectable } from '@nestjs/common';
import { CreateDataUsageLogDto } from './dto/create-data-usage-log.dto';
import { UpdateDataUsageLogDto } from './dto/update-data-usage-log.dto';
import { PrismaService } from 'src/prisma.service';
import crypto from "crypto";

@Injectable()
export class DataUsageLogsService {
  constructor(private prismaService: PrismaService) { }

  async create(createDataUsageLogDto: CreateDataUsageLogDto) {
    try {
      const currentTimestamp = new Date();
      //@ts-expect-error
      const oneHourAgo = new Date(currentTimestamp - 60 * 60 * 1000);

      const lastAddedLogs = await this.prismaService.data_usage_logs.findMany({
        where: {
          createdAt: {
            gte: oneHourAgo, // Greater than or equal to one hour ago
            lte: currentTimestamp,
          }
        },
        take: 1,
        orderBy: {
          createdAt: "desc"
        }
      });

      if (lastAddedLogs.length) {
        return await this.prismaService.data_usage_logs.update({
          data: {
            lastCheckedHash: "sasa",
            logs: createDataUsageLogDto.logs,
            topupid: createDataUsageLogDto.topupId
          },
          where: {
            id: lastAddedLogs[0].id
          }
        });
      }
      await this.prismaService.data_usage_logs.create({
        data: {
          lastCheckedHash: "sasa",
          logs: createDataUsageLogDto.logs,
          topupid: createDataUsageLogDto.topupId
        },
      })
    } catch (err) {
      console.log(err);
      return null;
    }
  }
  hash(key) {
    return crypto.createHash('md5').update(key).digest('hex');
  }

}
