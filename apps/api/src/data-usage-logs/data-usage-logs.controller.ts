import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
} from '@nestjs/common';
import { DataUsageLogsService } from './data-usage-logs.service';
import { CreateDataUsageLogDto } from './dto/create-data-usage-log.dto';
import { UpdateDataUsageLogDto } from './dto/update-data-usage-log.dto';
import { ApiExcludeEndpoint } from '@nestjs/swagger';
import { SentryInterceptor } from 'src/SentryInterceptor';

@Controller('data-usage-logs')
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class DataUsageLogsController {
  constructor(private readonly dataUsageLogsService: DataUsageLogsService) {}

  @Get()
  @ApiExcludeEndpoint()
  create(@Body() createDataUsageLogDto: CreateDataUsageLogDto) {
    return this.dataUsageLogsService.create(createDataUsageLogDto);
  }
}
