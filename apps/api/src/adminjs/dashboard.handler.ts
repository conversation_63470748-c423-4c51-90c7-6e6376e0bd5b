import { PAYMENT_STATUS, PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

// --- Helpers ---
function parseDateFilter(startDate?: string, endDate?: string) {
    if (startDate && endDate) {
        return { createdAt: { gte: new Date(startDate), lte: new Date(endDate) } };
    } else if (startDate) {
        return { createdAt: { gte: new Date(startDate) } };
    } else if (endDate) {
        return { createdAt: { lte: new Date(endDate) } };
    }
    return {};
}

async function getFromCache(redisClient: Redis, key: string) {
    try {
        const cached = await redisClient.get(key);
        if (cached) return JSON.parse(cached);
    } catch (e) { }
    return null;
}

async function setToCache(redisClient: Redis, key: string, value: any, ttl: number) {
    try {
        await redisClient.set(key, JSON.stringify(value), 'EX', ttl);
    } catch (e) { }
}

async function getTopCustomers(prisma: PrismaClient, dateFilter: any) {
    const successFilter = { ...dateFilter, paymentStatus: PAYMENT_STATUS.SUCCESS };
    const topCustomersRaw = await prisma.orders.groupBy({
        by: ['userId'],
        _count: { userId: true },
        orderBy: { _count: { userId: 'desc' } },
        take: 10,
        where: successFilter,
    });
    const userIds = topCustomersRaw.map(c => c.userId).filter(Boolean);
    const users = userIds.length > 0 ? await prisma.users.findMany({ where: { id: { in: userIds } } }) : [];
    const topCustomers = topCustomersRaw.map(c => {
        const user = users.find(u => u.id === c.userId);
        return {
            userId: c.userId,
            name: user ? `${user.firstName} ${user.lastName}` : '',
            email: user ? user.email : '',
            orderCount: c._count.userId,
        };
    });
    const topCustomersSpentRaw = await prisma.orders.groupBy({
        by: ['userId'],
        _sum: { finalPrice: true },
        orderBy: { _sum: { finalPrice: 'desc' } },
        take: 10,
        where: successFilter,
    });
    const topCustomersSpent = topCustomersSpentRaw.map(c => {
        const user = users.find(u => u.id === c.userId);
        return {
            userId: c.userId,
            name: user ? `${user.firstName} ${user.lastName}` : '',
            email: user ? user.email : '',
            totalSpent: c._sum.finalPrice || 0,
        };
    });
    return { topCustomers, topCustomersSpent };
}

// --- Tab Data Functions ---
async function getOrdersTabData(prisma: PrismaClient, dateFilter: any) {
    // --- Orders tab data ---
    const totalUsers = await prisma.users.count();
    const successFilter = { ...dateFilter, paymentStatus: PAYMENT_STATUS.SUCCESS };
    const totalOrders = await prisma.orders.count({ where: successFilter });
    const ordersByAppIdRaw = await prisma.orders.groupBy({
        by: ['appsId'],
        _count: { appsId: true },
        where: successFilter,
    });
    const ordersByAppId = ordersByAppIdRaw.map(o => ({ appId: o.appsId, count: o._count.appsId }));
    const ordersByDayRaw = await prisma.orders.groupBy({
        by: ['createdAt', 'paymentMethod'],
        _count: { id: true },
        orderBy: { createdAt: 'asc' },
        where: successFilter,
    });
    const ordersByDayMap = {};
    ordersByDayRaw.forEach(({ createdAt, paymentMethod, _count }) => {
        const date = createdAt.toISOString().slice(0, 10);
        if (!ordersByDayMap[date]) ordersByDayMap[date] = { date };
        ordersByDayMap[date][paymentMethod || 'Unknown'] = (ordersByDayMap[date][paymentMethod || 'Unknown'] || 0) + _count.id;
    });
    const ordersByDay = Object.values(ordersByDayMap);
    const topPaymentMethods = await prisma.orders.groupBy({
        by: ['paymentMethod'],
        _count: { paymentMethod: true },
        orderBy: { _count: { paymentMethod: 'desc' } },
        take: 5,
        where: successFilter,
    });
    const ordersBySourceAffiliateTrendRaw = await prisma.orders.groupBy({
        by: ['createdAt', 'source', 'affiliate'],
        _count: { id: true },
        orderBy: { createdAt: 'asc' },
        where: successFilter,
    });
    const ordersBySourceAffiliateTrend = ordersBySourceAffiliateTrendRaw.map(row => ({
        date: row.createdAt.toISOString().slice(0, 10),
        source: row.source || 'Unknown',
        affiliate: row.affiliate || 'Unknown',
        count: row._count.id,
    }));
    const revenueBySourceRaw = await prisma.orders.groupBy({
        by: ['source'],
        _sum: { finalPrice: true },
        orderBy: { _sum: { finalPrice: 'desc' } },
        take: 10,
        where: successFilter,
    });
    const revenueBySource = revenueBySourceRaw.map(s => ({ source: s.source || 'Unknown', revenue: s._sum.finalPrice || 0 }));
    const revenueByAffiliateRaw = await prisma.orders.groupBy({
        by: ['affiliate'],
        _sum: { finalPrice: true },
        orderBy: { _sum: { finalPrice: 'desc' } },
        take: 10,
        where: successFilter,
    });
    const revenueByAffiliate = revenueByAffiliateRaw.map(a => ({ affiliate: a.affiliate || 'Unknown', revenue: a._sum.finalPrice || 0 }));
    // Conversion Rate by Source
    const totalOrdersBySourceRaw = await prisma.orders.groupBy({
        by: ['source'],
        _count: { id: true },
        where: dateFilter,
    });
    const completedOrdersBySourceRaw = await prisma.orders.groupBy({
        by: ['source'],
        _count: { id: true },
        where: { ...dateFilter, paymentStatus: PAYMENT_STATUS.SUCCESS },
    });
    const completedOrdersBySourceMap = Object.fromEntries(
        completedOrdersBySourceRaw.map(row => [row.source || 'Unknown', row._count.id])
    );
    const conversionRateBySource = totalOrdersBySourceRaw.map(row => {
        const source = row.source || 'Unknown';
        const total = row._count.id;
        const completed = completedOrdersBySourceMap[source] || 0;
        return {
            source,
            conversionRate: total > 0 ? completed / total : null,
        };
    });
    // Conversion Rate by Affiliate
    const totalOrdersByAffiliateRaw = await prisma.orders.groupBy({
        by: ['affiliate'],
        _count: { id: true },
        where: dateFilter,
    });
    const completedOrdersByAffiliateRaw = await prisma.orders.groupBy({
        by: ['affiliate'],
        _count: { id: true },
        where: { ...dateFilter, paymentStatus: PAYMENT_STATUS.SUCCESS },
    });
    const completedOrdersByAffiliateMap = Object.fromEntries(
        completedOrdersByAffiliateRaw.map(row => [row.affiliate || 'Unknown', row._count.id])
    );
    const conversionRateByAffiliate = totalOrdersByAffiliateRaw.map(row => {
        const affiliate = row.affiliate || 'Unknown';
        const total = row._count.id;
        const completed = completedOrdersByAffiliateMap[affiliate] || 0;
        return {
            affiliate,
            conversionRate: total > 0 ? completed / total : null,
        };
    });
    const topSourcesRaw = await prisma.orders.groupBy({
        by: ['source'],
        _count: { source: true },
        orderBy: { _count: { source: 'desc' } },
        take: 10,
        where: successFilter,
    });
    const topSources = topSourcesRaw.map(s => ({ source: s.source || 'Unknown', count: s._count.source }));
    const topAffiliatesRaw = await prisma.orders.groupBy({
        by: ['affiliate'],
        _count: { affiliate: true },
        orderBy: { _count: { affiliate: 'desc' } },
        take: 10,
        where: successFilter,
    });
    const topAffiliates = topAffiliatesRaw.map(a => ({ affiliate: a.affiliate || 'Unknown', count: a._count.affiliate }));
    // Shared: Top Customers
    const { topCustomers, topCustomersSpent } = await getTopCustomers(prisma, dateFilter);
    // --- Alerts: Order Volume Change ---
    // Determine previous period
    let alerts = [];
    if (dateFilter.createdAt?.gte && dateFilter.createdAt?.lte) {
        const start = new Date(dateFilter.createdAt.gte);
        const end = new Date(dateFilter.createdAt.lte);
        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
        const prevStart = new Date(start);
        prevStart.setDate(start.getDate() - days);
        const prevEnd = new Date(end);
        prevEnd.setDate(end.getDate() - days);
        const prevFilter = {
            ...dateFilter,
            createdAt: { gte: prevStart, lte: prevEnd },
        };
        const prevOrders = await prisma.orders.count({ where: { ...prevFilter, paymentStatus: PAYMENT_STATUS.SUCCESS } });
        if (prevOrders > 0) {
            const percentChange = Math.round(1000 * (totalOrders - prevOrders) / prevOrders) / 10;
            if (percentChange >= 30) {
                alerts.push({
                    type: 'order_volume_surge',
                    message: `Order volume increased by ${percentChange}% compared to previous period.`,
                    severity: 'success',
                    tab: 'Orders',
                    data: { totalOrders, prevOrders, percentChange },
                });
            } else if (percentChange <= -50) {
                alerts.push({
                    type: 'order_volume_drop',
                    message: `Order volume dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                    severity: 'critical',
                    tab: 'Orders',
                    data: { totalOrders, prevOrders, percentChange },
                });
            } else if (percentChange <= -30) {
                alerts.push({
                    type: 'order_volume_drop',
                    message: `Order volume dropped by ${Math.abs(percentChange)}% compared to previous period.`,
                    severity: 'warning',
                    tab: 'Orders',
                    data: { totalOrders, prevOrders, percentChange },
                });
            }
        }
        // --- Payment Method Alerts ---
        const currPaymentMethods = await prisma.orders.groupBy({
            by: ['paymentMethod'],
            _count: { paymentMethod: true },
            where: { ...successFilter },
        });
        const prevPaymentMethods = await prisma.orders.groupBy({
            by: ['paymentMethod'],
            _count: { paymentMethod: true },
            where: { ...prevFilter, paymentStatus: PAYMENT_STATUS.SUCCESS },
        });
        const prevPayMap = Object.fromEntries(prevPaymentMethods.map(pm => [pm.paymentMethod || 'Unknown', pm._count.paymentMethod]));
        currPaymentMethods.forEach(pm => {
            const method = pm.paymentMethod || 'Unknown';
            const currCount = pm._count.paymentMethod;
            const prevCount = prevPayMap[method] || 0;
            if (prevCount > 0) {
                const percentChange = Math.round(1000 * (currCount - prevCount) / prevCount) / 10;
                if (percentChange >= 50) {
                    alerts.push({
                        type: 'payment_method_surge',
                        message: `Payment method '${method}' usage increased by ${percentChange}% compared to previous period.`,
                        severity: 'success',
                        tab: 'Orders',
                        data: { method, currCount, prevCount, percentChange },
                    });
                } else if (percentChange <= -50) {
                    alerts.push({
                        type: 'payment_method_drop',
                        message: `Payment method '${method}' usage dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                        severity: 'critical',
                        tab: 'Orders',
                        data: { method, currCount, prevCount, percentChange },
                    });
                }
            }
        });
        // --- Payment Method Dropped to 0 Alerts ---
        currPaymentMethods.forEach(pm => {
            const method = pm.paymentMethod || 'Unknown';
            const currCount = pm._count.paymentMethod;
            const prevCount = prevPayMap[method] || 0;
            if (currCount === 0 && prevCount > 0) {
                const prevAvg = Math.round(10 * prevCount / days) / 10;
                if (prevAvg >= 1) {
                    alerts.push({
                        type: 'payment_method_zero',
                        message: `Payment method '${method}' usage dropped to 0 in the last ${days} days (previous avg: ${prevAvg}/day).`,
                        severity: 'critical',
                        tab: 'Orders',
                        data: { method, currCount, prevCount, prevAvg, days },
                    });
                }
            }
        });
        // --- Source Alerts ---
        const currSources = await prisma.orders.groupBy({
            by: ['source'],
            _count: { source: true },
            where: { ...successFilter },
        });
        const prevSources = await prisma.orders.groupBy({
            by: ['source'],
            _count: { source: true },
            where: { ...prevFilter, paymentStatus: PAYMENT_STATUS.SUCCESS },
        });
        const prevSourceMap = Object.fromEntries(prevSources.map(s => [s.source || 'Unknown', s._count.source]));
        currSources.forEach(s => {
            const source = s.source || 'Unknown';
            const currCount = s._count.source;
            const prevCount = prevSourceMap[source] || 0;
            if (prevCount > 0) {
                const percentChange = Math.round(1000 * (currCount - prevCount) / prevCount) / 10;
                if (percentChange >= 50) {
                    alerts.push({
                        type: 'order_source_surge',
                        message: `Orders from source '${source}' increased by ${percentChange}% compared to previous period.`,
                        severity: 'success',
                        tab: 'Orders',
                        data: { source, currCount, prevCount, percentChange },
                    });
                } else if (percentChange <= -50) {
                    alerts.push({
                        type: 'order_source_drop',
                        message: `Orders from source '${source}' dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                        severity: 'critical',
                        tab: 'Orders',
                        data: { source, currCount, prevCount, percentChange },
                    });
                }
            }
        });
        // --- Source Dropped to 0 Alerts ---
        currSources.forEach(s => {
            const source = s.source || 'Unknown';
            const currCount = s._count.source;
            const prevCount = prevSourceMap[source] || 0;
            if (currCount === 0 && prevCount > 0) {
                const prevAvg = Math.round(10 * prevCount / days) / 10;
                if (prevAvg >= 1) {
                    alerts.push({
                        type: 'order_source_zero',
                        message: `No orders from source '${source}' in the last ${days} days (previous avg: ${prevAvg}/day).`,
                        severity: 'critical',
                        tab: 'Orders',
                        data: { source, currCount, prevCount, prevAvg, days },
                    });
                }
            }
        });
        // --- Affiliate Alerts ---
        const currAffiliates = await prisma.orders.groupBy({
            by: ['affiliate'],
            _count: { affiliate: true },
            where: { ...successFilter },
        });
        const prevAffiliates = await prisma.orders.groupBy({
            by: ['affiliate'],
            _count: { affiliate: true },
            where: { ...prevFilter, paymentStatus: PAYMENT_STATUS.SUCCESS },
        });
        const prevAffiliateMap = Object.fromEntries(prevAffiliates.map(a => [a.affiliate || 'Unknown', a._count.affiliate]));
        currAffiliates.forEach(a => {
            const affiliate = a.affiliate || 'Unknown';
            const currCount = a._count.affiliate;
            const prevCount = prevAffiliateMap[affiliate] || 0;
            if (prevCount > 0) {
                const percentChange = Math.round(1000 * (currCount - prevCount) / prevCount) / 10;
                if (percentChange >= 50) {
                    alerts.push({
                        type: 'order_affiliate_surge',
                        message: `Orders from affiliate '${affiliate}' increased by ${percentChange}% compared to previous period.`,
                        severity: 'success',
                        tab: 'Orders',
                        data: { affiliate, currCount, prevCount, percentChange },
                    });
                } else if (percentChange <= -50) {
                    alerts.push({
                        type: 'order_affiliate_drop',
                        message: `Orders from affiliate '${affiliate}' dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                        severity: 'critical',
                        tab: 'Orders',
                        data: { affiliate, currCount, prevCount, percentChange },
                    });
                }
            }
        });
        // --- Affiliate Dropped to 0 Alerts ---
        currAffiliates.forEach(a => {
            const affiliate = a.affiliate || 'Unknown';
            const currCount = a._count.affiliate;
            const prevCount = prevAffiliateMap[affiliate] || 0;
            if (currCount === 0 && prevCount > 0) {
                const prevAvg = Math.round(10 * prevCount / days) / 10;
                if (prevAvg >= 1) {
                    alerts.push({
                        type: 'order_affiliate_zero',
                        message: `No orders from affiliate '${affiliate}' in the last ${days} days (previous avg: ${prevAvg}/day).`,
                        severity: 'critical',
                        tab: 'Orders',
                        data: { affiliate, currCount, prevCount, prevAvg, days },
                    });
                }
            }
        });
    }
    // --- Orders by Day (total orders per day, all payment methods) ---
    const ordersTrendMap = {};
    const revenueByDayMap = {};
    const ordersByDayRaw2 = await prisma.orders.findMany({
        where: successFilter,
        select: { createdAt: true, finalPrice: true },
    });
    ordersByDayRaw2.forEach(({ createdAt, finalPrice }) => {
        const date = createdAt.toISOString().slice(0, 10);
        ordersTrendMap[date] = (ordersTrendMap[date] || 0) + 1;
        revenueByDayMap[date] = (revenueByDayMap[date] || 0) + (finalPrice || 0);
    });
    const ordersTrend = Object.entries(ordersTrendMap)
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => a.date.localeCompare(b.date));
    const revenueByDay = Object.entries(revenueByDayMap)
        .map(([date, revenue]) => ({ date, revenue }))
        .sort((a, b) => a.date.localeCompare(b.date));
    // --- Milestone Detection ---
    const milestones = [];
    // 1. First day with 1000+ orders
    const first1000Day = ordersTrend.find((d) => Number(d.count) >= 1000);
    if (first1000Day) {
        milestones.push({
            date: first1000Day.date,
            label: 'First day with 1,000+ orders',
            type: 'orders',
            value: Number(first1000Day.count),
        });
    }
    // 2. First week with 10,000+ revenue
    if (ordersTrend.length >= 7) {
        for (let i = 0; i <= ordersTrend.length - 7; i++) {
            const weekOrders = ordersTrend.slice(i, i + 7);
            const weekRevenue = revenueByDay.slice(i, i + 7).reduce((sum, d) => sum + Number(d.revenue), 0);
            if (weekRevenue >= 10000) {
                milestones.push({
                    date: weekOrders[0].date,
                    label: 'First week with ¥10,000+ revenue',
                    type: 'revenue',
                    value: weekRevenue,
                });
                break;
            }
        }
    }
    // 3. Record high orders in a day
    if (ordersTrend.length > 0) {
        const maxOrdersDay = ordersTrend.reduce((max, d) => Number(d.count) > Number(max.count) ? d : max, ordersTrend[0]);
        milestones.push({
            date: maxOrdersDay.date,
            label: 'Record high: most orders in a day',
            type: 'orders_record',
            value: Number(maxOrdersDay.count),
        });
    }
    // 4. Record high revenue in a day
    if (revenueByDay.length > 0) {
        const maxRevenueDay = revenueByDay.reduce((max, d) => Number(d.revenue) > Number(max.revenue) ? d : max, revenueByDay[0]);
        milestones.push({
            date: maxRevenueDay.date,
            label: 'Record high: most revenue in a day',
            type: 'revenue_record',
            value: Number(maxRevenueDay.revenue),
        });
    }
    // 5. First 1000 PayPay orders (cumulative)
    const paypayOrders = await prisma.orders.findMany({
        where: { ...successFilter, paymentMethod: 'PayPay' },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' },
    });
    if (paypayOrders.length >= 1000) {
        const first1000PayPay = paypayOrders[999];
        milestones.push({
            date: first1000PayPay.createdAt.toISOString().slice(0, 10),
            label: 'First 1,000 PayPay orders',
            type: 'paypay',
            value: 1000,
        });
    }
    return {
        totalUsers,
        totalOrders,
        ordersByAppId,
        ordersByDay,
        ordersTrend,
        revenueByDay,
        topPaymentMethods,
        topCustomers,
        topCustomersSpent,
        ordersBySourceAffiliateTrend,
        revenueBySource,
        revenueByAffiliate,
        conversionRateBySource,
        conversionRateByAffiliate,
        topSources,
        topAffiliates,
        alerts,
        milestones,
    };
}

async function getUsersTabData(prisma: PrismaClient, dateFilter: any) {
    // --- Users tab data ---
    const successFilter = { ...dateFilter, paymentStatus: PAYMENT_STATUS.SUCCESS };
    // 1. Users trend (signups per day)
    const usersInRange = await prisma.users.findMany({ where: dateFilter, select: { createdAt: true } });
    const usersTrendMap = {};
    usersInRange.forEach(u => {
        const date = u.createdAt.toISOString().slice(0, 10);
        usersTrendMap[date] = (usersTrendMap[date] || 0) + 1;
    });
    const usersTrend = Object.entries(usersTrendMap).map(([date, count]) => ({ date, count }));
    // 2. Returning users (users with >1 order)
    const returningUsersRaw = await prisma.orders.groupBy({ by: ['userId'], _count: { id: true }, where: successFilter });
    const returningUserIds = returningUsersRaw.filter(u => u._count.id > 1 && u.userId !== null).map(u => u.userId);
    const returningUsersCount = returningUserIds.length;
    // Trend: signups of returning users per day
    let returningUsersTrend = [];
    if (returningUserIds.length > 0) {
        const returningUsersInRange = await prisma.users.findMany({ where: { ...dateFilter, id: { in: returningUserIds } }, select: { createdAt: true } });
        const retTrendMap = {};
        returningUsersInRange.forEach(u => {
            const date = u.createdAt.toISOString().slice(0, 10);
            retTrendMap[date] = (retTrendMap[date] || 0) + 1;
        });
        returningUsersTrend = Object.entries(retTrendMap).map(([date, count]) => ({ date, count }));
    }
    // 3. Popular signup medium by idpProvider
    const idpProviderStatsRaw = await prisma.users.groupBy({ by: ['idpProvider'], _count: { id: true }, where: dateFilter });
    const idpProviderStats = idpProviderStatsRaw.map(i => ({ idpProvider: i.idpProvider || 'Unknown', count: i._count.id }));
    // Trend: signups per idpProvider per day
    const idpProviderUsers = await prisma.users.findMany({ where: dateFilter, select: { createdAt: true, idpProvider: true } });
    const idpProviderTrendMap = {};
    idpProviderUsers.forEach(u => {
        const date = u.createdAt.toISOString().slice(0, 10);
        const provider = u.idpProvider || 'Unknown';
        const key = `${date}__${provider}`;
        idpProviderTrendMap[key] = (idpProviderTrendMap[key] || 0) + 1;
    });
    const idpProviderTrend = Object.entries(idpProviderTrendMap).map(([key, count]) => {
        const [date, idpProvider] = key.split('__');
        return { date, idpProvider, count };
    });
    // 4. Users by userPool
    const userPoolStatsRaw = await prisma.users.groupBy({ by: ['userPool'], _count: { id: true }, where: dateFilter });
    const userPoolStats = userPoolStatsRaw.map(u => ({ userPool: u.userPool || 'Unknown', count: u._count.id }));
    // Trend: signups per userPool per day
    const userPoolUsers = await prisma.users.findMany({ where: dateFilter, select: { createdAt: true, userPool: true } });
    const userPoolTrendMap = {};
    userPoolUsers.forEach(u => {
        const date = u.createdAt.toISOString().slice(0, 10);
        const pool = u.userPool || 'Unknown';
        const key = `${date}__${pool}`;
        userPoolTrendMap[key] = (userPoolTrendMap[key] || 0) + 1;
    });
    const userPoolTrend = Object.entries(userPoolTrendMap).map(([key, count]) => {
        const [date, userPool] = key.split('__');
        return { date, userPool, count };
    });
    // 5. Guest users (orders from users with isVerified=false)
    const guestUserIdsRaw = await prisma.orders.findMany({ where: { ...successFilter, user: { isVerified: false } }, select: { userId: true }, distinct: ['userId'] });
    const guestUserIds = guestUserIdsRaw.map(u => u.userId).filter(Boolean);
    const guestUsersCount = guestUserIds.length;
    // Guest-to-user conversion: profileImage != 'Guest', isVerified=true, createdAt != updatedAt
    let guestToUserCount = 0;
    if (guestUserIds.length > 0) {
        const guestToUserRaw = await prisma.users.findMany({ where: { id: { in: guestUserIds }, isVerified: true, profileImage: { not: 'Guest' } }, select: { createdAt: true, updatedAt: true } });
        guestToUserCount = guestToUserRaw.filter(u => u.createdAt.getTime() !== u.updatedAt.getTime()).length;
    }
    // 6. Popular locale
    const localeStatsRaw = await prisma.users.groupBy({ by: ['locale'], _count: { id: true }, where: dateFilter });
    const localeStats = localeStatsRaw.map(l => ({ locale: l.locale || 'Unknown', count: l._count.id }));
    // Trend: signups per locale per day
    const localeUsers = await prisma.users.findMany({ where: dateFilter, select: { createdAt: true, locale: true } });
    const localeTrendMap = {};
    localeUsers.forEach(u => {
        const date = u.createdAt.toISOString().slice(0, 10);
        const locale = u.locale || 'Unknown';
        const key = `${date}__${locale}`;
        localeTrendMap[key] = (localeTrendMap[key] || 0) + 1;
    });
    const localeTrend = Object.entries(localeTrendMap).map(([key, count]) => {
        const [date, locale] = key.split('__');
        return { date, locale, count };
    });
    // 7. Users by source
    const userSourceStatsRaw = await prisma.users.groupBy({ by: ['source'], _count: { id: true }, where: dateFilter });
    const userSourceStats = userSourceStatsRaw.map(s => ({ source: s.source || 'Unknown', count: s._count.id }));
    // Trend: signups per source per day
    const userSourceUsers = await prisma.users.findMany({ where: dateFilter, select: { createdAt: true, source: true } });
    const userSourceTrendMap = {};
    userSourceUsers.forEach(u => {
        const date = u.createdAt.toISOString().slice(0, 10);
        const source = u.source || 'Unknown';
        const key = `${date}__${source}`;
        userSourceTrendMap[key] = (userSourceTrendMap[key] || 0) + 1;
    });
    const userSourceTrend = Object.entries(userSourceTrendMap).map(([key, count]) => {
        const [date, source] = key.split('__');
        return { date, source, count };
    });
    // 8. DAU/WAU (active users per day/week)
    let dau = [];
    let wau = [];
    const lastLoginFilter = {};
    if (dateFilter.createdAt?.gte) lastLoginFilter['gte'] = dateFilter.createdAt.gte;
    if (dateFilter.createdAt?.lte) lastLoginFilter['lte'] = dateFilter.createdAt.lte;
    const dauUsers = await prisma.users.findMany({ where: { lastLogin: { not: null, ...lastLoginFilter } }, select: { lastLogin: true } });
    const dauMap = {};
    dauUsers.forEach(u => {
        if (u.lastLogin) {
            const date = u.lastLogin.toISOString().slice(0, 10);
            dauMap[date] = (dauMap[date] || 0) + 1;
        }
    });
    dau = Object.entries(dauMap).map(([date, count]) => ({ date, count }));
    // WAU: group DAU by week start
    const wauMap = {};
    Object.keys(dauMap).forEach(dateStr => {
        const date = new Date(dateStr);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const weekKey = weekStart.toISOString().slice(0, 10);
        wauMap[weekKey] = (wauMap[weekKey] || 0) + dauMap[dateStr];
    });
    wau = Object.entries(wauMap).map(([week, count]) => ({ week, count }));
    // Shared: Top Customers
    const { topCustomers, topCustomersSpent } = await getTopCustomers(prisma, dateFilter);
    // --- Alerts: Drop in New Signups ---
    let alerts = [];
    if (dateFilter.createdAt?.gte && dateFilter.createdAt?.lte) {
        const start = new Date(dateFilter.createdAt.gte);
        const end = new Date(dateFilter.createdAt.lte);
        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
        const prevStart = new Date(start);
        prevStart.setDate(start.getDate() - days);
        const prevEnd = new Date(end);
        prevEnd.setDate(end.getDate() - days);
        const prevFilter = { ...dateFilter, createdAt: { gte: prevStart, lte: prevEnd } };
        // Previous period signups
        const prevUsersInRange = await prisma.users.findMany({ where: prevFilter, select: { createdAt: true } });
        const prevUsersTrendMap = {};
        prevUsersInRange.forEach(u => {
            const date = u.createdAt.toISOString().slice(0, 10);
            prevUsersTrendMap[date] = (prevUsersTrendMap[date] || 0) + 1;
        });
        const prevSignups = (Object.values(prevUsersTrendMap).reduce((a, b) => Number(a) + Number(b), 0)) as number;
        const currSignups = (usersTrend.reduce((a, b) => Number(a) + Number(b.count), 0)) as number;
        if (prevSignups > 0) {
            const percentChange = Math.round(1000 * (currSignups - prevSignups) / prevSignups) / 10;
            if (percentChange <= -50) {
                alerts.push({
                    type: 'user_signups_drop',
                    message: `User signups dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                    severity: 'critical',
                    tab: 'Users',
                    data: { currSignups, prevSignups, percentChange },
                });
            }
        }
        // --- Alerts: Spike in Guest Checkouts ---
        // Previous period guest users
        const prevGuestUserIdsRaw = await prisma.orders.findMany({ where: { ...prevFilter, user: { isVerified: false } }, select: { userId: true }, distinct: ['userId'] });
        const prevGuestUsersCount = prevGuestUserIdsRaw.map(u => u.userId).filter(Boolean).length;
        if (prevGuestUsersCount > 0) {
            const percentChange = Math.round(1000 * (guestUsersCount - prevGuestUsersCount) / prevGuestUsersCount) / 10;
            if (percentChange >= 200) {
                alerts.push({
                    type: 'guest_checkout_spike',
                    message: `Guest checkouts increased by ${percentChange}% compared to previous period!`,
                    severity: 'success',
                    tab: 'Users',
                    data: { guestUsersCount, prevGuestUsersCount, percentChange },
                });
            }
        }
    }
    return {
        usersTrend,
        returningUsers: { count: returningUsersCount, trend: returningUsersTrend },
        idpProviderStats,
        idpProviderTrend,
        userPoolStats,
        userPoolTrend,
        guestUsers: { count: guestUsersCount, guestToUserCount },
        localeStats,
        localeTrend,
        userSourceStats,
        userSourceTrend,
        dau,
        wau,
        topCustomers,
        topCustomersSpent,
        alerts,
    };
}

async function getCouponsTabData(prisma: PrismaClient, dateFilter: any) {
    // --- Coupons tab data ---
    let alerts = [];
    const couponOrderJoins = await prisma.coupons_orders.findMany({
        where: { ...dateFilter, state: "COMPLETED" },
        select: { couponsId: true, orderId: true },
    });
    const couponOrderIds = couponOrderJoins.map(c => Number(c.orderId)).filter(Boolean);
    const orderMap = couponOrderIds.length > 0 ? (await prisma.orders.findMany({
        where: { id: { in: couponOrderIds } },
        select: { id: true, userId: true, finalPrice: true, source: true, planId: true, markedJPYPrice: true, childOrders: true }
    }))
        .reduce((acc, o) => { acc[o.id] = o; return acc; }, {} as Record<number, any>) : {};
    const userIds = Object.values(orderMap).map(o => o.userId).filter(Boolean);
    const userMap = userIds.length > 0 ? (await prisma.users.findMany({ where: { id: { in: userIds } } })).reduce((acc, u) => { acc[u.id] = u; return acc; }, {} as Record<number, any>) : {};
    const couponIds = couponOrderJoins.map(c => Number(c.couponsId)).filter(Boolean);
    const couponMap = couponIds.length > 0 ? (await prisma.coupons.findMany({ where: { id: { in: couponIds } } })).reduce((acc, c) => { acc[c.id] = c; return acc; }, {} as Record<number, any>) : {};
    // 1. Most coupon-used users (by order's userId)
    const userCouponUsage: Record<number, number> = {};
    couponOrderJoins.forEach(co => {
        const order = orderMap[Number(co.orderId)];
        if (!order || !order.userId) return;
        userCouponUsage[order.userId] = (userCouponUsage[order.userId] || 0) + 1;
    });
    const mostCouponUsedUsers = Object.entries(userCouponUsage)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([userId, count]) => {
            const user = userMap[Number(userId)];
            return {
                userId: Number(userId),
                name: user ? `${user.firstName} ${user.lastName}` : '',
                email: user ? user.email : '',
                couponUsageCount: count,
            };
        });
    const topCouponCustomers = mostCouponUsedUsers;
    // 2. Total coupon usage
    const totalCouponUsage = couponOrderJoins.length;
    // 3. Total discount given (sum for all valid orders)
    let totalDiscountGiven = 0;
    for (const co of couponOrderJoins) {
        const coupon = couponMap[Number(co.couponsId)];
        const order = orderMap[Number(co.orderId)];
        if (!coupon || !order) continue;
        let discount = 0;
        discount = order.childOrders.reduce((sum, o) => sum + ((o.markedJPYPrice || 0) - (o.finalPrice || 0)), 0);
        totalDiscountGiven += discount;
    }
    const loss = totalDiscountGiven;
    // 4. Revenue impact of coupons
    const revenueWithCoupon = Object.values(orderMap).reduce((sum, o) => sum + (o.finalPrice || 0), 0);
    const allOrdersInRange = await prisma.orders.findMany({ where: { ...dateFilter, paymentStatus: PAYMENT_STATUS.SUCCESS }, select: { id: true, finalPrice: true } });
    const revenueWithoutCoupon = allOrdersInRange.filter(o => !couponOrderIds.includes(o.id)).reduce((sum, o) => sum + (o.finalPrice || 0), 0);
    const totalRevenue = revenueWithCoupon + revenueWithoutCoupon;
    const revenueImpact = {
        withCoupon: revenueWithCoupon,
        withoutCoupon: revenueWithoutCoupon,
        percentWithCoupon: totalRevenue > 0 ? Math.round(1000 * revenueWithCoupon / totalRevenue) / 10 : 0,
    };
    // 5. Coupons trend (usage per day)
    const couponOrders = await prisma.coupons_orders.findMany({ where: dateFilter, select: { createdAt: true } });
    const trendMap: Record<string, number> = {};
    couponOrders.forEach(c => {
        const date = c.createdAt.toISOString().slice(0, 10);
        trendMap[date] = (trendMap[date] || 0) + 1;
    });
    const couponsTrend = Object.entries(trendMap).map(([date, count]) => ({ date, count })).sort((a, b) => a.date.localeCompare(b.date));
    // 6. Top sources using coupons (from orders)
    const sourceAgg: Record<string, number> = {};
    couponOrderJoins.forEach(co => {
        const order = orderMap[Number(co.orderId)];
        const source = order?.source || 'Unknown';
        sourceAgg[source] = (sourceAgg[source] || 0) + 1;
    });
    const topSourcesWithCoupons = Object.entries(sourceAgg).map(([source, count]) => ({ source, count })).sort((a, b) => b.count - a.count).slice(0, 10);
    // 7. Coupons expiring soon (next 7 days)
    const now = new Date();
    const in7Days = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    let expiringSoonCoupons = await prisma.coupons.findMany({ where: { validTill: { gte: now, lte: in7Days } }, orderBy: { validTill: 'asc' } });
    expiringSoonCoupons = expiringSoonCoupons.filter(c => !(c.code?.startsWith('LY') || c.code.includes('KOREAFREECAMPAIGN'))).slice(0, 10);
    // 8. New coupons (last 30 days)
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    let newCoupons = await prisma.coupons.findMany({ where: { createdAt: { gte: last30Days } }, orderBy: { createdAt: 'desc' } });
    newCoupons = newCoupons.filter(c => !(c.code?.startsWith('LY') || c.code.includes('KOREAFREECAMPAIGN'))).slice(0, 10);
    // 9. Trending coupons (last 7 days vs previous 7 days)
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const prev7Days = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
    const trendingRaw = await prisma.coupons_orders.groupBy({ by: ['couponsId'], _count: { id: true }, where: { createdAt: { gte: prev7Days, lt: now } } });
    const trendingMap: Record<number, { prev: number; curr: number }> = {};
    trendingRaw.forEach(row => { trendingMap[Number(row.couponsId)] = { prev: row._count.id, curr: 0 }; });
    const trendingCurrRaw = await prisma.coupons_orders.groupBy({ by: ['couponsId'], _count: { id: true }, where: { createdAt: { gte: last7Days, lt: now } } });
    trendingCurrRaw.forEach(row => { if (!trendingMap[Number(row.couponsId)]) trendingMap[Number(row.couponsId)] = { prev: 0, curr: 0 }; trendingMap[Number(row.couponsId)].curr = row._count.id; });
    const trendingCoupons = Object.entries(trendingMap).map(([couponId, { prev, curr }]) => ({ couponId: Number(couponId), usageIncrease: curr - prev, percentChange: prev > 0 ? Math.round(1000 * (curr - prev) / prev) / 10 : null, })).filter(c => c.usageIncrease > 0).sort((a, b) => b.usageIncrease - a.usageIncrease).slice(0, 10);
    // 10. Popular plans with coupons
    const planAgg: Record<number, number> = {};
    couponOrderJoins.forEach(co => {
        const order = orderMap[Number(co.orderId)];
        if (!order?.planId) return;
        planAgg[order.planId] = (planAgg[order.planId] || 0) + 1;
    });
    const planIds = Object.keys(planAgg).map(Number);
    const planMap = planIds.length > 0 ? (await prisma.plans.findMany({ where: { id: { in: planIds } } })).reduce((acc, p) => { acc[p.id] = p; return acc; }, {} as Record<number, any>) : {};
    const popularPlansWithCoupons = Object.entries(planAgg).map(([planId, count]) => ({ planId: Number(planId), name: planMap[Number(planId)]?.name || '', couponUsageCount: count as number, })).sort((a, b) => b.couponUsageCount - a.couponUsageCount).slice(0, 10);
    // 11. Extra financial/accounting insights
    const avgDiscountPerOrder = totalCouponUsage > 0 ? Math.round(totalDiscountGiven / totalCouponUsage) : 0;
    const totalOrdersInRange = await prisma.orders.count({ where: dateFilter });
    const percentOrdersWithCoupons = totalOrdersInRange > 0 ? Math.round(1000 * totalCouponUsage / totalOrdersInRange) / 10 : 0;
    const usedCouponIds = couponIds.length > 0 ? couponIds : [];
    let unusedCoupons = await prisma.coupons.findMany({ where: { id: { notIn: usedCouponIds } } });
    unusedCoupons = unusedCoupons.slice(0, 10);
    const discountTypeAgg: Record<string, number> = {};
    Object.values(couponMap).forEach(coupon => { if (!coupon.type) return; discountTypeAgg[coupon.type] = (discountTypeAgg[coupon.type] || 0) + 1; });
    const topDiscountTypes = Object.entries(discountTypeAgg).map(([type, count]) => ({ type, count })).sort((a, b) => b.count - a.count);
    // Top coupons (by usage)
    const topCoupons = await prisma.coupons_orders.groupBy({ by: ['couponsId'], _count: { couponsId: true }, orderBy: { _count: { couponsId: 'desc' } }, take: 10, where: dateFilter });
    const couponIdsDetailed = topCoupons.map(c => c.couponsId);
    const coupons = couponIdsDetailed.length > 0 ? await prisma.coupons.findMany({ where: { id: { in: couponIdsDetailed } } }) : [];
    const topCouponsDetailed = topCoupons.map(c => { const coupon = coupons.find(cp => cp.id === c.couponsId); return { couponId: c.couponsId, code: coupon ? coupon.code : '', usageCount: c._count.couponsId, discountType: coupon ? coupon.type : '', discountValue: coupon ? coupon.discount : '', }; });
    // Most used coupons (trend: usage per day for top 3 coupons)
    let mostUsedCoupons = [];
    if (topCouponsDetailed.length > 0) {
        const top3 = topCouponsDetailed.slice(0, 3);
        for (const coupon of top3) {
            // Fetch all usages for this coupon in the date range
            const usages = await prisma.coupons_orders.findMany({
                where: { couponsId: coupon.couponId, ...dateFilter },
                select: { createdAt: true },
            });
            // Aggregate by date
            const trendMap: Record<string, number> = {};
            usages.forEach(u => {
                const date = u.createdAt.toISOString().slice(0, 10);
                trendMap[date] = (trendMap[date] || 0) + 1;
            });
            const trend = Object.entries(trendMap)
                .map(([date, count]) => ({ date, count }))
                .sort((a, b) => a.date.localeCompare(b.date));
            mostUsedCoupons.push({
                ...coupon,
                trend,
            });
        }
    }
    // --- New: Frequently Used High/Low Discount Coupons ---
    // Get all discount values from topCouponsDetailed
    const discountValues = topCouponsDetailed
        .map(c => typeof c.discountValue === 'number' ? c.discountValue : parseFloat(c.discountValue))
        .filter(v => !isNaN(v))
        .sort((a, b) => a - b);
    const quantileIdx = Math.floor(discountValues.length * 0.2);
    const lowThreshold = discountValues[quantileIdx] ?? 0;
    const highThreshold = discountValues[discountValues.length - quantileIdx - 1] ?? 0;
    // Low discount: discountValue <= lowThreshold
    const frequentlyUsedLowDiscountCoupons = topCouponsDetailed
        .filter(c => {
            const v = typeof c.discountValue === 'number' ? c.discountValue : parseFloat(c.discountValue);
            return !isNaN(v) && v <= lowThreshold;
        })
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, 10);
    // High discount: discountValue >= highThreshold
    const frequentlyUsedHighDiscountCoupons = topCouponsDetailed
        .filter(c => {
            const v = typeof c.discountValue === 'number' ? c.discountValue : parseFloat(c.discountValue);
            return !isNaN(v) && v >= highThreshold;
        })
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, 10);
    // --- Coupon Abuse Alert ---
    const since24h = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentCouponOrders = await prisma.coupons_orders.findMany({ where: { createdAt: { gte: since24h }, state: "COMPLETED" }, select: { couponsId: true, orderId: true } });
    const recentOrderIds = recentCouponOrders.map(c => Number(c.orderId)).filter(Boolean);
    const recentOrders = recentOrderIds.length > 0 ? await prisma.orders.findMany({ where: { id: { in: recentOrderIds } }, select: { id: true, userId: true } }) : [];
    const abuseMap = {};
    recentCouponOrders.forEach(co => {
        const order = recentOrders.find(o => o.id === Number(co.orderId));
        if (!order || !order.userId) return;
        const key = `${order.userId}_${co.couponsId}`;
        abuseMap[key] = (abuseMap[key] || 0) + 1;
    });
    Object.entries(abuseMap).forEach(([key, count]) => {
        if ((count as number) >= 10) {
            const [userId, couponsId] = key.split('_');
            alerts.push({
                type: 'coupon_abuse',
                message: `User ${userId} used coupon ${couponsId} ${count} times in the last 24h!`,
                severity: 'critical',
                tab: 'Coupons',
                data: { userId, couponsId, count },
            });
        }
    });
    // --- High Discount Coupon Usage Spike Alert ---
    // Current period high discount coupon usage
    const currHighDiscountUsage = Number(topCouponsDetailed
        .filter(c => {
            const v = typeof c.discountValue === 'number' ? c.discountValue : parseFloat(c.discountValue);
            return !isNaN(v) && v >= highThreshold;
        })
        .reduce((sum, c) => sum + c.usageCount, 0));
    // Previous period high discount coupon usage
    if (dateFilter.createdAt?.gte && dateFilter.createdAt?.lte) {
        const start = new Date(dateFilter.createdAt.gte);
        const end = new Date(dateFilter.createdAt.lte);
        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
        const prevStart = new Date(start);
        prevStart.setDate(start.getDate() - days);
        const prevEnd = new Date(end);
        prevEnd.setDate(end.getDate() - days);
        const prevFilter = { ...dateFilter, createdAt: { gte: prevStart, lte: prevEnd } };
        const prevCouponOrders = await prisma.coupons_orders.findMany({ where: { ...prevFilter, state: "COMPLETED" }, select: { couponsId: true } });
        const prevCouponIds = prevCouponOrders.map(c => Number(c.couponsId)).filter(Boolean);
        const prevCoupons = prevCouponIds.length > 0 ? await prisma.coupons.findMany({ where: { id: { in: prevCouponIds } } }) : [];
        const prevHighDiscountIds = prevCoupons.filter(c => {
            const v = typeof c.discount === 'number' ? c.discount : parseFloat(c.discount);
            return !isNaN(v) && v >= highThreshold;
        }).map(c => c.id);
        const prevHighDiscountUsage = Number(prevCouponOrders.filter(c => prevHighDiscountIds.includes(Number(c.couponsId))).length);
        const prevAvg = days > 0 ? prevHighDiscountUsage / days : 0;
        if (prevAvg > 0 && currHighDiscountUsage / days >= 3 * prevAvg) {
            alerts.push({
                type: 'high_discount_coupon_spike',
                message: `High discount coupons used 3x more than average in this period!`,
                severity: 'success',
                tab: 'Coupons',
                data: { currHighDiscountUsage, prevHighDiscountUsage, prevAvg, days },
            });
        }
        // --- Expiring Soon Alert ---
        const now = new Date();
        const in2Days = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000);
        let expiringSoonCoupons = await prisma.coupons.findMany({ where: { validTill: { gte: now, lte: in2Days } }, orderBy: { validTill: 'asc' } });
        expiringSoonCoupons = expiringSoonCoupons.filter(c => !(c.code?.startsWith('LY') || c.code.includes('KOREAFREECAMPAIGN')));
        // Count usages for each expiring coupon
        const expiringCouponIds = expiringSoonCoupons.map(c => c.id);
        const expiringCouponUsage = expiringCouponIds.length > 0 ? await prisma.coupons_orders.groupBy({ by: ['couponsId'], _count: { couponsId: true }, where: { couponsId: { in: expiringCouponIds }, state: "COMPLETED" } }) : [];
        const expiringWithUsage = expiringCouponUsage.filter(c => Number(c._count.couponsId) > 50);
        if (typeof expiringWithUsage.length === 'number' && expiringWithUsage.length >= 5) {
            alerts.push({
                type: 'expiring_soon',
                message: `${expiringWithUsage.length} coupons are expiring in the next 2 days with >50 usages each!`,
                severity: 'warning',
                tab: 'Coupons',
                data: { expiringWithUsage },
            });
        }
    }
    return {
        totalCouponUsage,
        mostCouponUsedUsers,
        totalDiscountGiven,
        loss,
        couponsTrend,
        topSourcesWithCoupons,
        expiringSoonCoupons,
        newCoupons,
        trendingCoupons,
        popularPlansWithCoupons,
        avgDiscountPerOrder,
        percentOrdersWithCoupons,
        unusedCoupons,
        topDiscountTypes,
        topCoupons: topCouponsDetailed,
        mostUsedCoupons,
        revenueImpact,
        topCouponCustomers,
        frequentlyUsedLowDiscountCoupons,
        frequentlyUsedHighDiscountCoupons,
        alerts,
    };
}

async function getPlansTabData(prisma: PrismaClient, dateFilter: any) {
    // --- Plans tab data ---
    const successFilter = { ...dateFilter, paymentStatus: PAYMENT_STATUS.SUCCESS };
    // Top 10 plans by order count (successful only)
    const topPlansRaw = await prisma.orders.groupBy({ by: ['planId'], _count: { planId: true }, _sum: { finalPrice: true }, orderBy: { _count: { planId: 'desc' } }, take: 10, where: successFilter });
    const planIds = topPlansRaw.map(p => p.planId).filter(Boolean);
    const plans = planIds.length > 0 ? await prisma.plans.findMany({ where: { id: { in: planIds } }, include: { country: true, serviceProvider: true } }) : [];
    const topPlans = topPlansRaw.map(p => {
        const plan = plans.find(pl => pl.id === p.planId);
        return {
            planId: p.planId,
            name: plan ? plan.name : '',
            country: plan?.country?.name || '',
            serviceProvider: plan?.serviceProvider?.name || '',
            orderCount: p._count.planId,
            revenue: p._sum.finalPrice || 0,
            validityDays: plan?.validityDays ?? null,
        };
    });
    // Top 10 countries by plan orders
    const topCountriesRaw = await prisma.orders.groupBy({ by: ['planId'], _count: { planId: true }, _sum: { finalPrice: true }, where: successFilter });
    const planCountryMap = plans.reduce((acc, plan) => { acc[plan.id] = plan.country?.name || 'Unknown'; return acc; }, {} as Record<number, string>);
    const countryAgg: Record<string, { country: string; orderCount: number; revenue: number }> = {};
    topCountriesRaw.forEach(p => {
        const country = planCountryMap[p.planId] || 'Unknown';
        if (!countryAgg[country]) countryAgg[country] = { country, orderCount: 0, revenue: 0 };
        countryAgg[country].orderCount += p._count.planId;
        countryAgg[country].revenue += p._sum.finalPrice || 0;
    });
    const topCountries = Object.values(countryAgg).sort((a, b) => b.orderCount - a.orderCount).slice(0, 10);
    // Top 10 service providers by plan orders
    const planProviderMap = plans.reduce((acc, plan) => { acc[plan.id] = plan.serviceProvider?.name || 'Unknown'; return acc; }, {} as Record<number, string>);
    const providerAgg: Record<string, { serviceProvider: string; orderCount: number; revenue: number }> = {};
    topPlansRaw.forEach(p => {
        const provider = planProviderMap[p.planId] || 'Unknown';
        if (!providerAgg[provider]) providerAgg[provider] = { serviceProvider: provider, orderCount: 0, revenue: 0 };
        providerAgg[provider].orderCount += p._count.planId;
        providerAgg[provider].revenue += p._sum.finalPrice || 0;
    });
    const topProviders = Object.values(providerAgg).sort((a, b) => b.orderCount - a.orderCount).slice(0, 10);
    // Plan orders trend by country (orders per country per day)
    const planOrdersTrendRaw = await prisma.orders.findMany({ where: successFilter, select: { createdAt: true, planId: true } });
    const planCountryTrendMap = {};
    planOrdersTrendRaw.forEach(o => {
        const date = o.createdAt.toISOString().slice(0, 10);
        const country = planCountryMap[o.planId] || 'Unknown';
        const key = `${date}__${country}`;
        planCountryTrendMap[key] = (planCountryTrendMap[key] || 0) + 1;
    });
    const planOrdersTrend: { date: string; country: string; count: number }[] = Object.entries(planCountryTrendMap).map(([key, count]) => {
        const [date, country] = key.split('__');
        return { date, country, count: count as number };
    });
    // Plan segment popularity (by dataVolume, validityDays, packageType)
    const planSegmentsRaw = planIds.length > 0 ? await prisma.plans.findMany({ where: { id: { in: planIds } }, select: { id: true, dataVolume: true, validityDays: true, packageType: true } }) : [];
    const segmentAgg: Record<string, { dataVolume: string; validityDays: number | null; packageType: string; orderCount: number }> = {};
    topPlansRaw.forEach(p => {
        const seg = planSegmentsRaw.find(s => s.id === p.planId);
        if (!seg) return;
        const key = `${seg.dataVolume || ''}_${seg.validityDays || ''}_${seg.packageType || ''}`;
        if (!segmentAgg[key]) segmentAgg[key] = { dataVolume: seg.dataVolume, validityDays: seg.validityDays, packageType: seg.packageType, orderCount: 0 };
        segmentAgg[key].orderCount += p._count.planId;
    });
    const planSegments = Object.values(segmentAgg).sort((a, b) => b.orderCount - a.orderCount);
    // Top 10 plans by coupon usage
    const topPlanCouponsRaw = await prisma.coupons_orders.groupBy({ by: ['orderId'], _count: { orderId: true }, orderBy: { _count: { orderId: 'desc' } }, take: 100, where: dateFilter });
    const orderIds = topPlanCouponsRaw.map(c => c.orderId).filter(Boolean);
    const couponOrders = orderIds.length > 0 ? await prisma.orders.findMany({ where: { id: { in: orderIds } }, select: { id: true, planId: true } }) : [];
    const couponPlanAgg: Record<number, number> = {};
    couponOrders.forEach(o => { couponPlanAgg[o.planId] = (couponPlanAgg[o.planId] || 0) + 1; });
    const topPlanCoupons = Object.entries(couponPlanAgg).map(([planId, count]) => {
        const plan = plans.find(pl => pl.id === Number(planId));
        return { planId: Number(planId), name: plan?.name || '', couponUsageCount: Number(count) };
    }).sort((a, b) => b.couponUsageCount - a.couponUsageCount).slice(0, 10);
    // Price bands: cheap/popular/expensive plans (by price quantiles)
    const allPlanPrices = plans.map(p => p.price).filter(p => typeof p === 'number') as number[];
    allPlanPrices.sort((a, b) => a - b);
    const quantile = (q: number) => allPlanPrices.length ? allPlanPrices[Math.floor(q * (allPlanPrices.length - 1))] : 0;
    const priceBands = {
        cheap: quantile(0.2),
        popular: quantile(0.5),
        expensive: quantile(0.8),
    };
    // Emerging/consistent countries (simple version: compare first and last 7 days in range)
    let emergingCountries: { country: string; growth: number; first: number; last: number }[] = [], consistentCountries: { country: string; growth: number; first: number; last: number }[] = [];
    if (planOrdersTrend.length > 14) {
        const byCountry: Record<string, { date: string; count: number }[]> = {};
        planOrdersTrend.forEach(row => {
            if (!byCountry[row.country]) byCountry[row.country] = [];
            byCountry[row.country].push({ date: row.date, count: row.count });
        });
        const growth = Object.entries(byCountry).map(([country, arr]) => {
            const sorted = arr.sort((a, b) => a.date.localeCompare(b.date));
            const first = sorted.slice(0, 7).reduce((sum, r) => sum + r.count, 0);
            const last = sorted.slice(-7).reduce((sum, r) => sum + r.count, 0);
            return { country, growth: last - first, first, last };
        });
        emergingCountries = growth.filter(g => g.growth > 3 && g.last > 0).sort((a, b) => b.growth - a.growth).slice(0, 5);
        consistentCountries = growth.filter(g => Math.abs(g.growth) < 3 && g.first > 0 && g.last > 0).sort((a, b) => b.first - a.first).slice(0, 5);
    }
    // --- New: Frequently Bought Cheap/Expensive Plans ---
    // Find all plans in cheap and expensive price bands
    const CHEAP_THRESHOLD = priceBands.cheap;
    const EXPENSIVE_THRESHOLD = priceBands.expensive;
    // Get all plans in the topPlans list with their price
    const topPlansWithPrice = topPlansRaw.map(p => {
        const plan = plans.find(pl => pl.id === p.planId);
        return {
            planId: p.planId,
            name: plan ? plan.name : '',
            country: plan?.country?.name || '',
            serviceProvider: plan?.serviceProvider?.name || '',
            validityDays: plan?.validityDays ?? null,
            orderCount: p._count.planId,
            price: plan?.price ?? null,
        };
    });
    // Cheap plans: price <= CHEAP_THRESHOLD
    const frequentlyBoughtCheapPlans = topPlansWithPrice
        .filter(p => typeof p.price === 'number' && p.price <= CHEAP_THRESHOLD)
        .sort((a, b) => b.orderCount - a.orderCount)
        .slice(0, 10);
    // Expensive plans: price >= EXPENSIVE_THRESHOLD
    const frequentlyBoughtExpensivePlans = topPlansWithPrice
        .filter(p => typeof p.price === 'number' && p.price >= EXPENSIVE_THRESHOLD)
        .sort((a, b) => b.orderCount - a.orderCount)
        .slice(0, 10);
    // Frequently bought plans (top 10 by order count, regardless of price)
    const frequentlyBoughtPlans = topPlansWithPrice
        .sort((a, b) => b.orderCount - a.orderCount)
        .slice(0, 10);

    // --- ALERTS LOGIC ---
    let alerts = [];
    // Only run alerts if date range is valid
    if (dateFilter.createdAt?.gte && dateFilter.createdAt?.lte) {
        const start = new Date(dateFilter.createdAt.gte);
        const end = new Date(dateFilter.createdAt.lte);
        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
        const prevStart = new Date(start);
        prevStart.setDate(start.getDate() - days);
        const prevEnd = new Date(end);
        prevEnd.setDate(end.getDate() - days);
        const prevFilter = { ...dateFilter, createdAt: { gte: prevStart, lte: prevEnd } };
        // --- 1. Plan Outlier Alert ---
        // Outlier: plan with orderCount > 2x median of top 10, or < 0.5x median (if orderCount >= 10)
        const orderCounts = topPlans.map(p => p.orderCount).sort((a, b) => a - b);
        const median = orderCounts.length ? orderCounts[Math.floor(orderCounts.length / 2)] : 0;
        topPlans.forEach(plan => {
            if (typeof plan.orderCount === 'number' && median > 0 && plan.orderCount >= 10 && plan.orderCount > 2 * median) {
                alerts.push({
                    type: 'plan_outlier_high',
                    message: `Plan '${plan.name}' has unusually high orders (${plan.orderCount}, >2x median ${median})`,
                    severity: 'success',
                    tab: 'Plans',
                    data: { plan, median },
                });
            } else if (typeof plan.orderCount === 'number' && median > 0 && plan.orderCount >= 10 && plan.orderCount < 0.5 * median) {
                alerts.push({
                    type: 'plan_outlier_low',
                    message: `Plan '${plan.name}' has unusually low orders (${plan.orderCount}, <0.5x median ${median})`,
                    severity: 'warning',
                    tab: 'Plans',
                    data: { plan, median },
                });
            }
        });
        // --- 2. Country/Provider Anomaly Alert ---
        // Compare topCountries and topProviders to previous period
        const prevOrdersRaw = await prisma.orders.findMany({ where: { ...prevFilter, paymentStatus: PAYMENT_STATUS.SUCCESS }, select: { planId: true } });
        const prevPlanIds = prevOrdersRaw.map(o => o.planId).filter(Boolean);
        const prevPlans = prevPlanIds.length > 0 ? await prisma.plans.findMany({ where: { id: { in: prevPlanIds } }, include: { country: true, serviceProvider: true } }) : [];
        // Country
        const prevCountryAgg = {};
        prevPlans.forEach(plan => {
            const country = plan.country?.name || 'Unknown';
            prevCountryAgg[country] = (prevCountryAgg[country] || 0) + 1;
        });
        topCountries.forEach(c => {
            const prev = prevCountryAgg[c.country] || 0;
            if (prev > 0) {
                const percentChange = Math.round(1000 * (c.orderCount - prev) / prev) / 10;
                if (percentChange >= 50) {
                    alerts.push({
                        type: 'country_surge',
                        message: `Orders for country '${c.country}' increased by ${percentChange}% compared to previous period`,
                        severity: 'success',
                        tab: 'Plans',
                        data: { country: c.country, curr: c.orderCount, prev, percentChange },
                    });
                } else if (percentChange <= -50) {
                    alerts.push({
                        type: 'country_drop',
                        message: `Orders for country '${c.country}' dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                        severity: 'critical',
                        tab: 'Plans',
                        data: { country: c.country, curr: c.orderCount, prev, percentChange },
                    });
                }
            }
        });
        // Provider
        const prevProviderAgg = {};
        prevPlans.forEach(plan => {
            const provider = plan.serviceProvider?.name || 'Unknown';
            prevProviderAgg[provider] = (prevProviderAgg[provider] || 0) + 1;
        });
        topProviders.forEach(p => {
            const prev = prevProviderAgg[p.serviceProvider] || 0;
            if (prev > 0) {
                const percentChange = Math.round(1000 * (p.orderCount - prev) / prev) / 10;
                if (percentChange >= 50) {
                    alerts.push({
                        type: 'provider_surge',
                        message: `Orders for provider '${p.serviceProvider}' increased by ${percentChange}% compared to previous period`,
                        severity: 'success',
                        tab: 'Plans',
                        data: { provider: p.serviceProvider, curr: p.orderCount, prev, percentChange },
                    });
                } else if (percentChange <= -50) {
                    alerts.push({
                        type: 'provider_drop',
                        message: `Orders for provider '${p.serviceProvider}' dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                        severity: 'critical',
                        tab: 'Plans',
                        data: { provider: p.serviceProvider, curr: p.orderCount, prev, percentChange },
                    });
                }
            }
        });
        // --- 3. Price Band Shift Alert ---
        // Compare distribution of orders among cheap, popular, expensive plans to previous period
        // Current period
        const currBandCounts = { cheap: 0, popular: 0, expensive: 0 };
        topPlansWithPrice.forEach(p => {
            if (typeof p.price === 'number') {
                if (p.price <= CHEAP_THRESHOLD) currBandCounts.cheap += p.orderCount;
                else if (p.price >= EXPENSIVE_THRESHOLD) currBandCounts.expensive += p.orderCount;
                else currBandCounts.popular += p.orderCount;
            }
        });
        // Previous period: get all plans and their prices
        const prevPlanObjs = prevPlanIds.length > 0 ? await prisma.plans.findMany({ where: { id: { in: prevPlanIds } } }) : [];
        const prevBandCounts = { cheap: 0, popular: 0, expensive: 0 };
        prevOrdersRaw.forEach(o => {
            const plan = prevPlanObjs.find(pl => pl.id === o.planId);
            if (!plan || typeof plan.price !== 'number') return;
            if (plan.price <= CHEAP_THRESHOLD) prevBandCounts.cheap += 1;
            else if (plan.price >= EXPENSIVE_THRESHOLD) prevBandCounts.expensive += 1;
            else prevBandCounts.popular += 1;
        });
        // For each band, if >50% increase or decrease
        ['cheap', 'popular', 'expensive'].forEach(band => {
            const curr = currBandCounts[band];
            const prev = prevBandCounts[band];
            if (prev > 0) {
                const percentChange = Math.round(1000 * (curr - prev) / prev) / 10;
                if (percentChange >= 50) {
                    alerts.push({
                        type: `price_band_${band}_surge`,
                        message: `Orders for ${band} plans increased by ${percentChange}% compared to previous period`,
                        severity: 'success',
                        tab: 'Plans',
                        data: { band, curr, prev, percentChange },
                    });
                } else if (percentChange <= -50) {
                    alerts.push({
                        type: `price_band_${band}_drop`,
                        message: `Orders for ${band} plans dropped by ${Math.abs(percentChange)}% compared to previous period!`,
                        severity: 'critical',
                        tab: 'Plans',
                        data: { band, curr, prev, percentChange },
                    });
                }
            }
        });
    }
    // --- END ALERTS ---
    return {
        topPlans,
        topCountries,
        topProviders,
        planOrdersTrend,
        planSegments,
        topPlanCoupons,
        priceBands,
        emergingCountries,
        consistentCountries,
        frequentlyBoughtCheapPlans,
        frequentlyBoughtExpensivePlans,
        frequentlyBoughtPlans,
        alerts,
    };
}

// --- Main Handler ---
export async function dashboardHandler(req, res, context, redisClient: Redis, prisma: PrismaClient) {
    // Parse params
    const startDate = req?.query?.startDate;
    const endDate = req?.query?.endDate;
    const tab = req?.query?.tab;
    const dateFilter = parseDateFilter(startDate, endDate);
    const CACHE_KEY = `adminjs:dashboard:${tab || 'all'}:${startDate || 'all'}:${endDate || 'all'}`;
    const CACHE_TTL = 60 * 60; // 1 hour
    const isDev = process.env.NODE_ENV === 'development';

    // Check cache
    if (!isDev) {
        const cached = await getFromCache(redisClient, CACHE_KEY);
        if (cached) return cached;
    }

    // Compute data
    let dashboardData = {};
    if (!tab || tab === 'Orders') {
        dashboardData = await getOrdersTabData(prisma, dateFilter);
    } else if (tab === 'Users') {
        dashboardData = await getUsersTabData(prisma, dateFilter);
    } else if (tab === 'Coupons') {
        dashboardData = await getCouponsTabData(prisma, dateFilter);
    } else if (tab === 'Plans') {
        dashboardData = await getPlansTabData(prisma, dateFilter);
    }

    // Cache result
    if (!isDev) {
        await setToCache(redisClient, CACHE_KEY, dashboardData, CACHE_TTL);
    }
    return dashboardData;
} 