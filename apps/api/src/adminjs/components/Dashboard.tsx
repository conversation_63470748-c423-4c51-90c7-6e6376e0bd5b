/*
  AdminJS Business Dashboard - Reference Overview

  This dashboard provides a comprehensive analytics view for the multi-service eSIM platform, designed for AdminJS (React) and powered by a NestJS backend.

  Key Features:
  - Global date range filter (default: last 7 days, with quick-selects)
  - Core metrics: total users, total orders, orders by app
  - Payment analytics: top payment methods, orders by day (stacked by payment method)
  - Source & Affiliate analytics:
      - Orders by Source/Affiliate Over Time (stacked bar)
      - Top Sources & Affiliates (pie + fallback table)
      - Revenue by Source/Affiliate
      - Conversion Rate by Source/Affiliate
  - Customer analytics: top customers by order count and total spent
  - Coupon analytics: top coupons, coupon usage trends
  - Responsive, modern UI using Chart.js (react-chartjs-2)
  - Info tooltips clarify the meaning of "source" (internal platform) and "affiliate" (external referrer)
  - All analytics only consider orders with paymentStatus === 'SUCCESS' as successful
  - Modularized AdminJS setup (see adminjs.module.ts)

  Implementation Notes:
  - Data is fetched from a custom AdminJS dashboard handler (see backend)
  - All metrics are filtered by the selected date range
  - Redis is used for backend caching
  - If a chart has no data, a fallback table or message is shown
  - The dashboard is grouped into Orders, Users, and Coupons sections for clarity

  For further details, see the backend handler and adminjs.module.ts.
*/
// @ts-nocheck
import { ApiClient } from 'adminjs';
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import React, { useEffect, useState } from 'react';
import { Bar, Line, Pie } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
);

const COLORS = [
  'rgba(54, 162, 235, 0.7)',
  'rgba(255, 99, 132, 0.7)',
  'rgba(255, 206, 86, 0.7)',
  'rgba(75, 192, 192, 0.7)',
  'rgba(153, 102, 255, 0.7)',
  'rgba(255, 159, 64, 0.7)',
];

const sectionStyle = {
  margin: '48px 0',
  padding: '40px 32px',
  borderRadius: 18,
  background: '#fff',
  boxShadow: '0 4px 24px 0 rgba(0,0,0,0.07)',
  border: '1px solid #e0e0e0',
};
const cardStyle = {
  background: '#f8fafd',
  padding: 32,
  borderRadius: 18,
  minWidth: 180,
  textAlign: 'center',
  boxShadow: '0 2px 12px 0 rgba(0,0,0,0.06)',
  marginBottom: 24,
  marginRight: 24,
  border: '1px solid #e0e0e0',
};
const tableStyle = {
  width: '100%',
  maxWidth: 800,
  borderCollapse: 'separate',
  borderSpacing: 0,
  background: '#f8fafd',
  borderRadius: 14,
  boxShadow: '0 2px 12px 0 rgba(0,0,0,0.04)',
  overflow: 'hidden',
  margin: '24px 0',
  border: '1px solid #e0e0e0',
};
const thStyle = {
  background: '#f1f3f6',
  padding: '16px 20px',
  fontWeight: 700,
  borderBottom: '1px solid #e0e0e0',
  textAlign: 'left',
  fontSize: 16,
};
const tdStyle = {
  padding: '16px 20px',
  borderBottom: '1px solid #f0f0f0',
  background: '#f8fafd',
  fontSize: 15,
};
const sectionHeaderStyle = {
  fontSize: 24,
  fontWeight: 800,
  marginBottom: 32,
  display: 'flex',
  alignItems: 'center',
  gap: 14,
  color: '#1a237e',
  letterSpacing: -0.5,
};
const dividerStyle = {
  height: 2,
  background: '#e0e0e0',
  margin: '48px 0',
  border: 'none',
  borderRadius: 1,
};

// Helper icon component
const InfoIcon = ({ text }) => (
  <span
    style={{
      display: 'inline-block',
      marginLeft: 6,
      color: '#888',
      cursor: 'pointer',
      fontSize: 16,
      borderRadius: '50%',
      border: '1px solid #bbb',
      width: 18,
      height: 18,
      textAlign: 'center',
      lineHeight: '16px',
      fontWeight: 700,
      background: '#f7f7f7',
    }}
    title={text}
  >
    i
  </span>
);

const TAB_ORDERS = 'Orders';
const TAB_USERS = 'Users';
const TAB_COUPONS = 'Coupons';
const TAB_PLANS = 'Plans';
const TABS = [TAB_ORDERS, TAB_USERS, TAB_COUPONS, TAB_PLANS];

// Helper: Use code from backend if available, fallback to name mapping if not
const flagImg = (country: string, code?: string) => {
  const cdnCode = (code || '').toLowerCase();
  if (!cdnCode) return null;
  return (
    <img
      src={`https://cdn.gmobile.biz/global-esim/assets/flags/16x16/${cdnCode}.png`}
      alt={country}
      style={{
        width: 18,
        height: 18,
        borderRadius: 3,
        marginRight: 8,
        verticalAlign: 'middle',
        boxShadow: '0 1px 2px #bbb',
      }}
      loading="lazy"
    />
  );
};

// Enhanced table style
const enhancedTableStyle = {
  ...tableStyle,
  borderRadius: 12,
  overflow: 'hidden',
  background: '#f9fbfd',
  boxShadow: '0 2px 12px 0 rgba(0,0,0,0.04)',
  marginBottom: 0,
};

const enhancedSectionStyle = {
  ...sectionStyle,
  background: '#f6f8fc',
  border: '1px solid #e3e7ef',
  boxShadow: '0 4px 24px 0 rgba(0,0,0,0.04)',
  marginBottom: 48,
};

const rowHoverStyle = {
  transition: 'background 0.2s',
  cursor: 'pointer',
};

const topPlansTableCellStyle = {
  padding: '10px 18px',
  fontSize: 16,
  verticalAlign: 'middle',
  background: 'transparent',
  minWidth: 0,
  border: 'none',
};
const topPlansHeaderCellStyle = {
  ...topPlansTableCellStyle,
  fontWeight: 700,
  fontSize: 17,
  color: '#1a237e',
  background: 'transparent',
  minWidth: 90,
  borderBottom: '1.5px solid #e3e7ef',
};
const topPlansRowStyle = {
  height: 48,
  transition: 'background 0.18s',
  cursor: 'pointer',
};
const topPlansRowHoverStyle = {
  background: '#f1f4fa',
};
const topPlansCountryCellStyle = {
  ...topPlansTableCellStyle,
  minWidth: 120,
  display: 'flex',
  alignItems: 'center',
  gap: 8,
};
const topPlansPlanCellStyle = {
  ...topPlansTableCellStyle,
  minWidth: 110,
};
const topPlansProviderCellStyle = {
  ...topPlansTableCellStyle,
  minWidth: 110,
};
const topPlansNumberCellStyle = {
  ...topPlansTableCellStyle,
  textAlign: 'right',
  fontFamily: 'Menlo, Monaco, Consolas, monospace',
  minWidth: 80,
};

const Dashboard = () => {
  const api = new ApiClient();

  // Per-tab data and loading states
  const [tabData, setTabData] = useState({}); // { [tab]: { [rangeKey]: data } }
  const [tabLoading, setTabLoading] = useState({}); // { [tab]: boolean }
  const [tabError, setTabError] = useState({}); // { [tab]: error }

  const today = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(today.getDate() - 6); // inclusive of today
  const formatDate = (d) => d.toISOString().slice(0, 10);

  const [startDate, setStartDate] = useState(formatDate(sevenDaysAgo));
  const [endDate, setEndDate] = useState(formatDate(today));
  const [appliedRange, setAppliedRange] = useState({
    start: formatDate(sevenDaysAgo),
    end: formatDate(today),
  });

  const [activeTab, setActiveTab] = useState(TAB_ORDERS);

  // Helper to get a unique key for the date range
  const getRangeKey = (range) => `${range.start || ''}_${range.end || ''}`;

  // Fetch data for a tab and date range
  const fetchTabData = (tab, range) => {
    const rangeKey = getRangeKey(range);
    setTabLoading((prev) => ({ ...prev, [tab]: true }));
    setTabError((prev) => ({ ...prev, [tab]: null }));
    const params = { tab };
    if (range.start) params.startDate = range.start;
    if (range.end) params.endDate = range.end;
    api
      .getDashboard({ params })
      .then((res) => {
        setTabData((prev) => ({
          ...prev,
          [tab]: { ...(prev[tab] || {}), [rangeKey]: res.data },
        }));
        setTabLoading((prev) => ({ ...prev, [tab]: false }));
      })
      .catch((err) => {
        setTabError((prev) => ({ ...prev, [tab]: err }));
        setTabLoading((prev) => ({ ...prev, [tab]: false }));
      });
  };

  // On mount, load the default tab's data
  useEffect(() => {
    fetchTabData(activeTab, {
      start: formatDate(sevenDaysAgo),
      end: formatDate(today),
    });
    setAppliedRange({
      start: formatDate(sevenDaysAgo),
      end: formatDate(today),
    });
    // eslint-disable-next-line
  }, []);

  // When tab changes, load data if not already loaded for this range
  useEffect(() => {
    const rangeKey = getRangeKey(appliedRange);
    if (!tabData[activeTab] || !tabData[activeTab][rangeKey]) {
      fetchTabData(activeTab, appliedRange);
    }
    // eslint-disable-next-line
  }, [activeTab]);

  // When date range changes, reload data for current tab
  const handleFilter = () => {
    const range = { start: startDate, end: endDate };
    setAppliedRange(range);
    fetchTabData(activeTab, range);
  };

  // Quick-select date range handlers
  const setRange = (type) => {
    const today = new Date();
    let start, end;
    if (type === '7d') {
      const s = new Date();
      s.setDate(today.getDate() - 6);
      start = formatDate(s);
      end = formatDate(today);
    } else if (type === '30d') {
      const s = new Date();
      s.setDate(today.getDate() - 29);
      start = formatDate(s);
      end = formatDate(today);
    } else if (type === 'thisMonth') {
      const s = new Date(today.getFullYear(), today.getMonth(), 1);
      start = formatDate(s);
      end = formatDate(today);
    } else if (type === 'lastMonth') {
      const firstDayLastMonth = new Date(
        today.getFullYear(),
        today.getMonth() - 1,
        1,
      );
      const lastDayLastMonth = new Date(
        today.getFullYear(),
        today.getMonth(),
        0,
      );
      start = formatDate(firstDayLastMonth);
      end = formatDate(lastDayLastMonth);
    } else if (type === 'all') {
      start = '';
      end = '';
    }
    setStartDate(start || '');
    setEndDate(end || '');
    setAppliedRange({ start: start || '', end: end || '' });
    fetchTabData(activeTab, { start: start || '', end: end || '' });
  };

  // Get current tab's data, loading, error
  const rangeKey = getRangeKey(appliedRange);
  const data = tabData[activeTab]?.[rangeKey] || null;
  const loading = !!tabLoading[activeTab];
  const error = tabError[activeTab];

  // --- Statistics ---
  const stats = [
    { label: 'Total Users', value: data?.totalUsers },
    { label: 'Total Orders', value: data?.totalOrders },
  ];
  const appStats =
    data?.ordersByAppId?.map((a) => ({
      label: `App ID: ${a.appId ?? 'N/A'}`,
      value: a.count,
    })) || [];

  // --- Payment Methods ---
  const paymentPieData = data?.topPaymentMethods?.length
    ? {
        labels: data.topPaymentMethods.map((m) => m.paymentMethod || 'Unknown'),
        datasets: [
          {
            data: data.topPaymentMethods.map((m) => m._count.paymentMethod),
            backgroundColor: COLORS,
          },
        ],
      }
    : null;

  // --- Orders by Day (Stacked Bar) ---
  let ordersByDayChart = null;
  if (data && data.ordersByDay && data.ordersByDay.length > 0) {
    const labels = data.ordersByDay.map((row) => row.date);
    const paymentMethods = Object.keys(data.ordersByDay[0]).filter(
      (k) => k !== 'date',
    );
    const datasets = paymentMethods.map((method, idx) => ({
      label: method,
      data: data.ordersByDay.map((row) => row[method] || 0),
      backgroundColor: COLORS[idx % COLORS.length],
      borderRadius: 6,
      maxBarThickness: 32,
    }));
    ordersByDayChart = { labels, datasets };
  }

  // --- Customers ---
  const topCustomers = data?.topCustomers || [];
  const topCustomersSpent = data?.topCustomersSpent || [];

  // --- Coupons ---
  const topCoupons = data?.topCoupons || [];
  const mostUsedCoupons = data?.mostUsedCoupons || [];

  const revenueImpact = data?.revenueImpact || null;
  const topCouponCustomers = data?.topCouponCustomers || [];

  // At the top of the Dashboard component, after other useState hooks:
  const [showAffiliates, setShowAffiliates] = useState(true);

  // Add this useEffect to auto-collapse if more than 3 affiliate alerts
  useEffect(() => {
    if (data?.alerts) {
      const affiliateAlerts = data.alerts.filter(
        (a) => a.type && a.type.startsWith('order_affiliate_'),
      );
      setShowAffiliates(affiliateAlerts.length <= 3);
    }
    // Only run when alerts change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.alerts]);

  return (
    <>
      {/* Sticky Date Range Filter - OUTSIDE main content */}
      <div
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 100,
          background: '#f4f6fa',
          paddingTop: 32,
          paddingBottom: 16,
        }}
      >
        {/* Date Filter Controls */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 16,
            flexWrap: 'wrap',
            marginBottom: 18,
          }}
        >
          {/* Quick-select buttons */}
          <div style={{ display: 'flex', gap: 8 }}>
            <button
              onClick={() => setRange('7d')}
              style={{
                padding: '4px 12px',
                borderRadius: 6,
                border: '1px solid #ccc',
                background: '#f7f7f7',
                cursor: 'pointer',
                fontWeight: 600,
              }}
            >
              Last 7 days
            </button>
            <button
              onClick={() => setRange('30d')}
              style={{
                padding: '4px 12px',
                borderRadius: 6,
                border: '1px solid #ccc',
                background: '#f7f7f7',
                cursor: 'pointer',
                fontWeight: 600,
              }}
            >
              Last 30 days
            </button>
            <button
              onClick={() => setRange('thisMonth')}
              style={{
                padding: '4px 12px',
                borderRadius: 6,
                border: '1px solid #ccc',
                background: '#f7f7f7',
                cursor: 'pointer',
                fontWeight: 600,
              }}
            >
              This Month
            </button>
            <button
              onClick={() => setRange('lastMonth')}
              style={{
                padding: '4px 12px',
                borderRadius: 6,
                border: '1px solid #ccc',
                background: '#f7f7f7',
                cursor: 'pointer',
                fontWeight: 600,
              }}
            >
              Last Month
            </button>
            <button
              onClick={() => setRange('all')}
              style={{
                padding: '4px 12px',
                borderRadius: 6,
                border: '1px solid #ccc',
                background: '#f7f7f7',
                cursor: 'pointer',
                fontWeight: 600,
              }}
            >
              All Time
            </button>
          </div>
          <label style={{ fontWeight: 600 }}>Date Range:</label>
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            style={{ padding: 6, borderRadius: 6, border: '1px solid #ccc' }}
          />
          <span style={{ fontWeight: 600 }}>to</span>
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            style={{ padding: 6, borderRadius: 6, border: '1px solid #ccc' }}
          />
          <button
            onClick={handleFilter}
            style={{
              padding: '6px 18px',
              borderRadius: 6,
              background: '#1a237e',
              color: '#fff',
              fontWeight: 700,
              border: 'none',
              cursor: 'pointer',
            }}
          >
            Filter
          </button>
          {(appliedRange.start || appliedRange.end) && (
            <span style={{ marginLeft: 16, color: '#444', fontWeight: 500 }}>
              Showing data from <b>{appliedRange.start || 'beginning'}</b> to{' '}
              <b>{appliedRange.end || 'now'}</b>
            </span>
          )}
          {!appliedRange.start && !appliedRange.end && (
            <span style={{ marginLeft: 16, color: '#888' }}>
              (Showing all records)
            </span>
          )}
        </div>
        {/* Tab Bar */}
        <div
          style={{
            display: 'flex',
            gap: 24,
            marginBottom: 36,
            background: '#fff',
            borderRadius: 12,
            boxShadow: '0 2px 12px 0 rgba(0,0,0,0.04)',
            padding: '16px 24px',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}
        >
          {TABS.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 36px',
                borderRadius: 10,
                border: 'none',
                background: activeTab === tab ? '#1a237e' : '#f1f3f6',
                color: activeTab === tab ? '#fff' : '#1a237e',
                fontWeight: 800,
                fontSize: 20,
                cursor: 'pointer',
                boxShadow:
                  activeTab === tab
                    ? '0 4px 16px 0 rgba(26,35,126,0.10)'
                    : 'none',
                transition: 'background 0.2s',
                letterSpacing: -0.5,
                outline: activeTab === tab ? '2px solid #3949ab' : 'none',
              }}
            >
              {tab}
            </button>
          ))}
        </div>
      </div>
      {/* Main dashboard content */}
      <div style={{ background: '#f4f6fa', minHeight: '100vh', padding: 32 }}>
        <h1
          style={{
            fontWeight: 800,
            fontSize: 32,
            marginBottom: 16,
            color: '#1a237e',
            letterSpacing: -1,
          }}
        >
          📊 Business Dashboard
        </h1>
        {loading && (
          <div style={{ minHeight: '90vh', padding: 32 }}>
            {/* Skeleton for header */}
            <div
              style={{
                height: 44,
                width: 340,
                background: '#e3e7ef',
                borderRadius: 12,
                marginBottom: 32,
              }}
            />
            {/* Skeleton for tab bar */}
            <div style={{ display: 'flex', gap: 24, marginBottom: 36 }}>
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  style={{
                    height: 48,
                    width: 140,
                    background: '#e3e7ef',
                    borderRadius: 10,
                  }}
                />
              ))}
            </div>
            {/* Skeleton for stats cards */}
            <div style={{ display: 'flex', gap: 32, marginBottom: 48 }}>
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  style={{
                    height: 110,
                    width: 180,
                    background: '#e3e7ef',
                    borderRadius: 18,
                  }}
                />
              ))}
            </div>
            {/* Skeleton for 3-4 main sections (charts/tables) */}
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                style={{
                  height: 320,
                  width: '100%',
                  maxWidth: 1000,
                  background: '#e3e7ef',
                  borderRadius: 18,
                  margin: '0 auto 40px auto',
                  boxShadow: '0 2px 12px 0 rgba(0,0,0,0.04)',
                }}
              />
            ))}
          </div>
        )}
        {error && <div>Error: {error}</div>}
        {!loading && !error && data && (
          <>
            {activeTab === TAB_ORDERS && (
              <>
                {/* --- Alerts Section (Orders) --- */}
                {data?.alerts?.length > 0 && (
                  <div style={{ marginBottom: 32 }}>
                    {/* Collapsible Affiliate Alerts */}
                    {(() => {
                      const affiliateAlerts = data.alerts.filter(
                        (a) => a.type && a.type.startsWith('order_affiliate_'),
                      );
                      const otherAlerts = data.alerts.filter(
                        (a) =>
                          !(a.type && a.type.startsWith('order_affiliate_')),
                      );
                      return (
                        <>
                          {/* Non-affiliate alerts */}
                          {otherAlerts.map((alert, idx) => {
                            let color = '#2196f3';
                            let icon = 'ℹ️';
                            if (alert.severity === 'success') {
                              color = '#43a047';
                              icon = '✅';
                            } else if (alert.severity === 'warning') {
                              color = '#ffa000';
                              icon = '⚠️';
                            } else if (alert.severity === 'critical') {
                              color = '#e53935';
                              icon = '❗';
                            }
                            return (
                              <div
                                key={idx}
                                style={{
                                  background: color + '22',
                                  border: `1.5px solid ${color}`,
                                  color,
                                  borderRadius: 10,
                                  padding: '16px 24px',
                                  marginBottom: 12,
                                  fontWeight: 600,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 16,
                                  fontSize: 18,
                                }}
                              >
                                <span style={{ fontSize: 24 }}>{icon}</span>
                                <span>{alert.message}</span>
                              </div>
                            );
                          })}
                          {/* Collapsible affiliate alerts */}
                          {affiliateAlerts.length > 0 && (
                            <div style={{ marginBottom: 12 }}>
                              <button
                                onClick={() => setShowAffiliates((v) => !v)}
                                style={{
                                  background: '#fff',
                                  border: '1.5px solid #2196f3',
                                  color: '#2196f3',
                                  borderRadius: 8,
                                  padding: '6px 18px',
                                  fontWeight: 700,
                                  fontSize: 16,
                                  cursor: 'pointer',
                                  marginBottom: 8,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 8,
                                }}
                              >
                                {showAffiliates ? '▼' : '▶'}{' '}
                                {affiliateAlerts.length} Affiliate Alerts
                              </button>
                              {showAffiliates && (
                                <div>
                                  {affiliateAlerts.map((alert, idx) => {
                                    let color = '#2196f3';
                                    let icon = 'ℹ️';
                                    if (alert.severity === 'success') {
                                      color = '#43a047';
                                      icon = '✅';
                                    } else if (alert.severity === 'warning') {
                                      color = '#ffa000';
                                      icon = '⚠️';
                                    } else if (alert.severity === 'critical') {
                                      color = '#e53935';
                                      icon = '❗';
                                    }
                                    return (
                                      <div
                                        key={idx}
                                        style={{
                                          background: color + '22',
                                          border: `1.5px solid ${color}`,
                                          color,
                                          borderRadius: 10,
                                          padding: '16px 24px',
                                          marginBottom: 8,
                                          fontWeight: 600,
                                          display: 'flex',
                                          alignItems: 'center',
                                          gap: 16,
                                          fontSize: 18,
                                        }}
                                      >
                                        <span style={{ fontSize: 24 }}>
                                          {icon}
                                        </span>
                                        <span>{alert.message}</span>
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                )}
                {/* --- Orders Group --- */}
                <h2
                  style={{
                    fontSize: 26,
                    fontWeight: 800,
                    margin: '40px 0 16px 0',
                    color: '#1a237e',
                    letterSpacing: -1,
                  }}
                >
                  🛒 Orders
                </h2>
                {/* --- Statistics Section (Orders) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>📈 Statistics</div>
                  <div style={{ display: 'flex', gap: 32, flexWrap: 'wrap' }}>
                    {stats.map((s) => (
                      <div key={s.label} style={cardStyle}>
                        <div
                          style={{
                            fontSize: 16,
                            color: '#888',
                            marginBottom: 8,
                          }}
                        >
                          {s.label}
                        </div>
                        <div
                          style={{
                            fontSize: 36,
                            fontWeight: 700,
                            color: '#1a237e',
                          }}
                        >
                          {s.value}
                        </div>
                      </div>
                    ))}
                    {appStats.map((s) => (
                      <div key={s.label} style={cardStyle}>
                        <div
                          style={{
                            fontSize: 16,
                            color: '#888',
                            marginBottom: 8,
                          }}
                        >
                          {s.label}
                        </div>
                        <div
                          style={{
                            fontSize: 36,
                            fontWeight: 700,
                            color: '#1a237e',
                          }}
                        >
                          {s.value}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>

                <hr style={dividerStyle} />

                {/* --- Revenue Trend (Line Only) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    💰 Revenue Trend (per Day)
                  </div>
                  <div
                    style={{
                      width: '100%',
                      maxWidth: 1000,
                      height: 420,
                      margin: '0 auto',
                    }}
                  >
                    {data?.revenueByDay && data.revenueByDay.length > 0 ? (
                      <Bar
                        data={{
                          labels: data.revenueByDay.map((r) => r.date),
                          datasets: [
                            {
                              type: 'line',
                              label: 'Revenue',
                              data: data.revenueByDay.map((r) => r.revenue),
                              borderColor: '#388e3c',
                              backgroundColor: 'rgba(67,160,71,0.15)',
                              borderWidth: 3,
                              pointRadius: 2,
                              fill: false,
                              tension: 0.3,
                              yAxisID: 'y',
                            },
                          ],
                        }}
                        options={{
                          responsive: true,
                          plugins: { legend: { display: true } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: {
                              grid: { color: '#e0e0e0' },
                              beginAtZero: true,
                            },
                          },
                        }}
                      />
                    ) : (
                      <div>No revenue trend data</div>
                    )}
                  </div>
                </section>

                {/* --- Orders Trend (Line Only) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    📊 Orders Trend (per Day)
                  </div>
                  <div
                    style={{
                      width: '100%',
                      maxWidth: 1000,
                      height: 420,
                      margin: '0 auto',
                    }}
                  >
                    {data?.ordersTrend && data.ordersTrend.length > 0 ? (
                      <Bar
                        data={{
                          labels: data.ordersTrend.map((r) => r.date),
                          datasets: [
                            {
                              type: 'line',
                              label: 'Orders',
                              data: data.ordersTrend.map((r) => r.count),
                              borderColor: '#3949ab',
                              backgroundColor: 'rgba(26,35,126,0.15)',
                              borderWidth: 3,
                              pointRadius: 2,
                              fill: false,
                              tension: 0.3,
                              yAxisID: 'y',
                            },
                          ],
                        }}
                        options={{
                          responsive: true,
                          plugins: { legend: { display: true } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: {
                              grid: { color: '#e0e0e0' },
                              beginAtZero: true,
                            },
                          },
                        }}
                      />
                    ) : (
                      <div>No orders trend data</div>
                    )}
                  </div>
                </section>

                <hr style={dividerStyle} />

                {/* --- Payment Methods Section (Orders) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>💳 Top Payment Methods</div>
                  {paymentPieData ? (
                    <div
                      style={{ width: 400, maxWidth: '100%', margin: '0 auto' }}
                    >
                      <Pie
                        data={paymentPieData}
                        options={{
                          plugins: { legend: { position: 'bottom' } },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No payment method data</div>
                  )}
                </section>

                <hr style={dividerStyle} />

                {/* --- Orders by Day (Stacked by Payment Method) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    📅 Orders by Day (Stacked by Payment Method)
                  </div>
                  <div
                    style={{
                      width: '100%',
                      maxWidth: 1000,
                      height: 420,
                      margin: '0 auto',
                    }}
                  >
                    {ordersByDayChart ? (
                      <Bar
                        data={ordersByDayChart}
                        options={{
                          responsive: true,
                          plugins: { legend: { position: 'top' } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    ) : (
                      <div>No orders by day data</div>
                    )}
                  </div>
                </section>

                <hr style={dividerStyle} />

                {/* --- Orders by Source/Affiliate Over Time (Stacked Bar + Table) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🌐 Orders by Source/Affiliate Over Time
                  </div>
                  {data?.ordersBySourceAffiliateTrend &&
                  data.ordersBySourceAffiliateTrend.length > 0 ? (
                    (() => {
                      // Group by date, then by source, then by affiliate
                      const grouped = {};
                      data.ordersBySourceAffiliateTrend.forEach((row) => {
                        if (!grouped[row.date]) grouped[row.date] = {};
                        if (!grouped[row.date][row.source])
                          grouped[row.date][row.source] = {};
                        grouped[row.date][row.source][row.affiliate] =
                          row.count;
                      });
                      const dates = Object.keys(grouped).sort();
                      const sources = Array.from(
                        new Set(
                          data.ordersBySourceAffiliateTrend.map(
                            (r) => r.source,
                          ),
                        ),
                      );
                      const affiliates = Array.from(
                        new Set(
                          data.ordersBySourceAffiliateTrend.map(
                            (r) => r.affiliate,
                          ),
                        ),
                      );
                      // For each source, stack affiliates
                      const datasets = [];
                      sources.forEach((source, sIdx) => {
                        affiliates.forEach((affiliate, aIdx) => {
                          datasets.push({
                            label: `${source} / ${affiliate}`,
                            data: dates.map(
                              (date) => grouped[date][source]?.[affiliate] || 0,
                            ),
                            backgroundColor:
                              COLORS[(sIdx + aIdx) % COLORS.length],
                            stack: source,
                          });
                        });
                      });
                      // Table columns: Date, then all source/affiliate pairs
                      // const allPairs = [];
                      // sources.forEach((source) => {
                      //   affiliates.forEach((affiliate) => {
                      //     allPairs.push(`${source} / ${affiliate}`);
                      //   });
                      // });
                      return (
                        <div
                          style={{
                            width: '100%',
                            maxWidth: 1000,
                            height: 420,
                            margin: '0 auto',
                          }}
                        >
                          <Bar
                            data={{ labels: dates, datasets }}
                            options={{
                              responsive: true,
                              plugins: { legend: { position: 'top' } },
                              scales: {
                                x: {
                                  stacked: true,
                                  grid: { color: '#e0e0e0' },
                                },
                                y: {
                                  stacked: true,
                                  grid: { color: '#e0e0e0' },
                                },
                              },
                            }}
                          />
                        </div>
                      );
                    })()
                  ) : (
                    <div>No source/affiliate trend data</div>
                  )}
                </section>

                <hr style={dividerStyle} />

                {/* --- Top Sources (Pie/Table) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🏆 Top Sources
                    <InfoIcon text="The platform/service where the order originated (e.g., airtrip, global-esim-jp)" />
                  </div>
                  {data?.topSources && data.topSources.length > 0 ? (
                    <div
                      style={{ width: 400, maxWidth: '100%', margin: '0 auto' }}
                    >
                      <Pie
                        data={{
                          labels: data.topSources.map((s) => s.source),
                          datasets: [
                            {
                              data: data.topSources.map((s) => s.count),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { position: 'bottom' } },
                        }}
                      />
                    </div>
                  ) : data?.topSources && data.topSources.length === 0 ? (
                    <div>No source data</div>
                  ) : (
                    <div style={{ overflowX: 'auto', marginTop: 16 }}>
                      <table style={tableStyle}>
                        <thead>
                          <tr>
                            <th style={thStyle}>Source</th>
                            <th style={thStyle}>Order Count</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data?.topSources &&
                            data.topSources.map((s) => (
                              <tr key={s.source}>
                                <td style={tdStyle}>{s.source}</td>
                                <td style={{ ...tdStyle, textAlign: 'center' }}>
                                  {s.count}
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </section>

                <hr style={dividerStyle} />

                {/* --- Top Affiliates (Pie/Table) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🤝 Top Affiliates
                    <InfoIcon text="The external referrer or partner that sent the user to our site." />
                  </div>
                  {data?.topAffiliates && data.topAffiliates.length > 0 ? (
                    <div
                      style={{ width: 400, maxWidth: '100%', margin: '0 auto' }}
                    >
                      <Pie
                        data={{
                          labels: data.topAffiliates.map((a) => a.affiliate),
                          datasets: [
                            {
                              data: data.topAffiliates.map((a) => a.count),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { position: 'bottom' } },
                        }}
                      />
                    </div>
                  ) : data?.topAffiliates && data.topAffiliates.length === 0 ? (
                    <div>No affiliate data</div>
                  ) : (
                    <div style={{ overflowX: 'auto', marginTop: 16 }}>
                      <table style={tableStyle}>
                        <thead>
                          <tr>
                            <th style={thStyle}>Affiliate</th>
                            <th style={thStyle}>Order Count</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data?.topAffiliates &&
                            data.topAffiliates.map((a) => (
                              <tr key={a.affiliate}>
                                <td style={tdStyle}>{a.affiliate}</td>
                                <td style={{ ...tdStyle, textAlign: 'center' }}>
                                  {a.count}
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </section>

                <hr style={dividerStyle} />

                {/* --- Revenue by Source (Bar) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    💵 Revenue by Source
                    <InfoIcon text="The platform/service where the order originated (e.g., airtrip, global-esim-jp)" />
                  </div>
                  {data?.revenueBySource && data.revenueBySource.length > 0 ? (
                    <div
                      style={{ width: 600, maxWidth: '100%', margin: '0 auto' }}
                    >
                      <Bar
                        data={{
                          labels: data.revenueBySource.map((s) => s.source),
                          datasets: [
                            {
                              label: 'Revenue',
                              data: data.revenueBySource.map((s) => s.revenue),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No revenue by source data</div>
                  )}
                </section>

                <hr style={dividerStyle} />

                {/* --- Revenue by Affiliate (Bar) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    💵 Revenue by Affiliate
                    <InfoIcon text="The external referrer or partner that sent the user to our site." />
                  </div>
                  {data?.revenueByAffiliate &&
                  data.revenueByAffiliate.length > 0 ? (
                    <div
                      style={{ width: 600, maxWidth: '100%', margin: '0 auto' }}
                    >
                      <Bar
                        data={{
                          labels: data.revenueByAffiliate.map(
                            (a) => a.affiliate,
                          ),
                          datasets: [
                            {
                              label: 'Revenue',
                              data: data.revenueByAffiliate.map(
                                (a) => a.revenue,
                              ),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No revenue by affiliate data</div>
                  )}
                </section>

                <hr style={dividerStyle} />

                {/* --- Conversion Rate by Source (Bar) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🔄 Conversion Rate by Source
                    <InfoIcon text="Conversion Rate = (Successful Orders) / (Total Orders) for each source, in the selected date range." />
                  </div>
                  <div
                    style={{
                      fontSize: 15,
                      color: '#555',
                      background: '#f1f3f6',
                      borderRadius: 8,
                      padding: '10px 18px',
                      marginBottom: 18,
                      maxWidth: 600,
                      boxShadow: '0 1px 4px 0 rgba(0,0,0,0.03)',
                    }}
                  >
                    <b>How is this calculated?</b> Conversion Rate ={' '}
                    <b>(Successful Orders)</b> / <b>(Total Orders)</b> for each
                    source, in the selected date range.
                  </div>
                  {data?.conversionRateBySource &&
                  data.conversionRateBySource.length > 0 ? (
                    <div
                      style={{ width: 600, maxWidth: '100%', margin: '0 auto' }}
                    >
                      <Bar
                        data={{
                          labels: data.conversionRateBySource.map(
                            (s) => s.source,
                          ),
                          datasets: [
                            {
                              label: 'Conversion Rate',
                              data: data.conversionRateBySource.map((s) =>
                                s.conversionRate
                                  ? Math.round(s.conversionRate * 1000) / 10
                                  : 0,
                              ),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: {
                              grid: { color: '#e0e0e0' },
                              min: 0,
                              max: 100,
                              ticks: { callback: (v) => `${v}%` },
                            },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No conversion rate by source data</div>
                  )}
                </section>

                <hr style={dividerStyle} />

                {/* --- Conversion Rate by Affiliate (Bar) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🔄 Conversion Rate by Affiliate
                    <InfoIcon text="The external referrer or partner that sent the user to our site." />
                  </div>
                  {data?.conversionRateByAffiliate &&
                  data.conversionRateByAffiliate.length > 0 ? (
                    <div
                      style={{ width: 600, maxWidth: '100%', margin: '0 auto' }}
                    >
                      <Bar
                        data={{
                          labels: data.conversionRateByAffiliate.map(
                            (a) => a.affiliate,
                          ),
                          datasets: [
                            {
                              label: 'Conversion Rate',
                              data: data.conversionRateByAffiliate.map((a) =>
                                a.conversionRate
                                  ? Math.round(a.conversionRate * 1000) / 10
                                  : 0,
                              ),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: {
                              grid: { color: '#e0e0e0' },
                              min: 0,
                              max: 100,
                              ticks: { callback: (v) => `${v}%` },
                            },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No conversion rate by affiliate data</div>
                  )}
                </section>
              </>
            )}
            {activeTab === TAB_USERS && (
              <>
                {/* --- Users Group --- */}
                <h2
                  style={{
                    fontSize: 26,
                    fontWeight: 800,
                    margin: '40px 0 16px 0',
                    color: '#1a237e',
                    letterSpacing: -1,
                  }}
                >
                  👤 Users
                </h2>

                {/* --- User Analytics Section --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>📊 User Analytics</div>
                  <div
                    style={{
                      display: 'flex',
                      gap: 32,
                      flexWrap: 'wrap',
                      marginBottom: 32,
                    }}
                  >
                    <div style={cardStyle}>
                      <div
                        style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                      >
                        Total Users
                      </div>
                      <div
                        style={{
                          fontSize: 36,
                          fontWeight: 700,
                          color: '#1a237e',
                        }}
                      >
                        {data?.totalUsers}
                      </div>
                    </div>
                    <div style={cardStyle}>
                      <div
                        style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                      >
                        Returning Users
                      </div>
                      <div
                        style={{
                          fontSize: 36,
                          fontWeight: 700,
                          color: '#1a237e',
                        }}
                      >
                        {data?.returningUsers?.count ?? 0}
                      </div>
                    </div>
                    <div style={cardStyle}>
                      <div
                        style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                      >
                        Guest Users
                      </div>
                      <div
                        style={{
                          fontSize: 36,
                          fontWeight: 700,
                          color: '#1a237e',
                        }}
                      >
                        {data?.guestUsers?.count ?? 0}
                      </div>
                    </div>
                    <div style={cardStyle}>
                      <div
                        style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                      >
                        Guest→User Conversions
                      </div>
                      <div
                        style={{
                          fontSize: 36,
                          fontWeight: 700,
                          color: '#1a237e',
                        }}
                      >
                        {data?.guestUsers?.guestToUserCount ?? 0}
                      </div>
                    </div>
                  </div>
                  {/* User Signups Trend */}
                  <div style={sectionHeaderStyle}>📈 User Signups Trend</div>
                  {data?.usersTrend && data.usersTrend.length > 0 ? (
                    <div
                      style={{
                        width: '100%',
                        maxWidth: 900,
                        margin: '0 auto',
                        marginBottom: 32,
                      }}
                    >
                      <Line
                        data={{
                          labels: data.usersTrend.map((u) => u.date),
                          datasets: [
                            {
                              label: 'Signups',
                              data: data.usersTrend.map((u) => u.count),
                              borderColor: COLORS[0],
                              backgroundColor: COLORS[0],
                              fill: false,
                              tension: 0.3,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No signup trend data</div>
                  )}
                  {/* Returning Users Trend */}
                  <div style={sectionHeaderStyle}>🔁 Returning Users Trend</div>
                  {data?.returningUsers?.trend &&
                  data.returningUsers.trend.length > 0 ? (
                    <div
                      style={{
                        width: '100%',
                        maxWidth: 900,
                        margin: '0 auto',
                        marginBottom: 32,
                      }}
                    >
                      <Line
                        data={{
                          labels: data.returningUsers.trend.map((u) => u.date),
                          datasets: [
                            {
                              label: 'Returning Users',
                              data: data.returningUsers.trend.map(
                                (u) => u.count,
                              ),
                              borderColor: COLORS[1],
                              backgroundColor: COLORS[1],
                              fill: false,
                              tension: 0.3,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No returning user trend data</div>
                  )}
                  {/* Signup Medium (idpProvider) */}
                  <div style={sectionHeaderStyle}>
                    🔑 Signup Medium (idpProvider)
                  </div>
                  {data?.idpProviderStats &&
                  data.idpProviderStats.length > 0 ? (
                    <div
                      style={{
                        width: 400,
                        maxWidth: '100%',
                        margin: '0 auto',
                        marginBottom: 24,
                      }}
                    >
                      <Pie
                        data={{
                          labels: data.idpProviderStats.map(
                            (i) => i.idpProvider,
                          ),
                          datasets: [
                            {
                              data: data.idpProviderStats.map((i) => i.count),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { position: 'bottom' } },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No signup medium data</div>
                  )}
                  {data?.idpProviderTrend && data.idpProviderTrend.length > 0
                    ? (() => {
                        const providers = Array.from(
                          new Set(
                            data.idpProviderTrend.map((i) => i.idpProvider),
                          ),
                        );
                        const labels = Array.from(
                          new Set(data.idpProviderTrend.map((i) => i.date)),
                        );
                        const datasets = providers.map((provider, idx) => ({
                          label: provider,
                          data: labels.map((date) => {
                            const found = data.idpProviderTrend.find(
                              (i) =>
                                i.date === date && i.idpProvider === provider,
                            );
                            return found ? found.count : 0;
                          }),
                          borderColor: COLORS[idx % COLORS.length],
                          backgroundColor: COLORS[idx % COLORS.length],
                          fill: false,
                          tension: 0.3,
                        }));
                        return (
                          <div
                            style={{
                              width: '100%',
                              maxWidth: 900,
                              margin: '0 auto',
                              marginBottom: 32,
                            }}
                          >
                            <Line
                              data={{ labels, datasets }}
                              options={{
                                plugins: { legend: { position: 'top' } },
                                scales: {
                                  x: { grid: { color: '#e0e0e0' } },
                                  y: { grid: { color: '#e0e0e0' } },
                                },
                              }}
                            />
                          </div>
                        );
                      })()
                    : null}
                  {/* Users by UserPool */}
                  <div style={sectionHeaderStyle}>🏊 Users by UserPool</div>
                  {data?.userPoolStats && data.userPoolStats.length > 0 ? (
                    <div
                      style={{
                        width: 400,
                        maxWidth: '100%',
                        margin: '0 auto',
                        marginBottom: 24,
                      }}
                    >
                      <Bar
                        data={{
                          labels: data.userPoolStats.map((u) => u.userPool),
                          datasets: [
                            {
                              label: 'Users',
                              data: data.userPoolStats.map((u) => u.count),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{ plugins: { legend: { display: false } } }}
                      />
                    </div>
                  ) : (
                    <div>No user pool data</div>
                  )}
                  {data?.userPoolTrend && data.userPoolTrend.length > 0
                    ? (() => {
                        const pools = Array.from(
                          new Set(data.userPoolTrend.map((u) => u.userPool)),
                        );
                        const labels = Array.from(
                          new Set(data.userPoolTrend.map((u) => u.date)),
                        );
                        const datasets = pools.map((pool, idx) => ({
                          label: pool,
                          data: labels.map((date) => {
                            const found = data.userPoolTrend.find(
                              (u) => u.date === date && u.userPool === pool,
                            );
                            return found ? found.count : 0;
                          }),
                          borderColor: COLORS[idx % COLORS.length],
                          backgroundColor: COLORS[idx % COLORS.length],
                          fill: false,
                          tension: 0.3,
                        }));
                        return (
                          <div
                            style={{
                              width: '100%',
                              maxWidth: 900,
                              margin: '0 auto',
                              marginBottom: 32,
                            }}
                          >
                            <Line
                              data={{ labels, datasets }}
                              options={{
                                plugins: { legend: { position: 'top' } },
                                scales: {
                                  x: { grid: { color: '#e0e0e0' } },
                                  y: { grid: { color: '#e0e0e0' } },
                                },
                              }}
                            />
                          </div>
                        );
                      })()
                    : null}
                  {/* Popular Locale */}
                  <div style={sectionHeaderStyle}>🌍 Popular Locale</div>
                  {data?.localeStats && data.localeStats.length > 0 ? (
                    <div
                      style={{
                        width: 400,
                        maxWidth: '100%',
                        margin: '0 auto',
                        marginBottom: 24,
                      }}
                    >
                      <Pie
                        data={{
                          labels: data.localeStats.map((l) => l.locale),
                          datasets: [
                            {
                              data: data.localeStats.map((l) => l.count),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { position: 'bottom' } },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No locale data</div>
                  )}
                  {data?.localeTrend && data.localeTrend.length > 0
                    ? (() => {
                        const locales = Array.from(
                          new Set(data.localeTrend.map((l) => l.locale)),
                        );
                        const labels = Array.from(
                          new Set(data.localeTrend.map((l) => l.date)),
                        );
                        const datasets = locales.map((locale, idx) => ({
                          label: locale,
                          data: labels.map((date) => {
                            const found = data.localeTrend.find(
                              (l) => l.date === date && l.locale === locale,
                            );
                            return found ? found.count : 0;
                          }),
                          borderColor: COLORS[idx % COLORS.length],
                          backgroundColor: COLORS[idx % COLORS.length],
                          fill: false,
                          tension: 0.3,
                        }));
                        return (
                          <div
                            style={{
                              width: '100%',
                              maxWidth: 900,
                              margin: '0 auto',
                              marginBottom: 32,
                            }}
                          >
                            <Line
                              data={{ labels, datasets }}
                              options={{
                                plugins: { legend: { position: 'top' } },
                                scales: {
                                  x: { grid: { color: '#e0e0e0' } },
                                  y: { grid: { color: '#e0e0e0' } },
                                },
                              }}
                            />
                          </div>
                        );
                      })()
                    : null}
                  {/* Users by Source */}
                  <div style={sectionHeaderStyle}>🌐 Users by Source</div>
                  {data?.userSourceStats && data.userSourceStats.length > 0 ? (
                    <div
                      style={{
                        width: 400,
                        maxWidth: '100%',
                        margin: '0 auto',
                        marginBottom: 24,
                      }}
                    >
                      <Bar
                        data={{
                          labels: data.userSourceStats.map((s) => s.source),
                          datasets: [
                            {
                              label: 'Users',
                              data: data.userSourceStats.map((s) => s.count),
                              backgroundColor: COLORS,
                            },
                          ],
                        }}
                        options={{ plugins: { legend: { display: false } } }}
                      />
                    </div>
                  ) : (
                    <div>No user source data</div>
                  )}
                  {data?.userSourceTrend && data.userSourceTrend.length > 0
                    ? (() => {
                        const sources = Array.from(
                          new Set(data.userSourceTrend.map((s) => s.source)),
                        );
                        const labels = Array.from(
                          new Set(data.userSourceTrend.map((s) => s.date)),
                        );
                        const datasets = sources.map((source, idx) => ({
                          label: source,
                          data: labels.map((date) => {
                            const found = data.userSourceTrend.find(
                              (s) => s.date === date && s.source === source,
                            );
                            return found ? found.count : 0;
                          }),
                          borderColor: COLORS[idx % COLORS.length],
                          backgroundColor: COLORS[idx % COLORS.length],
                          fill: false,
                          tension: 0.3,
                        }));
                        return (
                          <div
                            style={{
                              width: '100%',
                              maxWidth: 900,
                              margin: '0 auto',
                              marginBottom: 32,
                            }}
                          >
                            <Line
                              data={{ labels, datasets }}
                              options={{
                                plugins: { legend: { position: 'top' } },
                                scales: {
                                  x: { grid: { color: '#e0e0e0' } },
                                  y: { grid: { color: '#e0e0e0' } },
                                },
                              }}
                            />
                          </div>
                        );
                      })()
                    : null}
                  {/* DAU/WAU */}
                  <div style={sectionHeaderStyle}>
                    📊 DAU (Daily Active Users)
                  </div>
                  {data?.dau && data.dau.length > 0 ? (
                    <div
                      style={{
                        width: '100%',
                        maxWidth: 900,
                        margin: '0 auto',
                        marginBottom: 32,
                      }}
                    >
                      <Line
                        data={{
                          labels: data.dau.map((d) => d.date),
                          datasets: [
                            {
                              label: 'DAU',
                              data: data.dau.map((d) => d.count),
                              borderColor: COLORS[2],
                              backgroundColor: COLORS[2],
                              fill: false,
                              tension: 0.3,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No DAU data</div>
                  )}
                  <div style={sectionHeaderStyle}>
                    📊 WAU (Weekly Active Users)
                  </div>
                  {data?.wau && data.wau.length > 0 ? (
                    <div
                      style={{
                        width: '100%',
                        maxWidth: 900,
                        margin: '0 auto',
                        marginBottom: 32,
                      }}
                    >
                      <Line
                        data={{
                          labels: data.wau.map((w) => w.week),
                          datasets: [
                            {
                              label: 'WAU',
                              data: data.wau.map((w) => w.count),
                              borderColor: COLORS[3],
                              backgroundColor: COLORS[3],
                              fill: false,
                              tension: 0.3,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No WAU data</div>
                  )}
                </section>

                {/* --- Customers Section (Users) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    👤 Top 10 Customers (by Order Count)
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Name</th>
                          <th style={thStyle}>Email</th>
                          <th style={thStyle}>Orders</th>
                        </tr>
                      </thead>
                      <tbody>
                        {topCustomers.map((c) => (
                          <tr key={c.userId}>
                            <td style={tdStyle}>{c.name}</td>
                            <td style={tdStyle}>{c.email}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {c.orderCount}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div style={sectionHeaderStyle}>
                    💰 Top 10 Customers (by Total Spent)
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Name</th>
                          <th style={thStyle}>Email</th>
                          <th style={thStyle}>Total Spent</th>
                        </tr>
                      </thead>
                      <tbody>
                        {topCustomersSpent.map((c) => (
                          <tr key={c.userId}>
                            <td style={tdStyle}>{c.name}</td>
                            <td style={tdStyle}>{c.email}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {c.totalSpent}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>
              </>
            )}
            {activeTab === TAB_COUPONS && (
              <>
                <h2
                  style={{
                    fontSize: 26,
                    fontWeight: 800,
                    margin: '40px 0 16px 0',
                    color: '#1a237e',
                    letterSpacing: -1,
                  }}
                >
                  🎟️ Coupons
                </h2>
                {/* --- Coupon Stats Cards --- */}
                <section
                  style={{
                    ...sectionStyle,
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 32,
                  }}
                >
                  <div style={cardStyle}>
                    <div
                      style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                    >
                      Total Coupon Usage
                    </div>
                    <div
                      style={{
                        fontSize: 36,
                        fontWeight: 700,
                        color: '#1a237e',
                      }}
                    >
                      {data?.totalCouponUsage ?? 0}
                    </div>
                  </div>
                  <div style={cardStyle}>
                    <div
                      style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                    >
                      Total Discount Given
                    </div>
                    <div
                      style={{
                        fontSize: 36,
                        fontWeight: 700,
                        color: '#c62828',
                      }}
                    >
                      {data?.totalDiscountGiven?.toLocaleString() ?? 0}
                    </div>
                  </div>
                  <div style={cardStyle}>
                    <div
                      style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                    >
                      Loss (Revenue Lost to Coupons)
                    </div>
                    <div
                      style={{
                        fontSize: 36,
                        fontWeight: 700,
                        color: '#c62828',
                      }}
                    >
                      {data?.loss?.toLocaleString() ?? 0}
                    </div>
                  </div>
                  <div style={cardStyle}>
                    <div
                      style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                    >
                      Avg Discount per Order
                    </div>
                    <div
                      style={{
                        fontSize: 36,
                        fontWeight: 700,
                        color: '#1a237e',
                      }}
                    >
                      {data?.avgDiscountPerOrder?.toLocaleString() ?? 0}
                    </div>
                  </div>
                  <div style={cardStyle}>
                    <div
                      style={{ fontSize: 16, color: '#888', marginBottom: 8 }}
                    >
                      % Orders Using Coupons
                    </div>
                    <div
                      style={{
                        fontSize: 36,
                        fontWeight: 700,
                        color: '#1a237e',
                      }}
                    >
                      {data?.percentOrdersWithCoupons ?? 0}%
                    </div>
                  </div>
                </section>
                {/* --- Coupons Trend --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>📈 Coupons Usage Trend</div>
                  {data?.couponsTrend && data.couponsTrend.length > 0 ? (
                    <div
                      style={{
                        width: '100%',
                        maxWidth: 900,
                        margin: '0 auto',
                        marginBottom: 32,
                      }}
                    >
                      <Line
                        data={{
                          labels: data.couponsTrend.map((t) => t.date),
                          datasets: [
                            {
                              label: 'Coupon Usage',
                              data: data.couponsTrend.map((t) => t.count),
                              borderColor: COLORS[0],
                              backgroundColor: COLORS[0],
                              fill: false,
                              tension: 0.3,
                            },
                          ],
                        }}
                        options={{
                          plugins: { legend: { display: false } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No coupon trend data</div>
                  )}
                </section>
                {/* --- Existing Coupon Analytics (Top Coupons, Revenue Impact, etc.) --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>🏷️ Top Coupons</div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Code</th>
                          <th style={thStyle}>Usage Count</th>
                          <th style={thStyle}>Discount Type</th>
                          <th style={thStyle}>Discount Value</th>
                        </tr>
                      </thead>
                      <tbody>
                        {topCoupons.length === 0 && (
                          <tr>
                            <td colSpan={4} style={tdStyle}>
                              No coupon data
                            </td>
                          </tr>
                        )}
                        {topCoupons.map((c) => (
                          <tr key={c.couponId}>
                            <td style={tdStyle}>{c.code}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {c.usageCount}
                            </td>
                            <td style={tdStyle}>{c.discountType}</td>
                            <td style={tdStyle}>{c.discountValue}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>

                {/* --- Most Coupon-Used Users --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    👤 Most Coupon-Used Users
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Name</th>
                          <th style={thStyle}>Email</th>
                          <th style={thStyle}>Coupon Usage Count</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.mostCouponUsedUsers ||
                          data.mostCouponUsedUsers.length === 0) && (
                          <tr>
                            <td colSpan={3} style={tdStyle}>
                              No data
                            </td>
                          </tr>
                        )}
                        {data?.mostCouponUsedUsers?.map((u) => (
                          <tr key={u.userId}>
                            <td style={tdStyle}>{u.name}</td>
                            <td style={tdStyle}>{u.email}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {u.couponUsageCount}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>
                {/* --- Top Sources with Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🌐 Top Sources Using Coupons
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Source</th>
                          <th style={thStyle}>Coupon Usage Count</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.topSourcesWithCoupons ||
                          data.topSourcesWithCoupons.length === 0) && (
                          <tr>
                            <td colSpan={2} style={tdStyle}>
                              No data
                            </td>
                          </tr>
                        )}
                        {data?.topSourcesWithCoupons?.map((s) => (
                          <tr key={s.source}>
                            <td style={tdStyle}>{s.source}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {s.count}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>

                <div style={sectionHeaderStyle}>
                  📈 Most Used Coupons (Trend)
                </div>
                <div
                  style={{
                    display: 'flex',
                    gap: 32,
                    flexWrap: 'wrap',
                    justifyContent: 'flex-start',
                  }}
                >
                  {mostUsedCoupons.length === 0 && (
                    <div>No coupon trend data</div>
                  )}
                  {mostUsedCoupons.map((coupon, idx) => {
                    const trendData = {
                      labels: coupon.trend.map((t) => t.date),
                      datasets: [
                        {
                          label: coupon.code,
                          data: coupon.trend.map((t) => t.count),
                          borderColor: COLORS[idx % COLORS.length],
                          backgroundColor: COLORS[idx % COLORS.length],
                          fill: false,
                          tension: 0.3,
                        },
                      ],
                    };
                    return (
                      <div
                        key={coupon.couponId}
                        style={{
                          width: 320,
                          maxWidth: '100%',
                          background: '#fff',
                          borderRadius: 12,
                          boxShadow: '0 2px 12px 0 rgba(0,0,0,0.04)',
                          padding: 16,
                          marginBottom: 24,
                        }}
                      >
                        <Line
                          data={trendData}
                          options={{
                            plugins: { legend: { display: false } },
                            scales: {
                              x: { grid: { color: '#e0e0e0' } },
                              y: { grid: { color: '#e0e0e0' } },
                            },
                          }}
                        />
                      </div>
                    );
                  })}
                </div>
                {/* --- Trending Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    📈 Trending Coupons (Last 7 Days vs Previous 7 Days)
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Coupon ID</th>
                          <th style={thStyle}>Usage Increase</th>
                          <th style={thStyle}>% Change</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.trendingCoupons ||
                          data.trendingCoupons.length === 0) && (
                          <tr>
                            <td colSpan={3} style={tdStyle}>
                              No trending coupons
                            </td>
                          </tr>
                        )}
                        {data?.trendingCoupons?.map((c) => (
                          <tr key={c.couponId}>
                            <td style={tdStyle}>{c.couponId}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {c.usageIncrease}
                            </td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {c.percentChange !== null
                                ? `${c.percentChange}%`
                                : '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>
                {/* --- Top Discount Types Used --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🏷️ Top Discount Types Used
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Type</th>
                          <th style={thStyle}>Count</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.topDiscountTypes ||
                          data.topDiscountTypes.length === 0) && (
                          <tr>
                            <td colSpan={2} style={tdStyle}>
                              No data
                            </td>
                          </tr>
                        )}
                        {data?.topDiscountTypes?.map((t) => (
                          <tr key={t.type}>
                            <td style={tdStyle}>{t.type}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {t.count}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>
                {/* --- Frequently Used High Discount Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🏷️ Frequently Used High Discount Coupons
                    <InfoIcon text="Top 10 most frequently used coupons with the highest discount values." />
                  </div>
                  {data?.frequentlyUsedHighDiscountCoupons?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={tableStyle}>
                        <thead>
                          <tr>
                            <th style={thStyle}>Code</th>
                            <th style={thStyle}>Usage Count</th>
                            <th style={thStyle}>Discount Type</th>
                            <th style={thStyle}>Discount Value</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.frequentlyUsedHighDiscountCoupons.map((c) => (
                            <tr key={c.couponId}>
                              <td style={tdStyle}>{c.code}</td>
                              <td style={{ ...tdStyle, textAlign: 'center' }}>
                                {c.usageCount}
                              </td>
                              <td style={tdStyle}>{c.discountType}</td>
                              <td style={tdStyle}>{c.discountValue}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No high discount coupon data</div>
                  )}
                </section>
                {/* --- Frequently Used Low Discount Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🏷️ Frequently Used Low Discount Coupons
                    <InfoIcon text="Top 10 most frequently used coupons with the lowest discount values." />
                  </div>
                  {data?.frequentlyUsedLowDiscountCoupons?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={tableStyle}>
                        <thead>
                          <tr>
                            <th style={thStyle}>Code</th>
                            <th style={thStyle}>Usage Count</th>
                            <th style={thStyle}>Discount Type</th>
                            <th style={thStyle}>Discount Value</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.frequentlyUsedLowDiscountCoupons.map((c) => (
                            <tr key={c.couponId}>
                              <td style={tdStyle}>{c.code}</td>
                              <td style={{ ...tdStyle, textAlign: 'center' }}>
                                {c.usageCount}
                              </td>
                              <td style={tdStyle}>{c.discountType}</td>
                              <td style={tdStyle}>{c.discountValue}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No low discount coupon data</div>
                  )}
                </section>
                {/* --- Expiring Soon Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    ⏰ Coupons Expiring Soon (Next 7 Days)
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Code</th>
                          <th style={thStyle}>Valid Till</th>
                          <th style={thStyle}>Usage</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.expiringSoonCoupons ||
                          data.expiringSoonCoupons.length === 0) && (
                          <tr>
                            <td colSpan={3} style={tdStyle}>
                              No expiring coupons
                            </td>
                          </tr>
                        )}
                        {data?.expiringSoonCoupons?.map((c) => (
                          <tr key={c.id}>
                            <td style={tdStyle}>{c.code}</td>
                            <td style={tdStyle}>
                              {c.validTill
                                ? new Date(c.validTill).toLocaleDateString()
                                : '-'}
                            </td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {c.totalUsage}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>

                {/* --- New Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🆕 New Coupons (Last 30 Days)
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Code</th>
                          <th style={thStyle}>Created At</th>
                          <th style={thStyle}>Usage</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.newCoupons ||
                          data.newCoupons.length === 0) && (
                          <tr>
                            <td colSpan={3} style={tdStyle}>
                              No new coupons
                            </td>
                          </tr>
                        )}
                        {data?.newCoupons?.map((c) => (
                          <tr key={c.id}>
                            <td style={tdStyle}>{c.code}</td>
                            <td style={tdStyle}>
                              {c.createdAt
                                ? new Date(c.createdAt).toLocaleDateString()
                                : '-'}
                            </td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {c.totalUsage}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>

                {/* --- Popular Plans with Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    📦 Popular Plans with Coupons
                  </div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Plan Name</th>
                          <th style={thStyle}>Coupon Usage Count</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.popularPlansWithCoupons ||
                          data.popularPlansWithCoupons.length === 0) && (
                          <tr>
                            <td colSpan={2} style={tdStyle}>
                              No data
                            </td>
                          </tr>
                        )}
                        {data?.popularPlansWithCoupons?.map((p) => (
                          <tr key={p.planId}>
                            <td style={tdStyle}>{p.name}</td>
                            <td style={{ ...tdStyle, textAlign: 'center' }}>
                              {p.couponUsageCount}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>
                {/* --- Unused Coupons --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>🚫 Unused Coupons</div>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Code</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(!data?.unusedCoupons ||
                          data.unusedCoupons.length === 0) && (
                          <tr>
                            <td style={tdStyle}>No unused coupons</td>
                          </tr>
                        )}
                        {data?.unusedCoupons?.map((c) => (
                          <tr key={c.id}>
                            <td style={tdStyle}>{c.code}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>

                <div style={sectionHeaderStyle}>
                  💸 Revenue Impact of Coupons
                </div>
                <div style={{ overflowX: 'auto', marginBottom: 24 }}>
                  {revenueImpact ? (
                    <table style={tableStyle}>
                      <thead>
                        <tr>
                          <th style={thStyle}>Revenue With Coupon</th>
                          <th style={thStyle}>Revenue Without Coupon</th>
                          <th style={thStyle}>% Revenue With Coupon</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td style={tdStyle}>
                            {revenueImpact.withCoupon?.toLocaleString()}
                          </td>
                          <td style={tdStyle}>
                            {revenueImpact.withoutCoupon?.toLocaleString()}
                          </td>
                          <td style={tdStyle}>
                            {revenueImpact.percentWithCoupon}%
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  ) : (
                    <div>No revenue impact data</div>
                  )}
                </div>
              </>
            )}
            {activeTab === TAB_PLANS && (
              <>
                <h2
                  style={{
                    fontSize: 26,
                    fontWeight: 800,
                    margin: '40px 0 16px 0',
                    color: '#1a237e',
                    letterSpacing: -1,
                  }}
                >
                  🗺️ Plans
                </h2>
                {/* --- Top Plans --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🏆 Top Plans
                    <InfoIcon text="Top 10 plans by order count, with plan name, country, service provider, order count, and revenue." />
                  </div>
                  {data?.topPlans?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 700 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Plan</th>
                            <th style={topPlansHeaderCellStyle}>Country</th>
                            <th style={topPlansHeaderCellStyle}>Provider</th>
                            <th style={topPlansHeaderCellStyle}>
                              Validity (Days)
                            </th>
                            <th style={topPlansHeaderCellStyle}>Orders</th>
                            <th style={topPlansHeaderCellStyle}>Revenue</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.topPlans.map((p, idx) => (
                            <tr
                              key={p.planId}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansPlanCellStyle}>{p.name}</td>
                              <td style={topPlansCountryCellStyle}>
                                {flagImg(p.country, p.code)}
                                {p.country}
                              </td>
                              <td style={topPlansProviderCellStyle}>
                                {p.serviceProvider}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.validityDays ?? '-'}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.orderCount}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.revenue?.toLocaleString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No plan data</div>
                  )}
                </section>
                {/* --- Top Countries --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🌍 Top Countries
                    <InfoIcon text="Top 10 countries by plan orders and revenue." />
                  </div>
                  {data?.topCountries?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 500 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Country</th>
                            <th style={topPlansHeaderCellStyle}>Orders</th>
                            <th style={topPlansHeaderCellStyle}>Revenue</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.topCountries.map((c, idx) => (
                            <tr
                              key={c.country}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansCountryCellStyle}>
                                {flagImg(c.country, c.code)}
                                {c.country}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {c.orderCount}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {c.revenue?.toLocaleString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No country data</div>
                  )}
                </section>
                {/* --- Top Providers --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🏢 Top Service Providers
                    <InfoIcon text="Top 10 service providers by plan orders and revenue." />
                  </div>
                  {data?.topProviders?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 400 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Provider</th>
                            <th style={topPlansHeaderCellStyle}>Orders</th>
                            <th style={topPlansHeaderCellStyle}>Revenue</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.topProviders.map((p, idx) => (
                            <tr
                              key={p.serviceProvider}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansProviderCellStyle}>
                                {p.serviceProvider}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.orderCount}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.revenue?.toLocaleString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No provider data</div>
                  )}
                </section>
                {/* --- Plan Orders Trend by Country --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    📈 Plan Orders Trend by Country
                    <InfoIcon text="Orders per country per day (stacked line/bar chart)." />
                  </div>
                  {data?.planOrdersTrend?.length ? (
                    <div
                      style={{
                        width: '100%',
                        maxWidth: 900,
                        margin: '0 auto',
                        marginBottom: 32,
                      }}
                    >
                      <Line
                        data={{
                          labels: Array.from(
                            new Set(data.planOrdersTrend.map((t) => t.date)),
                          ),
                          datasets: (() => {
                            const countries = Array.from(
                              new Set(
                                data.planOrdersTrend.map((t) => t.country),
                              ),
                            );
                            return countries.map((country, idx) => ({
                              label: country,
                              data: Array.from(
                                new Set(
                                  data.planOrdersTrend.map((t) => t.date),
                                ),
                              ).map((date) => {
                                const found = data.planOrdersTrend.find(
                                  (t) =>
                                    t.date === date && t.country === country,
                                );
                                return found ? found.count : 0;
                              }),
                              borderColor: COLORS[idx % COLORS.length],
                              backgroundColor: COLORS[idx % COLORS.length],
                              fill: false,
                              tension: 0.3,
                            }));
                          })(),
                        }}
                        options={{
                          plugins: { legend: { position: 'top' } },
                          scales: {
                            x: { grid: { color: '#e0e0e0' } },
                            y: { grid: { color: '#e0e0e0' } },
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div>No trend data</div>
                  )}
                </section>
                {/* --- Plan Segment Popularity --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🧩 Plan Segment Popularity
                    <InfoIcon text="Popularity of plan segments (data volume, validity days, package type) by order count." />
                  </div>
                  {data?.planSegments?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 500 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Data Volume</th>
                            <th style={topPlansHeaderCellStyle}>
                              Validity Days
                            </th>
                            <th style={topPlansHeaderCellStyle}>
                              Package Type
                            </th>
                            <th style={topPlansHeaderCellStyle}>Orders</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.planSegments.map((s, idx) => (
                            <tr
                              key={idx}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansPlanCellStyle}>
                                {s.dataVolume}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {s.validityDays}
                              </td>
                              <td style={topPlansProviderCellStyle}>
                                {s.packageType}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {s.orderCount}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No segment data</div>
                  )}
                </section>
                {/* --- Top Plans by Coupon Usage --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🎟️ Top Plans by Coupon Usage
                    <InfoIcon text="Top 10 plans by coupon usage count." />
                  </div>
                  {data?.topPlanCoupons?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 350 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Plan</th>
                            <th style={topPlansHeaderCellStyle}>
                              Coupon Usage Count
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.topPlanCoupons.map((p, idx) => (
                            <tr
                              key={p.planId}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansPlanCellStyle}>{p.name}</td>
                              <td style={topPlansNumberCellStyle}>
                                {p.couponUsageCount}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No coupon usage data</div>
                  )}
                </section>
                {/* --- Price Bands --- */}
                <section style={sectionStyle}>
                  <div style={sectionHeaderStyle}>
                    💸 Price Bands
                    <InfoIcon text="Price quantiles: cheap (20th percentile), popular (50th), expensive (80th)." />
                  </div>
                  {data?.priceBands ? (
                    <div style={{ display: 'flex', gap: 32, flexWrap: 'wrap' }}>
                      <div style={cardStyle}>
                        <div
                          style={{
                            fontSize: 16,
                            color: '#888',
                            marginBottom: 8,
                          }}
                        >
                          Cheap (20th %ile)
                        </div>
                        <div
                          style={{
                            fontSize: 28,
                            fontWeight: 700,
                            color: '#1a237e',
                          }}
                        >
                          {data.priceBands.cheap?.toLocaleString()}
                        </div>
                      </div>
                      <div style={cardStyle}>
                        <div
                          style={{
                            fontSize: 16,
                            color: '#888',
                            marginBottom: 8,
                          }}
                        >
                          Popular (50th %ile)
                        </div>
                        <div
                          style={{
                            fontSize: 28,
                            fontWeight: 700,
                            color: '#1a237e',
                          }}
                        >
                          {data.priceBands.popular?.toLocaleString()}
                        </div>
                      </div>
                      <div style={cardStyle}>
                        <div
                          style={{
                            fontSize: 16,
                            color: '#888',
                            marginBottom: 8,
                          }}
                        >
                          Expensive (80th %ile)
                        </div>
                        <div
                          style={{
                            fontSize: 28,
                            fontWeight: 700,
                            color: '#1a237e',
                          }}
                        >
                          {data.priceBands.expensive?.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>No price band data</div>
                  )}
                </section>
                {/* --- Emerging Countries --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🚀 Emerging Countries
                    <InfoIcon text="Countries with highest order growth compared to previous period." />
                  </div>
                  {data?.emergingCountries?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 500 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Country</th>
                            <th style={topPlansHeaderCellStyle}>Growth</th>
                            <th style={topPlansHeaderCellStyle}>First 7d</th>
                            <th style={topPlansHeaderCellStyle}>Last 7d</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.emergingCountries.map((c, idx) => (
                            <tr
                              key={c.country}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansCountryCellStyle}>
                                {flagImg(c.country, c.code)}
                                {c.country}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {c.growth}
                              </td>
                              <td style={topPlansNumberCellStyle}>{c.first}</td>
                              <td style={topPlansNumberCellStyle}>{c.last}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No emerging country data</div>
                  )}
                </section>
                {/* --- Consistent Countries --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🔄 Consistent Countries
                    <InfoIcon text="Countries with steady order volume across periods." />
                  </div>
                  {data?.consistentCountries?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 400 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Country</th>
                            <th style={topPlansHeaderCellStyle}>First 7d</th>
                            <th style={topPlansHeaderCellStyle}>Last 7d</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.consistentCountries.map((c, idx) => (
                            <tr
                              key={c.country}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansCountryCellStyle}>
                                {flagImg(c.country, c.code)}
                                {c.country}
                              </td>
                              <td style={topPlansNumberCellStyle}>{c.first}</td>
                              <td style={topPlansNumberCellStyle}>{c.last}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No consistent country data</div>
                  )}
                </section>
                {/* --- Frequently Bought Cheap Plans --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    💸 Frequently Bought Cheap Plans
                    <InfoIcon text="Top 10 most frequently bought plans in the cheap price band." />
                  </div>
                  {data?.frequentlyBoughtCheapPlans?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 700 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Plan</th>
                            <th style={topPlansHeaderCellStyle}>Country</th>
                            <th style={topPlansHeaderCellStyle}>Provider</th>
                            <th style={topPlansHeaderCellStyle}>
                              Validity (Days)
                            </th>
                            <th style={topPlansHeaderCellStyle}>Orders</th>
                            <th style={topPlansHeaderCellStyle}>Price</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.frequentlyBoughtCheapPlans.map((p, idx) => (
                            <tr
                              key={p.planId}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansPlanCellStyle}>{p.name}</td>
                              <td style={topPlansCountryCellStyle}>
                                {flagImg(p.country, p.code)}
                                {p.country}
                              </td>
                              <td style={topPlansProviderCellStyle}>
                                {p.serviceProvider}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.validityDays ?? '-'}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.orderCount}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.price?.toLocaleString() ?? '-'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No cheap plan data</div>
                  )}
                </section>
                {/* --- Frequently Bought Expensive Plans --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    🏷️ Frequently Bought Expensive Plans
                    <InfoIcon text="Top 10 most frequently bought plans in the expensive price band." />
                  </div>
                  {data?.frequentlyBoughtExpensivePlans?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 700 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Plan</th>
                            <th style={topPlansHeaderCellStyle}>Country</th>
                            <th style={topPlansHeaderCellStyle}>Provider</th>
                            <th style={topPlansHeaderCellStyle}>
                              Validity (Days)
                            </th>
                            <th style={topPlansHeaderCellStyle}>Orders</th>
                            <th style={topPlansHeaderCellStyle}>Price</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.frequentlyBoughtExpensivePlans.map((p, idx) => (
                            <tr
                              key={p.planId}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansPlanCellStyle}>{p.name}</td>
                              <td style={topPlansCountryCellStyle}>
                                {flagImg(p.country, p.code)}
                                {p.country}
                              </td>
                              <td style={topPlansProviderCellStyle}>
                                {p.serviceProvider}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.validityDays ?? '-'}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.orderCount}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.price?.toLocaleString() ?? '-'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No expensive plan data</div>
                  )}
                </section>
                {/* --- Frequently Bought Plans --- */}
                <section style={enhancedSectionStyle}>
                  <div style={sectionHeaderStyle}>
                    📦 Frequently Bought Plans
                    <InfoIcon text="Top 10 most frequently bought plans overall (regardless of price)." />
                  </div>
                  {data?.frequentlyBoughtPlans?.length ? (
                    <div style={{ overflowX: 'auto' }}>
                      <table style={{ ...enhancedTableStyle, minWidth: 700 }}>
                        <thead>
                          <tr>
                            <th style={topPlansHeaderCellStyle}>Plan</th>
                            <th style={topPlansHeaderCellStyle}>Country</th>
                            <th style={topPlansHeaderCellStyle}>Provider</th>
                            <th style={topPlansHeaderCellStyle}>
                              Validity (Days)
                            </th>
                            <th style={topPlansHeaderCellStyle}>Orders</th>
                            <th style={topPlansHeaderCellStyle}>Price</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.frequentlyBoughtPlans.map((p, idx) => (
                            <tr
                              key={p.planId}
                              style={{
                                ...topPlansRowStyle,
                                ...(idx % 2 === 1
                                  ? { background: '#f7f9fc' }
                                  : {}),
                              }}
                              onMouseOver={(e) =>
                                (e.currentTarget.style.background = '#f1f4fa')
                              }
                              onMouseOut={(e) =>
                                (e.currentTarget.style.background =
                                  idx % 2 === 1 ? '#f7f9fc' : 'transparent')
                              }
                            >
                              <td style={topPlansPlanCellStyle}>{p.name}</td>
                              <td style={topPlansCountryCellStyle}>
                                {flagImg(p.country, p.code)}
                                {p.country}
                              </td>
                              <td style={topPlansProviderCellStyle}>
                                {p.serviceProvider}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.validityDays ?? '-'}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.orderCount}
                              </td>
                              <td style={topPlansNumberCellStyle}>
                                {p.price?.toLocaleString() ?? '-'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div>No frequently bought plan data</div>
                  )}
                </section>
              </>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default Dashboard;
