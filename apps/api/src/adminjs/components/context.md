# AdminJS Dashboard Analytics Context

## Definitions
- **Source**: The platform/service where the order originated (e.g., airtrip, global-esim-jp).
- **Affiliate**: The external referrer or partner that sent the user to our site.

## Analytics Filtering
- All analytics (counts, revenue, trends, top sources/affiliates, etc.) only consider orders with `paymentStatus === 'SUCCESS'` as successful payments.
- Conversion rates use all orders for the denominator, but only those with `paymentStatus: SUCCESS` for the numerator.

## Date Range Filter
- The dashboard has a global date range filter (default: last 7 days), with quick-select buttons for common ranges (Last 7 days, Last 30 days, This Month, Last Month, All Time).

## Troubleshooting Blank Analytics
- If **Top Sources**, **Top Affiliates**, **Revenue by Source/Affiliate** are blank:
  - There may be no successful orders in the selected date range.
  - The `source` or `affiliate` fields may be empty or null for all successful orders.
  - Try expanding the date range to "All Time" to check for data.

---

## **Dashboard Analytics Context (Saved State)**

### **Global Features**
- **Global date filter** (default: last 7 days, user can change/clear)
- **All visualizations respect the date filter**
- **Data is cached in Redis per date range for 1 hour**

---

### **Current & Planned Visualizations**

#### **Core Metrics**
- Total Users
- Total Orders
- Orders by App ID

#### **Payment Analytics**
- Top Payment Methods (pie chart)
- Orders by Day (stacked/grouped by payment method)

#### **Customer Analytics**
- Top 10 Customers (by order count, by total spent)

#### **Coupon Analytics**
- Top Coupons (table)
- Most Used Coupons (trend line)

#### **Source & Affiliate Analytics (Planned/To Implement)**
- Orders by Source (trend, grouped/stacked bar or line)
- Orders by Affiliate (trend, grouped/stacked bar or line)
- Top Sources (bar/pie)
- Top Affiliates (bar/pie)
- Source vs. Affiliate Matrix (heatmap/table)
- Revenue by Source/Affiliate (bar/line)
- Orders by Source/Affiliate Over Time (trend)
- (If possible) Conversion Rate by Source/Affiliate

---

### **Backend Requirements**
- Dashboard handler accepts `startDate` and `endDate` query params.
- All metrics are filtered by date range if provided.
- Redis cache key includes date range.

---

### **Frontend Requirements**
- Date range filter at top (default: last 7 days).
- All charts/tables update on filter.
- Modern, clean, responsive design.

---

### **Next Steps**
- Implement backend queries for all source/affiliate analytics.
- Add frontend visualizations for all new metrics.

---

## 2024-06: Per-Tab Analytics Fetch Optimization
- The backend dashboard handler now accepts a `tab` query parameter (e.g., 'Orders', 'Users', 'Coupons', 'Plans').
- Only the analytics data for the requested tab is fetched and returned, reducing load and improving performance.
- The frontend now sends the `tab` parameter with every dashboard data request, so only the active tab's analytics are loaded.
- The response schema for each tab remains unchanged, and the UI is not affected.
- This optimization is especially beneficial for analytics-heavy tabs (e.g., Coupons, Plans).

---
