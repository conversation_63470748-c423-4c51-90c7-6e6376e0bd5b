// @ts-nocheck
import { ApiClient } from 'adminjs';
import React, { useEffect, useRef, useState } from 'react';

const sectionStyle = {
  margin: '48px 0',
  padding: '40px 32px',
  borderRadius: 18,
  background: '#fff',
  boxShadow: '0 4px 24px 0 rgba(0,0,0,0.07)',
  border: '1px solid #e0e0e0',
  maxWidth: 600,
  marginLeft: 'auto',
  marginRight: 'auto',
};

const buttonStyle = {
  padding: '12px 32px',
  borderRadius: 8,
  border: 'none',
  background: '#1a237e',
  color: '#fff',
  fontWeight: 700,
  fontSize: 18,
  cursor: 'pointer',
  marginTop: 24,
};

const Settings = () => {
  const api = new ApiClient();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [keys, setKeys] = useState<string[]>([]);
  const [keysLoading, setKeysLoading] = useState(false);
  const [keysError, setKeysError] = useState('');
  const [search, setSearch] = useState('');
  const [selectedKey, setSelectedKey] = useState('');
  const apiRef = useRef<any>(null);

  const fetchKeys = async () => {
    setKeysLoading(true);
    setKeysError('');
    try {
      const res = await api.getPage({ pageName: 'Settings', method: 'get' });
      setKeys(res.data.keys || []);
    } catch (e) {
      setKeysError('Error fetching keys');
    }
    setKeysLoading(false);
  };

  useEffect(() => {
    fetchKeys();
  }, []);

  const handleDeleteKey = async () => {
    if (!selectedKey) return;
    setLoading(true);
    setMessage('');
    try {
      const res = await api.getPage({
        pageName: 'Settings',
        method: 'post',
        data: { key: selectedKey },
      });
      if (res && res.data && res.data.success) {
        setMessage(`✅ Deleted key: ${selectedKey}`);
        setSelectedKey('');
        fetchKeys();
      } else {
        setMessage('⚠️ Failed to delete key.');
      }
    } catch (e) {
      setMessage('❌ Error deleting key.');
    }
    setLoading(false);
  };

  const filteredKeys = keys.filter((k) =>
    k.toLowerCase().includes(search.toLowerCase()),
  );

  return (
    <section style={sectionStyle}>
      <h2
        style={{
          fontSize: 28,
          fontWeight: 800,
          marginBottom: 24,
          color: '#1a237e',
        }}
      >
        ⚙️ Settings
      </h2>
      <div style={{ fontSize: 18, marginBottom: 18 }}>
        Use the button below to clear the backend analytics cache (Redis). This
        will force all analytics to be recalculated on next load.
      </div>
      <div style={{ marginBottom: 18 }}>
        <b>Current Redis Keys (excluding BullMQ):</b>
        <button
          style={{
            marginLeft: 16,
            padding: '4px 12px',
            borderRadius: 6,
            border: '1px solid #ccc',
            background: '#f7f7f7',
            cursor: 'pointer',
            fontWeight: 600,
          }}
          disabled={keysLoading}
          onClick={fetchKeys}
        >
          {keysLoading ? 'Refreshing...' : 'Refresh'}
        </button>
        <div style={{ marginTop: 8, fontSize: 15 }}>
          {keysLoading ? (
            'Loading keys...'
          ) : keysError ? (
            <span style={{ color: 'red' }}>{keysError}</span>
          ) : (
            `${keys.length} key(s) found.`
          )}
        </div>
        {/* Searchable Dropdown */}
        <div style={{ marginTop: 12 }}>
          <input
            type="text"
            placeholder="Search keys..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            style={{
              padding: '6px 12px',
              borderRadius: 6,
              border: '1px solid #ccc',
              width: '100%',
              marginBottom: 8,
              fontSize: 15,
            }}
          />
          <select
            value={selectedKey}
            onChange={(e) => setSelectedKey(e.target.value)}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: 6,
              border: '1px solid #ccc',
              fontSize: 15,
            }}
          >
            <option value="">Select a key to delete...</option>
            {filteredKeys.map((k) => (
              <option key={k} value={k}>
                {k}
              </option>
            ))}
          </select>
          <button
            style={{
              ...buttonStyle,
              marginTop: 12,
              background: selectedKey ? '#c62828' : '#aaa',
              cursor: selectedKey ? 'pointer' : 'not-allowed',
            }}
            onClick={handleDeleteKey}
            disabled={!selectedKey || loading}
          >
            {loading ? 'Deleting...' : 'Delete Key'}
          </button>
        </div>
      </div>

      {message && <div style={{ marginTop: 18, fontSize: 17 }}>{message}</div>}
    </section>
  );
};

export default Settings;
