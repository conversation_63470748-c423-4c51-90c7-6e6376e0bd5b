import React, { useEffect, useState } from 'react';
//@ts-expect-error
import { ApiClient } from 'adminjs';
const sectionStyle = {
  margin: '48px 0',
  padding: '40px 32px',
  borderRadius: 18,
  background: '#fff',
  boxShadow: '0 4px 24px 0 rgba(0,0,0,0.07)',
  border: '1px solid #e0e0e0',
  maxWidth: 1200,
  marginLeft: 'auto',
  marginRight: 'auto',
};

const tableStyle: React.CSSProperties = {
  width: '100%',
  marginTop: 24,
};
const thStyle = {
  background: '#f7f7f7',
  fontWeight: 700,
  padding: '10px 8px',
  border: '1px solid #e0e0e0',
};
const tdStyle = {
  padding: '8px',
  border: '1px solid #e0e0e0',
  fontSize: 15,
};

type CouponOrderRow = {
  id: string;
  orderId: string;
  couponCode: string;
  userName: string;
  userLastName?: string;
  userEmail: string;
  usageDate: string;
  amount: string | number;
};

const CouponOrdersPage = () => {
  const api = new ApiClient();

  const [data, setData] = useState<CouponOrderRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');

  const fetchData = async () => {
    setLoading(true);
    setError('');
    try {
      const res = await api.getPage({
        pageName: 'CouponOrders',
        data: { search },
      });
      const rows =
        res.data &&
        typeof res.data === 'object' &&
        'rows' in res.data &&
        Array.isArray((res.data as any).rows)
          ? (res.data as any).rows
          : [];
      setData(rows);
    } catch (e) {
      setError('Failed to fetch coupon orders');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line
  }, [search]);

  const handleExport = () => {
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    params.append('export', '1');
    window.open(
      '/api/v1/xyz-main/pages/coupon-orders?' + params.toString(),
      '_blank',
    );
  };

  return (
    <section style={sectionStyle}>
      <h2
        style={{
          fontSize: 28,
          fontWeight: 800,
          marginBottom: 24,
          color: '#1a237e',
        }}
      >
        🧾 Coupon Orders
      </h2>

      {loading ? (
        <div>Loading...</div>
      ) : error ? (
        <div style={{ color: 'red' }}>{error}</div>
      ) : (
        <div style={{ overflowX: 'auto' }}>
          <table style={tableStyle}>
            <thead>
              <tr>
                <th style={thStyle}>Coupon Code</th>
                <th style={thStyle}>User Name</th>
                <th style={thStyle}>User Email</th>
                <th style={thStyle}>Usage Date</th>
                <th style={thStyle}>Amount</th>
              </tr>
            </thead>
            <tbody>
              {data.length === 0 && (
                <tr>
                  <td colSpan={5} style={tdStyle}>
                    No coupon orders found
                  </td>
                </tr>
              )}
              {data.map((row) => (
                <tr key={row.id}>
                  <td style={tdStyle}>{row.couponCode}</td>
                  <td style={tdStyle}>
                    {row.userName} {row.userLastName ? row.userLastName : ''}
                  </td>
                  <td style={tdStyle}>{row.userEmail}</td>
                  <td style={tdStyle}>{row.usageDate}</td>
                  <td style={tdStyle}>{row.amount}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </section>
  );
};

export default CouponOrdersPage;
