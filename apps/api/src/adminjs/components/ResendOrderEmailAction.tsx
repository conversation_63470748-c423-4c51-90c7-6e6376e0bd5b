import React, { useEffect, useState } from 'react';

const ResendOrderEmailAction = (props) => {
  const { record, resource, action } = props;
  const [loading, setLoading] = useState(false);
  const [notice, setNotice] = useState<{
    message: string;
    type: 'success' | 'error';
  } | null>(null);

  if (!record) return <div>No record found.</div>;

  useEffect(() => {
    setTimeout(() => window.history.back(), 1200);
  }, []);

  return (
    <div style={{ padding: 24 }}>
      Task added to queue. Please wait...Redirecting...
    </div>
  );
};

export default ResendOrderEmailAction;
