// @ts-nocheck
import { JsonEditor } from 'json-edit-react';
import React, { useState } from 'react';
// @ts-ignore
import { BasePropertyComponentProps } from 'adminjs';
import { cloneDeep, set } from 'lodash';

const unflatten = (obj) => {
  const result = {};
  for (const key in obj) {
    set(result, key, obj[key]);
  }
  return result;
};

const AppJSONEditor = (props: BasePropertyComponentProps) => {
  const { property, record, onChange } = props;
  const value = record.params[property.name];
  const [text, setText] = useState(
    unflatten(props.record.params)[property.name],
  );
  const [error, setError] = useState<string | null>(null);

  const handleChange = (changeObj) => {
    const updated = cloneDeep(text);
    set(updated, changeObj.path, changeObj.newValue);
    setText(updated);
    setError(null);
    onChange(property.name, updated);
  };

  return (
    <div>
      <label>{property.label}</label>
      <JsonEditor
        restrictAdd={props.where !== 'edit'}
        restrictDelete={props.where !== 'edit'}
        restrictMove={props.where !== 'edit'}
        restrictCopy={props.where !== 'edit'}
        restrictPaste={props.where !== 'edit'}
        restrictCut={props.where !== 'edit'}
        restrictCopyPaste={props.where !== 'edit'}
        restrictCopyPasteMove={props.where !== 'edit'}
        restrictCopyPasteMoveCut={props.where !== 'edit'}
        restrictEdit={props.where !== 'edit'}
        data={text}
        setData={setText}
        onChange={handleChange}
        onError={setError}
      />
    </div>
  );
};

export default AppJSONEditor;
