# Analytics Section Specification for AdminJS Dashboard

## 1. Section Header
- **Title:** Clear, descriptive, and includes an emoji or icon for quick recognition (e.g., 🛒 Orders).
- **Info Tooltip:** (Optional) Short explanation of the section's purpose or key terms.

## 2. Data Visualization
- **Primary Chart/Table:**  
  - Use an appropriate chart (Bar, Pie, Line, etc.) for the metric.
  - If a chart is not suitable, use a styled table.
- **Fallback State:**  
  - If there is no data, show a clear "No data" message or fallback table.
- **Legend/Labels:**  
  - All charts must have clear legends and axis labels.
  - Table columns must be labeled.

## 3. Data Source & Filtering
- **Date Range Respect:**  
  - All data must be filtered by the global date range.
- **Loading/Error Handling:**  
  - Show a loading spinner/message while fetching.
  - Show a clear error message if data fetch fails.

## 4. Layout & Responsiveness
- **Section Spacing:**  
  - Use consistent margins/padding between sections.
- **Card/Table Styling:**  
  - Use a card or table style consistent with the dashboard's design.
- **Responsive:**  
  - Charts/tables must be readable on various screen sizes.

## 5. Accessibility
- **Keyboard Navigation:**  
  - All interactive elements (tabs, buttons) must be keyboard accessible.
- **Contrast & Font Size:**  
  - Sufficient color contrast and readable font sizes.

## 6. Extensibility
- **Componentization:**  
  - Each section should be easy to extract into a standalone React component.
- **Defensive Coding:**  
  - Handle missing/undefined/null data gracefully.

## 7. Documentation
- **Inline Comments:**  
  - Briefly describe the purpose of the section and any non-obvious logic.
- **Section Summary:**  
  - (Optional) At the top of each section/component, include a summary comment of what it visualizes and any business rules.

---

## Example Section Skeleton

```jsx
<section style={sectionStyle}>
  <div style={sectionHeaderStyle}>
    🏆 Top Sources
    <InfoIcon text="The platform/service where the order originated (e.g., airtrip, global-esim-jp)" />
  </div>
  {data?.topSources && data.topSources.length > 0 ? (
    <Pie ... />
  ) : (
    <div>No source data</div>
  )}
</section>
```

---

## Summary Table

| Requirement         | Description                                                                 |
|---------------------|-----------------------------------------------------------------------------|
| Header              | Title + (optional) info tooltip                                              |
| Visualization       | Chart or table, with fallback if no data                                     |
| Data Filtering      | Respects global date range                                                   |
| Loading/Error       | Shows loading and error states                                               |
| Layout              | Consistent spacing, card/table style, responsive                             |
| Accessibility       | Keyboard accessible, good contrast, readable font                            |
| Extensibility       | Componentizable, handles missing data                                        |
| Documentation       | Inline comments, section summary                                             |

---

**Following this spec will ensure all dashboard sections are consistent, user-friendly, and easy to maintain or extend.** 