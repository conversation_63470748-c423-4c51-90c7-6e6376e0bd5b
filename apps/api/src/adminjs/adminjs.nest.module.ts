import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from 'src/auth/auth.module';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { createAdminJsOptions } from './adminjs.module';

@Module({})
export class AdminJsNestModule {
    static async registerAsync(): Promise<DynamicModule> {
        const { AdminModule } = await import('@adminjs/nestjs');
        return AdminModule.createAdminAsync({
            imports: [ConfigModule, AuthModule],
            inject: [ConfigService, AwsCognitoService],
            useFactory: async (configService: ConfigService, authService: AwsCognitoService, esimOrderService: EsimOrdersService) => {
                // createAdminJsOptions returns { adminJsOptions, auth, sessionOptions }
                // But AdminModule.createAdminAsync expects the full AdminModuleOptions object
                // So we need to spread the result or return as is
                const options = await createAdminJsOptions(configService);
                return {
                    ...options,
                };
            },
        });
    }
} 