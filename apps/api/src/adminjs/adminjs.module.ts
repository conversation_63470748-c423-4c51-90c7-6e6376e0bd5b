import { ConfigService } from '@nestjs/config';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { AwsCognitoService } from 'src/auth/aws-cognito.service';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import { getPlanDetailsApi } from 'src/utils';
import { dashboardHandler } from './dashboard.handler';

async function createAdminJsOptions(configService: ConfigService) {
    const [{ Database, Resource, getModelByName }, importExportModule, loggerModule, { AdminJS, ComponentLoader }] = await Promise.all([
        import('@adminjs/prisma'),
        import('@adminjs/import-export'),
        import('@adminjs/logger'),
        import('adminjs'),
    ]);
    const importExportFeature = importExportModule.default;
    const loggerFeature = loggerModule.default;
    const { createLoggerResource } = loggerModule;
    const componentLoader = new ComponentLoader();
    const Components = {
        JsonEditor: componentLoader.add('JsonEditor', './components/JsonEditor.tsx'),
        Dashboard: componentLoader.add('Dashboard', './components/Dashboard.tsx'),
        Settings: componentLoader.add('Settings', './components/Settings.tsx'),
        ResendOrderEmailAction: componentLoader.add('ResendOrderEmailAction', './components/ResendOrderEmailAction.tsx'),
        UserInfoCell: componentLoader.add('UserInfoCell', './components/UserInfoCell.tsx'),
        CouponOrdersPage: componentLoader.add('CouponOrdersPage', './components/CouponOrdersPage.tsx'),

    };
    AdminJS.registerAdapter({ Database, Resource });
    const prisma = new PrismaClient();
    const redisClient = new Redis({
        host: configService.get('redis.host') || 'localhost',
        port: parseInt(configService.get('redis.port') || '6379', 10),
    });
    let redisStore = undefined;
    try {
        const { RedisStore } = await import('connect-redis');
        redisStore = new RedisStore({ client: redisClient, prefix: 'adminjs-session:' });
    } catch (e) { }
    const models = [
        'users', 'orders', 'coupons', 'constraints', 'coupons_constraints', 'plans_prices', 'plans', 'networks', 'countries', 'corporates', 'apps', 'kvStore', 'esim_stocks', 'service_providers', 'corporates_users', "email_templates"
    ];
    const removeNullsBeforeHook = async (request) => {
        if (request.payload) {
            Object.keys(request.payload).forEach((key) => {
                if (request.payload[key] === null) {
                    delete request.payload[key];
                }
            });
        }
        return request;
    };
    const logResource = createLoggerResource({
        componentLoader,
        resource: {
            model: getModelByName('Log'),
            client: prisma,
        },
        featureOptions: {
            componentLoader,
            propertiesMapping: {
                recordId: 'id',
                user: 'userId',
                recordTitle: 'title',
                action: 'action',
                createdAt: 'createdAt',
                updatedAt: 'updatedAt',
                difference: 'difference',
            },
            userIdAttribute: 'userId',
            resourceOptions: {
                navigation: { name: 'Logs', icon: 'Document' },
            },
        },
    });
    return {
        adminJsOptions: {
            rootPath: '/api/v1/xyz-main',
            logoutPath: '/api/v1/xyz-main/exit',
            loginPath: '/api/v1/xyz-main/sign-in',
            componentLoader,
            resources: [
                logResource,
                ...models.map((model) => {
                    if (model === 'Log' || model === 'logs') return null;
                    const baseResource = {
                        resource: {
                            model: getModelByName(model),
                            client: prisma,
                        },
                        options: {
                            actions: { delete: { isAccessible: false } },
                        },
                        features: [
                            loggerFeature({
                                componentLoader,
                                propertiesMapping: { user: 'userId', recordTitle: 'name' },
                                userIdAttribute: 'id',
                            }),
                            importExportFeature({ componentLoader }),
                        ],
                    };
                    const fullModel = getModelByName(model);
                    baseResource.options = {
                        ...baseResource.options,
                        properties: {
                            ...fullModel.fields
                                .filter(field => field.type === 'Json')
                                .reduce((acc, curr) => {
                                    acc[curr.name] = {
                                        type: 'string',
                                        components: {
                                            edit: Components.JsonEditor,
                                            show: Components.JsonEditor,
                                            filter: Components.JsonEditor,
                                        },
                                    };
                                    return acc;
                                }, {})
                        },
                    } as any;
                    if (model === 'orders') {
                        baseResource.options = {
                            ...baseResource.options,
                            actions: {
                                ...((baseResource.options.actions as any) || {}),

                                resendOrderEmail: {
                                    actionType: 'record',
                                    icon: 'Email',
                                    label: 'Retry/Resend Email',
                                    isVisible: true,
                                    guard: 'Are you sure you want to retry the order email?',
                                    component: Components.ResendOrderEmailAction,
                                    handler: async (request, response, context) => {
                                        const { record, currentAdmin } = context;
                                        if (!record) {
                                            return {
                                                notice: {
                                                    message: 'Order not found',
                                                    type: 'error',
                                                },
                                            };
                                        }
                                        try {
                                            const order = record.toJSON(currentAdmin).params
                                            const app = (global as any).NEST_APP_INSTANCE;
                                            const esimOrderService = app.get(EsimOrdersService);

                                            await esimOrderService.queueESIMPurchaseRequest({
                                                planId: order.plan + '',
                                                lang: order.lang,
                                                chargeAmount: order.jpyPrice + '',
                                                metadata: order.orderMetaData || {},
                                                orderId: order.id,
                                                planUrl: getPlanDetailsApi(order.planId + ''),
                                                stripeChargeId: '',
                                                retryKey: 'adminjs' + Date.now(),
                                            });
                                            return {
                                                record: record.toJSON(currentAdmin),
                                                msg: 'Task added to queue',
                                            }
                                        } catch (e) {
                                            return {
                                                notice: {
                                                    message: 'Failed to resend order email: ' + (e.message || e),
                                                    type: 'error',
                                                },
                                            };
                                        }
                                    },
                                },
                            },
                        } as any;
                    }

                    if (model === 'coupons_orders') {
                        baseResource.options = {
                            ...baseResource.options,
                            listProperties: ["id"],
                            navigation: false,
                            properties: {
                                listProperties: ["id"],
                                ...((baseResource.options as any).properties || {}),
                                user: {
                                    reference: null, // 🛑 Stop AdminJS from trying to join `users`
                                    isVisible: false, // Optional: hide if not needed
                                },
                                userId: {
                                    isVisible: { list: true, show: true, edit: true, filter: true }, // ✅ show plain userId
                                },
                            },
                        } as any;
                    }
                    if (model === 'coupons') {
                        baseResource.options = {
                            ...baseResource.options,
                            actions: {
                                ...((baseResource.options.actions as any) || {}),
                                edit: {
                                    ...((baseResource.options.actions as any)?.edit || {}),
                                    before: removeNullsBeforeHook,
                                },
                                new: {
                                    ...((baseResource.options.actions as any)?.new || {}),
                                    before: removeNullsBeforeHook,
                                },
                            },
                        } as any;
                    }
                    if (model === 'email_templates') {
                        baseResource.options = {
                            ...baseResource.options,
                            properties: {
                                body: {
                                    type: 'richtext',
                                },
                            },
                            actions: {
                                ...((baseResource.options.actions as any) || {}),
                                edit: {
                                    ...((baseResource.options.actions as any)?.edit || {}),
                                    before: removeNullsBeforeHook,
                                },
                                new: {
                                    ...((baseResource.options.actions as any)?.new || {}),
                                    before: removeNullsBeforeHook,
                                },
                            },
                        } as any;
                    }
                    if (model === 'users') {
                        baseResource.options = {
                            ...baseResource.options,
                            actions: {
                                ...((baseResource.options.actions as any) || {}),
                                resendVerificationEmail: {
                                    actionType: 'record',
                                    icon: 'Email',
                                    label: 'Resend Verification Email',
                                    isVisible: true,
                                    guard: 'Are you sure you want to resend the verification email?',
                                    handler: async (request, response, context) => {
                                        const { record, currentAdmin } = context;
                                        if (!record) {
                                            return {
                                                notice: {
                                                    message: 'user not found',
                                                    type: 'error',
                                                },
                                            };
                                        }
                                        try {
                                            const app = (global as any).NEST_APP_INSTANCE;
                                            const awsCognitoService = app.get(AwsCognitoService);

                                            await awsCognitoService.resendVerificationEmail(
                                                record.toJSON(currentAdmin).email,
                                                record.toJSON(currentAdmin).phone_number,
                                                record.toJSON(currentAdmin).userPool
                                            );
                                            return {
                                                record: record.toJSON(currentAdmin),
                                                msg: 'Task added to queue',
                                            }
                                        } catch (e) {
                                            return {
                                                notice: {
                                                    message: 'Failed to resend order email: ' + (e.message || e),
                                                    type: 'error',
                                                },
                                            };
                                        }
                                    },
                                },
                                resendResetPasswordEmail: {
                                    actionType: 'record',
                                    icon: 'Email',
                                    label: 'Send Reset Password Email',
                                    isVisible: true,
                                    guard: 'Are you sure you want to send a reset password email?',
                                    handler: async (request, response, context) => {
                                        const { record, currentAdmin } = context;
                                        if (!record) {
                                            return {
                                                notice: {
                                                    message: 'User not found',
                                                    type: 'error',
                                                },
                                            };
                                        }
                                        try {
                                            const app = (global as any).NEST_APP_INSTANCE;
                                            const awsCognitoService = app.get(AwsCognitoService);
                                            await awsCognitoService.forgotUserPassword({ email: record.toJSON(currentAdmin).email }, record.toJSON(currentAdmin).userPool);
                                            return {
                                                record: record.toJSON(currentAdmin),
                                                msg: 'Task added to queue',
                                            }
                                        } catch (e) {
                                            return {
                                                notice: {
                                                    message: 'Failed to send reset password email: ' + (e.message || e),
                                                    type: 'error',
                                                },
                                            };
                                        }
                                    },
                                },
                                forceVerify: {
                                    actionType: 'record',
                                    icon: 'Email',
                                    label: 'Force Verify',
                                    isVisible: true,
                                    guard: 'Are you sure you want to force verify the user?',
                                    handler: async (request, response, context) => {
                                        const { record, currentAdmin } = context;
                                        if (!record) {
                                            return {
                                                notice: {
                                                    message: 'user not found',
                                                    type: 'error',
                                                },
                                            };
                                        }
                                        try {
                                            const app = (global as any).NEST_APP_INSTANCE;
                                            const awsCognitoService = app.get(AwsCognitoService);
                                            await awsCognitoService.forceVerify(record.toJSON(currentAdmin).username, record.toJSON(currentAdmin).userPool);
                                            return {
                                                record: record.toJSON(currentAdmin),
                                                msg: 'Task added to queue',
                                            }
                                        } catch (e) {
                                            return {
                                                notice: {
                                                    message: 'Failed to resend order email: ' + (e.message || e),
                                                    type: 'error',
                                                },
                                            };
                                        }
                                    },
                                },
                            },
                        } as any;
                    }
                    return baseResource;
                }).filter(Boolean),
            ],
            dashboard: {
                component: Components.Dashboard,
                handler: (req, res, context) => dashboardHandler(req, res, context, redisClient, prisma),
            },
            pages: {
                Settings: {
                    label: 'Settings',
                    icon: 'Settings',
                    component: Components.Settings,
                    handler: async (request, response, context) => {
                        if (request.method === 'post') {
                            if (!request.payload.key) return { success: false, error: 'Key is required' };

                            if (request.payload.key === '*' || request.payload.key === 'all' || request.payload.key === 'all-keys'
                                || request.payload.key === 'all-keys-with-prefix'
                                || request.payload.key === 'all-keys-with-prefix:'
                                || request.payload.key === 'all-keys-with-prefix:*'
                                || request.payload.key === 'all-keys-with-prefix:*'
                                || request.payload.key.includes('*') || request.payload.key.includes('bull') || request.payload.key.includes('bullmq')
                            ) {
                                return { success: true, deleted: 0 };
                            }
                            await redisClient.del(request.payload.key);
                            return { success: true, deleted: 1 };
                        }
                        const allKeys = await redisClient.keys('*');
                        const filtered = allKeys.filter(k => !k.startsWith('bull') && !k.startsWith('bullmq'));

                        return { success: true, keys: filtered };
                    },
                },
                CouponOrders: {
                    label: 'Coupon Orders',
                    icon: 'Document',
                    component: Components.CouponOrdersPage,
                    handler: async (request, response, context) => {
                        const search = (request.query?.search || request.payload?.search || '').toString().trim();
                        let whereClause = '';
                        let params: any[] = [];
                        if (search) {
                            whereClause = `AND (coupons.code LIKE ? OR users.email LIKE ?)`;
                            params = [`%${search}%`, `%${search}%`];
                        }
                        const sql = `
                            SELECT
                                coupons_orders.id,
                                coupons_orders.orderId,
                                coupons.code AS couponCode,
                                users.firstName AS userName,
                                users.lastName AS userLastName,
                                users.email AS userEmail,
                                DATE(orders.createdAt) AS usageDate,
                                orders.jpyPrice AS amount
                            FROM
                                coupons_orders
                            LEFT JOIN coupons ON coupons.id = coupons_orders.couponsId
                            LEFT JOIN orders ON orders.id = coupons_orders.orderId
                            LEFT JOIN users ON users.userId = coupons_orders.userId
                            WHERE
                                coupons_orders.state = "COMPLETED"
                                ${whereClause}
                            ORDER BY usageDate DESC, coupons_orders.id DESC
                            LIMIT 500
                        `;
                        const rows = await prisma.$queryRawUnsafe(sql, ...params) as any[];
                        if (request.query?.export === '1') {
                            const csvRows = [
                                'orderId,couponCode,userName,userEmail,usageDate,amount',
                                ...rows.map(r => [r.orderId, r.couponCode, r.userName, r.userEmail, r.usageDate, r.amount].join(',')),
                            ];
                            const csv = csvRows.join('\n');
                            response.setHeader('Content-Type', 'text/csv');
                            response.setHeader('Content-Disposition', 'attachment; filename="coupon_orders.csv"');
                            response.send(csv);
                            return;
                        }
                        return { rows };
                    },
                },
            },
            branding: { companyName: 'Inbound Platform Corp.' },
        },
        auth: {
            authenticate: async (username: string, password: string) => {
                const app = (global as any).NEST_APP_INSTANCE;
                const authService = app.get(AwsCognitoService);
                const authResult = await authService.authenticateUser({ email: username, password, grant_type: 'password' });
                if (authResult && authResult.accessToken) {
                    const user = await prisma.users.findFirst({
                        where: { email: username }, include: {
                            corporates_users: {
                                include: {
                                    corporate: true
                                }
                            },
                        }
                    });
                    const kvStore = await prisma.kvStore.findFirst({
                        where: {
                            key: 'super_admin_corporate_ids'
                        }
                    });
                    const superAdminCorporate = user.corporates_users.find(cu => Array.isArray(kvStore.value) && kvStore.value.includes(cu.corporate.id))
                    if (!superAdminCorporate.enabled) return null
                    if (
                        kvStore &&
                        superAdminCorporate
                    ) {
                        return { email: user.email, ...user, id: String(user.id), corporateId: superAdminCorporate.corporate.id };
                    }
                }
                return null;
            },
            cookieName: 'adminjs-session',
            cookiePassword: process.env.API_LAYER_EXCHANGE_KEY || 'default-secret',
        },
        sessionOptions: redisStore
            ? {
                store: redisStore,
                resave: false,
                saveUninitialized: false,
                secret: process.env.API_LAYER_EXCHANGE_KEY || 'default-secret',
                cookie: {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    sameSite: true,
                    maxAge: 1000 * 60 * 60 * 24,
                },
            }
            : undefined,
    };
}

export { createAdminJsOptions };
