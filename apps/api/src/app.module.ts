import { CacheModule } from '@nestjs/cache-manager';
import { Logger, MiddlewareConsumer, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { JwtModule } from '@nestjs/jwt';
import { NestExpressApplication } from '@nestjs/platform-express';
import { ScheduleModule } from '@nestjs/schedule';
import { TerminusModule } from '@nestjs/terminus';
import { ThrottlerModule } from '@nestjs/throttler';
import { GoogleRecaptchaModule } from '@nestlab/google-recaptcha';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';

import * as redisStore from 'cache-manager-redis-store';
import { loyalty } from 'config/loyalty';
import { loyaltyReminder } from 'config/loyalty-reminder.config';
import notificationConfiguration from 'config/notification.configuration';
import redisConfiguration from 'config/redis.configuration';
import referral from 'config/referral';
import { CookieResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import * as path from 'path';
import { AdminJsNestModule } from './adminjs/adminjs.nest.module';
import { ApikeyModule } from './apikey/apikey.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AppsModule } from './apps/apps.module';
import { AppsService } from './apps/apps.service';
import { AuthModule } from './auth/auth.module';
import { AwsCognitoService } from './auth/aws-cognito.service';
import { BullBoardModule } from './bull-board/bull-board.module';
import { CampaignsModule } from './campaigns/campaigns.module';
import { CorporatesModule } from './corporates/corporates.module';
import { CouponsModule } from './coupons/coupons.module';
import { CouponsService } from './coupons/coupons.service';
import { DataUsageLogsModule } from './data-usage-logs/data-usage-logs.module';
import { EmailTemplateModule } from './email-template/email-template.module';
import { EmailsModule } from './emails/emails.module';
import { EmailsService } from './emails/emails.service';
import { EsimImporterModule } from './esim-importer/esim-importer.module';
import { EsimOrdersModule } from './esim-orders/esim-orders.module';
import { EsimOrdersService } from './esim-orders/esim-orders.service';
import { EsimStocksModule } from './esim-stocks/esim-stocks.module';
import { EsimStocksService } from './esim-stocks/esim-stocks.service';
import { EsimUsageRulesModule } from './esim-usage-rules/esim-usage-rules.module';
import { EsimModule } from './esim/esim.module';
import { EsimService } from './esim/esim.service';
import { EsimProviderBuilder } from './esim/providers/EsimProviderBuilder';
import { EventDevNotify } from './events/handlers/event-dev-notify.listener';
import { HealthController } from './health/health.controller';
import { HealthModule } from './health/health.module';
import { TerminusLogger } from './health/terminus-logger.service';
import { CustomLanguagePayloadResolveer } from './i18n-resolvers/CustomLanguagePayloadResolveer';
import { KeyValueStoreModule } from './key-value-store/key-value-store.module';
import { KeyValueStoreService } from './key-value-store/key-value-store.service';
import { BasicAuthMiddleware } from './middleware/BasicAuthMiddleware';
import { AppLoggerMiddleware } from './middleware/httpLogMiddleware';
import { NotificationsModule } from './notifications/notifications.module';
import { PaymentModule } from './payment/payment.module';
import { PaymentService } from './payment/payment.service';
import { payPayConfig } from './paypay-payment/config/paypay.config';
import { PaypayPaymentModule } from './paypay-payment/paypay-payment.module';
import { PlansModule } from './plans/plans.module';
import { PlansService } from './plans/plans.service';
import { PrismaService } from './prisma.service';
import { BulkEsimPurchaseWorker } from './processors/bulk-esim-purchase.worker';
import { QueueEmailDispatcherWorker } from './processors/email-dispatcher.worker';
import { EsimPurchaseQueueWorker } from './processors/esim-create-buy.worker';
import { EsimPurchaseQueueProcessorModule } from './processors/EsimPurchaseQueueProcessor.module';
import { LoyaltyReminderWorker } from './processors/loyalty-reminder.worker';
import { NewEsimWorker } from './processors/new-esim.worker';
import { WebhookExecutorWorker } from './processors/webhook-executor.worker';
import { QrModule } from './qr/qr.module';
import { QueueModule } from './queue/queue.module';
import { RateModule } from './rate/rate.module';
import { ReferralsModule } from './referrals/referrals.module';
import { RolesGuard } from './roles/roles.guard';
import { SchedulerModule } from './scheduler/scheduler.module';
import { SessionCartModule } from './session-cart/session-cart.module';
import { ProxyAwareThrottlerGuard } from './throttler-behind-proxy.guard';
import { TransformInterceptor } from './transform.interceptor';
import { UkomiReviewModule } from './ukomi-review/ukomi-review.module';
import { UsersModule } from './users/users.module';
import { UsersService } from './users/users.service';
import { XchangeModule } from './xchange/xchange.module';
import { XchangeService } from './xchange/xchange.service';

const importModules = [
  SentryModule.forRoot(),
  ConfigModule.forRoot({
    envFilePath: ['.env'],
    cache: true,
    isGlobal: true,
    load: [
      redisConfiguration,
      notificationConfiguration,
      payPayConfig,
      referral,
      loyalty,
      loyaltyReminder,
    ],
  }),
  QueueModule,
  TerminusModule.forRoot({
    gracefulShutdownTimeoutMs: 1000,
    errorLogStyle: 'pretty',
    logger: TerminusLogger,
  }),
  PrometheusModule.register({
    path: '/metrics',
    defaultMetrics: {
      enabled: true,
      config: {},
    },
  }),
  GoogleRecaptchaModule.forRootAsync({
    useFactory() {
      return {
        secretKey: process.env['GOOGLE_RECAPTCHA_SECRET_KEY'],
        response: (req) => req.headers.recaptcha,
        // skipIf: process.env.NODE_ENV !== 'production',
        score: process.env.APP_ENV === 'development' ? 0.1 : 0.3,
      };
    },
  }),
  JwtModule.registerAsync({
    imports: [ConfigModule],
    useFactory: (configService: ConfigService) => {
      return {
        signOptions: {
          expiresIn: '1m',
        },
        secret:
          configService.get('PKCE_CODE_VERIFIER') +
          configService.get('PKCE_CODE_CHALLENGE'),
      };
    },
    inject: [ConfigService],
  }),

  I18nModule.forRootAsync({
    useFactory: () => ({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      viewEngine: 'hbs',
    }),
    resolvers: [
      { use: QueryResolver, options: ['lang'] },
      new CookieResolver(['lang']),
      CustomLanguagePayloadResolveer,
    ],
  }),

  ScheduleModule.forRoot(),
  EventEmitterModule.forRoot(),
  ThrottlerModule.forRoot(),
  AuthModule,
  PlansModule,
  EsimModule,
  EsimOrdersModule,
  PaymentModule,
  XchangeModule,
  PaypayPaymentModule,
  CacheModule.registerAsync({
    isGlobal: true,
    inject: [ConfigService],
    useFactory: (configService: ConfigService) => {
      return {
        store: redisStore,
        // Store-specific configuration:
        host: configService.getOrThrow('redis.host'),
        port: 6379, //configService.getOrThrow('redis.port'),
        db: 0, //!isDev() ? process.env.REDIS_DB_INDEX : undefined,
        max: 5000,
        ttl: 3600,
      };
    },
  }),
  KeyValueStoreModule,
  UsersModule,
  AppsModule,
  ApikeyModule,
  BullBoardModule,
  EsimPurchaseQueueProcessorModule,
  EsimImporterModule,
  EsimUsageRulesModule,
  NotificationsModule,
  SessionCartModule,
  EmailTemplateModule,
  DataUsageLogsModule,
  CorporatesModule,
  EmailsModule,
  HealthModule,
  CouponsModule,
  RateModule,
  EsimStocksModule,
  QrModule,
  CampaignsModule,
  UkomiReviewModule,
  AdminJsNestModule.registerAsync(),
  ReferralsModule,
  SchedulerModule,
];

@Module({
  imports: importModules,
  controllers: [AppController, HealthController],
  providers: [
    // {
    //   provide: APP_GUARD,
    //   useClass: ThrottlerGuard,
    // },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    CouponsService,
    EsimService,
    EsimProviderBuilder,
    AppService,
    PlansService,
    PrismaService,
    XchangeService,
    KeyValueStoreService,
    BulkEsimPurchaseWorker,
    QueueEmailDispatcherWorker,
    EsimPurchaseQueueWorker,
    LoyaltyReminderWorker,
    NewEsimWorker,
    WebhookExecutorWorker,
    Logger,
    AppsService,
    EsimOrdersService,

    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ProxyAwareThrottlerGuard,
    },
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
    EmailsService,
    EventDevNotify,
    TerminusLogger,
    EsimStocksService,
    EsimOrdersService,
    PaymentService,
    UsersService,
    AwsCognitoService,
  ],
})
export class AppModule {
  constructor() { }

  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AppLoggerMiddleware).forRoutes('*');
    consumer.apply(BasicAuthMiddleware).forRoutes('/xyz-main');
    consumer.apply(BasicAuthMiddleware).forRoutes('/env');
  }
  // static async registerAsync() {
  //   return {
  //     module: AppModule,
  //     imports: [...importModules, await AdminJsNestModule.registerAsync()],
  //   };
  // }
  static forRoot(app: NestExpressApplication) {
    return {
      module: AppModule,
      providers: [
        {
          provide: 'APP_INSTANCE',
          useValue: app,
        },
      ],
    };
  }
}
