import { Injectable } from '@nestjs/common';
import {
  DiskHealthIndicator,
  HealthCheckService,
  HttpHealthIndicator,
  MemoryHealthIndicator,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import { InjectMetric } from '@willsoto/nestjs-prometheus';
import { Counter, Gauge } from 'prom-client';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class HealthService {
  constructor(
    private healthService: HealthCheckService,
    private http: HttpHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private memory: MemoryHealthIndicator,
    private prismaHealth: PrismaHealthIndicator,
    private prisma: PrismaService,
    @InjectMetric('metric_name')
    public counter: Counter<string>,
    @InjectMetric('service_prisma_status')
    private prismaStatusGauge: Gauge<string>,
    @InjectMetric('service_memory_rss_status')
    private memoryRssGauge: Gauge<string>,
    @InjectMetric('service_memory_heap_status')
    private memoryHeapGauge: Gauge<string>,
    @InjectMetric('service_storage_status')
    private storageGauge: Gauge<string>,
  ) {}

  async health() {
    const healthReport = await this.healthService.check([
      async () => this.prismaHealth.pingCheck('prisma', this.prisma),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024 * 1024),
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () =>
        this.disk.checkStorage('storage', {
          path: '/',
          threshold: 250 * 1024 * 1024 * 1024 * 1024,
        }),
      //   () => this.http.pingCheck('nestjs-docs', 'https://docs.nestjs.com'),
    ]);

    this.prismaStatusGauge.set(
      healthReport.details.prisma.status === 'up' ? 1 : 0,
    );
    this.memoryRssGauge.set(
      healthReport.details.memory_rss.status === 'up' ? 1 : 0,
    );
    this.memoryHeapGauge.set(
      healthReport.details.memory_heap.status === 'up' ? 1 : 0,
    );
    this.storageGauge.set(healthReport.details.storage.status === 'up' ? 1 : 0);

    return healthReport;
  }
}
