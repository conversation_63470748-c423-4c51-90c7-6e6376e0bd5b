import { Injectable, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>og<PERSON>, <PERSON><PERSON> } from '@nestjs/common';

@Injectable({ scope: Scope.TRANSIENT })
export class TerminusLogger extends ConsoleLogger {
  private logger = new Logger('Terminus');
  error(message: any, stack?: string, context?: string): void;
  error(message: any, ...optionalParams: any[]): void;
  error(
    message: unknown,
    stack?: unknown,
    context?: unknown,
    ...rest: unknown[]
  ): void {
    this.logger.error(message, stack, context);
    // Overwrite here how error messages should be logged
  }
}
