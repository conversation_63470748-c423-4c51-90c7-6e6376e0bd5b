import { Console<PERSON>ogger, Modu<PERSON> } from '@nestjs/common';
import {
  PrometheusModule,
  makeCounterProvider,
  makeGaugeProvider,
} from '@willsoto/nestjs-prometheus';
import { HealthService } from './health.service';
import {
  DiskHealthIndicator,
  HealthCheck,
  HealthCheckService,
  HttpHealthIndicator,
  MemoryHealthIndicator,
  PrismaHealthIndicator,
  TerminusModule,
} from '@nestjs/terminus';
import { PrismaService } from 'src/prisma.service';
import { HealthCheckExecutor } from '@nestjs/terminus/dist/health-check/health-check-executor.service';
import { TerminusLogger } from './terminus-logger.service';

@Module({
  imports: [TerminusModule],
  providers: [
    makeCounterProvider({
      name: 'metric_name',
      help: 'metric_help',
    }),
    makeGaugeProvider({
      name: 'service_prisma_status',
      help: 'Status of Prisma service (1 = up, 0 = down)',
    }),
    makeGaugeProvider({
      name: 'service_memory_rss_status',
      help: 'Status of Memory RSS (1 = up, 0 = down)',
    }),
    makeGaugeProvider({
      name: 'service_memory_heap_status',
      help: 'Status of Memory Heap (1 = up, 0 = down)',
    }),
    makeGaugeProvider({
      name: 'service_storage_status',
      help: 'Status of Storage (1 = up, 0 = down)',
    }),
    HealthService,
    MemoryHealthIndicator,
    PrismaHealthIndicator,
    PrismaService,
    ConsoleLogger,
    TerminusLogger,
  ],
  exports: [HealthService],
})
export class HealthModule {}
