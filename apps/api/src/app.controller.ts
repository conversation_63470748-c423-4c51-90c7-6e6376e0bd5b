import { Controller, Get, Req, UseInterceptors } from '@nestjs/common';
import { ConfigService } from '@nestjs/config'; // Import ConfigService
import { ApiExcludeEndpoint } from '@nestjs/swagger';
import { Request } from 'express';
import { I18n, I18nContext } from 'nestjs-i18n';
import { SentryInterceptor } from './SentryInterceptor';

@Controller()
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class AppController {
  constructor(
    private readonly configService: ConfigService, // Inject ConfigService
  ) {}

  @Get('/env')
  @ApiExcludeEndpoint()
  async getHello(@Req() req: Request, @I18n() i18n: I18nContext) {
    if (this.configService.get('APP_ENV') === 'development') {
      return this.configService.get('APP_ENV'); // You can return the entire environment if needed
    }
  }

  @Get('/debug-sentry')
  getError() {
    if (this.configService.get('APP_ENV') === 'production') {
      return;
    }
    throw new Error('My first Sentry error!');
  }
}
