import * as Sentry from '@sentry/nestjs';
import { isAppEnvDev, isDev } from './utils';

Sentry.init({
  serverName: 'global-esim',
  // dsn: "https://<EMAIL>/4507174200868864'",
  _experiments: { enableLogs: true },
  dsn: isDev()
    ? null
    : 'https://<EMAIL>/4507174200868864',

  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for performance monitoring.
  // We recommend adjusting this value in production
  tracesSampleRate: 1.0,
  environment: isAppEnvDev() ? 'development' : 'production',
});
