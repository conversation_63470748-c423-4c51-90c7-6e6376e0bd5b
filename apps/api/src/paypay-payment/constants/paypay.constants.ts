export const PAY_TYPE = {
  LINK: 0,
} as const;

export const SERVICE_TYPE = {
  LINK: 0,
} as const;

export const PAYPAY_RES_RESULT_NG = 'NG';
export const PAYPAY_RES_RESULT_OK = 'OK';

export const PAYPAY_FIELD_ORDER_PURCHASE_REQUEST = [
  'pay_method',
  'merchant_id',
  'service_id',
  'cust_code',
  'order_id',
  'item_id',
  'item_name',
  'amount',
  'pay_type',
  'service_type',
  'success_url',
  'cancel_url',
  'error_url',
  'pagecon_url',
  'free1',
  'free2',
  'free3',
  'free_csv',
  'request_date',
  'limit_second',
] as const;

export const PAYPAY_FIELD_ORDER_SCREEN_RETURN = [
  'pay_method',
  'merchant_id',
  'service_id',
  'cust_code',
  'order_id',
  'item_id',
  'pay_item_id',
  'item_name',
  'tax',
  'amount',
  'pay_type',
  'auto_charge_type',
  'service_type',
  'div_settele',
  'last_charge_month',
  'camp_type',
  'tracking_id',
  'terminal_type',
  'free1',
  'free2',
  'free3',
  'request_date',
  'res_pay_method',
  'res_result',
  'res_tracking_id',
  'res_sps_cust_no',
  'res_sps_payment_no',
  'res_payinfo_key',
  'res_payment_date',
  'res_err_code',
  'res_date',
  'limit_second',
] as const;

export const PAYPAY_FIELD_ORDER_CGI = PAYPAY_FIELD_ORDER_SCREEN_RETURN;
