export type PayPayWebhookPayload = {
  terminal_type: '0';
  sps_cust_no: string;
  sps_payment_no: string;
  res_pay_method: 'paypay';
  res_result: 'OK' | 'NG';
  merchant_id: string;
  res_err_code: string;
  res_payinfo_key: string;
  free2: string;
  free3: string;
  service_id: string;
  free1: 'global-esim' | 'global-esim-jp' | 'airtrip'; //free text field used to send source information in webhook
  camp_type: string;
  pay_type: '0' | string;
  pay_item_id: string;
  tracking_id: string;
  res_sps_cust_no: string;
  amount: string;
  item_id: string;
  res_payment_date: string;
  item_name: string;
  tax: string;
  pay_method: 'paypay';
  res_date: string;
  cust_code: string;
  div_settele: string;
  limit_second: string;
  service_type: '0' | string;
  res_sps_payment_no: string;
  last_charge_month: string;
  request_date: string;
  auto_charge_type: string;
  order_id: string;
  sps_hashcode: string;
  res_tracking_id: string;
};
