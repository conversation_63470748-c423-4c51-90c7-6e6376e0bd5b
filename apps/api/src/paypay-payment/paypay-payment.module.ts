import { Modu<PERSON> } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { EsimOrdersModule } from 'src/esim-orders/esim-orders.module';
import { PaypPayPaymentController } from './paypay-payment.controller';
import { PayPayPaymentService } from './paypay-payment.service';
import { PrismaService } from 'src/prisma.service';

@Module({
  imports: [
    EsimOrdersModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
  ],
  controllers: [PaypPayPaymentController],
  providers: [PayPayPaymentService, JwtService, PrismaService],
})
export class PaypayPaymentModule {}
