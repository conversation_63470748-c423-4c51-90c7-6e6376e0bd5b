import { registerAs } from '@nestjs/config';
import { ensureTrailingSlash, isAppEnvDev } from 'src/utils';

const apiBaseUrl = process.env.NEXT_PUBLIC_API_HOST;

export const payPayConfig = registerAs('paypay', () => {
  // SPS payment purchase request URL
  const actionFormPaymentUrl = isAppEnvDev()
    ? 'https://stbfep.sps-system.com/f01/FepBuyInfoReceive.do'
    : 'https://fep.sps-system.com/f01/FepBuyInfoReceive.do';

  return {
    merchantId: '56007',
    serviceId: '001',
    successUrl: apiBaseUrl + '/paypay-payment/complete',
    errorUrl: apiBaseUrl + '/paypay-payment/error',
    pageconUrl: apiBaseUrl + '/paypay-payment/cgi',
    actionFormPaymentUrl,
    hashKey: process.env.PAYPAY_HASH_KEY,
  };
});

export const getFrontendBaseUrl = (source: string): string => {
  const frontendUrls = {
    airtrip: process.env.FRONTEND_HOST_AIRTRIP,
    'global-esim': process.env.FRONTEND_HOST,
    'global-esim-jp': process.env.NEXT_PUBLIC_ESIM_JP,
  };

  const frontendBaseUrl = frontendUrls[source] || frontendUrls['global-esim'];

  // Ensure the URL ends with a trailing slash
  return ensureTrailingSlash(frontendBaseUrl);
};

export const getErrorUrl = (source: string) => {
  const errorUrl = {
    airtrip: apiBaseUrl + '/paypay-payment/error/airtrip',
    'global-esim': apiBaseUrl + '/paypay-payment/error/global-esim',
    'global-esim-jp': apiBaseUrl + '/paypay-payment/error/global-esim-jp',
  };

  return errorUrl[source] || errorUrl['global-esim'];
};
