import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  UnprocessableEntityException,
} from '@nestjs/common';
import { ConfigService, ConfigType } from '@nestjs/config';
import { users } from '@prisma/client';
import * as crypto from 'crypto';
import { format, subSeconds } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import { EsimOrdersService } from 'src/esim-orders/esim-orders.service';
import {
  PAY_TYPE,
  PAYPAY_FIELD_ORDER_PURCHASE_REQUEST,
  SERVICE_TYPE,
} from './constants/paypay.constants';
import { PayPayPaymentDataDto } from './dtos/paypay.dto';
import { PayPayWebhookPayload } from './types/paypay-webhook.type';
import { PaymentMethodType } from 'src/esim-orders/dtos/BulkOrdersDto';
import {
  getErrorUrl,
  getFrontendBaseUrl,
  payPayConfig,
} from './config/paypay.config';
import * as iconv from 'iconv-lite';
import { PrismaService } from 'src/prisma.service';
import { getFormmattedOrderId, limitStringArrayByLength } from 'src/utils';

@Injectable()
export class PayPayPaymentService {
  private readonly logger = new Logger(PayPayPaymentService.name);
  constructor(
    private readonly esimOrderService: EsimOrdersService,
    private readonly prismaService: PrismaService,
    @Inject(payPayConfig.KEY)
    private readonly payPayConfigValue: ConfigType<typeof payPayConfig>,
  ) {}

  /**
   * Generate a hash for request verification
   */
  private generateHash(data: any, fieldOrder: ReadonlyArray<string>): string {
    try {
      const hash = this.computeHash(data, fieldOrder);
      return hash;
    } catch (error) {
      this.logger.error('Hash generation error:', error);
      throw new Error(`Failed to generate hash: ${error.message}`);
    }
  }

  /**
   * Create a PayPay payment using Link-type connection
   */
  async createPayPayPayment(orderData: PayPayPaymentDataDto, user?: users) {
    if (!orderData.source) {
      throw new BadRequestException('Source is missing in the request data');
    }

    const requestDate = this.formatDate();

    const MAX_LENGTH = 20;
    const originUrl = orderData.requestOriginUrl || '';
    const free2 = originUrl.substring(0, MAX_LENGTH);
    const free3 =
      originUrl.length > MAX_LENGTH
        ? originUrl.substring(MAX_LENGTH, MAX_LENGTH * 2)
        : '';

    const validLocales = ['vi', 'kr', 'es', 'fr', 'ph'];
    const url = new URL(free2 + free3);
    const pathname = url.pathname;
    const pathParts = pathname.split('/');
    let locale = pathParts[1];

    if (!locale || !validLocales.includes(locale)) {
      locale = null;
    }

    const requestData = {
      pay_method: PaymentMethodType.PAYPAY,
      merchant_id: this.payPayConfigValue.merchantId,
      service_id: this.payPayConfigValue.serviceId,
      cust_code: user?.userId || orderData.customerId,
      order_id: orderData.orderId,
      item_id: limitStringArrayByLength(orderData.planIds, 32, ','),
      item_name: limitStringArrayByLength(
        await this.getChildOrdersId(orderData.orderId),
        40,
        ',',
      ),
      amount: orderData.amount,
      pay_type: PAY_TYPE.LINK,
      service_type: SERVICE_TYPE.LINK,
      success_url: this.payPayConfigValue.successUrl,
      cancel_url: getFrontendBaseUrl(orderData.source),
      error_url: getErrorUrl(orderData.source) + (locale ? `/${locale}` : ''),
      pagecon_url: this.payPayConfigValue.pageconUrl,
      free1: orderData.source,
      free2,
      free3,
      free_csv: this.buildFreeCsv({
        ORDER_DESCRIPTION: `${orderData.orderId}`,
      }),
      request_date: requestDate,
    };

    const hash = this.generateHash(
      requestData,
      PAYPAY_FIELD_ORDER_PURCHASE_REQUEST,
    );
    requestData['sps_hashcode'] = hash;

    this.logger.log(
      'The generated request data for PayPay is: ' +
        JSON.stringify(requestData, null, 2),
    );

    return {
      action: this.payPayConfigValue.actionFormPaymentUrl,
      method: 'POST',
      fields: {
        ...requestData,
      },
    };
  }

  /**
   * Format date to SPS required format
   */
  private formatDate(): string {
    const timeZone = 'Asia/Tokyo';

    const nowInJST = utcToZonedTime(new Date(), timeZone);
    const jstDateMinus60 = subSeconds(nowInJST, 60);
    const requestDate = format(jstDateMinus60, 'yyyyMMddHHmmss');
    return requestDate;
  }

  async handleWebhookEvent(metadata: PayPayWebhookPayload) {
    //remove the prefix string characters from order Id example GEsimLocal109468 to 109468
    const orderId = metadata.order_id.match(/\d+/g)?.join('');
    metadata.order_id = orderId;
    const parentOrderId = Number(metadata.order_id);

    await this.validateEsimOrder(metadata);

    if (!parentOrderId || !metadata.res_tracking_id)
      throw new UnprocessableEntityException(
        'Invalid metadata: missing parent order ID or tracking ID',
      );

    await this.esimOrderService.updateOrder(
      { pi_id: metadata.res_tracking_id },
      { id: parentOrderId },
    );

    const { processedOrders } = await this.esimOrderService.processEsimOrder(
      parentOrderId,
      metadata.cust_code,
    );

    return { processedOrders };
  }

  private async validateEsimOrder(metadata: any) {
    const esimOrder = await this.esimOrderService.get({
      id: Number(metadata.order_id),
    });

    if (
      !esimOrder ||
      (esimOrder && esimOrder.jpyPrice !== Number(metadata.amount))
    ) {
      throw new BadRequestException('Issue with the order.');
    }

    return esimOrder;
  }

  validateSpsHashCode(
    payload: PayPayWebhookPayload,
    fieldOrder: ReadonlyArray<string>,
  ): boolean {
    try {
      const computedHash = this.computeHash(payload, fieldOrder).toUpperCase();

      return computedHash === payload.sps_hashcode;
    } catch (error) {
      this.logger.error('Hash validation error:', error);
      throw new Error(`Failed to validate hash: ${error.message}`);
    }
  }

  private concatenateFields(
    data: any,
    fieldOrder: ReadonlyArray<string>,
  ): string {
    return fieldOrder
      .map((key) => {
        const value = data[key] !== undefined ? data[key] : '';
        return value.toString().trim();
      })
      .join('');
  }

  private computeHash(payload: any, fieldOrder: ReadonlyArray<string>): string {
    const concatenated = this.concatenateFields(payload, fieldOrder);
    const hashString = concatenated + this.payPayConfigValue.hashKey;

    return crypto.createHash('sha1').update(hashString, 'utf-8').digest('hex');
  }

  async getOrderDetails(orderIdStr: string) {
    const orderId = orderIdStr.match(/\d+/g)?.join('');
    return await this.esimOrderService.get({
      id: Number(orderId),
    });
  }

  buildFreeCsv(fields: Record<string, string | number>) {
    const csv = Object.entries(fields)
      .map(([key, value]) => `${key}=${value}`)
      .join(',');

    const sjisBuffer = iconv.encode(csv, 'Shift_JIS');

    return sjisBuffer.toString('base64');
  }

  async getChildOrdersId(parentOrderId: string) {
    const order = await this.prismaService.orders.findFirstOrThrow({
      where: {
        orderId: parentOrderId,
      },
      include: {
        childOrders: true,
      },
    });

    return order.childOrders && order.childOrders.length > 0
      ? order.childOrders.map((childOrder) =>
          getFormmattedOrderId(childOrder.id),
        )
      : [];
  }
}
