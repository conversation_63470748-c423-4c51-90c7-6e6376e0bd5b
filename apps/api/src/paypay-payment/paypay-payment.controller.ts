import {
  Body,
  Controller,
  HttpStatus,
  Inject,
  InternalServerErrorException,
  Logger,
  Param,
  Post,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '@nestjs/passport';
import { users } from '@prisma/client';
import { Response } from 'express';
import { GetUser } from 'src/auth/get-user.decorator';
import { EsimService } from 'src/esim/esim.service';
import { ensureTrailingSlash, simpleStringEncode } from 'src/utils';
import { PayPayPaymentDataDto } from './dtos/paypay.dto';
import { PayPayPaymentService } from './paypay-payment.service';
import { PayPayWebhookPayload } from './types/paypay-webhook.type';
import {
  PAYPAY_FIELD_ORDER_CGI,
  PAYPAY_FIELD_ORDER_SCREEN_RETURN,
  PAYPAY_RES_RESULT_NG,
  PAYPAY_RES_RESULT_OK,
} from './constants/paypay.constants';
import { getFrontendBaseUrl } from './config/paypay.config';

@Controller('paypay-payment')
export class PaypPayPaymentController {
  private readonly logger = new Logger(PaypPayPaymentController.name);

  constructor(
    private readonly payPayPaymentService: PayPayPaymentService,
    private readonly jwtService: JwtService,
  ) {}

  @Post('create')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async createPayment(
    @GetUser() user: users,
    @Body()
    paymentData?: PayPayPaymentDataDto,
  ) {
    try {
      const response: any = await this.payPayPaymentService.createPayPayPayment(
        paymentData,
        user,
      );

      return response;
    } catch (error) {
      this.logger.error(`Payment creation failed: ${error.message}`);
      throw new InternalServerErrorException(
        error.message || 'Payment creation failed',
      );
    }
  }

  @Post('create/guest')
  @UsePipes(ValidationPipe)
  async createPaymentGuest(
    @Body()
    paymentData?: PayPayPaymentDataDto,
  ) {
    try {
      const response: any = await this.payPayPaymentService.createPayPayPayment(
        paymentData,
      );

      return response;
    } catch (error) {
      this.logger.error(`Payment creation failed: ${error.message}`);
      throw new InternalServerErrorException(
        error.message || 'Payment URL generation failed',
      );
    }
  }

  /**
   *
   * Function ID: A02-1 and A02-2
   * Processing name: Purchase result CGI and Purchase result CGI response
   * Send method: HTTP POST
   *
   */
  @Post('cgi')
  async handlePayPayCgi(
    @Body() payPayReq: PayPayWebhookPayload,
    @Res() res: Response,
  ) {
    const { free1: source } = payPayReq;
    this.logger.log(
      'Webhook received for PayPay cgi, processing PayPay CGI request...',
    );

    const isValidHash = this.payPayPaymentService.validateSpsHashCode(
      payPayReq,
      PAYPAY_FIELD_ORDER_CGI,
    );

    if (!isValidHash) {
      this.logger.error('Invalid hash code, webhook request rejected');
      return res.status(HttpStatus.OK).send(PAYPAY_RES_RESULT_NG); // Acknowledge receipt even if payment failed
    }

    try {
      if (payPayReq.res_result === 'NG') {
        this.logger.error('Payment result NG');
        // C001 - Case (3): result CGI NG response OK
        return res.status(HttpStatus.OK).send(PAYPAY_RES_RESULT_OK); // Acknowledge receipt even if payment failed
      }

      const { processedOrders } =
        await this.payPayPaymentService.handleWebhookEvent(payPayReq);

      if ((processedOrders && processedOrders.length <= 0) || !source) {
        // C001 - Case (2): result CGI OK response NG
        return res
          .status(HttpStatus.OK)
          .send(`${PAYPAY_RES_RESULT_NG}, No orders processed`);
      }

      // C001 - Case (1): result CGI OK response OK
      return res.status(HttpStatus.OK).send(PAYPAY_RES_RESULT_OK);
    } catch (error) {
      this.logger.error(`Error in PayPay CGI: ${error.message}`);
      // C001 - Case (2): result CGI OK response NG
      return res
        .status(HttpStatus.OK)
        .send(`${PAYPAY_RES_RESULT_NG}, ${error.message}`);
    }
  }

  /**
   *
   * Function ID: A03-1
   * Processing name: Purchase result (screen return)
   * Flow: SBPS to GM
   * Send method: Form POST
   */
  @Post('complete')
  async payPayPaymentComplete(
    @Body() payPayReq: PayPayWebhookPayload,
    @Res() res: Response,
  ) {
    try {
      //free2 and free3 contains the request origin base url
      const { free1: source, free2, free3 } = payPayReq;
      this.logger.log('Completing PayPay screen return request:');

      const isValidHash = this.payPayPaymentService.validateSpsHashCode(
        payPayReq,
        PAYPAY_FIELD_ORDER_SCREEN_RETURN,
      );

      if (!isValidHash) {
        this.logger.error('Invalid hash code, webhook request rejected');
        return res.redirect(
          HttpStatus.SEE_OTHER,
          getFrontendBaseUrl(source) + 'paypay-error',
        );
      }

      const order = await this.payPayPaymentService.getOrderDetails(
        payPayReq.order_id,
      );

      let instantEsimSecret = this.jwtService.sign(
        {
          orderId: order.orderId,
        },
        {
          secret: EsimService.getInstantEsimSecret(),
          expiresIn: '1h',
        },
      );

      const queryParams = new URLSearchParams({
        transaction: simpleStringEncode(String(Date.now())),
        event: 'complete',
        uid: simpleStringEncode(instantEsimSecret),
        planId: order.plan?.id?.toString() || 'none',
        country: order.plan?.country?.name || '',
      });

      const baseUrl =
        source === 'global-esim-jp' && !`${free2}${free3}`.includes('localhost') //global esim jp has "/esim" path params in host
          ? `${free2}${free3}/esim`
          : `${free2}${free3}`;

      // Build the final redirect URL
      const redirectUrl = `${ensureTrailingSlash(baseUrl)}checkout/${
        order.orderId
      }/complete?${queryParams}`;

      return res.redirect(HttpStatus.SEE_OTHER, redirectUrl);
    } catch (error) {
      this.logger.error(`Error in payPayPaymentComplete: ${error.message}`);
      return res.redirect(
        HttpStatus.SEE_OTHER,
        getFrontendBaseUrl(payPayReq?.free1 || 'global-esim-jp') +
          'paypay-error',
      );
    }
  }

  @Post('error/airtrip')
  async payPayErrorAirtrip(
    @Body() payPayReq: any,
    @Res() res: any,
  ): Promise<void> {
    const { free1: source } = payPayReq;
    this.logger.log('Processing PayPay error request for airtrip:');

    res.redirect(
      HttpStatus.SEE_OTHER,
      getFrontendBaseUrl(source || 'airtrip') + 'paypay-error',
    );
  }

  @Post('error/global-esim/:locale')
  async payPayErrorGlobalEsim(
    @Body() payPayReq: any,
    @Res() res: any,
    @Param('locale') locale: string,
  ): Promise<void> {
    const { free1: source } = payPayReq;
    this.logger.log(
      `Processing PayPay error request for airtrip with locale: ${locale}`,
    );

    res.redirect(
      HttpStatus.SEE_OTHER,
      getFrontendBaseUrl(source || 'global-esim') +
        (locale ? `${locale}/` : '') +
        'paypay-error',
    );
  }

  @Post('error/global-esim-jp')
  async payPayErrorGlobalEsimJP(
    @Body() payPayReq: any,
    @Res() res: any,
  ): Promise<void> {
    const { free1: source } = payPayReq;
    this.logger.log('Processing PayPay error request for airtrip:');

    res.redirect(
      HttpStatus.SEE_OTHER,
      getFrontendBaseUrl(source || 'global-esim-jp') + 'paypay-error',
    );
  }
}
