import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsPositive,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class PayPayPaymentDataDto {
  @ApiProperty({
    description: 'The unique order identifier',
    example: 'GEsimLocal109218',
  })
  @IsNotEmpty()
  @IsString()
  orderId: string;

  @ApiProperty({
    description: 'The payment amount',
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  amount: number;

  @ApiProperty({
    description: 'Customer email address',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  customerEmail: string;

  @ApiProperty({
    description: 'Customer full name',
    example: 'John Doe',
  })
  customerName: string;

  @ApiProperty({
    description: 'Customer Id',
    example: 'b7148a18-9061-701c-ba61-3cee50e5c0c4',
  })
  customerId: string;

  @ApiProperty({
    description: 'Plan identifier',
  })
  @IsNotEmpty()
  planIds: string[];

  @ApiProperty({
    description: 'Details about the payment source',
  })
  @IsNotEmpty()
  source: 'global-esim' | 'global-esim-jp' | 'airtrip';

  @ApiProperty({
    description: 'Request origin URL',
  })
  requestOriginUrl: string;
}
