import { Logger } from '@nestjs/common';
import {
  IEsimPurchaseRequestPayload,
  IParserStrategy,
} from './IParserStratefy';
import { format } from 'date-fns';

export interface IEsimStock {
  optionId: string;
  activationCode: string;
  iccid: string;
  topupid: string;
  qrCodeImgUrl: string;
  downloadLink: string;
  smdp: string;
}
export class EsimCSVParser implements IParserStrategy<IEsimStock> {
  parse(data: any) {
    return data;
  }
}
