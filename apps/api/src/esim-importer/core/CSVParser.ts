import { Logger } from '@nestjs/common';
import {
  IEsimPurchaseRequestPayload,
  IParserStrategy,
} from './IParserStratefy';
import { format } from 'date-fns';

export class CSVParser implements IParserStrategy {
  parse(data: any): IEsimPurchaseRequestPayload[] {
    const orders = data as Array<{ [key: string]: string }>;
    // Throwing headers
    const purchasePayload = orders.map((order) => {
      const {
        bookingNo,
        productNo,
        "Product's Name": productName,
        firstName: contactFirstName,
        lastName: contactLastName,
        country: contactCountryName,
        Hanlder: handler,
        'Order Status': orderStatus,
        currency,
        cost,
        email,
        language,
        passportNo,
        dob,
        phone,
        agencyNo,
        gender,
        countryCode,
        sendEmail,
        vendor,
      } = order;

      const match = productNo?.length
        ? productNo
        : productName.match(/\((\d+)\)$/)[1];

      const planId = match ? parseInt(match) : null;
      return {
        status: match ? 'PROCESSED' : 'FAILED',
        planId: planId,
        language: language || 'en',
        chargeAmount: isNaN(+cost) ? 0 : +cost,
        metadata: {
          vendor,
          sendEmail,
          agencyNo,
          gender,
          countryCode,
          firstName: contactFirstName,
          lastName: contactLastName,
          bookingNo: bookingNo,
          email: email,
          costPrice: isNaN(+cost) ? 0 : +cost,
          passportNo,
          dob: dob
            ? format(new Date(dob), 'yyyy-MM-dd') + 'T00:00:00Z'
            : undefined,
          phone,
        },
      };
    });

    return purchasePayload as unknown as IEsimPurchaseRequestPayload[];
  }
}
