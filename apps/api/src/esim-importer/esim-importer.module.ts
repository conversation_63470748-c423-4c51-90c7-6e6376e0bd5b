import { Lo<PERSON>, <PERSON>du<PERSON> } from '@nestjs/common';
import { EsimImporterService } from './esim-importer.service';
import { EsimImporterController } from './esim-importer.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BullModule } from '@nestjs/bullmq';
import { BULK_ESIM_PURCHASE, BULK_ESIM_PURCHASE_PROCESS } from 'src/constants';
import redisConnection from 'config/redis-connection';
import 'multer';
@Module({
  imports: [],
  controllers: [EsimImporterController],
  providers: [EsimImporterService, ConfigService, Logger],
})
export class EsimImporterModule {}
