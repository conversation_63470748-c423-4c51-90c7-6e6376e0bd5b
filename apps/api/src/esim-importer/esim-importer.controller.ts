import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  ParseFilePipe,
  UseGuards,
  Req,
} from '@nestjs/common';
import { EsimImporterService } from './esim-importer.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiExcludeEndpoint } from '@nestjs/swagger';
import { SentryInterceptor } from 'src/SentryInterceptor';
import 'multer';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { GetApp } from 'src/apps/get-app.decorator';

@Controller('esim-importer')
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class EsimImporterController {
  constructor(private readonly esimImporterService: EsimImporterService) {}

  @Post('upload')
  @UseGuards(AuthGuard('headerapikey'))
  @ApiExcludeEndpoint()
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(
    @Req() req: Request,
    @UploadedFile(new ParseFilePipe({ validators: [] }))
    file: Express.Multer.File,
    @GetApp() app: any,
  ) {
    const apiId = req.headers['x-api-id'] as string;
    const apiKey = req.headers['x-api-key'] as string;
    const signatureKey = req.headers['x-gat-signature-key'] as string;

    return this.esimImporterService.processFileAndQueue(file, {
      apiId,
      apiKey,
      requestSignatureKey: signatureKey,
      name: app.name,
    });
  }

  @Post('esims/upload')
  @UseGuards(AuthGuard('headerapikey'))
  @ApiExcludeEndpoint()
  @UseInterceptors(FileInterceptor('file'))
  esimsUpload(
    @Req() req: Request,
    @UploadedFile(new ParseFilePipe({ validators: [] }))
    file: Express.Multer.File,
    @GetApp() app: any,
  ) {
    const records = this.esimImporterService.importEsim(file);
    return [];
  }
}
