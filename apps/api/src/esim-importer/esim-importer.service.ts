import { Injectable, Logger } from '@nestjs/common';
import { BULK_ESIM_PURCHASE } from 'src/constants';
import { InjectQueue } from '@nestjs/bullmq';
import { ConfigService } from '@nestjs/config';
import { DataProcessor } from './core/EsimOrderRequestParser';
import { CSVParser } from './core/CSVParser';
import { Queue } from 'bullmq';
import { parse } from 'csv-parse/sync';
import { groupBy, startCase } from 'lodash';
import { IOrderMetadata } from 'src/esim-orders/interfaces/IOrderMetadata';
import 'multer';
import { encryptAppStandard, encryptToJWT } from 'src/utils';
import { EsimCSVParser } from './core/EsimCSVParser';

@Injectable()
export class EsimImporterService {
  private logger = new Logger(EsimImporterService.name);

  constructor(
    @InjectQueue(BULK_ESIM_PURCHASE)
    private bulkEsimPurchaseQueue: Queue,
    private configService: ConfigService,
  ) {}

  async importEsim(file: Express.Multer.File) {
    const rawOrders = parse(file.buffer.toString(), {
      columns: true,
    });

    const dataProcessor = new DataProcessor(new EsimCSVParser());
    const esims = dataProcessor.process(rawOrders);
    const esimjobs = esims.map((item) => {
      return {
        name: 'STOCK_ESIM',
        data: {
          ...item,
        },
      };
    });
    await this.bulkEsimPurchaseQueue.addBulk(esimjobs);
  }

  async processFileAndQueue(
    file: Express.Multer.File,
    credentials: {
      apiId: string;
      apiKey: string;
      requestSignatureKey: string;
      name: string;
    },
  ) {
    const rawOrders = parse(file.buffer.toString(), {
      columns: true,
    });

    const dataProcessor = new DataProcessor(new CSVParser());
    const esimPurchasePayloads = dataProcessor.process(rawOrders);
    const records = groupBy(esimPurchasePayloads, 'status');

    const queueJobs = records['PROCESSED']?.map(
      (payload: { metadata: IOrderMetadata }) => ({
        name: payload?.metadata?.bookingNo || Date.now() + '',
        data: {
          payload: {
            ...payload,
            metadata: {
              ...(payload.metadata || {}),
              channelName: startCase(credentials.name),
            },
          },
          credentials: encryptToJWT(
            {
              credentials: encryptAppStandard(JSON.stringify(credentials)),
            },
            '2h',
          ),
        },
        opts: {
          jobId: credentials.name + ':' + payload.metadata.bookingNo + '',
          ...(this.configService.get('redis').queue || {}),
          attempts: 2,
          delay: 5000,
        },
      }),
    );
    try {
      await this.bulkEsimPurchaseQueue.addBulk(queueJobs);
    } catch (err) {
      this.logger.error(err);
      throw err;
    }

    return records;
  }
}
