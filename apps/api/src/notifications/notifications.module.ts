import { Module } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { PassportModule } from '@nestjs/passport';
import { PrismaService } from 'src/prisma.service';
import { NotificationProcess } from './notification.process';
import { BullModule } from '@nestjs/bullmq';
import { SEND_NOTIFICATION } from 'src/constants';
import redisConnection from 'config/redis-connection';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailTemplateService } from 'src/email-template/email-template.service';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    BullModule.registerQueueAsync({
      name: SEND_NOTIFICATION,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return {
          ...redisConnection(configService),
          name: SEND_NOTIFICATION,
        };
      },
    }),
  ],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    PrismaService,
    NotificationProcess,
    EmailTemplateService,
  ],
})
export class NotificationsModule {}
