import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Logger,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { AuthGuard } from '@nestjs/passport';
import { GetUser } from 'src/auth/get-user.decorator';
import { IUser } from 'src/interface/IUser';
import { NotificationChannelEnum } from './NotificationChannelEnum';
import { ApiExcludeEndpoint } from '@nestjs/swagger';
import { SentryInterceptor } from 'src/SentryInterceptor';

@Controller('notifications')
@UseGuards(AuthGuard())
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class NotificationsController {
  private logger = new Logger(NotificationsController.name);

  constructor(private readonly notificationsService: NotificationsService) {}

  @ApiExcludeEndpoint()
  @Get('test')
  async sendTestNotification(
    @GetUser() user: IUser,
    @Query() query: { userId: string },
  ) {
    try {
      await this.notificationsService.sendNotificationToUser({
        messages: [
          {
            channel: NotificationChannelEnum.EMAIL,
            value: 'Test email notification',
          },
          {
            channel: NotificationChannelEnum.PUSH_NOTIFICATION,
            value: 'Test push notification',
          },
        ],
        type: 'URGENT',
        //@ts-expect-error
        userId: query?.userId || user.appId,
      });
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }
}
