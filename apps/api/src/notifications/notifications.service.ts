import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { PrismaService } from 'src/prisma.service';
import { INotification } from './INotification';
import { InjectQueue } from '@nestjs/bullmq';
import { SEND_NOTIFICATION } from 'src/constants';
import { Queue } from 'bullmq';
import { v4 } from 'uuid';
import { NotificationNotifier } from './NotificationNotifier';
import { NotificationChannelEnum } from './NotificationChannelEnum';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class NotificationsService {
  private logger = new Logger('notification');

  private httpClient: AxiosInstance;

  constructor(
    private prismaService: PrismaService,
    @InjectQueue(SEND_NOTIFICATION)
    private sendNotificationQueue: Queue,
    private emailTemplate: EmailTemplateService,
    private configService: ConfigService,
  ) {
    this.httpClient = axios.create({
      baseURL: process.env['NOTIFICATION_SERVER_HOST'] + '/api/v1',
    });

    this.httpClient.interceptors.request.use((config) => {
      config.headers[
        'Authorization'
      ] = `Bearer ${process.env['NOTIFICATION_SERVER_TOKEN']}`;

      return config;
    });
  }

  isAllowedToThisUser(userId: any) {
    const allowedUser =
      this.configService.get('notification.allowedUsers') || [];
    if (allowedUser && allowedUser.length && !allowedUser.includes(userId)) {
      return false;
    }
    return true;
  }

  getAudienceIdOfUser(userId: number) {
    return this.prismaService.notification_users.findFirstOrThrow({
      where: {
        userId,
      },
    });
  }
  getAudiencesIdOfUser(userId: number) {
    return this.prismaService.notification_users.findMany({
      where: {
        userId,
      },
    });
  }
  async subscribeAudienceToChannel(audienceId, channelId, value: string) {
    try {
      const response = await this.httpClient.post(
        `/audience/${audienceId}/channels`,
        {
          type: channelId,
          value: value,
        },
      );
      return response.data;
    } catch (err) {
      this.logger.log(err.message, err);
    }
  }
  async createAudience(userId: number) {
    try {
      const audienceRecord =
        await this.prismaService.notification_users.findFirstOrThrow({
          where: {
            userId: userId,
          },
        });
      return audienceRecord;
    } catch (err) {
      try {
        const response = await this.httpClient.post('/audiences', {
          tokens: ['user_' + userId],
        });
        this.logger.log(
          `Saving audience data of audience ${JSON.stringify(response.data)}`,
        );
        await this.prismaService.notification_users.createMany({
          data: response.data
            // ?.filter(item => payload.token.includes(item.token))
            .map((item) => {
              return {
                userId: userId,
                firebaseToken: item.token,
                audienceId: item.id,
              };
            }),
        });
        return this.prismaService.notification_users.findFirstOrThrow({
          where: {
            userId: userId,
          },
        });
      } catch (err) {
        return null;
      }
    }
  }

  async usage(payload: { topupid: string }) {
    try {
      const response = await this.httpClient.post('/apps/order/usage', {
        topupid: payload.topupid,
      });

      return response.data;
    } catch (err) {
      throw err;
    }
  }

  async sendNotificationToUser(
    notification: Omit<INotification, 'audiences'> & { userId: number },
  ) {
    try {
      const audiences = await this.getAudienceIdOfUser(notification.userId);
      return this.createNotificationQueue({
        ...notification,
        audiences: [audiences.audienceId],
      });
    } catch (err) {
      this.logger.log(err);
      throw err;
    }
  }

  async sendNotification(notification: INotification) {
    try {
      const pushNotification = notification.messages.filter(
        (message) =>
          message.channel === NotificationChannelEnum.PUSH_NOTIFICATION,
      );
      const htmlEmails = notification.messages.filter(
        (message) => message.channel === NotificationChannelEnum.EMAIL,
      );

      const nextHtmlEmails = [];
      for await (let email of htmlEmails) {
        try {
          const value = JSON.parse(email.value);
          const html = await this.emailTemplate.getEmailTemplate({
            language: value?.value?.language || 'en',
            layout: 'email-layout',
            templateName: value.value.templateName,
            ...value.value.data,
          });

          nextHtmlEmails.push({
            channel: email.channel,
            value: {
              title: value.title,
              value: html,
            },
          });
        } catch (err) {
          this.logger.log(err);
          nextHtmlEmails.push({
            channel: email.channel,
            value: email,
          });
          this.logger.log(
            `Error while getting template form ${JSON.stringify(email)}`,
          );
        }
      }
      const nextNotification = {
        ...notification,
        messages: (pushNotification || []).concat(nextHtmlEmails || []),
      };
      this.logger.log(
        `Dispatching notification ${JSON.stringify(
          nextNotification.audiences,
        )}`,
      );
      const response = await this.httpClient.post(
        '/notifications',
        nextNotification,
      );
      return response.data;
    } catch (err) {
      this.logger.log(err.response);
      throw err;
    }
  }

  async createNotificationQueue(notification: INotification) {
    const notificationId = v4();
    return this.sendNotificationQueue.add(notificationId, notification);
  }

  async registerNotifier(notifier: NotificationNotifier) {
    notifier.register(this);
    return this;
  }

  async updateMetadata(orderId: number, userId: number, metadata: object) {
    try {
      const userRecord = await this.prismaService.order_notifications.findFirst(
        {
          where: {
            usersId: userId,
            orderId,
          },
        },
      );
      if (!userRecord) {
        return await this.prismaService.order_notifications.create({
          data: {
            usersId: userId,
            orderId,
            metadata: {
              //@ts-expect-error
              ...(userRecord?.metadata || {}),
              ...metadata,
            },
          },
        });
      }

      return await this.prismaService.order_notifications.update({
        where: {
          id: userRecord.id,
        },
        data: {
          metadata: {
            //@ts-expect-error
            ...(userRecord.metadata || {}),
            ...metadata,
          },
        },
      });
    } catch (err) {
      this.logger.error(err);
      return null;
    }
  }
}
