import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { Job } from "bullmq";
import { SEND_NOTIFICATION } from "src/constants";
import { NotificationsService } from "./notifications.service";


@Processor(SEND_NOTIFICATION)
export class NotificationProcess extends WorkerHost {
    private readonly logger = new Logger(NotificationProcess.name);

    constructor(private notificationService: NotificationsService) {
        super()
    }

    async process(job: Job<any, any, string>, token?: string): Promise<any> {
        this.logger.log("sending notification: " + job.name)
        await this.notificationService.sendNotification(job.data)
        return
    }
}