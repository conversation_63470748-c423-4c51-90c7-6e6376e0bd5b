//@ts-ignore
import './instrument';

import { WinstonTransport as AxiomTransport } from '@axiomhq/axiom-node';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BaseAdapter } from '@bull-board/api/dist/src/queueAdapters/base';
import { ExpressAdapter } from '@bull-board/express';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Queue, Worker } from 'bullmq';
import redisConnection from 'config/redis-connection';
import 'dotenv/config';
import basicAuth from 'express-basic-auth';
import hbs from 'hbs';
import helmet from 'helmet';
import hpp from 'hpp';
import {
  utilities as nestWinstonModuleUtilities,
  WinstonModule,
} from 'nest-winston';
import os from 'os';
import { join } from 'path';
import { expressMiddleware } from 'prometheus-api-metrics';
import tooBusy from 'toobusy-js';
import { createLogger, format, transports } from 'winston';
import SlackHook from 'winston-slack-webhook-transport';
import { AppModule } from './app.module';
import { getBullBoardQueues } from './bull-board/bull-board.service';
import {
  BULK_ESIM_PURCHASE,
  QUEUE_ESIM_CREATE_BUY,
  QUEUE_LOYALTY_REMINDER,
} from './constants';
import { GoogleRecaptchaFilter } from './filters/GoogleRecaptchaFilter';
import rawBodyMiddleware from './middleware/rawBody.middleware';
import { BulkEsimPurchaseWorker } from './processors/bulk-esim-purchase.worker';
import { EsimPurchaseQueueWorker } from './processors/esim-create-buy.worker';
import { isAppEnvDev } from './utils';
import { ShiftJisMiddleware } from './middleware/shift-jis.middleware';
import { json, urlencoded } from 'body-parser';
import { LoyaltyReminderWorker } from './processors/loyalty-reminder.worker';

// new changees2
const { combine, errors, json: winstonJson, timestamp } = format;
async function bootstrap() {
  const axiomTransport = new AxiomTransport({
    dataset: 'global-esim-prod',
    token: process.env['LOGTAIL_SECRET'],
    orgId: 'ipc-kclc',
  });

  // createLogger of Winstonm
  const instance = createLogger({
    exceptionHandlers: [axiomTransport],
    rejectionHandlers: [axiomTransport],
    // options of Winston
    format: format.combine(
      errors({ stack: true }),
      winstonJson(),
      format.label({
        label: isAppEnvDev()
          ? `DEVELOPMENT: ${os.hostname()} ${process.env.name} ${
              process.env.pm_id
            }`
          : `PRODUCTION ${os.hostname()} ${process.env.name} ${
              process.env.pm_id
            }`,
      }),
      timestamp(),
      format.ms(),
      nestWinstonModuleUtilities.format.nestLike('global-esim-api', {
        // options
        prettyPrint: true,
        colors: true,
      }),
    ),
  });
  if (!isAppEnvDev()) {
    instance.add(axiomTransport);
    instance.add(
      new SlackHook({
        level: 'error',
        webhookUrl:
          '*********************************************************************************',
        formatter(options) {
          return {
            text: `Environment: ${
              process.env.NODE_ENV
            }, os: ${os.hostname()}, ip: ${options.ip}, service: ${
              process.env.name
            }, pm_id: ${process.env.pm_id}, *${options.level}:* ${
              options.message
            }`,
          };
        },
      }),
    );
  }
  // // Enable it later
  if (process.env.NODE_ENV !== 'production') {
    instance.add(
      new transports.Console({
        format: format.combine(
          errors({ stack: true }),
          winstonJson(),
          timestamp(),
          format.ms(),
          nestWinstonModuleUtilities.format.nestLike('global-esim-api', {
            // options
            prettyPrint: true,
            colors: true,
          }),
        ),
      }),
    );
  }

  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: WinstonModule.createLogger({
      instance,
    }),
    bodyParser: false,
  });
  // For recaptcha errors
  app.useGlobalFilters(new GoogleRecaptchaFilter());
  // For CORS
  app.enableCors();

  app.use((req, res, next) => {
    const contentType = req.headers['content-type']?.toLowerCase() || '';
    const isShiftJis = contentType.includes('charset=shift_jis');

    /**For some reason paypay has changed content type header to not include charset=shift_jis for A03-1*/
    if (isShiftJis || req.url.includes('paypay-payment/complete')) {
      const middleware = new ShiftJisMiddleware();
      return middleware.use(req, res, next);
    } else {
      next();
    }
  });

  app.use((req, res, next) => {
    if (req.body || !req.is('application/json')) {
      return next();
    }
    json()(req, res, next);
  });

  app.use((req, res, next) => {
    const contentType = req.headers['content-type']?.toLowerCase() || '';
    if (
      req.body ||
      contentType.includes('charset=shift_jis') ||
      !req.is('application/x-www-form-urlencoded')
    ) {
      return next();
    }
    urlencoded({ extended: false })(req, res, (err) => {
      if (err) return next(err);

      // urlencoded({ extended: false }) creates prototype-less objects to prevent prototype pollution attacks thus converting to normal object
      req.body = Object.assign({}, req.body);
      next();
    });
  });

  app.use(rawBodyMiddleware());
  app.use(hpp());
  app.use(helmet());
  app.use(helmet.hsts());
  app.use(helmet.noSniff());
  app.use(helmet.hidePoweredBy());

  app.useStaticAssets(join(__dirname, '.', 'public'));
  app.setBaseViewsDir(join(__dirname, '.', 'views'));
  app.setViewEngine('hbs');
  // app.set('view options', { layout: 'email-layout' });
  hbs.registerPartials(join(__dirname, '.', 'views/partials'));
  // hbs.registerPartial("purchase-email",)
  hbs.registerHelper('whichPartial', function (context) {
    if (context.data.root.body.length > 50) return 'blank';
    return context.data.root.body.trim().replaceAll('-', '_');
  });
  hbs.registerHelper('isAppOnlyJapanese', function (language) {
    return language === 'jp';
  });
  hbs.registerHelper('isAirTrip', function (source) {
    return source === 'airtrip';
  });
  hbs.registerHelper('isAirTripOrJapanese', function (source, language) {
    return source === 'airtrip' || language === 'jp';
  });
  hbs.registerHelper('isGMEnglishOrEnglishLang', function (source, language) {
    return source === 'global-esim' || language === 'en';
  });

  const serverAdapter = new ExpressAdapter();
  // test
  const queues = getBullBoardQueues();
  const bullBoard = createBullBoard({
    queues: [
      QUEUE_ESIM_CREATE_BUY,
      BULK_ESIM_PURCHASE,
      QUEUE_LOYALTY_REMINDER,
    ].map(
      (item) =>
        new BullMQAdapter(
          new Queue(item, {
            connection: redisConnection().connection,
          }),
        ),
    ),
    serverAdapter,
  });
  queues.forEach((queue: BaseAdapter) => {
    bullBoard.addQueue(queue);
  });
  serverAdapter.setBasePath('/api/v1/suadmin/queues');
  // Some changes
  app.use(
    '/api/v1/suadmin/queues',
    basicAuth({
      users: { superAccess: process.env['PKCE_CODE_VERIFIER'] },
      challenge: true, // <--- needed to actually show the login dialog!
    }),
    serverAdapter.getRouter(),
  );

  app.use((_, res, next) => {
    const logger = app.get(Logger);
    if (tooBusy()) {
      logger.log(`App is busy, not serving.`);
      res.send(503, 'Service Unavailable.');
    } else {
      next();
    }
  });

  app.setGlobalPrefix('/api/v1');
  app.use(
    expressMiddleware({
      metricsPrefix: 'express',
      metricsPath: '/metrics',
    }),
  );
  const config = new DocumentBuilder()
    .setDescription(
      'Order a country eSIM. Travel different countries with the same eSIM. Stay connected at afforable prices. Purchase, scan and connect.',
    )
    .setBasePath('https://esim-dev.gmobile.biz')
    .addServer('https://esim.gmobile.biz', 'Production')
    .addServer('https://esim-dev.gmobile.biz', 'Development')
    .setContact('Developer', '', '<EMAIL>')
    .setTitle('Global eSIM APIs')
    .setLicense('Apache 2.0', 'http://www.apache.org/licenses/LICENSE-2.0.html')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  if (process.env['APP_ENV'] === 'development') {
    SwaggerModule.setup('api', app, document, {
      customSiteTitle: 'Global esim APIs',
    });
  }
  app.set('trust proxy', 'loopback'); // Trust requests from the loopback address
  app.set('trust proxy', 1);

  const server = await app.listen(process.env['APP_PORT'] || 3001);
  console.log(`App running on port ${process.env['APP_PORT'] || 3001}`);
  return { server, app };
}

bootstrap()
  .then(({ server, app }) => {
    const logger = app.get(Logger);

    const workers: Worker[] =
      process.env['RUN_QUEUE_WORKERS'] === 'true'
        ? [
            app.get(BulkEsimPurchaseWorker).startWorker(),
            app.get(EsimPurchaseQueueWorker).startWorker(),
            app.get(LoyaltyReminderWorker).startWorker(),
          ]
        : [];

    process.on('uncaughtRejection', function (err) {
      // clean up allocated resources
      /////////////////..//
      // log necessary error details to log files
      logger.log(err);
    });

    process.on('uncaughtException', function (err) {
      // clean up allocated resources
      // log necessary error details to log files
      logger.log(err);
      process.exit(); // exit the process to avoid unknown state
    });

    process.on('SIGINT', async function () {
      server.close?.();
      // calling .shutdown allows your process to exit normally
      // tooBusy.shutdown();
      for (const worker of workers) {
        await worker?.close?.();
      }
      process.exit();
    });

    process.on('SIGTERM', async function () {
      server.close?.();
      // calling .shutdown allows your process to exit normally
      tooBusy.shutdown();
      process.exit();
    });
  })
  .catch((e) => {
    console.log(e);
    process.exit(1);
  });
