import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import axios from 'axios';

export const EVENT_DEV_NOTIFY = 'EVENT_DEV_NOTIFY';

@Injectable()
export class EventDevNotify {
  constructor(private logger: Logger) {

  }

  @OnEvent(EVENT_DEV_NOTIFY)
  async handleOrderSuccessful(data: {
    error: Error | string;
    subject?: string;
    message: string;
  }) {
    if (process.env.NODE_ENV !== 'production') return;
    try {
      const webhookUrl = '*********************************************************************************';
      const payload = {
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: ':rotating_light: *API ALERT / WARNING* :rotating_light: <!channel> <@U076LN70Y2V>',
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Subject:*\n${data.subject || 'No subject'}`,
              },
              {
                type: 'mrkdwn',
                text: `*Time:*\n${new Date().toLocaleString('en-US', { timeZone: 'Asia/Tokyo' })}`,
              },
            ],
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Message:*\n${data.message}`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Stack/Error:*\n\`${typeof data.error === 'string' ? data.error : (data.error?.stack || data.error)}`.slice(0, 2900) + '\n\`',
            },
          },
        ],
      };
      const response = await axios.post(webhookUrl, payload);
      this.logger.log('Sent dev notification to Slack', response.data);
    } catch (err) {
      this.logger.error('Failed to send Slack notification', err);
    }
  }
}
