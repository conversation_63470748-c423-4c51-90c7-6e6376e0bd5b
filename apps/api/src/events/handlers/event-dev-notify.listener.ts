import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EmailsService } from 'src/emails/emails.service';

export const EVENT_DEV_NOTIFY = 'EVENT_DEV_NOTIFY';

@Injectable()
export class EventDevNotify {
  constructor(private emailService: EmailsService, private logger: Logger) { }

  @OnEvent(EVENT_DEV_NOTIFY)
  async handleOrderSuccessful(data: {
    error: Error | string;
    subject?: string;
    message: string;
  }) {
    try {
      const message = await this.emailService.sendToDevelop(
        `
      Message: ${data.message}
      stack : ${data.error}
    `,
        data.subject,
      );
      this.logger.log(message)
    } catch (err) {
      this.logger.error(err)
    }
  }
}
