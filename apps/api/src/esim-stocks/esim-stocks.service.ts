import { Injectable, Logger } from '@nestjs/common';
import { IEsimStock } from 'src/esim-importer/core/EsimCSVParser';
import { PlansService } from 'src/plans/plans.service';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class EsimStocksService {
  constructor(
    private prismaService: PrismaService,
    private plansService: PlansService,
    private logger: Logger,
  ) {}

  async addEsims(esim: IEsimStock) {
    const plan = await this.plansService.getPlanByOptionId(esim.optionId);
    if (!plan) {
      this.logger.log(
        `This esim was rejected as no plan id exist for this esim, OptionID: ${esim.optionId} ICCID: ${esim.iccid}`,
      );
    }

    const savedEsims = await this.prismaService.esim_stocks.create({
      data: {
        planId: plan.id,
        status: 'AVAILABLE',
        activateCode: esim.activationCode,
        downloadLink: esim.downloadLink,
        qrCodeImgUrl: esim.qrCodeImgUrl,
        iccid: esim.iccid,
        topupId: esim.topupid,
        smdp: esim.smdp,
      },
    });

    return { savedEsims };
  }

  async getEsimsByOptionId(planId: string) {
    const plan = await this.prismaService.plans.findFirst({
      where: {
        planId,
      },
    });
    const esim = await this.prismaService.esim_stocks.findFirst({
      where: {
        planId: plan.id,
        status: 'AVAILABLE',
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
    if (!esim) {
      throw new Error(
        `Stock not available for this plan id ${plan.id} (${plan.dataId} /  ${plan.validityDays} days / ${plan.packageType})`,
      );
    }
    await this.prismaService.esim_stocks.update({
      where: {
        iccid: esim.iccid,
      },
      data: {
        status: 'DISPATCHED',
      },
    });
    return esim;
  }
}
