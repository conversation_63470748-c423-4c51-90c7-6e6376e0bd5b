import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { EsimStocksService } from './esim-stocks.service';
import { PrismaService } from 'src/prisma.service';
import { PlansService } from 'src/plans/plans.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { EsimStockController } from './esim-stock.controller';

@Module({
  providers: [
    EsimStocksService,
    PrismaService,
    PlansService,
    Logger,
    XchangeService,
    KeyValueStoreService,
  ],
  controllers: [EsimStockController],
})
export class EsimStocksModule {}
