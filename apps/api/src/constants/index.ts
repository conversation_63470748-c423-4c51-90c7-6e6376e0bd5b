import { CURRENCY } from '@prisma/client';

export const USIMSA_ACCESS_KEY = 'USIMSA_ACCESS_KEY';
export const USIMSA_SECRET_KEY = 'USIMSA_SECRET_KEY';
export const STRIPE_CURRENCY = 'STRIPE_CURRENCY';
export enum PAYMENT_STATUS {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  PAYMENT_SUCCESS_ESIM_FAILED = 'PAYMENT_SUCCESS_ESIM_FAILED',
  PAYMENT_SUCCESS_ESIM_PENDING = 'PAYMENT_SUCCESS_ESIM_PENDING',
  THIRD_PARTY_PURCHASE = 'THIRD_PARTY_PURCHASE',
}
export const STRIPE_ENDPOINT_SECRET = 'STRIPE_ENDPOINT_SECRET';
export enum INTENT_TYPE {
  TOPUP = 'TOPUP',
  SUBSCRIBE = 'SUBSCRIBE',
}
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly',
}
export const ER_DUP_ENTRY = 'ER_DUP_ENTRY';

export enum CurrencyList {
  AED = 'AED',
  AFN = 'AFN',
  ALL = 'ALL',
  AMD = 'AMD',
  ANG = 'ANG',
  AOA = 'AOA',
  ARS = 'ARS',
  AUD = 'AUD',
  AWG = 'AWG',
  AZN = 'AZN',
  BAM = 'BAM',
  BBD = 'BBD',
  BDT = 'BDT',
  BGN = 'BGN',
  BHD = 'BHD',
  BIF = 'BIF',
  BMD = 'BMD',
  BND = 'BND',
  BOB = 'BOB',
  BRL = 'BRL',
  BSD = 'BSD',
  BTC = 'BTC',
  BTN = 'BTN',
  BWP = 'BWP',
  BYN = 'BYN',
  BZD = 'BZD',
  CAD = 'CAD',
  CDF = 'CDF',
  CHF = 'CHF',
  CLF = 'CLF',
  CLP = 'CLP',
  CNH = 'CNH',
  CNY = 'CNY',
  COP = 'COP',
  CRC = 'CRC',
  CUC = 'CUC',
  CUP = 'CUP',
  CVE = 'CVE',
  CZK = 'CZK',
  DJF = 'DJF',
  DKK = 'DKK',
  DOP = 'DOP',
  DZD = 'DZD',
  EGP = 'EGP',
  ERN = 'ERN',
  ETB = 'ETB',
  EUR = 'EUR',
  FJD = 'FJD',
  FKP = 'FKP',
  GBP = 'GBP',
  GEL = 'GEL',
  GGP = 'GGP',
  GHS = 'GHS',
  GIP = 'GIP',
  GMD = 'GMD',
  GNF = 'GNF',
  GTQ = 'GTQ',
  GYD = 'GYD',
  HKD = 'HKD',
  HNL = 'HNL',
  HRK = 'HRK',
  HTG = 'HTG',
  HUF = 'HUF',
  IDR = 'IDR',
  ILS = 'ILS',
  IMP = 'IMP',
  INR = 'INR',
  IQD = 'IQD',
  IRR = 'IRR',
  ISK = 'ISK',
  JEP = 'JEP',
  JMD = 'JMD',
  JOD = 'JOD',
  JPY = 'JPY',
  KES = 'KES',
  KGS = 'KGS',
  KHR = 'KHR',
  KMF = 'KMF',
  KPW = 'KPW',
  KRW = 'KRW',
  KWD = 'KWD',
  KYD = 'KYD',
  KZT = 'KZT',
  LAK = 'LAK',
  LBP = 'LBP',
  LKR = 'LKR',
  LRD = 'LRD',
  LSL = 'LSL',
  LYD = 'LYD',
  MAD = 'MAD',
  MDL = 'MDL',
  MGA = 'MGA',
  MKD = 'MKD',
  MMK = 'MMK',
  MNT = 'MNT',
  MOP = 'MOP',
  MRU = 'MRU',
  MUR = 'MUR',
  MVR = 'MVR',
  MWK = 'MWK',
  MXN = 'MXN',
  MYR = 'MYR',
  MZN = 'MZN',
  NAD = 'NAD',
  NGN = 'NGN',
  NIO = 'NIO',
  NOK = 'NOK',
  NPR = 'NPR',
  NZD = 'NZD',
  OMR = 'OMR',
  PAB = 'PAB',
  PEN = 'PEN',
  PGK = 'PGK',
  PHP = 'PHP',
  PKR = 'PKR',
  PLN = 'PLN',
  PYG = 'PYG',
  QAR = 'QAR',
  RON = 'RON',
  RSD = 'RSD',
  RUB = 'RUB',
  RWF = 'RWF',
  SAR = 'SAR',
  SBD = 'SBD',
  SCR = 'SCR',
  SDG = 'SDG',
  SEK = 'SEK',
  SGD = 'SGD',
  SHP = 'SHP',
  SLL = 'SLL',
  SOS = 'SOS',
  SRD = 'SRD',
  SSP = 'SSP',
  STD = 'STD',
  STN = 'STN',
  SVC = 'SVC',
  SYP = 'SYP',
  SZL = 'SZL',
  THB = 'THB',
  TJS = 'TJS',
  TMT = 'TMT',
  TND = 'TND',
  TOP = 'TOP',
  TRY = 'TRY',
  TTD = 'TTD',
  TWD = 'TWD',
  TZS = 'TZS',
  UAH = 'UAH',
  UGX = 'UGX',
  USD = 'USD',
  UYU = 'UYU',
  UZS = 'UZS',
  VES = 'VES',
  VND = 'VND',
  VUV = 'VUV',
  WST = 'WST',
  XAF = 'XAF',
  XAG = 'XAG',
  XAU = 'XAU',
  XCD = 'XCD',
  XDR = 'XDR',
  XOF = 'XOF',
  XPD = 'XPD',
  XPF = 'XPF',
  XPT = 'XPT',
  YER = 'YER',
  ZAR = 'ZAR',
  ZMW = 'ZMW',
  ZWL = 'ZWL',
}
export const SUPER_ACCESS_ADMIN = 'c9cd4d81-2a15-40be-b910-50380ad6a08c';
export const AN_HOUR_IN_MILLISECONDS = 3600000;
export const FIVE_MIN_IN_MILLISECONDS = 300000;
export const ESIM_EXPIRE_TIME_IF_NOT_ACTIVATED_DAYS = 30;
export const QUEUE_ESIM_CREATE_BUY = 'QUEUE_ESIM_CREATE_BUY';
export const QUEUE_WEBHOOK_EXECUTOR = 'QUEUE_WEBHOOK_EXECUTOR';
export const QUEUE_EMAIL_DISPATCHER = 'QUEUE_EMAIL_DISPATCHER';
export const QUEUE_NEW_ESIM = 'QUEUE_NEW_ESIM';
export const BULK_ESIM_PURCHASE = 'BULK_ESIM_PURCHASE';
export const QUEUE_LOYALTY_REMINDER = 'QUEUE_LOYALTY_REMINDER';
export const USIMSA_WEBHOOK_PROCESS = 'USIMSA_WEBHOOK_PROCESS';
export const THIRD_PARTY_APP_PURCHASE = 'THIRD_PARTY_APP_PURCHASE';
export const BULK_ESIM_PURCHASE_PROCESS = 'BULK_ESIM_PURCHASE_PROCESS';
export const INCOMPLETE_ORDERS_PROCESS = 'INCOMPLETE_ORDERS_PROCESS';
export const SEND_NOTIFICATION = 'SEND_NOTIFICATION';
export const USAGE_QUEUE = 'USAGE_QUEUE';
export const NOT_ACTIVATED_AND_EXPIRED = 'NOT_ACTIVATED_AND_EXPIRED';
export const USED_AND_EXPIRED = 'USED_AND_EXPIRED';
export const NOT_ACTIVATED = 'NOT_ACTIVATED';
export const RUNNING = 'RUNNING';
export const ONE_DAY_IN_MILLISECONDS = 86400000;
const COGNITO_IDENTITY_PROVIDERS = {
  IDP_GOOGLE: 'Google',
  IDP_FACEBOOK: 'Facebook',
  IDP_APPLE: 'SignInWithApple',
  IDP_AMAZON: 'LoginWithAmazon',
  IDP_MICROSOFT: 'Microsoft', // Custom OIDC provider
  IDP_GITHUB: 'GitHub', // Custom OIDC provider
  IDP_TWITTER: 'Twitter', // Custom OIDC provider
  IDP_LINKEDIN: 'LinkedIn', // Custom OIDC provider
  IDP_COGNITO: 'Cognito', // Manual email/password login
};

export default COGNITO_IDENTITY_PROVIDERS;

export const CURRENCY_LOCALE_MAP: Record<CURRENCY, string> = {
  JPY: 'ja-JP',
  USD: 'en-US',
  EUR: 'de-DE',
  CAD: 'en-CA',
  GBP: 'en-GB',
  TWD: 'zh-TW',
  HKD: 'zh-HK',
  CNY: 'zh-CN',
  KRW: 'ko-KR',
  AUD: 'en-AU',
} as const;

export type SupportedLocale = (typeof CURRENCY_LOCALE_MAP)[CURRENCY];
