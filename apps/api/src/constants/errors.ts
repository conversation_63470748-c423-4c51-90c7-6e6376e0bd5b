export const ApiErrorCodes = {
  // 400 Bad Request
  ERR_400_INVALID_EMAIL_FOUND: {
    message: 'Email address already exists.',
    errorCode: 4000,
  },

  ERR_400_INVALID_REQUEST: {
    message: 'The request is malformed or missing required parameters.',
    errorCode: 4001,
  },
  ERR_400_INVALID_JSON: {
    message: 'The request body is not a valid JSON format.',
    errorCode: 4002,
  },
  ERR_400_INVALID_EMAIL: {
    message: 'The provided email address is not valid.',
    errorCode: 4003,
  },

  ERR_400_INVALID_USERNAME: {
    message: 'The provided username does not meet the requirements.',
    errorCode: 4004,
  },
  ERR_400_INVALID_USERNAME_PASSWORD: {
    message: 'The provided username and password combination does not match.',
    errorCode: 4005,
  },
  ERR_400_INVALID_PHONE_FOUND: {
    message: 'Phone number already exists.',
    errorCode: 4006,
  },
  ERR_400_EMAIL_NOT_VERIFIED: {
    message: 'Email address is not verified.',
    errorCode: 4007,
  },
  ERR_400_AccountNotIDPManual: {
    message: 'Account was created using social login.',
    errorCode: 4008,
  },
  ERR_400_EMAIL_CONFLICT_WITH_SOCIAL_LOGIN: {
    message: 'An account already exists with the same email address logged in using social login. Please use your social login to sign in.',
    errorCode: 4009,
  },
  ERR_409_CONTENT_CONFLICT: {
    message: 'Duplicate content.',
    errorCode: 4097,
  },
  // 401 Unauthorized
  ERR_401_MISSING_TOKEN: {
    message: 'Authentication token is missing in the request headers.',
    errorCode: 4011,
  },
  ERR_401_INVALID_TOKEN: {
    message: 'The provided authentication token is invalid or expired.',
    errorCode: 4012,
  },
  ERR_401_INSUFFICIENT_PERMISSIONS: {
    message:
      'The user does not have sufficient permissions to perform the action.',
    errorCode: 4013,
  },

  // 403 Forbidden
  ERR_403_ACCESS_DENIED: {
    message: 'The user is not allowed to access the requested resource.',
    errorCode: 4031,
  },
  ERR_403_BLOCKED_USER: {
    message: 'The user account is blocked or deactivated.',
    errorCode: 4032,
  },

  // 404 Not Found
  ERR_404_RESOURCE_NOT_FOUND: {
    message: 'The requested resource does not exist.',
    errorCode: 4041,
  },
  ERR_404_USER_NOT_FOUND: {
    message: 'The specified user does not exist.',
    errorCode: 4042,
  },
  ERR_404_PRODUCT_NOT_FOUND: {
    message: 'The specified product does not exist.',
    errorCode: 4043,
  },

  // 409 Conflict
  ERR_409_DUPLICATE_RESOURCE: {
    message: 'A resource with the same identifier already exists.',
    errorCode: 4091,
  },
  ERR_409_CONFLICTING_STATE: {
    message:
      'The requested action conflicts with the current state of the resource.',
    errorCode: 4092,
  },

  // 422 Unprocessable Entity
  ERR_422_VALIDATION_FAILED: {
    message: 'Input data failed validation rules.',
    errorCode: 4221,
  },
  ERR_422_INVALID_OPERATION: {
    message: 'The requested operation cannot be performed on the resource.',
    errorCode: 4222,
  },

  // 500 Internal Server Error
  ERR_500_INTERNAL_SERVER_ERROR: {
    message: 'An unexpected error occurred on the server.',
    errorCode: 5001,
  },
  ERR_500_DATABASE_ERROR: {
    message: 'Database operation failed.',
    errorCode: 5002,
  },
  ERR_500_EXTERNAL_SERVICE_ERROR: {
    message: 'Error occurred while communicating with an external service.',
    errorCode: 5003,
  },
} as const;
