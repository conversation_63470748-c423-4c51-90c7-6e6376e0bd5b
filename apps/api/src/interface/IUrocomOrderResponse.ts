export interface IUrocomOrderResponse {
  tradeCode: 1000 | number;
  tradeMsg: 'ok' | 'other';
  tradeData: {
    sm_dp: string; //"2ea7b.2ba537b63f.7e5",
    matching_id: string; //"cc99270420bcd52",
    qr_str: string; //"1$2ea7b.2ba537b63f.7e5$cc99270420bcd52",
    qrCodeContent: string; //"LPA:1$2ea7b.2ba537b63f.7e5$cc99270420bcd52",
    qrcode: string; //"https://macaroon-app.dev.vipwifi.com//data/upload/qrcode/1cf39d42df2621c22354b81e9f762806_.png",
    iccid: string; //"89852290190120114452"
    orderStart?: string;
    orderEnd?: string;
    traffic?: string;
  };
  timestamp: number;
}
