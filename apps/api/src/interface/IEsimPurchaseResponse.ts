export interface IEsimPurchaseResponse {
  code: '0000';
  message?: string;
  products: Array<{
    topupId: string;
    optionId?: string;
    iccid: string;
    smdp: string;
    activateCode: string;
    downloadLink: string;
    qrCodeImgUrl: string;
    /**
     * @deprecated
     */
    qrcodeImgUrl: string;

    esimVendorUniqueReference?: string;
  }>;
  lguTopup?: {
    startDate: Date;
    endDate: Date;
  };
}
