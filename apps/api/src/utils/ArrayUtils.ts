export class ArrayUtils {
  /**
   * Removes falsy values from an array.
   * Falsy values are values that evaluate to false in a Boolean context:
   * false, null, undefined, 0, NaN, and an empty string ('').
   *
   * @param {Array} array - The array from which falsy values should be removed.
   * @returns {Array} - A new array containing only truthy values from the input array.
   *                   If the input array is empty or contains no truthy values, an empty array is returned.
   */
  static removeFalsyValues<T>(array: T[]): T[] {
    return array.filter(Boolean);
  }

  /**
   * Check if array is empty
   * @param arr
   * @returns true if the array is empty, null or undefined; false otherwise
   */
  static isEmpty<T>(arr: T[]): boolean {
    return !arr || arr.length <= 0;
  }

  /**
   * Check if array is not empty
   * @param arr
   * @returns true if the array is empty, null or undefined; false otherwise
   */
  static isNotEmpty<T>(arr: T[]): boolean {
    return !this.isEmpty(arr);
  }

  /** Batch the array into an array of specified batch size */
  static batchArray<T>(array: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }
}
