import { CURRENCY, IDENTITY_PROVIDER } from '@prisma/client';
import * as Buffer from 'buffer';
import { ValidationError } from 'class-validator';
import * as CryptoJS from 'crypto-js';
import { format, parse } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import * as jwt from 'jsonwebtoken';
import { forOwn, set } from 'lodash';
import { CurrencyList, SupportedLocale } from 'src/constants';

export const encryptToJWT = (
  payload: object,
  expiresIn: string | number = '1h',
): string => {
  return jwt.sign(payload, process.env['AWS_SECRET_ACCESS_KEY'], { expiresIn });
};

export const decryptFromJWT = (token: string): object | string => {
  try {
    return jwt.verify(token, process.env['AWS_SECRET_ACCESS_KEY']);
  } catch (error) {
    throw new Error('Invalid token');
  }
};

export const encryptAppStandard = (text: string): string => {
  const secretKey = CryptoJS.enc.Utf8.parse(
    process.env['AWS_SECRET_ACCESS_KEY'],
  ); // Replace with a 32-byte key
  const iv = CryptoJS.lib.WordArray.random(16); // Generate a random IV
  const encrypted = CryptoJS.AES.encrypt(text, secretKey, { iv: iv });
  return iv.toString() + ':' + encrypted.toString();
};

export const decryptAppStandard = (encryptedText: string): string => {
  const secretKey = CryptoJS.enc.Utf8.parse(
    process.env['AWS_SECRET_ACCESS_KEY'],
  ); // Replace with a 32-byte key
  const textParts = encryptedText.split(':');
  const iv = CryptoJS.enc.Hex.parse(textParts.shift());
  const encrypted = textParts.join(':');
  const decrypted = CryptoJS.AES.decrypt(encrypted, secretKey, { iv: iv });
  return decrypted.toString(CryptoJS.enc.Utf8);
};

export const getFormmattedOrderId = (idFormatter: any) => {
  return process.env.NODE_ENV === 'development'
    ? `GEsimLocal${idFormatter}`
    : `GEsim${idFormatter}`;
};

export const justSuccessJSON = () => ({
  success: true,
});

export const getIdentityProvider = (username: string): IDENTITY_PROVIDER => {
  if (username.toLowerCase().includes('facebook_')) {
    return 'IDP_FACEBOOK';
  }

  if (username.toLowerCase().includes('google_')) {
    return 'IDP_GOOGLE';
  }
  if (username.toLowerCase().includes('SignInWithApple_')) {
    return 'IDP_APPLE';
  }

  return 'IDP_MANUAL';
};

export const apiIdWrapper = (apiId: string) => `API_ID_${apiId}`;

/**
 * Converts date string in yyyyMMddHHmmss format used by LGUProvider to Date
 * @param dateString Date string in yyyyMMddHHmmss format
 * @returns Date
 */
export function parseLGUTime(dateString: string): Date {
  return parse(dateString, 'yyyyMMddHHmmss', new Date());
}

export const formatUTCtoJST = (date: string) => {
  return format(
    utcToZonedTime(new Date(date), 'asia/tokyo'),
    'yyyy/M/d hh:mm:ss a',
  );
};

export function convertJSTtoUTC(jstTimeString: string) {
  // Parse the JST time string to a Date object
  const jstDate = parse(jstTimeString, 'yyyy-MM-dd HH:mm:ss', new Date());

  // Convert JST time to UTC
  const utcDate = utcToZonedTime(jstDate, 'UTC');

  // Format the UTC time as a string
  const utcTimeString = format(utcDate, 'yyyy-MM-dd HH:mm:ss');

  return utcTimeString;
}

export function convertKSTtoUTC(jstTimeString: string) {
  // Parse the JST time string (in 'yyyyMMddHHmmss' format) to a Date object
  const jstDate = parse(jstTimeString, 'yyyyMMddHHmmss', new Date());

  // Convert JST time to UTC
  const utcDate = utcToZonedTime(jstDate, 'UTC');

  // Format the UTC time as a string
  const utcTimeString = format(utcDate, 'yyyy-MM-dd HH:mm:ss');

  return utcTimeString;
}

export const isDev = () => process.env['NODE_ENV'] !== 'production';
export const isAppEnvDev = () => process.env['APP_ENV'] === 'development';
export function capitalizeFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
export const countryAlias = (countryName: string) => {
  switch (countryName.toLowerCase()) {
    case 'usa':
      return 'United States';
    case 'uk':
      return 'United Kingdom';
    case 'korea':
      return 'South Korea';
    default:
      return countryName;
  }
};

export function sizeToBytes(size: string): number {
  const unit = size.includes('GB') ? 'GB' : 'MB';
  let bytes = +size.replace(/GB|MB/g, '');
  if (unit === 'GB') {
    bytes *= 1024 * 1024 * 1024; // 1 GB = 1024^3 bytes
  } else if (unit === 'MB') {
    bytes *= 1024 * 1024; // 1 MB = 1024^2 bytes
  }
  return bytes;
}
export function convertBytesToMB(bytes) {
  const bytesPerMB = 1048576; // 1 MB = 1,048,576 bytes
  const megabytes = bytes / bytesPerMB;
  return megabytes;
}

export function getCountryFlagImage(countryName: string) {
  return `https://cdn-dev.japan-wireless.com/global-esim/assets/flags/212x128/${countryName}.png`;
}

export function getProductPlanImage(planId: string | number) {
  return `https://cdn-dev.japan-wireless.com/global-esim/assets/plan-images/plan-${planId}.png`;
}

export function extractDigits(inputString: string) {
  // Use a regular expression to match digits (\d) and the global (g) flag to find all occurrences.
  const digitArray = inputString.match(/\d/g);

  // Join the matched digits to form a single string.
  const digits = digitArray ? digitArray.join('') : '';

  return digits;
}
export function getPlanDetailsApi(planId: string) {
  return isAppEnvDev()
    ? `https://esim-dev.gmobile.biz/api/v1/plans/${planId}`
    : `https://esim.gmobile.biz/api/v1/plans/${planId}`;
}

export function encodeToBase64(input: string): string {
  const buffer = Buffer.Buffer.from(input);
  return buffer.toString('base64');
}

// Function to decode a Base64 encoded string
export function decodeFromBase64(input: string): string {
  const buffer = Buffer.Buffer.from(input, 'base64');
  return buffer.toString('utf-8');
}

export function getAllConstraintMessages(errors: ValidationError[]): string[] {
  const messages: string[] = [];

  function collectMessages(error: ValidationError, path: string) {
    const currentPath = path ? `${path}.${error.property}` : error.property;

    if (error.constraints) {
      for (const key in error.constraints) {
        if (error.constraints.hasOwnProperty(key)) {
          messages.push(`${currentPath}: ${error.constraints[key]}`);
        }
      }
    }

    if (error.children && error.children.length > 0) {
      for (const childError of error.children) {
        collectMessages(childError, currentPath);
      }
    }
  }

  for (const error of errors) {
    collectMessages(error, '');
  }

  return messages;
}

export function simpleStringEncode(string: string) {
  const strings = string.split('').reverse().join('');
  return encodeToBase64(strings);
}

export function deserializeFromDotNotation(dotObject) {
  const result = {};
  forOwn(dotObject, (value, key) => {
    set(result, key, value);
  });
  return result;
}

export function deepOmitKeys<T extends object>(
  data: T,
  keysToOmit: (keyof any)[],
): Partial<T> {
  function recurse(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(recurse);
    }
    if (obj && typeof obj === 'object') {
      return Object.entries(obj).reduce((acc, [key, val]) => {
        if (keysToOmit.includes(key as any)) {
          return acc;
        }
        (acc as any)[key] = recurse(val);
        return acc;
      }, {} as any);
    }

    return obj;
  }

  return recurse(data);
}

export const priceFormatted = (
  locale: SupportedLocale = 'ja-JP',
  currencyCode: CURRENCY = 'JPY',
  price: number,
) => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
    }).format(price || 0);
  } catch (error) {
    console.log(error);
    return `${currencyCode} ${price}`;
  }
};

export function sanitizeCurrencyCode(code?: string | null): CURRENCY {
  if (typeof code === 'string') {
    const normalized = code.toUpperCase() as keyof typeof CURRENCY;
    const isValid = Object.values(CurrencyList).includes(code as CurrencyList);

    if (normalized in CURRENCY && isValid) {
      return CURRENCY[normalized];
    }
  }

  return CURRENCY.JPY;
}

export function ensureTrailingSlash(url: string): string {
  return url.endsWith('/') ? url : `${url}/`;
}

/**  Takes an array of strings and returns a subset that fits within the specified length limit */
export function limitStringArrayByLength(
  items: string[],
  maxLength: number,
  separator: string = ',',
): string {
  const result: string[] = [];
  let totalLength = 0;

  for (const item of items) {
    const itemLength = item.length;
    // Calculate what the total length would be if we add this item
    const nextLength =
      result.length === 0
        ? itemLength
        : totalLength + separator.length + itemLength;

    // Break if adding this item would exceed the limit
    if (nextLength > maxLength) break;

    result.push(item);
    totalLength = nextLength;
  }

  return result.join(separator);
}

/**
 * Converts a number to its ordinal representation (1st, 2nd, 3rd, etc.)
 * @param num - The number to convert
 * @returns The ordinal string representation
 */
export function ordinal(num: number): string {
  const absNum = Math.abs(num);
  const lastDigit = absNum % 10;
  const lastTwoDigits = absNum % 100;

  // Handle special cases for 11th, 12th, 13th
  if (lastTwoDigits >= 11 && lastTwoDigits <= 13) {
    return `${num}th`;
  }

  // Handle regular cases
  switch (lastDigit) {
    case 1:
      return `${num}st`;
    case 2:
      return `${num}nd`;
    case 3:
      return `${num}rd`;
    default:
      return `${num}th`;
  }
}

export function startOfUTCDay(date: Date): Date {
  const utc = new Date(
    Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()),
  );
  return utc;
}

export function endOfUTCDay(date: Date): Date {
  const utc = new Date(
    Date.UTC(
      date.getUTCFullYear(),
      date.getUTCMonth(),
      date.getUTCDate(),
      23,
      59,
      59,
      999,
    ),
  );
  return utc;
}

export function getUserSource(
  userSource: string,
  userPool: string,
  userLocale: string,
): string {
  return userSource || (userPool === 'GLOBAL' && userLocale === 'jp')
    ? 'global-esim-jp'
    : userPool === 'AIRTRIP'
      ? 'airtrip'
      : 'global-esim';
}

export function getBrandNameFromSource(
  orderSource: string,
  orderLang: string,
  isSubject: boolean = false,
) {
  //For all airtrip related orders airtrip, global_esim_airtrip, airtrip-esim-ios-app
  if (orderSource.includes('airtrip')) {
    return isSubject ? '【エアトリeSIM】' : 'エアトリeSIM';
  }

  //For global esim [web] related orders global-esim
  if (orderSource === 'global-esim') {
    return 'Global Mobile eSIM';
  }

  //For rest of the global esim related orders global-esim-jp, global-esim-android-app, global-esim-ios-app
  if (orderSource.includes('global-esim') && orderLang === 'jp') {
    return isSubject ? '【グロモバeSIM】' : 'グロモバeSIM';
  }

  //For global esim [app] related orders with language not japanese
  return 'Global Mobile eSIM';
}
export const unflatten = (obj) => {
  const result = {};
  for (const key in obj) {
    set(result, key, obj[key]);
  }
  return result;
};
