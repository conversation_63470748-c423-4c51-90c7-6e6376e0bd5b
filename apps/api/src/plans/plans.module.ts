import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { PlansService } from './plans.service';
import { PlansController } from './plans.controller';
import { PrismaService } from 'src/prisma.service';
import { XchangeService } from 'src/xchange/xchange.service';
import { KeyValueStoreService } from 'src/key-value-store/key-value-store.service';
import { RequestOriginCacheInterceptor } from './RequestOriginCacheInterceptor';

@Module({
  providers: [
    PlansService,
    PrismaService,
    XchangeService,
    KeyValueStoreService,
    Logger,
    RequestOriginCacheInterceptor,
  ],
  controllers: [PlansController],
})
export class PlansModule {}
