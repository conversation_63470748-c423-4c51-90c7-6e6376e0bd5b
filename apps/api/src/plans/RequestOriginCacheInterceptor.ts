import { CacheInterceptor, ExecutionContext, Injectable } from '@nestjs/common';
import { isAppEnvDev } from 'src/utils';

@Injectable()
export class RequestOriginCacheInterceptor extends CacheInterceptor {
  trackBy(context: ExecutionContext): string | undefined {
    const request = context.switchToHttp().getRequest();

    // Check if the environment is development
    if (isAppEnvDev()) {
      return undefined;
    }

    // Read the header value (e.g., 'x-custom-header')
    const customHeaderValue =
      request.headers['x-service-name']?.toLowerCase?.();

    // Optionally, include request URL or other unique identifiers
    const url = request.url;

    // Construct a custom cache key including the header value
    if (customHeaderValue) {
      return `cache-key:${url}:${customHeaderValue}`;
    }

    // Fallback to the default behavior if the header is not present
    return super.trackBy(context);
  }
}
