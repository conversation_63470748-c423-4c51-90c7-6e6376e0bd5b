import { plans } from '@prisma/client';

type Network = {
  name: string; // e.g., "KDDI" or "NTT Docomo"
  types: string[]; // e.g., ["4GLTE", "5G"]
};

type Coverage = {
  code: string; // e.g., "JP"
  networks: Network[];
};

type InternetBreakout = {
  country: string;
};

type EsimDBApiResponse = {
  planType: 'Fixed' | 'Unlimited';
  internetBreakouts: InternetBreakout[];
  planProvider: 'KDDI' | 'NTT Docomo';
  coverages: Coverage[];
  dataCap: number;
  reducedSpeed: string;
};

export type PlanWithJPY = plans & {
  xe: {
    [key: string]: any;
  };
};

export type EsimDBPlanWithDetails = PlanWithJPY & EsimDBApiResponse;
