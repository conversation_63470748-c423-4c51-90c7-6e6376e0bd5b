import {
  BadRequestException,
  CACHE_MANAGER,
  Controller,
  Get,
  Inject,
  Logger,
  NotFoundException,
  Param,
  Query,
  Req,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { groupBy, sortBy, uniqBy } from 'lodash';
import { HTTPHeaderInterceptor } from 'src/core/HTTPHeaderInterceptor';
import { SentryInterceptor } from 'src/SentryInterceptor';
import { PlansQueryDto } from './dtos/plans-query.dto';
import { PlanResponseDto, PlansResponseDto } from './dtos/plans-response.dto';
import { PlansService } from './plans.service';
// TODO: make this dynamic
import { Throttle } from '@nestjs/throttler';
import { Cache } from 'cache-manager';
import { deepOmitKeys } from 'src/utils';
import { COUNTRY_LIST } from './app-countries-list';
import { RequestOriginCacheInterceptor } from './RequestOriginCacheInterceptor';
import {
  EsimDBPlanWithDetails
} from './types/plans-response.type';

@Controller()
@UseInterceptors(RequestOriginCacheInterceptor)
@ApiTags('Plans list and details')
@UseInterceptors(SentryInterceptor) // APPLY THE INTERCEPTOR
export class PlansController {
  private logger = new Logger(PlansController.name);

  constructor(
    private planService: PlansService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) { }

  @Get('plans')
  @ApiOkResponse({
    type: PlansResponseDto,
  })
  @Throttle(5000, 60000)
  @UseInterceptors(HTTPHeaderInterceptor)
  async getAllPlans(@Req() request: Request, @Query() query: PlansQueryDto) {
    let requestOriginServiceName =
      query.requestOriginServiceName ||
      request.headers?.['x-service-name'] ||
      undefined;

    // --- Platform Detection ---
    const userAgent = (request.headers['user-agent'] || '').toLowerCase();
    const xPlatform = (request.headers['x-platform'] || '').toString().toLowerCase();
    let platform: 'android' | 'ios' | 'unknown' = 'unknown';
    if (xPlatform === 'android' || userAgent.includes('okhttp') || userAgent.includes('dalvik') || userAgent.includes('android')) {
      platform = 'android';
    } else if (
      userAgent.includes('cfnetwork') || userAgent.includes('iphone') || userAgent.includes('darwin')) {
      platform = 'ios';
    }
    // --- End Platform Detection ---

    if (query.featured) {
      const countries = await this.planService.getFeaturedPlans(true);
      const regions = await this.planService.getFeaturedPlans(false);
      return { countries, regions };
    }

    const data = await this.planService.getAllPlans({
      ...query,
      requestOriginServiceName: requestOriginServiceName,
      fallbackRequestOriginServiceName: request.headers?.['x-service-name'],
    });
    const groupedData = groupBy(data, 'packageType');

    // --- Filter out 5G-only plans for non-Japan mobile requests ---
    let filteredData = data;
    if ((platform === 'android' || platform === 'ios') && query.country?.toLowerCase() !== 'japan') {
      filteredData = data.filter(plan => {
        //@ts-expect-error
        // const gen = plan.network?.networkGeneration?.replace(/\s/g, '').toUpperCase();
        // Exclude if only 5G (e.g., '5G' or '5G/5G')
        if (plan.network?.networkGeneration === '5G') {
          return false;
        }
        return true;
      });
    }
    // --- End Filtering ---

    const groupedFilteredData = groupBy(filteredData, 'packageType');

    try {
      groupedFilteredData['PER_DAY'] = sortBy(groupedFilteredData['PER_DAY'], ['validityDays']);
      groupedFilteredData['FIXED_DAY'] = sortBy(groupedFilteredData['FIXED_DAY'], [
        function (item) {
          return parseInt(item.dataVolume);
        },
      ]);
    } catch (err) {
      this.logger.error(err);
    }

    if (query.country) {
      return {
        // Type cast to any[] to access network property, as PlanWithJPY includes network via Prisma include
        network: uniqBy(filteredData as any[], (item) => item.network?.name).map((item) => item.network),
        //@ts-expect-error
        country: filteredData[0]?.country,
        plans: groupedFilteredData,
      };
    }

    return filteredData;
  }

  @Get('plans/external')
  @ApiOkResponse({
    type: PlansResponseDto,
  })
  @Throttle(5000, 60000)
  @UseInterceptors(HTTPHeaderInterceptor)
  async listPlans(@Req() request: Request, @Query() query: PlansQueryDto) {
    try {
      let requestOriginServiceName =
        query.requestOriginServiceName ||
        request.headers?.['x-service-name'] ||
        undefined;

      const fallbackServiceName = request.headers?.['x-service-name'];

      if (query.featured) {
        const countries = await this.planService.getFeaturedPlans(true);
        const regions = await this.planService.getFeaturedPlans(false);
        return { countries, regions };
      }

      const data = await this.planService.getPlansForExternalSource({
        ...query,
        isForThirdPartySources: true,
        requestOriginServiceName: requestOriginServiceName,
        fallbackRequestOriginServiceName: fallbackServiceName,
      });
      const groupedData = groupBy(data, 'packageType');

      try {
        groupedData['PER_DAY'] = sortBy(groupedData['PER_DAY'], [
          'validityDays',
        ]);
        groupedData['FIXED_DAY'] = sortBy(groupedData['FIXED_DAY'], [
          function (item) {
            return parseInt(item.dataVolume);
          },
        ]);
      } catch (err) {
        this.logger.error(err);
      }

      const plansWithDetails = this.addPlanDetails(data);

      if (query.country) {
        return {
          //@ts-expect-error
          network: uniqBy(data, 'network.name').map((item) => item.network),
          //@ts-expect-error
          country: data[0]?.country,
          plans: groupedData,
        };
      }

      return plansWithDetails;
    } catch (error) {
      this.logger.log(error.message);
      throw new BadRequestException(error.message);
    }
  }

  @Get('countries-list')
  @ApiTags('Countries list for mobile')
  async getCountries() {
    return COUNTRY_LIST;
  }

  @Get('plans/:planId')
  @ApiOkResponse({
    type: PlanResponseDto,
  })
  async getPlan(
    @Param('planId') planId: string,
    @Query()
    query: Pick<
      PlansQueryDto,
      'requestOriginServiceName' | 'requestOriginServiceNameOnly'
    >,
    @Req() request: Request,
  ) {
    let requestOriginServiceName =
      query.requestOriginServiceName ||
      request.headers?.['x-service-name'] ||
      undefined;
    const plan = await this.planService.getPlanById(+planId, {
      serviceProvider: {
        enabled: undefined,
      },
      requestOriginServiceName: requestOriginServiceName,
      requestOriginServiceNameOnly: query.requestOriginServiceNameOnly,
      fallbackRequestOriginServiceName: request.headers?.['x-service-name'],
    });
    if (!plan) throw new NotFoundException();
    return plan;
  }

  private addPlanDetails(plans): EsimDBPlanWithDetails[] {
    return plans.map((plan): EsimDBPlanWithDetails => {
      const planProvider = plan.network.name;
      const planType =
        plan.name === 'unlimited' && plan.packageType === 'PER_DAY'
          ? 'Unlimited'
          : 'Fixed';
      const dataCap = this.calculateDataCap(
        planType,
        planProvider,
        plan.dataId,
      );

      const types = (plan.network.networkGeneration as string)
        .split('/')
        .map((type: string) => type.trim());

      const networkSpeedMatch = ((plan.network?.code as string) || '').match(
        /-(\d+(?:\.\d+)?[kmg]?b(?:ps|\/s))-/i,
      );

      plan = deepOmitKeys(plan, [
        'createdAt',
        'updatedAt',
        'prices',
        'serviceProviderId',
        'serviceProvider',
        'enabled',
        'provision_price',
        'plans_prices',
        'planId',
      ]);

      return {
        ...plan,
        dataCap,
        planType,
        planProvider,
        reducedSpeed: networkSpeedMatch
          ? networkSpeedMatch[1]
          : plan.network?.qos || '',
        internetBreakouts: [{ country: plan.country.code }],
        coverages: [
          {
            code: plan.country.code,
            networks: [
              {
                name: planProvider,
                types,
              },
            ],
          },
        ],
      };
    });
  }

  private calculateDataCap(
    planType: string,
    planProvider: string,
    dataId: string,
  ): number {
    // Default to 0 for unlimited plans (except NTT Docomo)
    if (
      planType !== 'Fixed' &&
      !(planType === 'Unlimited' && planProvider === 'NTT Docomo')
    ) {
      return 0;
    }

    let dataCap = parseInt(dataId, 10);
    if (dataId.toUpperCase().includes('MB')) {
      dataCap = dataCap / 1000;
    }

    return dataCap;
  }
}
