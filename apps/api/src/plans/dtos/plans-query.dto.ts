import { ApiPropertyOptional } from '@nestjs/swagger';
import { DATA_PACKAGE_TYPE } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ServicePlansEnum } from '../enums/ServicePlansEnum';

/**
 * Data Transfer Object for querying plans.
 *
 * @swagger
 * components:
 *   schemas:
 *     PlansQueryDto:
 *       type: object
 *       properties:
 *         country:
 *           type: string
 *           description: Returns plans for only that specific country.
 *         provider:
 *           type: string
 *           description: Returns plans for only that specific provider.
 *         featured:
 *           type: boolean
 *           description: Returns featured plans (cheap) for each country, which can be listed on home page.
 *         region:
 *           type: string
 *           description: Returns featured plans (cheap) for each country, which can be listed on home page.
 *         id:
 *           type: number
 *           description: Returns featured plans (cheap) for each country, which can be listed on home page.
 *         requestOriginServiceName:
 *           $ref: '#/components/schemas/ServicePlansEnum'
 *           description: Returns plans as per request origin.
 *         requestOriginServiceNameOnly:
 *           type: string
 *           description: Returns plans as per request origin only.
 *         type:
 *           type: string
 *           enum: [SUBSCRIBE, TOPUP]
 *         packageType:
 *           type: string
 *           enum: [PER_DAY, FIXED_DAY]
 */
export class PlansQueryDto {
  @ApiPropertyOptional({
    description: 'Returns plans for only that specific country',
  })
  @IsOptional()
  country?: string;

  @ApiPropertyOptional({
    description: 'Returns plans for only that specific provider',
  })
  @IsOptional()
  provider?: string;

  @ApiPropertyOptional({
    description:
      'Returns featured plans (cheap) for each country, which can be listed on home page',
  })
  @IsOptional()
  @Type(() => Boolean)
  featured?: boolean;

  @ApiPropertyOptional({
    description:
      'Returns featured plans (cheap) for each country, which can be listed on home page',
  })
  @IsOptional()
  region?: string;

  @ApiPropertyOptional({
    description:
      'Returns featured plans (cheap) for each country, which can be listed on home page',
  })
  @IsOptional()
  @Type(() => Number)
  id?: number;

  @ApiPropertyOptional({
    description: 'Returns plans as per request origin',
  })
  @IsOptional()
  requestOriginServiceName?: ServicePlansEnum;

  @ApiPropertyOptional({
    description: 'Returns plans as per request origin only',
  })
  @IsOptional()
  requestOriginServiceNameOnly?: string;

  @ApiPropertyOptional({
    description: 'Returns plans which can be top up',
  })
  type?: 'SUBSCRIBE' | 'TOPUP';

  @ApiPropertyOptional({
    description: 'Returns plans according to package type unlimited or fixed',
  })
  packageType?: DATA_PACKAGE_TYPE;

  @ApiPropertyOptional({})
  @IsString()
  @IsOptional()
  source?: string;

  @ApiPropertyOptional({})
  @IsEnum(['3G', '4G', '5G'])
  @IsOptional()
  generation?: string;

  @ApiPropertyOptional({})
  @IsOptional()
  @Type(() => Number)
  validityDays?: number;
}

export class PlansFilter extends PlansQueryDto {
  serviceProvider?: {
    enabled?: boolean;
    name?: string;
  };

  isForThirdPartySources?: boolean;

  includeProvisionPrice?: boolean;

  requestOriginServiceName?: ServicePlansEnum;

  fallbackRequestOriginServiceName?: ServicePlansEnum;
}
