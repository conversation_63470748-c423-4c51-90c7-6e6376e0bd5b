import { ApiProperty } from '@nestjs/swagger';

export const PlanExampleResponse = {
  id: 3013,
  name: '1 days',
  planId: 'IPC_500MB_1_14817',
  price: 3.8181,
  dataId: '500 MB',
  description: '',
  createdAt: '2024-03-06T06:47:45.185Z',
  updatedAt: '2024-03-14T10:12:21.195Z',
  validityDays: 1,
  dataVolume: '500',
  dataUnit: 'MB',
  validityDaysCycle: 'days',
  metadata: {},
  networkId: 101,
  enabled: true,
  packageType: 'PER_DAY',
  countryId: 3,
  serviceProviderId: 2370,
  network: {
    id: 101,
    name: 'japan wireless esim',
    enabled: true,
    code: 'www.dtac.co.th-japan wireless esim-4G-384KB (Single day)-ROAMING',
    apn: 'www.dtac.co.th',
    qos: '384KB (Single day)',
    type: 'ROAMING',
    networkGeneration: '4G',
    countryId: 3,
    createdAt: '2024-01-26T04:11:01.321Z',
    updatedAt: '2024-03-14T10:12:21.866Z',
  },
  country: {
    name: 'japan',
    enabled: true,
    subCountries: null,
  },
  serviceProvider: {
    name: 'UROCOMM',
    enabled: true,
  },
  provision_price: null,
  xe: {
    JPY: 566,
    EUR: '3.50',
    AUD: '5.80',
    CAD: '5.16',
    GBP: '2.99',
    TWD: '120.46',
    HKD: '29.87',
    CNY: '27.46',
    USD: 3.8181,
  },
};
export class PlansResponseDto {
  @ApiProperty({
    example: [PlanExampleResponse],
  })
  data: object;
}
export class PlanResponseDto {
  @ApiProperty({
    example: PlanExampleResponse,
  })
  data: object;
}
