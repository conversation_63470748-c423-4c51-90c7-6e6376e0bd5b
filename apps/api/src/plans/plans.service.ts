import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  DATA_PACKAGE_TYPE,
  DataUnit,
  Prisma,
  countries,
  plans,
} from '@prisma/client';
import * as Sentry from '@sentry/nestjs';
import { Cache } from 'cache-manager';
import { groupBy, isNaN, mapValues, sortBy, toNumber, uniqBy } from 'lodash';
import { ONE_DAY_IN_MILLISECONDS } from 'src/constants';
import { ESIM_PROVIDER } from 'src/esim/providers/EsimProvider';
import { PrismaService } from 'src/prisma.service';
import { deserializeFromDotNotation } from 'src/utils';
import { XchangeService } from 'src/xchange/xchange.service';
import { PlansFilter, PlansQueryDto } from './dtos/plans-query.dto';
import { ServicePlansEnum } from './enums/ServicePlansEnum';
import { PlanWithJPY } from './types/plans-response.type';

@Injectable()
export class PlansService {
  constructor(
    private prismaService: PrismaService,
    private xe: XchangeService,
    private logger: Logger,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async getPlanById(
    planId: number,
    filter: object & {
      fallbackRequestOriginServiceName?: ServicePlansEnum;
      serviceProvider?: any;
      includeProvisionPrice?: boolean;
      debugSource?: string;
    } & Pick<
        PlansQueryDto,
        'requestOriginServiceName' | 'requestOriginServiceNameOnly'
      > = {},
  ) {
    const plans = await this.getAllPlans({
      id: planId,
      ...filter,
      fallbackRequestOriginServiceName: filter.fallbackRequestOriginServiceName,
    });
    return plans.pop();
  }

  getPlan(id: number) {
    return this.prismaService.plans.findFirst({
      where: {
        id,
      },
      include: {
        serviceProvider: true,
        country: true,
      },
    });
  }
  async getAllPlans(options: PlansFilter): Promise<PlanWithJPY[]> {
    const whereParams: {
      id?: number;
      country?: object;
      enabled: boolean;
      topupEnabled?: boolean;
      packageType?: DATA_PACKAGE_TYPE;
      network?:
        | { networkGeneration?: any }
        | { OR: { networkGeneration?: any }[] };
      validityDays?: number;
    } = {
      enabled: true,
    };

    const serviceProviderFilters = { ...(options.serviceProvider || {}) };
    if (options.type === 'TOPUP') {
      whereParams.topupEnabled = true;

      // change options for LGU plus unlimited
      if (
        options.packageType === DATA_PACKAGE_TYPE.PER_DAY &&
        options.country === 'korea'
      ) {
        whereParams.packageType = 'PER_DAY';
        serviceProviderFilters.name = ESIM_PROVIDER.LGUPLUS;
      } else {
        whereParams.packageType = 'FIXED_DAY';
        serviceProviderFilters.name = ESIM_PROVIDER.UROCOMM;
      }
    }

    if (options.id) {
      whereParams.id = +options.id;
    }

    if (options.generation) {
      // For third party sources like esimDB, 4G should return KDDI plans (5G) too
      if (options.isForThirdPartySources && options.generation.includes('4G')) {
        whereParams.network = {
          OR: [
            { networkGeneration: { contains: '4G' } },
            { networkGeneration: { contains: '5G' } },
          ],
        };
      } else {
        if (
          !whereParams.network ||
          'networkGeneration' in whereParams.network
        ) {
          whereParams.network = {
            networkGeneration: {
              contains: options.generation,
            },
          };
        }
      }
    }

    whereParams.country = { enabled: true };
    if (options.country) {
      whereParams.country['name'] = options.country;
    }

    if (options.validityDays) {
      whereParams.validityDays = +options.validityDays;
    }

    const where: any = {
      ...whereParams,
      serviceProvider: {
        enabled: options.provider ? undefined : true,
        name: options.provider || undefined,
        ...(serviceProviderFilters || {}),
      },
      planId: {
        not: !options.region
          ? {
              startsWith: 'SEA_',
            }
          : undefined,
        startsWith: options.region || undefined,
      },
    };

    const include = {
      network: true,
      country: {
        select: { name: true, enabled: true, subCountries: true, code: true },
      },
      serviceProvider: {
        select: { name: true, enabled: true },
      },
      plans_prices: options.requestOriginServiceName
        ? {
            where: {
              service: {
                in: [
                  options.requestOriginServiceName || '',
                  options.fallbackRequestOriginServiceName || '',
                ],
              },
              // enabled: true,
            },
          }
        : false,
    };

    const plans = await this.prismaService.plans.findMany({
      where,
      orderBy: {
        validityDays: 'asc',
      },
      include,
    });

    let nextPlans = plans as unknown as plans;
    //@ts-expect-error
    nextPlans = plans
      .map((plan) => {
        let servicePrice = options.requestOriginServiceName
          ? plan.plans_prices.find(
              (item) => item.service === options.requestOriginServiceName,
            ) || plan.plans_prices?.[0]
          : plan.plans_prices?.[0];

        servicePrice = !servicePrice?.enabled ? null : servicePrice;
        if (plan.plans_prices?.[0] && !plan.plans_prices?.[0]?.enabled) {
          this.logger.log(`Accessing disabled plan ${plan.id}`);
          return null;
        }
        if (servicePrice && servicePrice?.priceEnabled === false) {
          this.logger.log(`Accessing disabled plan price ${plan.id}`);
          return plan;
        }
        const prices = {
          //@ts-expect-error
          JPY: servicePrice?.priceJPY || plan?.prices?.JPY,
          //@ts-expect-error
          USD: servicePrice?.price || plan?.prices?.USD,
        };

        return {
          ...plan,
          defaultCurrency:
            (prices?.[servicePrice?.defaultCurrency]
              ? servicePrice?.defaultCurrency
              : plan.defaultCurrency) || 'USD',
          price: servicePrice?.price || plan?.price,
          plans_prices: null,
          prices: {
            ...((plan.prices as object) || {}),
            ...prices,
          },
        };
      })
      .filter(Boolean);

    if (options.region && !plans.length)
      return this.getAllPlans({ ...options, region: undefined });

    //@ts-expect-error
    nextPlans = await this.addExchangeRates(nextPlans, {
      includeProvisionPrice: options.includeProvisionPrice,
    });

    if (!options.featured) {
      //@ts-expect-error
      return sortBy(nextPlans, ['validityDays', 'price']);
    }
    //@ts-expect-error
    return Object.values(groupBy(nextPlans, 'dataId')).map((items) => {
      //@ts-ignore
      return items.reduce((prev, cur) => {
        if (!prev.price) return cur;
        //@ts-expect-error
        if (prev.price > cur.price) {
          return cur;
        }
        return prev;
      }, {} as PlanWithJPY);
    });
  }

  async getPlansForExternalSource(options: PlansFilter) {
    const internalPlans = await this.getAllPlans(options);
    const normalizedPlans = internalPlans.map((plan) => ({
      ...plan,
      price: plan.xe?.['USD'] !== plan.price ? plan.xe?.['USD'] : plan.price,
    }));

    return this.includeFreePlan(normalizedPlans, options);
  }

  async addExchangeRates(
    plans: plans[],
    options?: {
      includeProvisionPrice?: boolean;
      baseCurrency?: string;
    },
  ) {
    const nextPlans = await Promise.all(
      plans.map(async ({ provision_price, ...plan }) => {
        const baseCurrency =
          options?.baseCurrency ||
          (plan.prices &&
            plan.prices[plan.defaultCurrency] &&
            plan.defaultCurrency) ||
          'USD';

        const xe = await this.handleCurrency({
          plan: { ...plan, provision_price },
          baseCurrency: baseCurrency,
        });

        return {
          ...plan,
          provision_price: options?.includeProvisionPrice
            ? provision_price
            : NaN,
          xe,
        };
      }),
    );
    return nextPlans;
  }

  private async handleCurrency({
    plan,
    baseCurrency,
  }: {
    plan: plans;
    baseCurrency;
  }) {
    const xe = {};
    const currencies = [
      'JPY',
      'USD',
      'KRW',
      'GBP',
      'EUR',
      'CNY',
      'TWD',
      'HKD',
      'CAD',
      'AUD',
    ];

    let nextbasecurrency = baseCurrency;
    for (const compareCurrency of currencies) {
      let price = (plan.prices && plan.prices[nextbasecurrency]) || 0;
      if (!price) {
        nextbasecurrency = 'USD';
        price = plan.price;
      }
      const xeRate = await this.xe.getRateOfCurrency('1', {
        baseCurrency: nextbasecurrency,
      });

      if (compareCurrency === baseCurrency) {
        xe[compareCurrency] = price;
      } else if (compareCurrency === 'JPY') {
        const roundBy = 10;
        xe[compareCurrency] =
          Math.ceil(Number(price * xeRate.rates[compareCurrency]) / roundBy) *
          roundBy;
      } else {
        xe[compareCurrency] = +Number(
          price * xeRate.rates[compareCurrency],
        ).toFixed(2);
      }
    }
    return xe;
  }

  getPlansByOptionId(optionId: string[]) {
    return this.prismaService.plans.findMany({
      where: {
        planId: {
          in: optionId,
        },
      },
    });
  }

  getPlanByOptionId(planId: string) {
    return this.prismaService.plans.findFirst({
      where: {
        planId,
      },
    });
  }

  async getFeaturedPlans(country: boolean): Promise<plans[]> {
    const regions = ['worldwide', 'asia', 'europe'];

    const rawQuery: any = Prisma.sql`
      SELECT distinct c.name AS country, c.subCountries as subCountries, c.code as countryCode,  p.*
      FROM plans p
      JOIN (
        SELECT min(price) AS min_price, countryId
        FROM plans
        where plans.enabled = true
        and (plans.packageType="PER_DAY" or plans.packageType="FIXED_DAY")
        and ( plans.validityDays = 30  )
        and ( plans.dataId = "500 MB"    )
        GROUP BY countryId
      ) AS min_plans ON p.price = min_plans.min_price AND p.countryId = min_plans.countryId
      JOIN countries c ON p.countryId = c.id
      WHERE c.enabled = true
      and p.enabled = true
      and c.name ${country ? Prisma.sql`Not` : Prisma.sql``} 
      IN (${Prisma.join(regions)})
      ORDER BY c.name ASC
  `;

    let plans = await this.prismaService.$queryRaw(Prisma.sql`${rawQuery}`);
    //@ts-expect-error
    plans = plans.map((plan) => ({
      ...plan,
      originalPrice: plan.price,
      price: +Number(plan.price / plan.validityDays).toFixed(2),
      subCountries: undefined,
      numOfCountries: plan.subCountries
        ? JSON.parse(plan.subCountries)?.length
        : undefined,
    }));
    const nextPlans = await this.addExchangeRates(plans as plans[]);
    return uniqBy(nextPlans, (item) => item.countryId);
  }

  async createCountry({
    countryName,
    subCountries,
    enabled,
    code,
  }: {
    countryName: string;
    subCountries?: string;
    enabled?: boolean;
    code?: string;
  }) {
    const countryModel = await this.prismaService.countries.upsert({
      where: {
        name: countryName,
      },
      create: {
        name: countryName,
        enabled,
        subCountries,
        code,
      },
      update: {
        name: countryName,
        enabled,
        subCountries,
        code,
      },
    });
    return countryModel;
  }
  async seedCountryPlans(
    {
      countryName,
      subCountries,
      enabled,
    }: {
      countryName: string;
      subCountries?: string;
      enabled?: boolean;
    },
    plans: any[],
  ) {
    const countryModel = await this.createCountry({
      countryName,
      subCountries,
      enabled,
    });
    for (const plan of plans) {
      try {
        const planseed = await this.seed(countryModel, plan);
      } catch (err) {
        Sentry.captureException(err);
        this.logger.log(err.message);
        throw err;
      }
    }
    this.logger.log(`Seeded plans of country ${countryName}`);
  }

  async seed(countryModel: countries, plan) {
    const serviceProviders =
      await this.prismaService.service_providers.findMany({});
    let {
      country,
      provisionPrice,
      price,
      optionId,
      planProvider,
      name,
      dataId,
      dataVolume,
      dataUnit,
      validityDays,
      validityDaysCycle,
      packageType,
      networkProvider,
      networkType,
      networkQos,
      networkApn,
      enabled,
      serviceProviderName: serviceProviderId,
      prices,
      defaultCurrency,
      services,
      topupEnabled,
    } = deserializeFromDotNotation(plan) as any;

    price = Number(price);
    provisionPrice = Number(provisionPrice);
    validityDays = Number(validityDays);
    enabled = enabled == 1 ? true : false;

    let provider = serviceProviders.find(
      (item) => item.name === serviceProviderId,
    );
    if (!provider && serviceProviderId) {
      provider = await this.prismaService.service_providers.create({
        data: {
          name: serviceProviderId,
          enabled: true,
        },
      });
      serviceProviders.push(provider);
    }
    try {
      let parsedNetworkQos = networkQos
        ?.replaceAll('(Single day)', '')
        ?.replaceAll('(total)', '')
        ?.replaceAll('kbps', 'KB');
      parsedNetworkQos = parsedNetworkQos.trim() + '/s';

      const networkModel = await this.prismaService.networks.upsert({
        where: {
          code: `${networkProvider}-${networkType}-${networkQos}-${planProvider}`,
        },
        create: {
          code: `${networkProvider}-${networkType}-${networkQos}-${planProvider}`,
          apn: networkApn,
          qos: parsedNetworkQos,
          type: planProvider,
          name: networkProvider,
          countryId: countryModel.id,
          networkGeneration: networkType,
        },
        update: {
          apn: networkApn,
          qos: parsedNetworkQos,
          type: planProvider,
          name: networkProvider,
          countryId: countryModel.id,
          networkGeneration: networkType,
        },
      });
      const nextPrices =
        mapValues(prices || {}, (value) => {
          // Attempt to convert the value to a number
          const num = toNumber(value);
          return isNaN(num) ? value : num; // Keep the value if conversion fails
        }) || {};
      const plan = await this.prismaService.plans.upsert({
        where: {
          planId: optionId + '',
        },
        create: {
          topupEnabled: topupEnabled === 'TRUE',
          name,
          planId: optionId + '',
          price: price as unknown as number,
          dataId,
          dataUnit,
          dataVolume: dataVolume + '',
          validityDaysCycle,
          description: '',
          provision_price: provisionPrice as unknown as number,
          validityDays: validityDays as unknown as number,
          metadata: {},
          networkId: networkModel.id,
          packageType,
          countryId: countryModel.id,
          enabled,
          defaultCurrency: defaultCurrency || 'USD',
          prices: nextPrices || {},
          serviceProviderId: provider?.id,
        },
        update: {
          topupEnabled: topupEnabled === 'TRUE',
          countryId: countryModel.id,
          name,
          planId: optionId + '',
          price: price as unknown as number,
          dataId,
          dataUnit,
          dataVolume: dataVolume + '',
          validityDaysCycle,
          description: '',
          provision_price: provisionPrice as unknown as number,
          validityDays: validityDays as unknown as number,
          metadata: {},
          networkId: networkModel.id,
          packageType,
          enabled,
          serviceProviderId: provider?.id,
          defaultCurrency: defaultCurrency || 'USD',
          prices: nextPrices || {},
        },
      });
      const serviceKeys = Object.keys(services || {}).filter(Boolean);
      for (const key of serviceKeys) {
        const plansUUID = `${plan.id}-${key}`;
        if (!services[key]) continue;
        const item = JSON.parse(services[key]);
        const data = {
          plans_uuid: plansUUID,
          price: item.price,
          priceEnabled: item.priceEnabled,
          priceJPY: item.priceJPY,
          service: key,
          defaultCurrency: item.defaultCurrency,
          enabled: item.enabled,
          planId: plan.id,
        };
        const planPrices = await this.prismaService.plans_prices.upsert({
          where: {
            plans_uuid: plansUUID,
          },
          create: data,
          update: data,
        });
        this.logger.log(
          `Plan prices created for plan ${plan.id} -> PlanPrices ${planPrices.id}`,
        );
      }
      this.logger.log('inserting done', optionId);
    } catch (e) {
      this.logger.error(e);
      this.logger.log('couldnt insert,', { optionId }, e.message);
      throw e;
    }
  }

  async getPlanByDataSpecification(
    dataVolume: string,
    dataUnit: DataUnit,
  ): Promise<PlanWithJPY | null> {
    const plan = await this.prismaService.plans.findFirst({
      where: {
        dataVolume,
        dataUnit,
      },
      include: {
        serviceProvider: true,
        country: true,
        network: true,
      },
    });

    if (plan) {
      const ratedPlan = await this.addExchangeRates([plan]);
      return ratedPlan[0];
    }

    return null;
  }

  async checkIsSpecialAPNPlan(plan: {
    dataVolume: string;
    dataUnit: DataUnit;
    validityDays: number;
    name: string;
    packageType: 'FIXED_DAY' | 'PER_DAY' | string;
  }): Promise<boolean> {
    const cacheKey = `specialAPNPlan-${plan.dataVolume}${plan.dataUnit}-${plan.validityDays}days-${plan.name}-${plan.packageType}`;

    const cachedResult = await this.cacheManager.get<boolean>(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }

    const isSpecialPlan = await this.evaluateSpecialPlanConditions(plan);

    await this.cacheManager.set(
      cacheKey,
      isSpecialPlan,
      ONE_DAY_IN_MILLISECONDS,
    );
    return isSpecialPlan;
  }

  private async checkSpecialPlanMap(
    validityDays: number,
    dataVolume: string,
    dataUnit: DataUnit,
  ): Promise<boolean> {
    const dataId = dataVolume + dataUnit;
    return (
      (await this.prismaService.kvStore.count({
        where: {
          key: 'specialPlanMap',
          value: {
            path: `$."${validityDays}"`,
            array_contains: dataId,
          },
        },
      })) > 0
    );
  }

  private async checkSpecialUnlimitedDays(
    validityDays: number,
  ): Promise<boolean> {
    return (
      (await this.prismaService.kvStore.count({
        where: {
          key: 'specialUnlimitedDays',
          value: {
            array_contains: validityDays,
          },
        },
      })) > 0
    );
  }

  private async evaluateSpecialPlanConditions(plan: {
    validityDays: number;
    name: string;
    packageType: string;
    dataVolume: string;
    dataUnit: DataUnit;
  }): Promise<boolean> {
    if (plan.name === 'unlimited') {
      const isInSpecialUnlimitedDays = await this.checkSpecialUnlimitedDays(
        plan.validityDays,
      );
      if (isInSpecialUnlimitedDays) {
        return true;
      }
    }

    if (plan.packageType !== 'FIXED_DAY') {
      return false;
    }

    return await this.checkSpecialPlanMap(
      plan.validityDays,
      plan.dataVolume,
      plan.dataUnit,
    );
  }

  freePlanMatchesFilter(freePlan: any, options: PlansFilter): boolean {
    if (
      options.generation &&
      !freePlan.network.networkGeneration.includes(options.generation)
    ) {
      return false;
    }

    if (
      options.validityDays &&
      freePlan.validityDays !== Number(options.validityDays)
    ) {
      return false;
    }

    if (options.id && freePlan.id !== Number(options.id)) {
      return false;
    }

    return true;
  }

  private async includeFreePlan(
    plans: PlanWithJPY[],
    options: PlansFilter,
  ): Promise<PlanWithJPY[]> {
    const freePlan = await this.getPlanByDataSpecification('300', 'MB');

    if (!freePlan) {
      return plans;
    }

    if (!this.freePlanMatchesFilter(freePlan, options)) {
      return plans;
    }

    const freePlanWithRates = (await this.addExchangeRates([freePlan]))[0];

    // Check if the plan is already in the results
    const planExists = plans.find((plan) => plan.id === freePlanWithRates.id);
    if (planExists) {
      return plans;
    }

    return [...plans, freePlanWithRates];
  }
}
