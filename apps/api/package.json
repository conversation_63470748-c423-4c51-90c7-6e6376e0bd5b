{"name": "api", "version": "2.49.1", "private": true, "description": "", "license": "UNLICENSED", "author": "", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "dev": "yarn run start:dev", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prisma": "prisma generate", "prisma:migrate-dev": "prisma migrate dev --name", "prisma:migrate-prod": "prisma migrate deploy", "prod": "node -r dist/src/main.js", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch", "test": "jest", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:watch": "jest --watch", "ukomi:create": "ts-node data/create-ukomi-product.ts"}, "jest": {"collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "moduleFileExtensions": ["js", "json", "ts"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1", "^config/(.*)$": "<rootDir>/config/$1"}, "rootDir": ".", "testEnvironment": "node", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}}, "dependencies": {"recharts": "3.1.0", "react-chartjs-2": "5.3.0", "chart.js": "4.5.0", "@adminjs/import-export": "3.0.0", "json-edit-react": "1.28.1", "@adminjs/logger": "5.0.1", "@adminjs/design-system": "4.1.1", "express-formidable": "^1.2.0", "express-session": "^1.18.1", "@adminjs/express": "6.1.1", "@adminjs/nestjs": "6.1.0", "@adminjs/prisma": "5.0.3", "adminjs": "^7.8.15", "connect-redis": "8.1.0", "ioredis": "5.6.1", "@axiomhq/axiom-node": "0.12.0", "@bull-board/api": "5.21.4", "@bull-board/express": "5.21.4", "@casl/ability": "6.7.1", "@logtail/node": "^0.1.15", "@logtail/winston": "^0.1.15", "@nestjs/axios": "^1.0.1", "@nestjs/bullmq": "10.2.1", "@nestjs/cache-manager": "2.2.2", "@nestjs/cli": "^9.0.0", "@nestjs/common": "9.0.0", "@nestjs/config": "2.3.1", "@nestjs/core": "9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/swagger": "^6.1.4", "@nestjs/terminus": "10.2.3", "@nestjs/testing": "^9.0.0", "@nestlab/google-recaptcha": "^3.7.0", "@prisma/client": "^4.8.0", "@react-pdf/renderer": "3.4.4", "@sentry/core": "^7.54.0", "@sentry/node": "7.112.2", "@sentry/tracing": "^7.54.0", "@types/multer": "1.4.11", "@types/qrcode": "1.5.5", "@nestjs/event-emitter": "1.4.1", "@nestjs/graphql": "12.1.1", "@nestjs/jwt": "10.0.3", "@nestjs/mapped-types": "*", "@nestjs/microservices": "9.3.12", "@nestjs/passport": "9.0.3", "@nestjs/platform-express": "9.0.0", "@nestjs/schedule": "6.0.0", "@nestjs/throttler": "4.0.0", "@willsoto/nestjs-prometheus": "6.0.1", "amazon-cognito-identity-js": "^5.2.10", "aws-sdk": "^2.1391.0", "axios": "^1.4.0", "base-64": "^1.0.0", "bcrypt": "^5.1.0", "bull-board": "2.1.3", "bullmq": "5.12.14", "cache-manager": "5.1.4", "cache-manager-redis-store": "2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "crypto-js": "^4.1.1", "csv-parse": "5.5.6", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "dotenv": "16.4.5", "express-basic-auth": "^1.2.1", "flat": "^6.0.1", "google-spreadsheet": "3.3.0", "handlebars": "^4.7.7", "hbs": "^4.2.0", "helmet": "^6.0.1", "hpp": "^0.2.3", "json-rules-engine": "6.4.2", "json2csv": "6.0.0-alpha.2", "jwks-rsa": "^2.1.5", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "nest-winston": "^1.8.0", "nestjs-i18n": "^10.2.6", "node-xlsx": "^0.21.0", "os": "0.1.2", "passport": "^0.6.0", "passport-headerapikey": "^1.2.2", "passport-jwt": "^4.0.0", "prettysize": "^2.0.0", "prisma": "^4.8.0", "prom-client": "15.1.3", "prometheus-api-metrics": "3.2.2", "qrcode": "^1.5.4", "react": "18.3.1", "react-dom": "18.3.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "shortid": "^2.2.16", "winston-slack-webhook-transport": "^1.0.0", "stripe": "^11.6.0", "tablemark": "^3.1.0", "toobusy-js": "^0.5.1", "uuid": "^9.0.0", "v8": "0.1.0", "voucher-code-generator": "^1.3.0", "winston": "^3.8.2"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/cron": "^2.0.0", "@types/crypto-js": "^4.2.1", "@types/lodash": "^4.14.191", "@types/multer": "1.4.11", "@types/passport-jwt": "^3.0.7", "@types/stripe": "^8.0.417", "class-transformer": "^0.5.1", "@nestjs/cli": "9.0.0", "@nestjs/schematics": "9.0.0", "@nestjs/testing": "9.0.0", "@types/express": "4.17.13", "@types/jest": "28.1.8", "@types/node": "16.0.0", "@types/nodemailer": "6.4.7", "@types/supertest": "2.0.11", "@types/uuid": "9.0.1", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "form-data": "4.0.0", "jest": "28.1.3", "nodemailer": "^6.9.5", "prettier": "^2.3.2", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4", "@sentry/nestjs": "9.23.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}