const axios = require('axios');
const chalk = require('chalk');

const delay = (delayInms) => {
  return new Promise((resolve) => setTimeout(resolve, delayInms));
};

const plans = [
  { planId: 4186, expectedDiscount: 20, coupon: 'KAKAKU20' },
  { planId: 4188, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 3514, expectedDiscount: 50, coupon: 'KAKAKU50' },
  { planId: 8712, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 8703, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 8135, expectedDiscount: 95, coupon: 'KAKAKU95' },
  { planId: 4912, expectedDiscount: 40, coupon: 'KAKAKU40' },
  { planId: 4914, expectedDiscount: 20, coupon: 'KAKAKU20' },
  { planId: 4229, expectedDiscount: 90, coupon: 'KAKAKU90' },
  { planId: 8849, expectedDiscount: 30, coupon: 'KAKAKU30' },
  { planId: 3813, expectedDiscount: 90, coupon: 'KAKAKU90' },
  { planId: 8173, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 8175, expectedDiscount: 55, coupon: 'KAKAKU55' },
  { planId: 4704, expectedDiscount: 90, coupon: 'KAKAKU90' },
  { planId: 8130, expectedDiscount: 95, coupon: 'KAKAKU95' },
  { planId: 9220, expectedDiscount: 55, coupon: 'KAKAKU55' },
  { planId: 9229, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 3533, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 9289, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 3552, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 9363, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 9368, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 9377, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 8354, expectedDiscount: 45, coupon: 'KAKAKU45' },
  { planId: 9407, expectedDiscount: 92, coupon: 'KAKAKU92' },
  { planId: 4398, expectedDiscount: 40, coupon: 'KAKAKU40' },
  { planId: 4400, expectedDiscount: 40, coupon: 'KAKAKU40' },
  { planId: 4428, expectedDiscount: 97, coupon: 'KAKAKU97' },
  { planId: 8623, expectedDiscount: 85, coupon: 'KAKAKU85' },
  { planId: 8625, expectedDiscount: 65, coupon: 'KAKAKU65' },
  { planId: 10772, expectedDiscount: 90, coupon: 'KAKAKU90' },
  { planId: 10856, expectedDiscount: 77, coupon: 'KAKAKU77' },
  { planId: 8148, expectedDiscount: 95, coupon: 'KAKAKU95' },
  { planId: 4882, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 4377, expectedDiscount: 95, coupon: 'KAKAKU95' },
  { planId: 9637, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 9646, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 3571, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 9706, expectedDiscount: 60, coupon: 'KAKAKU60' },
  { planId: 8122, expectedDiscount: 50, coupon: 'KAKAKU50' },
  { planId: 4189, expectedDiscount: 70, coupon: 'KAKAKU70' },
  { planId: 10833, expectedDiscount: 97, coupon: 'KAKAKU97' },
  { planId: 4942, expectedDiscount: 0, coupon: '' },
  { planId: 3793, expectedDiscount: 91, coupon: 'KAKAKU91' },
  { planId: 9407, expectedDiscount: 92, coupon: 'KAKAKU92' },
  { planId: 8559, expectedDiscount: 92, coupon: 'KAKAKU92' },
];

const API_URL =
  'https://esim.gmobile.biz/api/v1/esim/order/quotation-new?requestOriginServiceName=';

async function testPlan({ planId, expectedDiscount, coupon }) {
  const payload = {
    recaptha: '',
    couponId: coupon,
    products: [{ optionId: planId.toString() }],
  };

  try {
    const response = await axios.post(`${API_URL}${coupon}`, payload);
    const result = response.data?.data;
    const actualDiscount = result?.coupon?.discount ?? 0;

    if (result.coupon?.code === coupon && actualDiscount === expectedDiscount) {
      console.log(
        chalk.green(
          `✅ Plan ${planId} | Coupon ${coupon} | Discount ${actualDiscount}%`,
        ),
      );
    } else {
      console.log(
        chalk.yellow(
          `⚠️ Plan ${planId} | Coupon ${coupon} | Expected ${expectedDiscount}% but got ${actualDiscount}%`,
        ),
      );
    }
    await delay(1000);
  } catch (error) {
    console.log(
      chalk.red(
        `❌ Plan ${planId} | Coupon ${coupon} | Error: ${
          error.response?.data?.message || error.message
        }`,
      ),
    );
  }
}

(async () => {
  for (const plan of plans) {
    await testPlan(plan);
  }
})();
