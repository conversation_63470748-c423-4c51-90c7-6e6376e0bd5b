# -------------------------------
# Builder stage
# -------------------------------
FROM node:20-alpine3.18 AS builder

# Install build tools and dependencies
RUN apk update && apk add --no-cache libc6-compat build-base jpeg-dev

WORKDIR /app

# Install Turbo globally
RUN yarn global add turbo

# Copy source code
COPY . .

# Prune dependencies for Docker
RUN turbo prune --scope=api --docker

# -------------------------------
# Installer stage
# -------------------------------
FROM node:20-alpine3.18 AS installer

# Install build tools and dependencies
RUN apk update && apk add --no-cache libc6-compat build-base jpeg-dev

WORKDIR /app

# Copy necessary files
COPY .gitignore .gitignore
COPY --from=builder /app/apps/api/.env /app/apps/api/.env
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/yarn.lock ./yarn.lock

# Install dependencies
RUN yarn install

# Copy full source
COPY --from=builder /app/out/full/ .
COPY turbo.json turbo.json

# Run Prisma and build API
RUN yarn run prisma
RUN yarn turbo run build --filter=api...

# -------------------------------
# Runner stage
# -------------------------------
FROM node:20-alpine3.18 AS runner

# PM2 and app setup
WORKDIR /app
RUN apk add --no-cache libc6-compat

# Create user and group
RUN addgroup --system --gid 1001 esim-api && \
    adduser --system --uid 1001 esim-api

# Install PM2 globally
RUN npm install -g pm2

# Copy built app and config
COPY --from=installer /app ./
COPY ecosystem.config.js ecosystem.config.js
COPY --from=installer /app/apps/api/.env ./apps/api/.env
COPY --chown=esim-api:esim-api entrypoint.sh entrypoint.sh

# Make entrypoint executable
RUN chmod +x ./entrypoint.sh

# Fix permissions for esim-api user
RUN chown -R esim-api:esim-api /app

# Use non-root user
USER esim-api

# Expose necessary ports
EXPOSE 80 443 43554 9229

# Environment variable (example)
ENV SENTRY_AUTH_TOKEN=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
# Start the app using PM2 runtime
#CMD ["pm2-runtime", "start",  "apps/api/dist/src/main.js" , "--node-args='--inspect'"]
ENTRYPOINT ["./entrypoint.sh"]
CMD ["pm2-runtime", "ecosystem.config.js", "--only=global-esim-api"]
