AdminJS.UserComponents = {}
import JsonEditor from '../src/adminjs/components/JsonEditor'
AdminJS.UserComponents.JsonEditor = JsonEditor
import Dashboard from '../src/adminjs/components/Dashboard'
AdminJS.UserComponents.Dashboard = Dashboard
import Settings from '../src/adminjs/components/Settings'
AdminJS.UserComponents.Settings = Settings
import ResendOrderEmailAction from '../src/adminjs/components/ResendOrderEmailAction'
AdminJS.UserComponents.ResendOrderEmailAction = ResendOrderEmailAction
import UserInfoCell from '../src/adminjs/components/UserInfoCell'
AdminJS.UserComponents.UserInfoCell = UserInfoCell
import CouponOrdersPage from '../src/adminjs/components/CouponOrdersPage'
AdminJS.UserComponents.CouponOrdersPage = CouponOrdersPage
import RecordDifference from '../../../node_modules/@adminjs/logger/lib/components/RecordDifference'
AdminJS.UserComponents.RecordDifference = RecordDifference
import RecordLink from '../../../node_modules/@adminjs/logger/lib/components/RecordLink'
AdminJS.UserComponents.RecordLink = RecordLink
import ImportComponent from '../../../node_modules/@adminjs/import-export/lib/components/ImportComponent'
AdminJS.UserComponents.ImportComponent = ImportComponent
import ExportComponent from '../../../node_modules/@adminjs/import-export/lib/components/ExportComponent'
AdminJS.UserComponents.ExportComponent = ExportComponent