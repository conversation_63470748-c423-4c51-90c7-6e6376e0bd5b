import { PlanProvider, PrismaClient } from '@prisma/client';
//@ts-ignore
import * as lguPlus from '../data/master-formatted-plans-lgu.json';

const plans = [].concat(lguPlus);

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

interface PlanData {
  country: 'Azerbaijan';
  provisionPrice: number;
  price: number;
  optionId: string;
  planProvider: PlanProvider;
  name: string;
  dataVolume: string;
  dataUnit: 'GB';
  validityDays: string;
  validityDaysCycle: 'days';
  packageType: 'PER_DAY';
  dataId: string;
  networkProvider: string;
  networkType: string;
  networkQos: string;
  netowkrApn: string;
}

export const enabledPlans = [
  'usa',
  'europe',
  'new zealand',
  'guam',
  'canada',
  'asia',
  'uk',
  'france',
  'italy',
  'singapore',
  'philippines',
  'australia',
  'worldwide',
  'vietnam',
  'korea',
  'thailand',
  'germany',
  'japan',
];
const alias = {
  '34 countries in europe': {
    name: 'europe',
    subCountries: [
      { name: 'Greece', shortName: 'GR' },
      { name: 'Netherlands', shortName: 'NL' },
      { name: 'Belgium', shortName: 'BE' },
      { name: 'France', shortName: 'FR' },
      { name: 'Spain', shortName: 'ES' },
      { name: 'Hungary', shortName: 'HU' },
      { name: 'Bulgaria', shortName: 'BG' },
      { name: 'Slovenian', shortName: 'SI' },
      { name: 'Gibraltar', shortName: 'GI' },
      { name: 'Luxembourg', shortName: 'LU' },
      { name: 'Ireland', shortName: 'IE' },
      { name: 'Iceland', shortName: 'IS' },
      { name: 'Malta', shortName: 'MT' },
      { name: 'Cyprus', shortName: 'CY' },
      { name: 'Finland', shortName: 'FI' },
      { name: 'Lithuania', shortName: 'LT' },
      { name: 'Latvia', shortName: 'LV' },
      { name: 'Ukraine', shortName: 'UA' },
      { name: 'Poland', shortName: 'PL' },
      { name: 'Germany', shortName: 'DE' },
      { name: 'Slovakia', shortName: 'SK' },
      { name: 'Austria', shortName: 'AT' },
      { name: 'UK', shortName: 'UK' },
      { name: 'Denmark', shortName: 'DK' },
      { name: 'Sweden', shortName: 'SE' },
      { name: 'Norway', shortName: 'NO' },
      { name: 'Bosnia', shortName: 'BA' },
      { name: 'Croatia', shortName: 'HR' },
      { name: 'Serbia', shortName: 'RS' },
      { name: 'Italy', shortName: 'IT' },
      { name: 'Romania', shortName: 'RO' },
      { name: 'Czech Republic', shortName: 'CZ' },
    ],
  },
  '13 asian countries': {
    name: 'asia',
    subCountries: [
      { name: 'Taiwan', shortName: 'TW' },
      { name: 'Laos', shortName: 'LA' },
      { name: 'Macau', shortName: 'MO' },
      { name: 'Malaysia', shortName: 'MY' },
      { name: 'Singapore', shortName: 'SG' },
      { name: 'Indonesia', shortName: 'ID' },
      { name: 'Japan', shortName: 'JP' },
      { name: 'China', shortName: 'CN' },
      { name: 'Cambodia', shortName: 'KH' },
      { name: 'Thailand', shortName: 'TH' },
      { name: 'Philippines', shortName: 'PH' },
      { name: 'Hong Kong', shortName: 'HK' },
    ],
  },
  worldwide: {
    name: 'worldwide',
    subCountries: [
      { name: 'Ghana', shortName: 'GH' },
      { name: 'Guyana', shortName: 'GY' },
      { name: 'Guatemala', shortName: 'GT' },
      { name: 'Guam', shortName: 'GU' },
      { name: 'Grenada', shortName: 'GD' },
      { name: 'Greece', shortName: 'GR' },
      { name: 'Republic of South Africa', shortName: 'ZA' },
      { name: 'Netherlands', shortName: 'NL' },
      { name: 'Nepal', shortName: 'NP' },
      { name: 'Norway', shortName: 'NO' },
      { name: 'New Zealand', shortName: 'NZ' },
      { name: 'Nicaragua', shortName: 'NI' },
      { name: 'Taiwan', shortName: 'TW' },
      { name: 'Korea', shortName: 'KR' },
      { name: 'Denmark', shortName: 'DK' },
      { name: 'Dominican Republic', shortName: 'DO' },
      { name: 'Dominica', shortName: 'DM' },
      { name: 'Germany', shortName: 'DE' },
      { name: 'Laos', shortName: 'LA' },
      { name: 'Liberia', shortName: 'LR' },
      { name: 'Latvia', shortName: 'LV' },
      { name: 'Russia', shortName: 'RU' },
      { name: 'Romania', shortName: 'RO' },
      { name: 'Luxembourg', shortName: 'LU' },
      { name: 'Rwanda', shortName: 'RW' },
      { name: 'Lithuania', shortName: 'LT' },
      { name: 'Madagascar', shortName: 'MG' },
      { name: 'Martinique', shortName: 'MQ' },
      { name: 'Macau', shortName: 'MO' },
      { name: 'Malawi', shortName: 'MW' },
      { name: 'Malaysia', shortName: 'MY' },
      { name: 'Mexico', shortName: 'MX' },
      { name: 'Morocco', shortName: 'MA' },
      { name: 'Mauritius', shortName: 'MU' },
      { name: 'Mozambique', shortName: 'MZ' },
      { name: 'Montenegro', shortName: 'ME' },
      { name: 'Moldova', shortName: 'MD' },
      { name: 'Malta', shortName: 'MT' },
      { name: 'Mongolia', shortName: 'MN' },
      { name: 'USA', shortName: 'US' },
      { name: 'Vanuatu', shortName: 'VU' },
      { name: 'Barbados', shortName: 'BB' },
      { name: 'Bangladesh', shortName: 'BD' },
      { name: 'Bermuda Shorts', shortName: 'BM' },
      { name: 'Vietnam', shortName: 'VN' },
      { name: 'Belgium', shortName: 'BE' },
      { name: 'Belarus', shortName: 'BY' },
      { name: 'Bosnia', shortName: 'BA' },
      { name: 'Bulgaria', shortName: 'BG' },
      { name: 'Brazil', shortName: 'BR' },
      { name: 'Brunei', shortName: 'BN' },
      { name: 'Saudi Arabia', shortName: 'SA' },
      { name: 'Serbia', shortName: 'RS' },
      { name: 'Seychelles', shortName: 'SC' },
      { name: 'Saint Lucia', shortName: 'LC' },
      { name: 'Method', shortName: 'MT' },
      { name: 'Sri Lanka', shortName: 'LK' },
      { name: 'Swaziland', shortName: 'SZ' },
      { name: 'Sweden', shortName: 'SE' },
      { name: 'Swiss', shortName: 'CH' },
      { name: 'Spain', shortName: 'ES' },
      { name: 'Slovakia', shortName: 'SK' },
      { name: 'Slovenian', shortName: 'SI' },
      { name: 'Sierra Leone', shortName: 'SL' },
      { name: 'Singapore', shortName: 'SG' },
      { name: 'United Arab Emirates', shortName: 'AE' },
      { name: 'Aruba', shortName: 'AW' },
      { name: 'Armenia', shortName: 'AM' },
      { name: 'Argentina', shortName: 'AR' },
      { name: 'Iceland', shortName: 'IS' },
      { name: 'Haiti', shortName: 'HT' },
      { name: 'Ireland', shortName: 'IE' },
      { name: 'Azerbaijan', shortName: 'AZ' },
      { name: 'Albania', shortName: 'AL' },
      { name: 'Algeria', shortName: 'DZ' },
      { name: 'Antigua and Barbuda', shortName: 'AG' },
      { name: 'Anguilla', shortName: 'AI' },
      { name: 'Estonia', shortName: 'EE' },
      { name: 'Ecuador', shortName: 'EC' },
      { name: 'El Salvador', shortName: 'SV' },
      { name: 'UK', shortName: 'GB' },
      { name: 'British Virgin Islands', shortName: 'VG' },
      { name: 'Yemen', shortName: 'YE' },
      { name: 'Oman', shortName: 'OM' },
      { name: 'Australia', shortName: 'AU' },
      { name: 'Austria', shortName: 'AT' },
      { name: 'Honduras', shortName: 'HN' },
      { name: 'Jordan', shortName: 'JO' },
      { name: 'Uganda', shortName: 'UG' },
      { name: 'Uruguay', shortName: 'UY' },
      { name: 'Uzbekistan', shortName: 'UZ' },
      { name: 'Ukraine', shortName: 'UA' },
      { name: 'Iran', shortName: 'IR' },
      { name: 'Israel', shortName: 'IL' },
      { name: 'Egypt', shortName: 'EG' },
      { name: 'Italy', shortName: 'IT' },
      { name: 'India', shortName: 'IN' },
      { name: 'Indonesia', shortName: 'ID' },
      { name: 'Japan', shortName: 'JP' },
      { name: 'Jamaica', shortName: 'JM' },
      { name: 'Georgia', shortName: 'GE' },
      { name: 'Gibraltar', shortName: 'GI' },
      { name: 'Czech Republic', shortName: 'CZ' },
      { name: 'Chile', shortName: 'CL' },
      { name: 'Cameroon', shortName: 'CM' },
      { name: 'Kazakhstan', shortName: 'KZ' },
      { name: 'Catarrh', shortName: 'CR' },
      { name: 'Cambodia', shortName: 'KH' },
      { name: 'Canada', shortName: 'CA' },
      { name: 'Kenya', shortName: 'KE' },
      { name: 'Cayman Islands', shortName: 'KY' },
      { name: 'Costa Rica', shortName: 'CR' },
      { name: 'Ivory Coast', shortName: 'CI' },
      { name: 'Colombia', shortName: 'CO' },
      { name: 'Democratic Republic of Congo', shortName: 'CD' },
      { name: 'Kuwait', shortName: 'KW' },
      { name: 'Croatia', shortName: 'HR' },
      { name: 'Kyrgyzstan', shortName: 'KG' },
      { name: 'Cyprus', shortName: 'CY' },
      { name: 'Tajikistan', shortName: 'TJ' },
      { name: 'Tanzania', shortName: 'TZ' },
      { name: 'Thailand', shortName: 'TH' },
      { name: 'Made in Turks and Caicos', shortName: 'TC' },
      { name: 'Türkiye (Turkiye)', shortName: 'TR' },
      { name: 'Tonga', shortName: 'TO' },
      { name: 'Tunisia', shortName: 'TN' },
      { name: 'Trinidad and Tobago', shortName: 'TT' },
      { name: 'Panama', shortName: 'PA' },
      { name: 'Paraguay', shortName: 'PY' },
      { name: 'Pakistan', shortName: 'PK' },
      { name: 'Papua New Guinea', shortName: 'PG' },
      { name: 'Faroe Islands', shortName: 'FO' },
      { name: 'Peru', shortName: 'PE' },
      { name: 'Portugal', shortName: 'PT' },
      { name: 'Poland', shortName: 'PL' },
      { name: 'France', shortName: 'FR' },
      { name: 'French Guiana', shortName: 'GF' },
      { name: 'Sebum', shortName: 'SB' },
      { name: 'Finland', shortName: 'FI' },
      { name: 'Philippines', shortName: 'PH' },
      { name: 'Hungary', shortName: 'HU' },
      { name: 'Australia/New Zealand', shortName: 'AN' },
      { name: 'Hong Kong', shortName: 'HK' },
    ],
  },
};
async function makePlansUnlimited() {
  await prisma.plans.updateMany({
    where: {
      dataId: {
        in: ['3GB'],
      },
      packageType: 'PER_DAY',
    },
    data: {
      name: 'unlimited',
    },
  });
}
async function main() {
  // Update rakuten metadata
  await prisma.apps.updateMany({
    where: {
      name: "ipc-rakuten",
    },
    data: {
      metadata: {
        template: "order-rakuten-layout",
        isHideSubjectOrderId: true,
      }
    }
  })
}

main()
  .then(async () => {
    // await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('Closing connection', e);
    await prisma.$disconnect();
    process.exit(1);
  });
