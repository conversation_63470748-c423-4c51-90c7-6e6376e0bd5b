// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum PAYMENT_STATUS {
  SUCCESS
  PENDING
  FAILED
  REFUNDED
  PAYMENT_SUCCESS_ESIM_FAILED
  PAYMENT_SUCCESS_ESIM_PENDING
  THIRD_PARTY_PURCHASE
  PARENT_ORDER
  INVOICE_PAYMENT_PENDING
  INVOICE_PAYMENT_SUCCESS
}

enum EsimStockStatus {
  AVAILABLE // Stock is available and ready for use
  RESERVED // Stock is reserved or awaiting further action
  DISPATCHED // Stock has been dispatched to the user
  ACTIVATED // Stock has been activated by the user
  EXPIRED // Stock is no longer valid or has expired
}

enum USERPOOL {
  GLOBAL
  AIRTRIP
}

enum INTENT_TYPE {
  TOPUP
  SUBSCRIBE
}

enum IDENTITY_PROVIDER {
  IDP_FACEBOOK
  IDP_GOOGLE
  IDP_MANUAL
  IDP_APPLE
}

enum DATA_PACKAGE_TYPE {
  PER_DAY
  FIXED_DAY
}

enum PlanProvider {
  ROAMING
  LOCAL
}

enum DataUnit {
  GB
  KB
  MB
}

model plans {
  id                Int                @id @default(autoincrement())
  name              String
  planId            String             @unique
  price             Float
  dataId            String
  description       String
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @default(now()) @updatedAt
  orders            orders[]
  provision_price   Float?
  validityDays      Int?
  dataVolume        String
  dataUnit          DataUnit
  validityDaysCycle String
  metadata          Json
  networkId         Int
  network           networks           @relation(fields: [networkId], references: [id])
  enabled           Boolean?           @default(true)
  packageType       DATA_PACKAGE_TYPE
  countryId         Int
  country           countries          @relation(fields: [countryId], references: [id])
  session_carts     session_carts[]
  serviceProviderId Int?
  serviceProvider   service_providers? @relation(fields: [serviceProviderId], references: [id])
  prices            Json?
  defaultCurrency   String             @default("USD")
  topupEnabled      Boolean?           @default(false)
  esim_stocks       esim_stocks[]
  plans_prices      plans_prices[]
}

model plans_prices {
  id              Int      @id @default(autoincrement())
  service         String
  plan            plans    @relation(fields: [planId], references: [id])
  planId          Int
  price           Float
  priceJPY        Int
  defaultCurrency String   @default("USD")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now()) @updatedAt
  enabled         Boolean  @default(true) // If the plan is enabled or not
  priceEnabled    Boolean  @default(true) // If the price is enabled or not

  plans_uuid String @unique()
}

model countries {
  id           Int        @id @default(autoincrement())
  name         String     @unique
  enabled      Boolean?   @default(true)
  code         String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @default(now()) @updatedAt
  networks     networks[]
  plans        plans[]
  subCountries String?    @db.MediumText()
}

model networks {
  id                Int          @id @default(autoincrement())
  name              String       @db.MediumText()
  enabled           Boolean?     @default(true)
  code              String?      @unique
  apn               String
  qos               String
  type              PlanProvider
  networkGeneration String
  countryId         Int
  country           countries    @relation(fields: [countryId], references: [id])
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @default(now()) @updatedAt
  plans             plans[]
}

model orders {
  id            Int            @id @default(autoincrement())
  userId        Int?
  user          users?         @relation(fields: [userId], references: [id])
  response      Json
  orders        Json
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  pi_id         String         @default("")
  paymentStatus PAYMENT_STATUS
  txnId         String         @default("")
  type          INTENT_TYPE
  planId        Int?
  plan          plans?         @relation(fields: [planId], references: [id])

  stripeLatestChargeId String?
  jpyPrice             Int? // Our actual selling price after discount and insurance if applicable
  markedJPYPrice       Int? // Our selling price before discount and insurance if applicable
  orderId              String?               @unique
  orderCreatedAt       DateTime?
  downloadLink         String?
  iccid                String?
  topupId              String?
  activateCode         String?
  qrCodeImgUrl         String?               @db.MediumText()
  isActivated          Boolean?
  activateDate         DateTime?
  expireTime           DateTime?
  provisionPrice       Float?
  price                Float? // Our Selling price
  lang                 String?               @default("en")
  apps                 apps?                 @relation(fields: [appsId], references: [id])
  appsId               Int?
  orderMetaData        Json?
  order_notifications  order_notifications[]
  // this will be bookingNo 
  bookingNo            String?
  externalBookingNo    String?
  source               String?
  countBatchLGUPlus    Int?
  isRated              Boolean?              @default(false)
  affiliate            String?               @db.Text
  coupons_orders       coupons_orders[]
  insurances           insurances[]
  rates                rate[]
  currency             CURRENCY?
  exchangeRate         Float? // The exact exchange rate used at the time of conversion to the value in currency table
  finalPrice           Float? // Our actual selling price after discount and insurance if applicable based on price and exchange rate

  // Self-referential relations
  parentOrderId          Int?
  parentOrder            orders?                  @relation(name: "ParentOrder", fields: [parentOrderId], references: [id])
  childOrders            orders[]                 @relation(name: "ParentOrder") // Use the same relation name for children
  jpyExchangeUSD         Int? // how much was JPY against 1 USD.
  paymentMethod          String?                  @default("stripe")
  orders_loyalty_coupons orders_loyalty_coupons[]
}

model users {
  id                     Int                   @id @default(autoincrement())
  userId                 String                @unique
  stripeId               String
  firstName              String
  lastName               String
  email                  String
  phone_number           String?
  createdAt              DateTime              @default(now())
  updatedAt              DateTime              @default(now()) @updatedAt
  orders                 orders[]
  profileImage           String?
  defaultPaymentMethodId String?
  isVerified             Boolean?
  idpProvider            IDENTITY_PROVIDER?
  locale                 String?               @default("en")
  username               String                @unique
  apps                   apps[]
  notification_users     notification_users[]
  session_carts          session_carts[]
  order_notifications    order_notifications[]
  corporates_users       corporates_users[]
  source                 String?
  userPool               String                @default("GLOBAL") @db.VarChar(20)
  isPhoneVerified        Boolean?              @default(false)
  coupons_orders         coupons_orders[]
  referrals              referrals[]
  users_coupons          users_coupons[]
  rates                  rate[]
  users_accounts         users_accounts[]
  reminder_emails        reminder_emails[]
}

model users_accounts {
  id              Int                @id @default(autoincrement())
  user            users              @relation(fields: [usersId], references: [id])
  usersId         Int
  idpProvider     IDENTITY_PROVIDER?
  enabled         Boolean            @default(true)
  cognitoUserSub  String             @unique
  cognitoUserName String             @unique
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @default(now()) @updatedAt
}

model kvStore {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model apps {
  id             Int              @id @default(autoincrement())
  isActive       Boolean
  key            String           @unique
  secret         String           @unique @db.VarChar(500)
  name           String
  activateDate   DateTime?
  expireTime     DateTime?
  orders         orders[]
  signatureKey   String?          @unique @db.VarChar(500)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @default(now()) @updatedAt
  userId         users            @relation(fields: [usersId], references: [id])
  usersId        Int
  webhooks       Json?
  emails         Json?
  metadata       Json?
  corporate_apps corporate_apps[]
}

model notification_users {
  id            Int      @id @default(autoincrement())
  userId        Int
  user          users    @relation(fields: [userId], references: [id])
  audienceId    String
  firebaseToken String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now()) @updatedAt
  metadata      Json?
}

model service_providers {
  id        Int      @id @default(autoincrement())
  metadata  Json?
  name      String
  enabled   Boolean?
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  plans     plans[]
}

enum CART_STATUS {
  PENDING
  PICKED_FOR_FOLLOW_UP
  SUCCESS
  REMOVED
}

model session_carts {
  id        Int         @id @default(autoincrement())
  userId    Int?
  user      users?      @relation(fields: [userId], references: [id])
  sessionId String      @unique()
  planId    Int
  plan      plans?      @relation(fields: [planId], references: [id])
  status    CART_STATUS
  createdAt DateTime    @default(now())
  updatedAt DateTime    @default(now()) @updatedAt
  language  String?     @default("en")
}

model data_usage_logs {
  id              Int      @id @default(autoincrement())
  topupid         String
  logs            Json
  lastCheckedHash String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now()) @updatedAt
}

model order_notifications {
  id        Int      @id @default(autoincrement())
  user      users    @relation(fields: [usersId], references: [id])
  usersId   Int
  orderId   Int
  order     orders   @relation(fields: [orderId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  metadata  Json?
}

model corporates {
  id               Int                @id @default(autoincrement())
  name             String
  description      String?
  logo             String?
  website          String?
  contact          Json?
  code             String             @unique
  emailAddress     String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  corporates_users corporates_users[]
  corporate_apps   corporate_apps[]
}

model corporates_users {
  id          Int        @id @default(autoincrement())
  user        users      @relation(fields: [usersId], references: [id])
  usersId     Int
  corporateId Int
  corporate   corporates @relation(fields: [corporateId], references: [id])
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @default(now()) @updatedAt
  enabled     Boolean?
}

model corporate_apps {
  id          Int         @id @default(autoincrement())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @default(now()) @updatedAt
  enabled     Boolean?
  apps        apps?       @relation(fields: [appsId], references: [id])
  appsId      Int?
  corporateId Int?
  corporate   corporates? @relation(fields: [corporateId], references: [id])
}

model lgu_order_temps {
  id        Int      @id @default(autoincrement())
  rsvNo     String   @unique @db.VarChar(225)
  data      Json?
  jncoRsvNo String?  @db.VarChar(225)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("lgu_order_temps")
}

enum COUPON_DISCOUNT_TYPE {
  PERCENTAGE
  AMOUNT
}

enum CURRENCY {
  JPY
  USD
  EUR
  CAD
  GBP
  TWD
  HKD
  CNY
  KRW
  AUD
}

model coupons {
  id                    Int                     @id @default(autoincrement())
  discount              Float
  type                  COUPON_DISCOUNT_TYPE
  code                  String                  @unique
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @default(now()) @updatedAt
  validTill             DateTime?
  validFrom             DateTime
  totalUsage            Int
  usagePerPerson        Int                     @default(1)
  coupons_orders        coupons_orders[]
  coupons_constraints   coupons_constraints[]
  banners               Json?
  promote               Boolean?                @default(false)
  message_code          String?
  referrals             referrals[]
  users_coupons         users_coupons[]
  campaig_coupons       campaign_coupons[]
  currency              CURRENCY?               @default(USD) // ✅ Set a valid default value
  services              Json?
  reminder_emails       reminder_emails[]
  orders_loyalty_coupon orders_loyalty_coupons?
}

model referrals {
  id      Int     @id @default(autoincrement())
  user    users   @relation(fields: [usersId], references: [id])
  coupon  coupons @relation(fields: [couponsId], references: [id])
  enabled Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  usersId   Int
  couponsId Int
}

model constraints {
  id                  Int                   @id @default(autoincrement())
  name                String                @unique
  rule                Json
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @default(now()) @updatedAt
  coupons_constraints coupons_constraints[]
}

model coupons_constraints {
  id            Int          @id @default(autoincrement())
  couponsId     Int
  coupon        coupons      @relation(fields: [couponsId], references: [id])
  constraintsId Int?
  constraints   constraints? @relation(fields: [constraintsId], references: [id])
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @default(now()) @updatedAt
}

enum COUPON_ORDER_STATE {
  STARTED
  PROCESSING
  COMPLETED
  REFERRAL
}

model coupons_orders {
  id          Int                @id @default(autoincrement())
  order       orders?            @relation(fields: [orderId], references: [id])
  user        users?             @relation(fields: [userId], references: [userId])
  userId      String?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @default(now()) @updatedAt
  state       COUPON_ORDER_STATE
  orderId     Int?
  referenceId String
  coupon      coupons            @relation(fields: [couponsId], references: [id])
  couponsId   Int
}

model users_coupons {
  id        Int      @id @default(autoincrement())
  coupon    coupons  @relation(fields: couponId, references: id)
  enabled   Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  user      users?   @relation(fields: [userId], references: [userId])
  userId    String?
  couponId  Int
}

model insurances {
  id        Int                  @id @default(autoincrement())
  createdAt DateTime             @default(now())
  updatedAt DateTime             @default(now()) @updatedAt
  order     orders?              @relation(fields: [orderId], references: [id])
  orderId   Int
  price     Float
  jpyPrice  Int
  name      String
  charge    Float
  type      COUPON_DISCOUNT_TYPE
}

model rate {
  id        Int      @id @default(autoincrement())
  userId    Int
  orderId   Int
  rating    Int?
  comment   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  user      users    @relation(fields: [userId], references: [id])
  order     orders   @relation(fields: [orderId], references: [id])
}

model esim_stocks {
  id           Int             @id @default(autoincrement())
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @default(now()) @updatedAt
  plan         plans           @relation(fields: [planId], references: [id])
  planId       Int
  downloadLink String?
  iccid        String?         @unique
  topupId      String?         @unique
  activateCode String?         @unique
  qrCodeImgUrl String?         @db.MediumText()
  status       EsimStockStatus
  expireTime   DateTime?
  smdp         String?
}

model campaigns {
  id              Int                @id @default(autoincrement())
  name            String
  description     String? // Made optional if not always required
  coverImages     Json? // Stores multiple images (optional)
  code            String             @unique
  startDate       DateTime
  endDate         DateTime
  enabled         Boolean            @default(true)
  metadata        Json? // Additional data (optional)
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @default(now()) @updatedAt
  services        Json? // Services that the campaign is applicable to
  campaignCoupons campaign_coupons[]
}

model campaign_coupons {
  id         Int      @id @default(autoincrement())
  campaignId Int
  couponId   Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now()) @updatedAt

  campaign campaigns @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  coupon   coupons   @relation(fields: [couponId], references: [id], onDelete: Cascade)

  @@unique([campaignId, couponId]) // Prevent duplicate entries
}

enum EMAIL_TYPE {
  THREE_MONTHS
  SIX_MONTHS
}

enum EMAIL_STATUS {
  SUCCESS
  FAILED
}

model reminder_emails {
  id            Int          @id @default(autoincrement())
  userId        Int
  emailType     EMAIL_TYPE
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @default(now()) @updatedAt
  status        EMAIL_STATUS
  service       String
  retryCount    Int?         @default(0)
  reminderCount Int?
  errorMessage  String?      @db.Text
  couponId      Int

  user   users?  @relation(fields: [userId], references: [id])
  coupon coupons @relation(fields: [couponId], references: [id])

  @@unique([userId, couponId, emailType], name: "userId_couponId_emailType")
}

model orders_loyalty_coupons {
  id          Int      @id @default(autoincrement())
  orderId     Int //parent orderId associated with the orders (only one coupon per order)
  couponId    Int      @unique
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt
  loyaltyRule Json?

  order  orders  @relation(fields: [orderId], references: [id])
  coupon coupons @relation(fields: [couponId], references: [id])
}
