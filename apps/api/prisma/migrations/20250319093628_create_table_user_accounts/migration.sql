-- CreateTable
CREATE TABLE `users_accounts` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `usersId` INTEGER NOT NULL,
    `idpProvider` ENUM('IDP_FACEBOOK', 'IDP_GOOGLE', 'IDP_MANUAL', 'IDP_APPLE') NULL,
    `enabled` BOOLEAN NOT NULL DEFAULT true,
    `cognitoUserSub` VARCHAR(191) NOT NULL,
    `cognitoUserName` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `users_accounts` ADD CONSTRAINT `users_accounts_usersId_fkey` FOREIGN KEY (`usersId`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
