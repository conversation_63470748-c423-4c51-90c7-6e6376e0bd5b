-- CreateTable
CREATE TABLE `esim_stocks` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `planId` INTEGER NOT NULL,
    `downloadLink` VARCHAR(191) NULL,
    `iccid` VARCHAR(191) NULL,
    `topupId` VARCHAR(191) NULL,
    `activateCode` VARCHAR(191) NULL,
    `qrCodeImgUrl` MEDIUMTEXT NULL,
    `status` ENUM('AVAILABLE', 'RESERVED', 'DISPATCHED', 'ACTIVATED', 'EXPIRED') NOT NULL,
    `expireTime` DATETIME(3) NULL,

    UNIQUE INDEX `esim_stocks_iccid_key`(`iccid`),
    UNIQUE INDEX `esim_stocks_topupId_key`(`topupId`),
    UNIQUE INDEX `esim_stocks_activateCode_key`(`activateCode`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `esim_stocks` ADD CONSTRAINT `esim_stocks_planId_fkey` FOREIGN KEY (`planId`) REFERENCES `plans`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
