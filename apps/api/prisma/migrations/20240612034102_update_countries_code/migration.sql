-- This is an empty migration.
UPDATE countries SET code = 'TH' WHERE name = 'Thailand';
UPDATE countries SET code = 'TW' WHERE name = 'Taiwan';
UPDATE countries SET code = 'JP' WHERE name = 'Japan';
UPDATE countries SET code = 'CN' WHERE name = 'China';
UPDATE countries SET code = 'PH' WHERE name = 'Philippines';
UPDATE countries SET code = 'US' WHERE name = 'USA';
UPDATE countries SET code = 'VN' WHERE name = 'Vietnam';
UPDATE countries SET code = 'MY' WHERE name = 'Malaysia';
UPDATE countries SET code = 'SG' WHERE name = 'Singapore';
UPDATE countries SET code = 'HK' WHERE name = 'Hong Kong';
UPDATE countries SET code = 'MO' WHERE name = 'Macau';
UPDATE countries SET code = 'ID' WHERE name = 'Indonesia';
UPDATE countries SET code = 'KH' WHERE name = 'Cambodia';
UPDATE countries SET code = 'AU' WHERE name = 'Australia';
UPDATE countries SET code = 'NZ' WHERE name = 'New Zealand';
UPDATE countries SET code = 'GU' WHERE name = 'Guam';
UPDATE countries SET code = 'AE' WHERE name = 'Dubai';
UPDATE countries SET code = 'QA' WHERE name = 'Qatar';
UPDATE countries SET code = 'SA' WHERE name = 'Saudi Arabia';
UPDATE countries SET code = 'CH' WHERE name = 'Switzerland';
UPDATE countries SET code = 'SE' WHERE name = 'Sweden';
UPDATE countries SET code = 'IT' WHERE name = 'Italy';
UPDATE countries SET code = 'RU' WHERE name = 'Russia';
UPDATE countries SET code = 'NL' WHERE name = 'Netherlands';
UPDATE countries SET code = 'MX' WHERE name = 'Mexico';
UPDATE countries SET code = 'AR' WHERE name = 'Argentina';
UPDATE countries SET code = 'BR' WHERE name = 'Brazil';
UPDATE countries SET code = 'LA' WHERE name = 'Laos';
UPDATE countries SET code = 'MU' WHERE name = 'Mauritius';
UPDATE countries SET code = 'MN' WHERE name = 'Mongolia';
UPDATE countries SET code = 'NO' WHERE name = 'Norway';
UPDATE countries SET code = 'PL' WHERE name = 'Poland';
UPDATE countries SET code = 'DK' WHERE name = 'Denmark';
UPDATE countries SET code = 'GR' WHERE name = 'Greece';
UPDATE countries SET code = 'FR' WHERE name = 'France';
UPDATE countries SET code = 'ES' WHERE name = 'Spain';
UPDATE countries SET code = 'GB' WHERE name = 'UK';
UPDATE countries SET code = 'PT' WHERE name = 'Portugal';
UPDATE countries SET code = 'CA' WHERE name = 'Canada';
UPDATE countries SET code = 'KR' WHERE name = 'South Korea';
UPDATE countries SET code = 'IL' WHERE name = 'Israel';
UPDATE countries SET code = 'IN' WHERE name = 'India';
UPDATE countries SET code = 'IR' WHERE name = 'Iran';
UPDATE countries SET code = 'JO' WHERE name = 'Jordan';
UPDATE countries SET code = 'KZ' WHERE name = 'Kazakhstan';
UPDATE countries SET code = 'KE' WHERE name = 'Kenya';
UPDATE countries SET code = 'LV' WHERE name = 'Latvia';
UPDATE countries SET code = 'KW' WHERE name = 'Kuwait';
UPDATE countries SET code = 'UZ' WHERE name = 'Uzbekistan';
UPDATE countries SET code = 'UG' WHERE name = 'Uganda';
UPDATE countries SET code = 'ME' WHERE name = 'Montenegro';
UPDATE countries SET code = 'MA' WHERE name = 'Morocco';
UPDATE countries SET code = 'NP' WHERE name = 'Nepal';
UPDATE countries SET code = 'LT' WHERE name = 'Lithuania';
UPDATE countries SET code = 'EC' WHERE name = 'Ecuador';
UPDATE countries SET code = 'SV' WHERE name = 'El Salvador';
UPDATE countries SET code = 'ZA' WHERE name = 'South Africa';
UPDATE countries SET code = 'LK' WHERE name = 'Sri Lanka';
UPDATE countries SET code = 'CZ' WHERE name = 'Czech Republic';
UPDATE countries SET code = 'TR' WHERE name = 'Türkiye';
UPDATE countries SET code = 'JM' WHERE name = 'Jamaica';
UPDATE countries SET code = 'PY' WHERE name = 'Paraguay';
UPDATE countries SET code = 'AM' WHERE name = 'Armenia';
UPDATE countries SET code = 'AZ' WHERE name = 'Azerbaijan';
UPDATE countries SET code = 'BE' WHERE name = 'Belgium';
UPDATE countries SET code = 'AT' WHERE name = 'Austria';
UPDATE countries SET code = 'HR' WHERE name = 'Croatia';
UPDATE countries SET code = 'UA' WHERE name = 'Ukraine';
UPDATE countries SET code = 'SI' WHERE name = 'Slovenia';
UPDATE countries SET code = 'RO' WHERE name = 'Romania';
UPDATE countries SET code = 'SK' WHERE name = 'Slovakia';
UPDATE countries SET code = 'BA' WHERE name = 'Bosnia';
UPDATE countries SET code = 'EE' WHERE name = 'Estonia';
UPDATE countries SET code = 'LU' WHERE name = 'Luxembourg';
UPDATE countries SET code = 'IS' WHERE name = 'Iceland';
UPDATE countries SET code = 'IE' WHERE name = 'Ireland';
UPDATE countries SET code = 'MT' WHERE name = 'Malta';
UPDATE countries SET code = 'UY' WHERE name = 'Uruguay';
UPDATE countries SET code = 'CL' WHERE name = 'Chile';
UPDATE countries SET code = 'MD' WHERE name = 'Moldova';
UPDATE countries SET code = 'RS' WHERE name = 'Serbia';
UPDATE countries SET code = 'SC' WHERE name = 'Seychelles';
UPDATE countries SET code = 'AL' WHERE name = 'Albania';
UPDATE countries SET code = 'BD' WHERE name = 'Bangladesh';
UPDATE countries SET code = 'BN' WHERE name = 'Brunei';
UPDATE countries SET code = 'BG' WHERE name = 'Bulgaria';
UPDATE countries SET code = 'CM' WHERE name = 'Cameroon';
UPDATE countries SET code = 'EG' WHERE name = 'Egypt';
UPDATE countries SET code = 'GE' WHERE name = 'Georgia';
UPDATE countries SET code = 'GH' WHERE name = 'Ghana';
UPDATE countries SET code = 'HN' WHERE name = 'Honduras';
UPDATE countries SET code = 'FI' WHERE name = 'Finland';
UPDATE countries SET code = 'HU' WHERE name = 'Hungary';
UPDATE countries SET code = 'SI' WHERE name = 'Slovenia';
UPDATE countries SET code = 'ZA' WHERE name = 'South Africa';
UPDATE countries SET code = 'CH' WHERE name = 'Switzerland';
UPDATE countries SET code = 'QA' WHERE name = 'Qatar';
UPDATE countries SET code = 'DE' WHERE name = 'Germany';
UPDATE countries SET code = 'KR' WHERE name = 'korea';  -- Assuming 'Korean' refers to South Korea
UPDATE countries SET code = 'AS' WHERE name = 'Asia';          -- Asia as a continent
UPDATE countries SET code = 'WW' WHERE name = 'Worldwide';    -- Common abbreviation for worldwide
UPDATE countries SET code = 'EU' WHERE name = 'Europe';       -- Europe as a continent
