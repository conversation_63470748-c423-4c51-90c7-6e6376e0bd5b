-- CreateTable
CREATE TABLE `reminder_emails` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `emailType` ENUM('THREE_MONTHS', 'SIX_MONTHS') NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `status` ENUM('SUCCESS', 'FAILED') NOT NULL,
    `service` VARCHAR(191) NOT NULL,
    `retryCount` INTEGER NULL DEFAULT 0,
    `reminderCount` INTEGER NULL,
    `errorMessage` TEXT NULL,
    `couponId` INTEGER NOT NULL,

    UNIQUE INDEX `reminder_emails_userId_couponId_emailType_key`(`userId`, `couponId`, `emailType`),
    PRIMARY <PERSON>EY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `reminder_emails` ADD CONSTRAINT `reminder_emails_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `reminder_emails` ADD CONSTRAINT `reminder_emails_couponId_fkey` FOREIGN KEY (`couponId`) REFERENCES `coupons`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
