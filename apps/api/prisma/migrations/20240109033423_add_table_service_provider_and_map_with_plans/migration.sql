-- AlterTable
ALTER TABLE `plans` ADD COLUMN `serviceProviderId` INTEGER NULL;

-- CreateTable
CREATE TABLE `service_providers` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `metadata` JSO<PERSON> NULL,
    `name` VARCHAR(191) NOT NULL,
    `enabled` BOOLEAN NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `plans` ADD CONSTRAINT `plans_serviceProviderId_fkey` FOREIGN KEY (`serviceProviderId`) REFERENCES `service_providers`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
