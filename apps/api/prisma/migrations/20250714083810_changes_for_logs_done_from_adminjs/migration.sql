-- AlterTable
ALTER TABLE `users` ADD COLUMN `lastLogin` DATETIME(3) NULL,
    ADD COLUMN `lastPasswordChange` DATETIME(3) NULL;

-- CreateTable
CREATE TABLE `Log` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recordId` INTEGER NOT NULL,
    `recordTitle` VARCHAR(128) NULL,
    `difference` JSON NULL,
    `action` VARCHAR(128) NOT NULL,
    `resource` VARCHAR(128) NOT NULL,
    `userId` VARCHAR(128) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
