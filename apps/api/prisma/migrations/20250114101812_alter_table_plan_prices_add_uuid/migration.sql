/*
  Warnings:

  - A unique constraint covering the columns `[plans_uuid]` on the table `plans_prices` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `plans_uuid` to the `plans_prices` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `plans_prices` ADD COLUMN `plans_uuid` VARCHAR(191) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `plans_prices_plans_uuid_key` ON `plans_prices`(`plans_uuid`);
