-- CreateTable
CREATE TABLE `plans` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `planId` VARCHAR(191) NOT NULL,
    `price` DOUBLE NOT NULL,
    `dataId` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `provision_price` DOUBLE NULL,
    `validityDays` INTEGER NULL,
    `dataVolume` VARCHAR(191) NOT NULL,
    `dataUnit` ENUM('GB', 'KB', 'MB') NOT NULL,
    `validityDaysCycle` VARCHAR(191) NOT NULL,
    `metadata` JSON NOT NULL,
    `networkId` INTEGER NOT NULL,
    `enabled` BOOLEAN NULL DEFAULT true,
    `packageType` ENUM('PER_DAY', 'FIXED_DAY') NOT NULL,
    `countryId` INTEGER NOT NULL,

    UNIQUE INDEX `plans_planId_key`(`planId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `countries` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `enabled` BOOLEAN NULL DEFAULT true,
    `code` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `countries_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `networks` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `enabled` BOOLEAN NULL DEFAULT true,
    `code` VARCHAR(191) NULL,
    `apn` VARCHAR(191) NOT NULL,
    `qos` VARCHAR(191) NOT NULL,
    `type` ENUM('ROAMING', 'LOCAL') NOT NULL,
    `networkGeneration` VARCHAR(191) NOT NULL,
    `countryId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `networks_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `orders` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `response` JSON NOT NULL,
    `orders` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `pi_id` VARCHAR(191) NOT NULL DEFAULT '',
    `paymentStatus` ENUM('SUCCESS', 'PENDING', 'FAILED', 'REFUNDED', 'PAYMENT_SUCCESS_ESIM_FAILED', 'PAYMENT_SUCCESS_ESIM_PENDING') NOT NULL,
    `txnId` VARCHAR(191) NOT NULL DEFAULT '',
    `type` ENUM('TOPUP', 'SUBSCRIBE') NOT NULL,
    `planId` INTEGER NOT NULL,
    `iccid` VARCHAR(191) NULL,
    `topupId` VARCHAR(191) NULL,
    `activateCode` VARCHAR(191) NULL,
    `qrCodeImgUrl` VARCHAR(191) NULL,
    `stripeLatestChargeId` VARCHAR(191) NULL,
    `jpyPrice` INTEGER NULL,
    `orderId` VARCHAR(191) NULL,
    `orderCreatedAt` DATETIME(3) NULL,
    `downloadLink` VARCHAR(191) NULL,
    `isActivated` BOOLEAN NULL,
    `activateDate` DATETIME(3) NULL,
    `expireTime` DATETIME(3) NULL,
    `provisionPrice` DOUBLE NULL,
    `price` DOUBLE NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `users` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` VARCHAR(191) NOT NULL,
    `stripeId` VARCHAR(191) NOT NULL,
    `firstName` VARCHAR(191) NOT NULL,
    `lastName` VARCHAR(191) NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `profileImage` VARCHAR(191) NULL,
    `defaultPaymentMethodId` VARCHAR(191) NULL,
    `isVerified` BOOLEAN NULL,
    `idpProvider` ENUM('IDP_FACEBOOK', 'IDP_GOOGLE', 'IDP_MANUAL') NULL,
    `locale` VARCHAR(191) NULL DEFAULT 'en',
    `username` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `users_userId_key`(`userId`),
    UNIQUE INDEX `users_email_key`(`email`),
    UNIQUE INDEX `users_username_key`(`username`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `kvStore` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `key` VARCHAR(191) NOT NULL,
    `value` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `kvStore_key_key`(`key`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `plans` ADD CONSTRAINT `plans_networkId_fkey` FOREIGN KEY (`networkId`) REFERENCES `networks`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `plans` ADD CONSTRAINT `plans_countryId_fkey` FOREIGN KEY (`countryId`) REFERENCES `countries`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `networks` ADD CONSTRAINT `networks_countryId_fkey` FOREIGN KEY (`countryId`) REFERENCES `countries`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_planId_fkey` FOREIGN KEY (`planId`) REFERENCES `plans`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
