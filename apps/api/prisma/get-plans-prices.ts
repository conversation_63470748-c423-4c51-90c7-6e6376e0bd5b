import { PlanProvider, PrismaClient } from '@prisma/client';
// import * as usimsaPlans from '../data/master-formatted-plan.json';
//@ts-expect-error
import * as uroCommPlans from '../data/master-formatted-plan-urocomm.json';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

const processPlans = async () => {
  for (const plan of uroCommPlans) {
    try {
      console.log('<START> searchinggggg...', plan);
      const nextPlan = await prisma.plans.findFirstOrThrow({
        where: {
          price: {
            gt: 1,
          },
          //   serviceProviderId: 2369,
          packageType: plan.packageType,
          dataId: plan.dataId,
          dataUnit: plan.dataUnit,
          //   dataVolume: plan.dataVolume,
          validityDays: plan.validityDays,

          country: {
            name: plan.country,
          },
          //   network: {
          //     type: plan.planProvider,
          //   },
        },
        select: {
          dataId: true,
          dataUnit: true,
          dataVolume: true,
          validityDays: true,
          packageType: true,
          price: true,
          id: true,
          network: {
            select: {
              type: true,
            },
          },
        },
      });

      plan.price = nextPlan?.price;
      console.log('found', nextPlan, ' <END> ');
    } catch (err) {
      console.log(err.message);
    }
  }
};

// Call the async function
processPlans().then(() => {
  //   console.log(uroCommPlans);
});
