# Changelog

## [2.47.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.47.0...api-v2.47.1) (2025-07-04)


### Bug Fixes

* **loyalty-coupon-email:** fix loyalty coupon service name in template and subject ([58df299](https://github.com/InboundPlatform/global-esim/commit/58df299ebdeef8472baec0ce337cf7612745dcc5))

## [2.47.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.46.1...api-v2.47.0) (2025-07-04)


### Features

* **coupon-referral:** refactor the scheduler ([4218421](https://github.com/InboundPlatform/global-esim/commit/42184214d14fb01b9caa6092ec4c8c27ce83f181))
* **ges-267:** add review cards on GM/AT landing page ([bec5463](https://github.com/InboundPlatform/global-esim/commit/bec546336421b29f5762974118822f4decf516e1))

## [2.46.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.46.0...api-v2.46.1) (2025-07-03)


### Bug Fixes

* trigger deploy ([7eaf72f](https://github.com/InboundPlatform/global-esim/commit/7eaf72f172e741d9a0e7f71f0eb974de4524ce63))

## [2.46.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.45.3...api-v2.46.0) (2025-07-02)


### Features

* **airtrip-documentation:** change cache key for /external api to add the request origin service name and fallabck request origin service name ([721dde2](https://github.com/InboundPlatform/global-esim/commit/721dde2496d5acfde5835f79760236a92499d4c1))
* **airtrip-documentation:** filter the plans based on requestOriginServiceName i.e GLOBAL_ESIM_JAPANESE or GLOBAL_ESIM_AIRTRIP ([f7c3977](https://github.com/InboundPlatform/global-esim/commit/f7c397705ad4d2547809e96144849b79981858e3))
* **airtrip-documentation:** fix the type of reduced speed i.e number -&gt; string ([cd97274](https://github.com/InboundPlatform/global-esim/commit/cd97274cd726c152883ade9508f73e207877e2b2))
* **airtrip-documentation:** make price consistent between price and xe rate of USD ([423ed40](https://github.com/InboundPlatform/global-esim/commit/423ed40b2011ad424d3ddf6c9df5fb2552bb235b))
* **airtrip-documentation:** remove manual caching and add reducedSpeed field in /plans/external api by getting qos from network -&gt; code ([740967d](https://github.com/InboundPlatform/global-esim/commit/740967dec79e97fde55feaae8b214a97d85ba3c3))
* **airtrip-documentation:** remove the where clause in plans filter ([ca8bac5](https://github.com/InboundPlatform/global-esim/commit/ca8bac51ae9fc9f0e075da5601d02dd583f8ba5c))
* **lgu-template:** add campaign content for LGU (GM and Airtrip) black pink ([05bb20c](https://github.com/InboundPlatform/global-esim/commit/05bb20c935b6db11fff73a636b6ce687fc1e1b88))


### Bug Fixes

* flickering issue ([f854b41](https://github.com/InboundPlatform/global-esim/commit/f854b413364f8dc16b2f22963259b42df98feefa))
* **ukomi:** add debugging logs to ukomi access token request function ([76c73bd](https://github.com/InboundPlatform/global-esim/commit/76c73bd14002058c839520020e964c28710f278f))

## [2.45.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.45.2...api-v2.45.3) (2025-06-24)


### Bug Fixes

* add payment method in request ([6a27188](https://github.com/InboundPlatform/global-esim/commit/6a27188f18e6d38963879fe7dc9280db6cf4f08d))

## [2.45.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.45.1...api-v2.45.2) (2025-06-24)


### Bug Fixes

* **api:** use correct api response for expiry date of lgu plan ([e2f66e5](https://github.com/InboundPlatform/global-esim/commit/e2f66e532eaa21043c89625239f5f0c2287ed71e))

## [2.45.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.45.0...api-v2.45.1) (2025-06-23)


### Bug Fixes

* build issues ([df7c6bd](https://github.com/InboundPlatform/global-esim/commit/df7c6bd84d0636c559b5a2aef3cbd9f4186db6c4))

## [2.45.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.44.1...api-v2.45.0) (2025-06-23)


### Features

* **paypay:** add return statements to response send in paypay ([90c6936](https://github.com/InboundPlatform/global-esim/commit/90c6936ac795e1a26e10fdbb1edd51c988137ff0))

## [2.44.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.44.0...api-v2.44.1) (2025-06-20)


### Bug Fixes

* use correct prettysize import ([ed6417b](https://github.com/InboundPlatform/global-esim/commit/ed6417b6e04152bde6f3132c25f983996407eaf7))

## [2.44.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.43.0...api-v2.44.0) (2025-06-19)


### Features

* lgu topup ([c1c40d9](https://github.com/InboundPlatform/global-esim/commit/c1c40d9731c7ee971d28134d567af1e495806d4a))
* **paypay:** fix the stripe payment intent creation on paypay payment and add tracking id in pi_id of parent order id ([97cb441](https://github.com/InboundPlatform/global-esim/commit/97cb441fb7809c83d03b1d3cb12f0a59eafd7189))
* **paypay:** for paypay payment method stop creating payment intent ([13deab7](https://github.com/InboundPlatform/global-esim/commit/13deab7975a0fddae65ba934ecbd2391a8989bce))
* **paypay:** remove unrequired comment ([696aef6](https://github.com/InboundPlatform/global-esim/commit/696aef6fd35ef4185ead0b344b2db7775b04bb8b))

## [2.43.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.42.1...api-v2.43.0) (2025-06-18)


### Features

* **paypay:** add 32 characters constraint to the item_id in request data for paypay purchase request ([4f2d6d2](https://github.com/InboundPlatform/global-esim/commit/4f2d6d2c1a6896724e93dfa49c745e39da2b5b8e))
* **paypay:** add missing call to isAppEnvDev function ([f0e4e92](https://github.com/InboundPlatform/global-esim/commit/f0e4e9290468aee5e3df28fe95e923cccafc5cb9))
* **paypay:** add the error path with locale for the global esim multilang ([b101fa4](https://github.com/InboundPlatform/global-esim/commit/b101fa4c9f5942907a086aad1f48c2efb9903169))
* **paypay:** add try catch block for the paypay complete url ([d4574fa](https://github.com/InboundPlatform/global-esim/commit/d4574fa5a0aa8e55556a30a04a4cf4ed720f5cf9))
* **paypay:** add utility function to limit the length of string for request data fields ([63f5da8](https://github.com/InboundPlatform/global-esim/commit/63f5da83a4561df62ba3182db93e0ec840b86c19))
* **template-update:** add order Ids for child orders in product name of paypay purchase request data ([f8f2362](https://github.com/InboundPlatform/global-esim/commit/f8f23629cec1a279a447725f2934aa4764da3b1e))

## [2.42.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.42.0...api-v2.42.1) (2025-06-12)


### Bug Fixes

* trigger release ([070eac0](https://github.com/InboundPlatform/global-esim/commit/070eac0d03921868802021aaa59bbc2e667ae8d2))

## [2.42.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.41.2...api-v2.42.0) (2025-06-12)


### Features

* **template-update:** add gmnikkeitrendi to the check list for non async free esim dispatch ([e6a7dd4](https://github.com/InboundPlatform/global-esim/commit/e6a7dd42c761503043c2b405f71945833754329d))
* **template-update:** add hongkong, taiwan verification guide for airtrip and webjp only and remove lgu plus topup section ([10787ba](https://github.com/InboundPlatform/global-esim/commit/10787ba5a8c736e99ae84073c7834d8e4a903440))
* **template-update:** fix email send issue for the free esim campaign GM JP ([0c7439e](https://github.com/InboundPlatform/global-esim/commit/0c7439eb59ace5687353ade7f990a04b2aca7a8b))
* **template-update:** permanent fix for the email send issue for the free esim campaign GM JP ([73cbdc6](https://github.com/InboundPlatform/global-esim/commit/73cbdc6e0442d209e03aa866b39aa86d0a081aaf))
* **template-update:** remove stray debugger from dispatchFreeEsim ([0cd441d](https://github.com/InboundPlatform/global-esim/commit/0cd441dc27972369884999440c053a17e7a4da93))


### Bug Fixes

* add logger for request ([eefc635](https://github.com/InboundPlatform/global-esim/commit/eefc6356eeae47a976c6de6dd7cb35af2d92733f))
* add logger for request ([b88530c](https://github.com/InboundPlatform/global-esim/commit/b88530c25b169f9db26e8bac8b0a94305e757c7a))

## [2.41.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.41.1...api-v2.41.2) (2025-06-11)


### Bug Fixes

* free esim campaign changes, change to sync for some campaign ([b85e1f0](https://github.com/InboundPlatform/global-esim/commit/b85e1f0c3fe3c50867cd1a26cf38dab807a1c6a6))

## [2.41.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.41.0...api-v2.41.1) (2025-06-10)


### Bug Fixes

* add cryptojs back ([5d2d87f](https://github.com/InboundPlatform/global-esim/commit/5d2d87ffa588bb801e6c857c78de0f015cb968d1))
* list coupon from parent order ([9703fde](https://github.com/InboundPlatform/global-esim/commit/9703fde0d3e318b17d719b16faf87827c3bcd8f5))

## [2.41.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.40.1...api-v2.41.0) (2025-06-09)


### Features

* **coupon-function:** add back deepOmitKeys utils function ([0edff52](https://github.com/InboundPlatform/global-esim/commit/0edff52ca0fc1a158045ab95344e479413625818))
* **coupon-function:** add currency code default JPY in priceFormatted function utils ([b25b761](https://github.com/InboundPlatform/global-esim/commit/b25b7613641d93eb8570d7dca16f5d2161a248c6))
* **coupon-function:** add function to sanitize currency and setup default value for currency ([12e9ebb](https://github.com/InboundPlatform/global-esim/commit/12e9ebb03b9cc320416d9d7bec86b53f4acc5cd2))
* **coupon-function:** add migratin file for currency code addtiton ([bcd9f51](https://github.com/InboundPlatform/global-esim/commit/bcd9f5194a3e46a2b01beaf51da42447cd4ef003))
* **esimdb-documentation:** add validity days to search params for external plans api for eismdb ([d49dc7e](https://github.com/InboundPlatform/global-esim/commit/d49dc7e2f8fbf8663eb2d4f3a4f73d6dbf38d742))
* **esimdb-documentation:** properly type the validity days and optons id in freeplan matches filter ([1d55cc2](https://github.com/InboundPlatform/global-esim/commit/1d55cc292d452ebc8f08c3094f8587576e78f7ae))
* **seo-optimization:** add esim guide setup text to the support/help page in h1 tag ([833790f](https://github.com/InboundPlatform/global-esim/commit/833790f5f01fa161e9f3f94b35d704cd6e2eaeef))
* **seo-optimization:** delete accidentally pushed file referral-worked.hbs from commit ([a6d34ed](https://github.com/InboundPlatform/global-esim/commit/a6d34ed0cd9396491a41c4c6106535e37d069bcd))


### Bug Fixes

* add new coulmns for export to spreadsheet ([250271a](https://github.com/InboundPlatform/global-esim/commit/250271abf1c0c5dcec1fc62dbf7e39eb1b6b4661))
* **affiliate-column:** fix the issue where for longer affiliate urls the affilate column type wasn't sufficient to store data ([ccadabb](https://github.com/InboundPlatform/global-esim/commit/ccadabb4cefe6b5fce1552a910e9b0eab10abdd8))
* increase timeout to 2 minutes for urocomm ([7d63d92](https://github.com/InboundPlatform/global-esim/commit/7d63d92bd00d9dbb09e9245bae4c926a8e511d0e))

## [2.40.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.40.0...api-v2.40.1) (2025-05-30)


### Bug Fixes

* trigger release ([869affa](https://github.com/InboundPlatform/global-esim/commit/869affa3bc989182fd24f2aec89e3fe637df0c54))

## [2.40.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.39.0...api-v2.40.0) (2025-05-30)


### Features

* **paypay:** fix order placement issue due to inaccesible totalwithoutDiscountXe in commit quotation ([994b116](https://github.com/InboundPlatform/global-esim/commit/994b116ecd67bb40df90df978f816768162028b0))
* **ukomi:** add correct logger logs and remove thrown exception ([41d42ce](https://github.com/InboundPlatform/global-esim/commit/41d42ce6ae8a76bbff5bb429aa3af7d48caae65a))
* **ukomi:** request from third party source is not processed further for ukomi order creation ([9566e92](https://github.com/InboundPlatform/global-esim/commit/9566e92a61f996e3f0883e013246e7858ab0614d))
* **ukomi:** ukomi the product payload for global eism jp, global esim and airtrip ([10f57ea](https://github.com/InboundPlatform/global-esim/commit/10f57ea45dc817e76e7d6e654d0ce6a642840865))


### Bug Fixes

* add sentry to catch all global and missing errors ([69c0689](https://github.com/InboundPlatform/global-esim/commit/69c06890cf34a3f8c348a60d84a4f0b1836dc15d))
* currency changes error ([f2e12b5](https://github.com/InboundPlatform/global-esim/commit/f2e12b56c49c641742037ecf0dd0fe0794b6a7d5))
* discount fixes for quotation ([ae38093](https://github.com/InboundPlatform/global-esim/commit/ae380938d705366144f4b0aaa8a7a32fe2df1966))
* log more details ([2b768c0](https://github.com/InboundPlatform/global-esim/commit/2b768c0731312e71a920b60813fb88d11878fd1a))
* log more details ([3fa1ed6](https://github.com/InboundPlatform/global-esim/commit/3fa1ed6b3cbd01917f9c79750d80830bf1ab025b))
* sentry env for webjp and web ([9a27005](https://github.com/InboundPlatform/global-esim/commit/9a27005587c9090488bd8adcb30a0808830d7f3d))

## [2.39.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.38.1...api-v2.39.0) (2025-05-29)


### Features

* **coupon-function:** fix discount amount discrepancy issue with the usd ([b330097](https://github.com/InboundPlatform/global-esim/commit/b3300972ca5534c0607133d4760e6347e5c9d9af))
* **coupon-function:** fix issue with the discounted pproduct price and added columns for final price ([a4f6a85](https://github.com/InboundPlatform/global-esim/commit/a4f6a85f81a377002c853a6e971b66753b5f2d24))
* **ukomi:** remove stray debugger in listener ([1fff3e4](https://github.com/InboundPlatform/global-esim/commit/1fff3e48a56f1b650129d677421e9cb0b7651990))
* **ukomi:** update the api key of ukomi in order update listener ([e2082cf](https://github.com/InboundPlatform/global-esim/commit/e2082cf6fbb14352c82512beea15030f34118775))
* **ukomi:** update the ukomi api key and secret to use the config from the config service ([f7d95af](https://github.com/InboundPlatform/global-esim/commit/f7d95af428957e1bc806dab1382133fa5f1db3f0))

## [2.38.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.38.0...api-v2.38.1) (2025-05-28)


### Bug Fixes

* trigger release ([3d4f0c3](https://github.com/InboundPlatform/global-esim/commit/3d4f0c3256138256eaad3445a0e87d35f8246fbc))

## [2.38.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.37.1...api-v2.38.0) (2025-05-28)


### Features

* **ukomi:** add environment prefix in order updates listener ([df961be](https://github.com/InboundPlatform/global-esim/commit/df961be619a658ae0858e505b6a77e87117d9df9))
* **ukomi:** add environment prefix to product id as well ([2e96d91](https://github.com/InboundPlatform/global-esim/commit/2e96d919d56653d6edf29fafa698b1cf1de83428))
* **ukomi:** add missing api key and secret handling ([3a7c02e](https://github.com/InboundPlatform/global-esim/commit/3a7c02eb4f2ed931ac0337c0fd35da95e9b13604))
* **ukomi:** add ukomi review module and services, create order in ukomi when order completed ([71b9046](https://github.com/InboundPlatform/global-esim/commit/71b904614cb2d6360fa87787513921f9bf40ee15))
* **ukomi:** remove the debugger statement ([8bf2b62](https://github.com/InboundPlatform/global-esim/commit/8bf2b621cca2bad9a114aeac94e1b7377126a65c))

## [2.37.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.37.0...api-v2.37.1) (2025-05-27)


### Bug Fixes

* gmocp free esim fixes ([9164f61](https://github.com/InboundPlatform/global-esim/commit/9164f618ceeafe4bdc597a173840afd1a6e60bf4))

## [2.37.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.36.2...api-v2.37.0) (2025-05-26)


### Features

* invoke gmocp url ([e7d0bca](https://github.com/InboundPlatform/global-esim/commit/e7d0bca54649232e0b62400513cee7d3f0c1eecf))


### Bug Fixes

* build issues ([a7823da](https://github.com/InboundPlatform/global-esim/commit/a7823daa8d2854e2a66927473b3690a49e680fa8))

## [2.36.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.36.1...api-v2.36.2) (2025-05-20)


### Bug Fixes

* signup error issue for airtrip mobile app ([d414d1b](https://github.com/InboundPlatform/global-esim/commit/d414d1bf5ae97a9bfa9e4053253889597bf27432))

## [2.36.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.36.0...api-v2.36.1) (2025-05-20)


### Bug Fixes

* handle website ([885b0fa](https://github.com/InboundPlatform/global-esim/commit/885b0fad405e86e7ea220993241b63aa60f08a6b))

## [2.36.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.35.2...api-v2.36.0) (2025-05-19)


### Features

* service specific coupon ([8fca803](https://github.com/InboundPlatform/global-esim/commit/8fca8031632062e49493b1a20c0d14dcb860df97))


### Bug Fixes

* **api:** add a line break in the order confirmation email ([afd0390](https://github.com/InboundPlatform/global-esim/commit/afd039040aac00aa5c02b49442f42920590683cb))
* **api:** add missing backframe in email template ([fc7434b](https://github.com/InboundPlatform/global-esim/commit/fc7434bc21185e14f71849c42c9d3c87df202569))
* **api:** correct pdf links for airtrip ([8e82fa7](https://github.com/InboundPlatform/global-esim/commit/8e82fa79b6234d8c7feac4c35d143fb9d9703935))
* **api:** make campaign in email template only for jp lang ([1ba3820](https://github.com/InboundPlatform/global-esim/commit/1ba3820c84d3e805b415d0d96e7efc95d2723263))
* **api:** order email confirmation rollback to 891066219460e24df9b9f2498c71fd521bb9fbec ([d4cd012](https://github.com/InboundPlatform/global-esim/commit/d4cd012c82358e51f7906ca32e50121a3f371729))
* **api:** remove airtrip campaign from order confirmation email ([7fe3001](https://github.com/InboundPlatform/global-esim/commit/7fe3001348dcbaad5620b3f7db4c6a8bb980e4bb))
* **api:** replace link in order confirmation order ([76a974d](https://github.com/InboundPlatform/global-esim/commit/76a974d2eee2effd86d4c479a799962d5791b914))
* **api:** set lgu manual only for lgu plans on airtrip ([02358ec](https://github.com/InboundPlatform/global-esim/commit/02358ecca8f50fcdafd27a91af6e401695ce493a))
* **api:** use correct pdf links for lgu plans ordered from airtrip ([3778ada](https://github.com/InboundPlatform/global-esim/commit/3778ada85db74e9d91134f21e11a61fabc71881b))
* multiple email sends when signinup for guest user ([1fabf3d](https://github.com/InboundPlatform/global-esim/commit/1fabf3da04a77152459376db2a40ab1f03456f34))
* **pdf:** fix value of the country/region in the pdf ([50ad1ee](https://github.com/InboundPlatform/global-esim/commit/50ad1eec6b0a918b0099f3a5e9bf04ca49dac0f9))
* reset password not being sent ([466c4d6](https://github.com/InboundPlatform/global-esim/commit/466c4d6668033459997dcf3d517f6a920cb6224a))
* sending two verification email ([87de923](https://github.com/InboundPlatform/global-esim/commit/87de923f6cd1e42d3b2b5fa5550ea0afba324f4d))
* sending two verification emails ([0555b57](https://github.com/InboundPlatform/global-esim/commit/0555b577f1ce7bc71d574ebf36caa819eacdb512))
* show campaign only for LGU ([871f91e](https://github.com/InboundPlatform/global-esim/commit/871f91eecfa0a5c7f04bffc4e7e801113a4eb55f))
* update email verified to true if account is verified ([db8d63e](https://github.com/InboundPlatform/global-esim/commit/db8d63e86f796c7cfe1d9ed55497c9a1845cd566))

## [2.35.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.35.1...api-v2.35.2) (2025-04-24)


### Bug Fixes

* trigger release ([8017a65](https://github.com/InboundPlatform/global-esim/commit/8017a656921c1f0d7ed9d16af3194d17c734de66))

## [2.35.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.35.0...api-v2.35.1) (2025-04-24)


### Bug Fixes

* check duplicate phone number and  block ([3efebad](https://github.com/InboundPlatform/global-esim/commit/3efebad4bfb4b0560f88984385e521f43cd902bb))
* corporate feature ([3efebad](https://github.com/InboundPlatform/global-esim/commit/3efebad4bfb4b0560f88984385e521f43cd902bb))
* invoice information on database ([711c52f](https://github.com/InboundPlatform/global-esim/commit/711c52ff1e854a34c9d08bec8124a10e388eb75c))
* trigger release ([42482f1](https://github.com/InboundPlatform/global-esim/commit/42482f1010b72f7f79e3ef5e35bb93c4affa2890))

## [2.35.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.34.5...api-v2.35.0) (2025-04-22)


### Features

* **planspecific-coupon:** add plan specific coupon rule ([61a5f67](https://github.com/InboundPlatform/global-esim/commit/61a5f675d245b71fbe2f3a383f15092bd1e81b18))


### Bug Fixes

* log response ([53c4d98](https://github.com/InboundPlatform/global-esim/commit/53c4d98056686028b26b9526570862b6dd7c681f))
* trigger release ([5445a6b](https://github.com/InboundPlatform/global-esim/commit/5445a6b97a5cc1d99f2b6f2ed848a2f0d2518207))

## [2.34.5](https://github.com/InboundPlatform/global-esim/compare/api-v2.34.4...api-v2.34.5) (2025-04-17)


### Bug Fixes

* **api:** update translation for email template ([#1618](https://github.com/InboundPlatform/global-esim/issues/1618)) ([8910662](https://github.com/InboundPlatform/global-esim/commit/891066219460e24df9b9f2498c71fd521bb9fbec))

## [2.34.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.34.3...api-v2.34.4) (2025-04-16)


### Bug Fixes

* throttle limit to 5000 from 200 for plans api ([8cd8371](https://github.com/InboundPlatform/global-esim/commit/8cd837119c20c24780226e43b7a43b2f535e98d0))

## [2.34.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.34.2...api-v2.34.3) (2025-04-15)


### Bug Fixes

* add throttle in quotation ([4c5f7fd](https://github.com/InboundPlatform/global-esim/commit/4c5f7fd9952e1b51281bae1ad964527229650903))
* handle throttle within proxy ([2ef93e0](https://github.com/InboundPlatform/global-esim/commit/2ef93e0593ba54ecac3e2c359dc72203dd154134))
* log coupon code as well on error ([acc1b7b](https://github.com/InboundPlatform/global-esim/commit/acc1b7b5e56674e26e580cb95f5660ea675a7d4f))
* log coupon code as well on error ([fabf425](https://github.com/InboundPlatform/global-esim/commit/fabf425076e8747d273a907fae3100efa3fe66d4))

## [2.34.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.34.1...api-v2.34.2) (2025-04-14)


### Bug Fixes

* dont redirect when its not korea free campaign ([f6dd15a](https://github.com/InboundPlatform/global-esim/commit/f6dd15a2b4c8fd547d62f047fa21197b52bd3131))

## [2.34.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.34.0...api-v2.34.1) (2025-04-14)


### Bug Fixes

* gmo campaign form ([158872a](https://github.com/InboundPlatform/global-esim/commit/158872a7446aecbdb536842e5d5291b83e220671))

## [2.34.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.33.1...api-v2.34.0) (2025-04-11)


### Features

* **json-key:** add dataCap 0 to unlimited plan kddi but skip ntt docomo unlimited plan ([33f04eb](https://github.com/InboundPlatform/global-esim/commit/33f04eb081b658f4cc76ba9bd0dfd60739b49110))
* **json-key:** add new json keys dataCap for esimDB api request ([04c0466](https://github.com/InboundPlatform/global-esim/commit/04c04663c22ae1d36c6bfa2cb035b04a5385037b))


### Bug Fixes

* add throttle limit of order api ([008e68d](https://github.com/InboundPlatform/global-esim/commit/008e68d6f3bed7015f8edf38c4f54081b66cd9b2))

## [2.33.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.33.0...api-v2.33.1) (2025-04-10)


### Bug Fixes

* unable to create user ([ed6eaf7](https://github.com/InboundPlatform/global-esim/commit/ed6eaf791161656ffb14aa7e2906376dc64499ca))

## [2.33.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.32.3...api-v2.33.0) (2025-04-08)


### Features

* **json-key:** add new json keys for esimDB api request for plans GET api ([fbd16e3](https://github.com/InboundPlatform/global-esim/commit/fbd16e379ee88f5946950cc32e0fbec605488f2e))


### Bug Fixes

* **api:** handle unhandled exception and optimize db calls ([8f5b49a](https://github.com/InboundPlatform/global-esim/commit/8f5b49acc0f934db4b4033d56ce7a9db42cb3a29))
* default name for lgu ([31c86f6](https://github.com/InboundPlatform/global-esim/commit/31c86f6440331f742bf8ce64dee5dc4ed5a33d3f))
* dont send verification email for guest user ([4415629](https://github.com/InboundPlatform/global-esim/commit/4415629d1d0876f39be40a68ec3a4259748f1359))
* social login ([c14edb7](https://github.com/InboundPlatform/global-esim/commit/c14edb7367d34869478f9651d2ce61fe7499c2a4))

## [2.32.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.32.2...api-v2.32.3) (2025-03-27)


### Bug Fixes

* add dummmy data for lgu esim korea ([882a078](https://github.com/InboundPlatform/global-esim/commit/882a07840c5a9f94e566fead388dada9f02936c1))
* correct price for bulk orders ([35d634d](https://github.com/InboundPlatform/global-esim/commit/35d634d7125fad93a2b95fa5a7805f657dd97f86))
* kakakucom coupon hotfix ([8e97185](https://github.com/InboundPlatform/global-esim/commit/8e9718585033bda202e6590269bfb4e886ef2f97))
* lguprovider dummy data, correct country code ([8bf0a48](https://github.com/InboundPlatform/global-esim/commit/8bf0a481851ca275f3e3409235057b07e703f101))
* make passport information optional ([4cbcb85](https://github.com/InboundPlatform/global-esim/commit/4cbcb851e675958b69e7d9965d7e80e4b84ac76c))
* phone not encrypted while sending to lgu ([b93b7b7](https://github.com/InboundPlatform/global-esim/commit/b93b7b7de8a507e27ed8843d3f217cc98f0cc899))
* wrong bl ([f6e5103](https://github.com/InboundPlatform/global-esim/commit/f6e51035c6bf3f92d73a493ba4b0fe24f43f6363))

## [2.32.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.32.1...api-v2.32.2) (2025-03-25)


### Bug Fixes

* new api for lgu plans ([36ae6c2](https://github.com/InboundPlatform/global-esim/commit/36ae6c252dac6830ae7df4d79684d6df9d978591))

## [2.32.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.32.0...api-v2.32.1) (2025-03-25)


### Bug Fixes

* **api:** move cm link logic for email ([056dd82](https://github.com/InboundPlatform/global-esim/commit/056dd82689740dd0c22a274790c6da72f5de29d1))
* **api:** update email confirmation template ([5414d8d](https://github.com/InboundPlatform/global-esim/commit/5414d8dc091a5eb664f24e01e7d99c284232b8e0))

## [2.32.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.31.0...api-v2.32.0) (2025-03-24)


### Features

* add instant esim pdf for airtrip in jp ([a76c111](https://github.com/InboundPlatform/global-esim/commit/a76c1113f1c57920468d3e3af4b9c9d4bbfa60d2))
* add new esim provider LINK Korea for vietnam, SKT, thailand ([07bb8c4](https://github.com/InboundPlatform/global-esim/commit/07bb8c4ab7d4956e3ea73bafaff739fe03e50725))
* basket in  webjp ([c6dfd57](https://github.com/InboundPlatform/global-esim/commit/c6dfd5792db30f8ce6504a5735800721e2647ad6))
* topup api and ui with flow ([a4945cd](https://github.com/InboundPlatform/global-esim/commit/a4945cd6001339fb275df6a50dde1263fe049ce6))


### Bug Fixes

* add coupon currency with default to USD ([d4a03da](https://github.com/InboundPlatform/global-esim/commit/d4a03dac96e9edec89c0e3b120b08b532030668e))
* allow lost user to continue campaign if they abandon in middle ([2d0df3c](https://github.com/InboundPlatform/global-esim/commit/2d0df3c4ce25c142ad7d507bab6880fcc7449264))
* language update for airtrip, missing locale for qatar, turkey and republic of south africa ([165dd55](https://github.com/InboundPlatform/global-esim/commit/165dd550380927e55d013780dd468a7aa4ea48d2))
* language, mobile app locale addition for airtrip mobile app ([bb53ec1](https://github.com/InboundPlatform/global-esim/commit/bb53ec1c0068bd414ac839495d3ef140183e6143))
* misc fixes ([907830c](https://github.com/InboundPlatform/global-esim/commit/907830c29baa6425232f824cfda5778ac0b5b042))
* remove publishableKey ([58df838](https://github.com/InboundPlatform/global-esim/commit/58df83851d896ec79c56da80a6ecad5318dd8b5a))

## [2.31.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.30.1...api-v2.31.0) (2025-03-06)


### Features

* korea and tokukusho coupon compatible ([afaa9dc](https://github.com/InboundPlatform/global-esim/commit/afaa9dc45f14bb0a117916727e4c9591c63ec015))

## [2.30.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.30.0...api-v2.30.1) (2025-02-28)


### Bug Fixes

* build issues ([d1fb212](https://github.com/InboundPlatform/global-esim/commit/d1fb212ccd3c294fc80b1fae29678679639e9f0f))

## [2.30.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.29.4...api-v2.30.0) (2025-02-28)


### Features

* **APN change:** add unlimited days 11,12,13,14,15 for apn check ([5cc06fb](https://github.com/InboundPlatform/global-esim/commit/5cc06fbf9b6deaa4c234b7c316c37c8068a6909f))


### Bug Fixes

* error handling of queue send to develop email ([b08060a](https://github.com/InboundPlatform/global-esim/commit/b08060a69278e208ec2310c808a7af879770095c))

## [2.29.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.29.3...api-v2.29.4) (2025-02-28)


### Bug Fixes

* lower recaptcha score temporally until mobile app testing is completed ([b4a26fa](https://github.com/InboundPlatform/global-esim/commit/b4a26fadb90c0f90c653e84cc72443cc12b46943))

## [2.29.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.29.2...api-v2.29.3) (2025-02-27)


### Bug Fixes

* build issues ([014d127](https://github.com/InboundPlatform/global-esim/commit/014d127280859a5d06667ae787df63f10acafc2f))

## [2.29.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.29.1...api-v2.29.2) (2025-02-27)


### Bug Fixes

* korea campaign fixes ([975bc66](https://github.com/InboundPlatform/global-esim/commit/975bc667dc8d69e53de81b3364b442542a5aa72b))

## [2.29.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.29.0...api-v2.29.1) (2025-02-26)


### Bug Fixes

* basket hotfixes ([ed10a81](https://github.com/InboundPlatform/global-esim/commit/ed10a81767de78b1df0a62bc2dee8bef1c57cc3b))

## [2.29.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.28.2...api-v2.29.0) (2025-02-26)


### Features

* **api:** campaign prices issues fixes and standardize ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))
* **api:** improve basket feature ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))


### Bug Fixes

* **api:** enhanced api caching for plans ([63719be](https://github.com/InboundPlatform/global-esim/commit/63719becbc50c7d9adecff8af959dbc76a53549d))
* **web:** add progress bar while page loads ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))
* **web:** reload after currency is changed ([63719be](https://github.com/InboundPlatform/global-esim/commit/63719becbc50c7d9adecff8af959dbc76a53549d))
* **web:** remove japanese text ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))

## [2.28.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.28.1...api-v2.28.2) (2025-02-25)


### Bug Fixes

* optional source ([951e615](https://github.com/InboundPlatform/global-esim/commit/951e615a75388af1f05dd15a2c21927b1fb71bfe))

## [2.28.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.28.0...api-v2.28.1) (2025-02-20)


### Bug Fixes

* taiwan prices for campaign update ([1834a7e](https://github.com/InboundPlatform/global-esim/commit/1834a7e8a406c690464843889a0befb104cc0612))

## [2.28.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.27.1...api-v2.28.0) (2025-02-20)


### Features

* campaign sampo ([89a317c](https://github.com/InboundPlatform/global-esim/commit/89a317c7ce6ab84584349bd5646351d0fc1fc35c))


### Bug Fixes

* correct campaign prices not showing ([a389e7b](https://github.com/InboundPlatform/global-esim/commit/a389e7b751170e8caa50e1bb455881b55a1d1439))

## [2.27.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.27.0...api-v2.27.1) (2025-02-17)


### Bug Fixes

* free esim campaign mails from airtrip being sent from gmobile ([8c2220b](https://github.com/InboundPlatform/global-esim/commit/8c2220bb7fcf9343e8b6d1c935571b06e5c7940b))
* new apn for 30 days - 20 gb plans ([8c2220b](https://github.com/InboundPlatform/global-esim/commit/8c2220bb7fcf9343e8b6d1c935571b06e5c7940b))
* request api customer-sheet ([3fcefc5](https://github.com/InboundPlatform/global-esim/commit/3fcefc552a47a0583228fd279d31950202c00f24))
* request api customer-sheet ([ac14f46](https://github.com/InboundPlatform/global-esim/commit/ac14f46d857ff9eed71c5559119d0e3150532a5c))
* user source and stripe seperation for mobile app ([8c2220b](https://github.com/InboundPlatform/global-esim/commit/8c2220bb7fcf9343e8b6d1c935571b06e5c7940b))

## [2.27.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.26.5...api-v2.27.0) (2025-02-14)


### Features

* add source stripe payment to api customer-sheet ([c1d9754](https://github.com/InboundPlatform/global-esim/commit/c1d97540a16f4e0f81df2ec300a40a1dea30527c))


### Bug Fixes

* update source payment esim with body ([f43d788](https://github.com/InboundPlatform/global-esim/commit/f43d788c22d31b3a0ea904da59b3a9c3b0c5b344))

## [2.26.5](https://github.com/InboundPlatform/global-esim/compare/api-v2.26.4...api-v2.26.5) (2025-02-07)


### Bug Fixes

* change and update lguplus api ([f9f6bc6](https://github.com/InboundPlatform/global-esim/commit/f9f6bc6b81c4ce411d76e4a4aa48bf42d0a41b40))
* change subject as per user source ([a80ffca](https://github.com/InboundPlatform/global-esim/commit/a80ffcaf1e16594ef2e4a9938bd98bc73e74aec3))
* enable new lgu plus api only on dev environment ([71096e0](https://github.com/InboundPlatform/global-esim/commit/71096e092181bc4b90f39af69ef436e42f0ceb8f))
* misc fixes ([f2ade7e](https://github.com/InboundPlatform/global-esim/commit/f2ade7ecce162fac31b816d02aec12ae2ea86f65))
* missing user source for social login users ([4581a7d](https://github.com/InboundPlatform/global-esim/commit/4581a7d04609db3a7520383cbb8e458b014085c3))

## [2.26.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.26.3...api-v2.26.4) (2025-01-31)


### Bug Fixes

* mobile number checked on undefined undefined ([d5e2716](https://github.com/InboundPlatform/global-esim/commit/d5e2716ba884b5e71d8043aeb79204f425d594ed))

## [2.26.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.26.2...api-v2.26.3) (2025-01-31)


### Bug Fixes

* trigger release ([eb2107f](https://github.com/InboundPlatform/global-esim/commit/eb2107f9606ed09206ff69f8f357389d9c9df7c9))

## [2.26.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.26.1...api-v2.26.2) (2025-01-31)


### Bug Fixes

* campaign plan esim not being disabled because of plan being disabled ([b48eac1](https://github.com/InboundPlatform/global-esim/commit/b48eac10e8bc99550166019dbd059e869cfe746d))
* mail issue fixe ([8a0587e](https://github.com/InboundPlatform/global-esim/commit/8a0587e32e2a2f0bc395bd9620ef24614e4350df))

## [2.26.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.26.0...api-v2.26.1) (2025-01-30)


### Bug Fixes

* trigger release ([4d2112c](https://github.com/InboundPlatform/global-esim/commit/4d2112c025af36a2427d31f8cd7b1d81427481c0))

## [2.26.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.25.2...api-v2.26.0) (2025-01-30)


### Features

* enhanced authentication flow ([ca7a64c](https://github.com/InboundPlatform/global-esim/commit/ca7a64c2c712241ab2c12ffffb0b3e496e2b2c2a))


### Bug Fixes

* send airtrip email from airtrip origin ([fff680b](https://github.com/InboundPlatform/global-esim/commit/fff680b0466b775df234d13dda80b7a9aadd766a))
* treat them as non existent user, so they are forced to create an account ([6bc8265](https://github.com/InboundPlatform/global-esim/commit/6bc826555107656cc08b76cca8e54cf2c5ec0252))

## [2.25.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.25.1...api-v2.25.2) (2025-01-28)


### Bug Fixes

* log esim purchase and auth errors with more details ([f70a3e6](https://github.com/InboundPlatform/global-esim/commit/f70a3e6e33d93f0d65f5949130888476421f26d0))

## [2.25.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.25.0...api-v2.25.1) (2025-01-27)


### Bug Fixes

* add slack logs ([752a4ad](https://github.com/InboundPlatform/global-esim/commit/752a4ad66596a3325c9041fb3ffab78fd957a3f9))
* APN plan changes ([f4902ef](https://github.com/InboundPlatform/global-esim/commit/f4902ef86376fe57365aee157480ca3ee20a3597))
* dev deploy ([57390a1](https://github.com/InboundPlatform/global-esim/commit/57390a1008e0b70f25d43b3dc851fb37db490752))
* env issues for dockerfile ([de79151](https://github.com/InboundPlatform/global-esim/commit/de79151d23de9626c8ac909911345c854b0d9215))

## [2.25.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.24.2...api-v2.25.0) (2025-01-23)


### Features

* seperate pricing for different services ([901786c](https://github.com/InboundPlatform/global-esim/commit/901786cca7c8ca7c6d089ea90af7e62c0dc842a0))
* seperate pricing for different services ([bb65398](https://github.com/InboundPlatform/global-esim/commit/bb65398927c1ca7583c9a2f69488d345410aa783))
* set up cron ([2ba9310](https://github.com/InboundPlatform/global-esim/commit/2ba931085b411dbe91611582bbb68fd27fbf8bcc))
* set up cron + merge code from rating reviews ([b002769](https://github.com/InboundPlatform/global-esim/commit/b00276918d0d1dbaff5b35749abcec0136643a47))


### Bug Fixes

* add header for cache ([50fdec7](https://github.com/InboundPlatform/global-esim/commit/50fdec764930f401c7fc03d544a67b81ec5ff37f))
* add header for cache ([4a755b1](https://github.com/InboundPlatform/global-esim/commit/4a755b1b0b8a1e431e59dae102b3a9cfc1b3bf5b))
* add headers of origin in plan details api as well ([9885f2b](https://github.com/InboundPlatform/global-esim/commit/9885f2b41f10a54a69a0c200149b8065b34c6403))
* add headers of origin in plan details api as well ([c9597c6](https://github.com/InboundPlatform/global-esim/commit/c9597c606ee89f34bb9875b5741bca2f3c22a10e))
* api message for invalid phone number ([7081110](https://github.com/InboundPlatform/global-esim/commit/7081110ee51e653d93b3cea86e0e42e42c873753))
* build code ([fadd396](https://github.com/InboundPlatform/global-esim/commit/fadd396e53749191caaccb501dade66ab42b9932))
* check special plan ([b040c9b](https://github.com/InboundPlatform/global-esim/commit/b040c9bce60ef35ca3fdd764246915b26bcb4d69))
* lower captcha on dev env ([405dd0d](https://github.com/InboundPlatform/global-esim/commit/405dd0d043bb369db69d1deb580db341ea47d31f))
* migration database ([c3d1a48](https://github.com/InboundPlatform/global-esim/commit/c3d1a4841c229174a6ac8474eab6f0a663862533))
* misc fixes ([3f48883](https://github.com/InboundPlatform/global-esim/commit/3f48883a8ba9e58f0ad548109de8fa20e6488bd5))
* payment issue with seperate pricing ([f389281](https://github.com/InboundPlatform/global-esim/commit/f389281e00c1f60a5edc13efa4e4e5611867072b))
* plans with new apn ([c855f09](https://github.com/InboundPlatform/global-esim/commit/c855f09f9fa3b10e3619b1aafe8e26b57f1c5fc8))
* remote code with cron ([8d4a8b1](https://github.com/InboundPlatform/global-esim/commit/8d4a8b1521b495946bf587d8ee93d25e555cf5ff))
* remove code push noti ([c7bd21d](https://github.com/InboundPlatform/global-esim/commit/c7bd21d2824685d14d4bca41d2bc45365caf155f))
* remove redundant console log ([b7c4ffe](https://github.com/InboundPlatform/global-esim/commit/b7c4ffead2640d0d954dce0e17e1b41712000152))
* seperate plan pricing stripe payment bug fixes: ([f02b853](https://github.com/InboundPlatform/global-esim/commit/f02b8531d327709167ce47fb00b7e81a869d3eb4))
* seperate plan pricing stripe payment bug fixes: ([3c76fb1](https://github.com/InboundPlatform/global-esim/commit/3c76fb189c1923d71eda972d1d01aff9f5357e4b))
* throttle limit and migration fixes ([3e87dd7](https://github.com/InboundPlatform/global-esim/commit/3e87dd7a0f2bc9be42aa17168a7b9170521b9db8))

## [2.24.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.24.1...api-v2.24.2) (2025-01-08)


### Bug Fixes

* increase throttle ([5f00a35](https://github.com/InboundPlatform/global-esim/commit/5f00a35e2b578f6371b4286f8ad2074df66e1065))
* remove nikkei top campaign banner ([8773369](https://github.com/InboundPlatform/global-esim/commit/87733695a1591e38d11be329fd77ea8e292606d0))

## [2.24.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.24.0...api-v2.24.1) (2025-01-06)


### Bug Fixes

* increase qr generation throttle ([d354ee9](https://github.com/InboundPlatform/global-esim/commit/d354ee91e81437c57cf5f514b4477f9714f0f19b))

## [2.24.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.23.3...api-v2.24.0) (2024-12-27)


### Features

* send email to campaign after sms verification ([7899a98](https://github.com/InboundPlatform/global-esim/commit/7899a982c4556d0127376618c78216998f9ec413))


### Bug Fixes

* bcc cs mail group for post sms verification mail ([b76430d](https://github.com/InboundPlatform/global-esim/commit/b76430d61bec50287f7733bcf6f7c0e907c99f71))
* email template bug ([851ad8c](https://github.com/InboundPlatform/global-esim/commit/851ad8cfa1077915d374875db66ab9fd21ffbe2b))
* email translations for country names ([dc97090](https://github.com/InboundPlatform/global-esim/commit/dc97090b95503a7d8179c779b4c489754728986d))
* us virgin islands misspell ([22910ec](https://github.com/InboundPlatform/global-esim/commit/22910ec7aefdf1f7e01fd64490489041c9284f70))
* usvirginisland fixes ([5944abf](https://github.com/InboundPlatform/global-esim/commit/5944abfe80be79980138b52a3ddf31f10fc2ef93))
* virgin island image name ([412d4e7](https://github.com/InboundPlatform/global-esim/commit/412d4e744735ed047e7cc41a283a96b0806d81cb))

## [2.23.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.23.2...api-v2.23.3) (2024-12-25)


### Bug Fixes

* revise gm confirmation email template ([#1202](https://github.com/InboundPlatform/global-esim/issues/1202)) ([01ccbc0](https://github.com/InboundPlatform/global-esim/commit/01ccbc001ce154625a4f01f53aeeadba9b213913))

## [2.23.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.23.1...api-v2.23.2) (2024-12-24)


### Bug Fixes

* build issues ([dec71f2](https://github.com/InboundPlatform/global-esim/commit/dec71f26830d5c62b57691caa327c9b1cb0259d8))

## [2.23.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.23.0...api-v2.23.1) (2024-12-24)


### Bug Fixes

* hide mail issue ([02d4bb8](https://github.com/InboundPlatform/global-esim/commit/02d4bb8d66f8167d24888c2d4bd7b5b2fed60fd9))

## [2.23.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.19...api-v2.23.0) (2024-12-23)


### Features

* in house QR code generator ([8a6c99c](https://github.com/InboundPlatform/global-esim/commit/8a6c99c635468a4d12353f6be774d4eeb1887c57))


### Bug Fixes

* correct activation guide for taiwan, hk and korea ([f47039b](https://github.com/InboundPlatform/global-esim/commit/f47039bf0946f51bc183cb23e5648280f90fcc91))
* remove japanese from multi lang email template ([0026c7b](https://github.com/InboundPlatform/global-esim/commit/0026c7b03434f200676f822a5e3249555c612cae))

## [2.22.19](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.18...api-v2.22.19) (2024-12-19)


### Bug Fixes

* confirmation email not being sent ([4b214f0](https://github.com/InboundPlatform/global-esim/commit/4b214f0fd5dde35200b617a10090659ae8bbe69a))
* increase bulk purchase concurrency ([ffdeb5a](https://github.com/InboundPlatform/global-esim/commit/ffdeb5a131cff2a35a49fd89eadccb501dbccaed))
* syntax error of usd being pulled ([5bcb45e](https://github.com/InboundPlatform/global-esim/commit/5bcb45e7ba3aabc98df19c4ca430e327ac52f14b))
* undefined access ([b5b343e](https://github.com/InboundPlatform/global-esim/commit/b5b343e2e912fd1340e928c33b98a2d5216b50c7))

## [2.22.18](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.17...api-v2.22.18) (2024-12-19)


### Bug Fixes

* add sms authentication in the korea campaign flow ([a5b8620](https://github.com/InboundPlatform/global-esim/commit/a5b86208959f1651e18dc302fb7e8ef4386a5e17))
* email template line qr code ([86715a0](https://github.com/InboundPlatform/global-esim/commit/86715a01504ea9bff235db74146c7206f691ecc0))

## [2.22.17](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.16...api-v2.22.17) (2024-12-18)


### Bug Fixes

* email template line information ([#1159](https://github.com/InboundPlatform/global-esim/issues/1159)) ([#1167](https://github.com/InboundPlatform/global-esim/issues/1167)) ([54927b1](https://github.com/InboundPlatform/global-esim/commit/54927b16a6bdb4bbc69495cb0883b1aed86e4f5b))

## [2.22.16](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.15...api-v2.22.16) (2024-12-17)


### Bug Fixes

* build issues ([e7054dd](https://github.com/InboundPlatform/global-esim/commit/e7054dd1e065a3e32e439a157b0c14da59bb7242))

## [2.22.15](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.14...api-v2.22.15) (2024-12-17)


### Bug Fixes

* airtrip campaign free esim ([346c8a1](https://github.com/InboundPlatform/global-esim/commit/346c8a1710dd20670c23cec36434e1b10e46e08d))

## [2.22.14](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.13...api-v2.22.14) (2024-12-17)


### Bug Fixes

* build issues ([af646ed](https://github.com/InboundPlatform/global-esim/commit/af646ed6a09d9ba94080ec1328b054c7d12280d7))

## [2.22.13](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.12...api-v2.22.13) (2024-12-16)


### Bug Fixes

* build issues ([57640ba](https://github.com/InboundPlatform/global-esim/commit/57640bac3235aba2137872f6dd35ebf088142baf))
* build issues ([0765e56](https://github.com/InboundPlatform/global-esim/commit/0765e56581c964a3ec61d038772f06c7e11aa0ae))

## [2.22.12](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.11...api-v2.22.12) (2024-12-13)


### Bug Fixes

* add bullet points to email template ([#1143](https://github.com/InboundPlatform/global-esim/issues/1143)) ([059c9f8](https://github.com/InboundPlatform/global-esim/commit/059c9f82156591675981e7a34f549590421aba6b))
* add name registration guide ([#1138](https://github.com/InboundPlatform/global-esim/issues/1138)) ([dd006be](https://github.com/InboundPlatform/global-esim/commit/dd006be069b3d49b9b8064e25e64f13af59affa3))
* double href in email template ([#1141](https://github.com/InboundPlatform/global-esim/issues/1141)) ([27205e1](https://github.com/InboundPlatform/global-esim/commit/27205e15a49332d1941536ee03f54e4115bc5d2c))

## [2.22.11](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.10...api-v2.22.11) (2024-12-12)


### Bug Fixes

* build issues webjp ([b490ee2](https://github.com/InboundPlatform/global-esim/commit/b490ee2a0608337e3c13eba4977116f664c38e97))

## [2.22.10](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.9...api-v2.22.10) (2024-12-12)


### Bug Fixes

* update email template styling ([#1127](https://github.com/InboundPlatform/global-esim/issues/1127)) ([bdc4c39](https://github.com/InboundPlatform/global-esim/commit/bdc4c39323fb174902ffbb409726e451610a936d))
* update order confirmation email template ([#1126](https://github.com/InboundPlatform/global-esim/issues/1126)) ([38f3eb1](https://github.com/InboundPlatform/global-esim/commit/38f3eb1031f24f7b51e8d591b7b99c4d39f78228))

## [2.22.9](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.8...api-v2.22.9) (2024-12-11)


### Bug Fixes

* add topup id in japanese in mail template ([10baf91](https://github.com/InboundPlatform/global-esim/commit/10baf918aeb2b1e8638ed8a9f3256ae4db52fbf4))
* build ([39531e3](https://github.com/InboundPlatform/global-esim/commit/39531e3c733970f1ca72c0763fcb0093d48bad13))
* disable service being listed ([f4b290a](https://github.com/InboundPlatform/global-esim/commit/f4b290ab4069f2475d709c81be87c14595e2b865))
* subscribe error ([ad0ab6c](https://github.com/InboundPlatform/global-esim/commit/ad0ab6c6f359c2e079575eb20eb57c84d6786992))
* update dockerfile with migration ([f302ee0](https://github.com/InboundPlatform/global-esim/commit/f302ee0806908d21ac4ddfb5578b8c8b1120f84f))

## [2.22.8](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.7...api-v2.22.8) (2024-12-06)


### Bug Fixes

* build issues ([0eb7b24](https://github.com/InboundPlatform/global-esim/commit/0eb7b24e119430db955ae2a67737b4e41660df5a))

## [2.22.7](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.6...api-v2.22.7) (2024-11-20)


### Bug Fixes

* redeploy api ([dc57a69](https://github.com/InboundPlatform/global-esim/commit/dc57a69c99b6555f54a78cf0e90549d6347cd925))

## [2.22.6](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.5...api-v2.22.6) (2024-11-19)


### Bug Fixes

* email template issue fix ([5357b36](https://github.com/InboundPlatform/global-esim/commit/5357b36df2b7331246459c7614d5191a68bf91f0))
* manual fixes for pdf ([2be75d3](https://github.com/InboundPlatform/global-esim/commit/2be75d301852b0d9008a44987de3cee112a60b03))
* send emails with related sender as per email language ([e9b1e70](https://github.com/InboundPlatform/global-esim/commit/e9b1e704d425a16b7489d2a15f58bc0f0922817b))

## [2.22.5](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.4...api-v2.22.5) (2024-11-12)


### Bug Fixes

* build issues ([47ab8cd](https://github.com/InboundPlatform/global-esim/commit/47ab8cd6138eeb72d08e0a02cdf46e10467a5fc1))

## [2.22.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.3...api-v2.22.4) (2024-11-12)


### Bug Fixes

* build issues ([52da4a2](https://github.com/InboundPlatform/global-esim/commit/52da4a27ff74e5e9922c78c1cc6954c0486cdc2b))

## [2.22.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.2...api-v2.22.3) (2024-11-11)


### Bug Fixes

* update env ([1d52a56](https://github.com/InboundPlatform/global-esim/commit/1d52a56beec017c79b38ea9bd5043f2fbbfc3f5c))

## [2.22.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.1...api-v2.22.2) (2024-11-08)


### Bug Fixes

* cache reduced to 1hr ([df86556](https://github.com/InboundPlatform/global-esim/commit/df86556785a5967017d8e4446709566762a6e405))
* mastersheet update ([0508998](https://github.com/InboundPlatform/global-esim/commit/0508998b2eddf86dec8cd0a16a7b652b071aa1ff))
* sea plans ([4d48982](https://github.com/InboundPlatform/global-esim/commit/4d48982b82aba3c53e579d9586bbfde7608e12f5))

## [2.22.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.22.0...api-v2.22.1) (2024-11-01)


### Bug Fixes

* remove auto update currency ([a670ed5](https://github.com/InboundPlatform/global-esim/commit/a670ed5795faa87d8285b4303530e7a2d24ea305))

## [2.22.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.21.0...api-v2.22.0) (2024-10-31)


### Features

* discount on top page, check user phone number on register ([bd7b146](https://github.com/InboundPlatform/global-esim/commit/bd7b146e4cb57c3f9561a2b425db4ab7e954d9aa))
* get coupon api ([2608dbe](https://github.com/InboundPlatform/global-esim/commit/2608dbe613299c817f02060f31b3bc54ea732bff))
* get coupon api ([ad1ddc1](https://github.com/InboundPlatform/global-esim/commit/ad1ddc18354316b7308bbc9d419239f1e81c7ad1))
* get coupon api ([a925552](https://github.com/InboundPlatform/global-esim/commit/a925552fe9db7560a4a7baa333b5b9f5d2dc952b))

## [2.21.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.20.0...api-v2.21.0) (2024-10-30)


### Features

* email test ([faadece](https://github.com/InboundPlatform/global-esim/commit/faadece6ef6e8330afcfa60b3f8db112bd93aa15))
* email test ([c500ee3](https://github.com/InboundPlatform/global-esim/commit/c500ee3516bda42e4dd889e983c126e0fbff8fae))


### Bug Fixes

* campaign order email send fix ([7f3c9a3](https://github.com/InboundPlatform/global-esim/commit/7f3c9a3a4e7383a38e9ae817984455cd4b67d5e5))
* campaign order email send fix ([49407ac](https://github.com/InboundPlatform/global-esim/commit/49407acf1ef04c8352f7288c72205fa9e293b222))
* gm campaign email fix ([2223d11](https://github.com/InboundPlatform/global-esim/commit/2223d11f601b3b18f9ad983b7a7f2449dbf8fc1d))
* gm campaign email fix ([0789083](https://github.com/InboundPlatform/global-esim/commit/0789083b50cfcd2a92ad6d89c23d2a737f17bc8e))
* gm campaign email fix ([813a408](https://github.com/InboundPlatform/global-esim/commit/813a40857a8bc3efdbbdcd5f9e4f2d6ece4c6365))
* gm campaign email fix ([e925364](https://github.com/InboundPlatform/global-esim/commit/e925364266a33bf336306153aeda00ecbdb36422))
* gm campaign email fix ([b41d2ae](https://github.com/InboundPlatform/global-esim/commit/b41d2aef0d330bfaf40844e3164a2fb85f233c8b))
* gm campaign email fix ([12ca784](https://github.com/InboundPlatform/global-esim/commit/12ca7843a1c9d5f807760e09be26240105aa907b))

## [2.20.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.19.1...api-v2.20.0) (2024-10-29)


### Features

* remove top banner, email template ([007a758](https://github.com/InboundPlatform/global-esim/commit/007a7584d250c23bc97216840195c91c3055a893))

## [2.19.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.19.0...api-v2.19.1) (2024-10-29)


### Bug Fixes

* export csv by month ([6a2cc61](https://github.com/InboundPlatform/global-esim/commit/6a2cc61b43e85683cf46389185606a677d772a1d))

## [2.19.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.18.3...api-v2.19.0) (2024-10-29)


### Features

* country list for mobile ([d9a631f](https://github.com/InboundPlatform/global-esim/commit/d9a631fefe1c8f959983d06166ea4dede9f6b5d9))

## [2.18.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.18.2...api-v2.18.3) (2024-10-29)


### Bug Fixes

* build issues ([e6b8748](https://github.com/InboundPlatform/global-esim/commit/e6b87485663ff0728d876e624964c17765de5221))

## [2.18.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.18.1...api-v2.18.2) (2024-10-19)


### Bug Fixes

* plans api overwritten revert ([b115d68](https://github.com/InboundPlatform/global-esim/commit/b115d680862a77dc6f2c19f72126587069507c2e))

## [2.18.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.18.0...api-v2.18.1) (2024-10-18)


### Bug Fixes

* redis port ([7b4b563](https://github.com/InboundPlatform/global-esim/commit/7b4b5634881a32b73c0fd451ecfda960c9147111))

## [2.18.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.17.1...api-v2.18.0) (2024-10-18)


### Features

* country list for mobile app ([a8ebeea](https://github.com/InboundPlatform/global-esim/commit/a8ebeea4298b793050f42bd251e3539d3293ea43))


### Bug Fixes

* changes ([80617f6](https://github.com/InboundPlatform/global-esim/commit/80617f6c0c66f60932cc049388175c8d30712e42))
* chores ([d2b0632](https://github.com/InboundPlatform/global-esim/commit/d2b0632e0747c1698c5385e6e17b19984229c56a))
* process env to config.service ([086e613](https://github.com/InboundPlatform/global-esim/commit/086e613d6e350183d6909a6442b51eb90bde8ecb))

## [2.17.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.17.0...api-v2.17.1) (2024-10-17)


### Bug Fixes

* redeploy ([d68fa9c](https://github.com/InboundPlatform/global-esim/commit/d68fa9c479daa18695ee2fcd4c463b28bba85f73))

## [2.17.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.16.1...api-v2.17.0) (2024-10-15)


### Features

* revert rakuten order template ([f5df1ba](https://github.com/InboundPlatform/global-esim/commit/f5df1ba781ae30c91e2540f33776a3f441e57213))

## [2.16.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.16.0...api-v2.16.1) (2024-10-15)


### Bug Fixes

* redeploy ([9d0f962](https://github.com/InboundPlatform/global-esim/commit/9d0f962c3446943218280af69d8945d82e701125))

## [2.16.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.15.0...api-v2.16.0) (2024-10-10)


### Features

* redeploy ([bdd33cd](https://github.com/InboundPlatform/global-esim/commit/bdd33cdfa5df224688e5d04ab85bec27c33396f0))

## [2.15.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.14.0...api-v2.15.0) (2024-10-09)


### Features

* redeploy ([1b2d6a6](https://github.com/InboundPlatform/global-esim/commit/1b2d6a64f5e1fdc43975cd1555ee0e4294d704a6))


### Bug Fixes

* log more data ([67f3922](https://github.com/InboundPlatform/global-esim/commit/67f3922895d06bd4e23a0490688eaeb206d9b25d))
* metrics for express router ([60eb599](https://github.com/InboundPlatform/global-esim/commit/60eb599056654fbe5d097a702e28cb5940d22f8a))
* metrics for express router ([901dce6](https://github.com/InboundPlatform/global-esim/commit/901dce6d667b85a5dda85061d75e010496c85058))

## [2.14.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.13.1...api-v2.14.0) (2024-10-04)


### Features

* rakuten order email template update ([20343d4](https://github.com/InboundPlatform/global-esim/commit/20343d4866211868ae20ef7185e6097644eef749))


### Bug Fixes

* end access with bracket ([e284535](https://github.com/InboundPlatform/global-esim/commit/e284535b66033d9f3778409f149dd8e3d23f1d7a))

## [2.13.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.13.0...api-v2.13.1) (2024-10-04)


### Bug Fixes

* hard code redis as temp ([e9c7b8a](https://github.com/InboundPlatform/global-esim/commit/e9c7b8ac033be6be8928d9666b87894201cbcb3d))

## [2.13.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.12.4...api-v2.13.0) (2024-10-04)


### Features

* check if discount is 0 ([0a43a77](https://github.com/InboundPlatform/global-esim/commit/0a43a776640ec8a95794f449ab6ae615e396e4ba))
* check if discount is 0 ([43edcdf](https://github.com/InboundPlatform/global-esim/commit/43edcdf18397f133919f9c9c26321ea8649b3994))
* strip stop round up for coupon price ([ac881fd](https://github.com/InboundPlatform/global-esim/commit/ac881fd09d31d656b7f6f74756ff711689187f10))


### Bug Fixes

* chore ([2fc244a](https://github.com/InboundPlatform/global-esim/commit/2fc244aaa0c282879f6e2e723028a9f67e8668d3))
* dev-test ([f9cd370](https://github.com/InboundPlatform/global-esim/commit/f9cd37011439385fe79e96159b87cbce5ccbc52e))
* docker file, esim worker only mode ([0a9f8e8](https://github.com/InboundPlatform/global-esim/commit/0a9f8e8979f3e503769f100233d39262bb9a68cc))
* docker pm2 run time and process.env.RUN_QUEUE_WORKERS ([f255315](https://github.com/InboundPlatform/global-esim/commit/f255315fde583c10160bde7e340608d9b288b242))
* enable bull board for all ([f638923](https://github.com/InboundPlatform/global-esim/commit/f638923cc9d7839178f55e86b37d1e863adff12a))
* enable cache for plans ([6ade784](https://github.com/InboundPlatform/global-esim/commit/6ade784ab0ff89fa690f42a184b0701a33087cac))
* get it from .env ([4b8e0b4](https://github.com/InboundPlatform/global-esim/commit/4b8e0b43179d8751f793eb5dc176658314425c8a))
* hard code redis configuration ([d571031](https://github.com/InboundPlatform/global-esim/commit/d5710313dfc57599a5bfb80e7fd2e86c0d16611a))
* no defination and declation ([9e8ec10](https://github.com/InboundPlatform/global-esim/commit/9e8ec1037cccac2a5d2be4e4473dbe6c1de40576))
* pm2 deployment ([33707fc](https://github.com/InboundPlatform/global-esim/commit/33707fca2276d6b4391733643e7fc815bb464781))
* worker handler ([1cd6460](https://github.com/InboundPlatform/global-esim/commit/1cd6460d9e3d2b3fb5d7d852b595246d3aa6c014))

## [2.12.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.12.3...api-v2.12.4) (2024-09-26)


### Bug Fixes

* revert ([0e6d1c6](https://github.com/InboundPlatform/global-esim/commit/0e6d1c65b0d4321b2c80be1b2bbc717372d6cebb))

## [2.12.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.12.2...api-v2.12.3) (2024-09-26)


### Bug Fixes

* hardcode credentials ([705f3dc](https://github.com/InboundPlatform/global-esim/commit/705f3dcd953a4da3dbc0b14945d58dbc2380c812))

## [2.12.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.12.1...api-v2.12.2) (2024-09-26)


### Bug Fixes

* docker update ([f9e1c1d](https://github.com/InboundPlatform/global-esim/commit/f9e1c1dde2e7b7d4722f7f49e1daeeaee88f4c93))

## [2.12.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.12.0...api-v2.12.1) (2024-09-26)


### Bug Fixes

* lgu and urocom credentials fix ([4058df3](https://github.com/InboundPlatform/global-esim/commit/4058df3d47472d1b7126f4039ee34d2c2e760b83))
* redeploy ([ad3dc92](https://github.com/InboundPlatform/global-esim/commit/ad3dc92672dc4b2b8933d8fbd4f9657b8a394d12))

## [2.12.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.11.6...api-v2.12.0) (2024-09-26)


### Features

* redeploy 3 ([e9450d5](https://github.com/InboundPlatform/global-esim/commit/e9450d54688929b3db07eb9cb7d02e779ba3d200))

## [2.11.6](https://github.com/InboundPlatform/global-esim/compare/api-v2.11.5...api-v2.11.6) (2024-09-25)


### Bug Fixes

* logs and format job id ([9b27f0a](https://github.com/InboundPlatform/global-esim/commit/9b27f0ad4ad00437cd46fa5af39a59c476f3faf9))

## [2.11.5](https://github.com/InboundPlatform/global-esim/compare/api-v2.11.4...api-v2.11.5) (2024-09-25)


### Bug Fixes

* smdp correct ([82850b8](https://github.com/InboundPlatform/global-esim/commit/82850b8eacc5434250954f41e8cc5b77ba10b042))

## [2.11.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.11.3...api-v2.11.4) (2024-09-25)


### Bug Fixes

* recover from db if job data is empty ([bd5dca8](https://github.com/InboundPlatform/global-esim/commit/bd5dca870d45455eb02e7add3dcda35953be36b4))
* recover if the newOrder is empty ([c30cb69](https://github.com/InboundPlatform/global-esim/commit/c30cb695c5d4fe727b17a5e939711da86e21ccb0))
* worker name ([dd1376f](https://github.com/InboundPlatform/global-esim/commit/dd1376fb8c249d1691e1de6ece7000517966f6f3))

## [2.11.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.11.2...api-v2.11.3) (2024-09-25)


### Bug Fixes

* build issues ([9f30cc6](https://github.com/InboundPlatform/global-esim/commit/9f30cc6ad251e107dd2f36f6a2829d2841b5eea4))
* handle worker errors and log ([9ceeab0](https://github.com/InboundPlatform/global-esim/commit/9ceeab0e887b97a1b7b3849b674609a1b752037c))

## [2.11.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.11.1...api-v2.11.2) (2024-09-25)


### Bug Fixes

* dynamic job id ([63265e1](https://github.com/InboundPlatform/global-esim/commit/63265e1a681c3123db2cf0892ff34cfe6c7fe5f1))
* job id dynamic ([8973f47](https://github.com/InboundPlatform/global-esim/commit/8973f4717d45481bd5530f5477f5aaa031e409a1))
* log on all environment ([f396723](https://github.com/InboundPlatform/global-esim/commit/f3967233b3b13c5e76a0c41c5b7257dba68b657d))

## [2.11.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.11.0...api-v2.11.1) (2024-09-25)


### Bug Fixes

* log proper messages on esim purchase ([dca42ff](https://github.com/InboundPlatform/global-esim/commit/dca42fffa56ce99a627a62fbe39822ac6f6d8d88))

## [2.11.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.18...api-v2.11.0) (2024-09-25)


### Features

* redeploy ([8078100](https://github.com/InboundPlatform/global-esim/commit/8078100120442a76e958ea6413d74ce0a83f4627))


### Bug Fixes

* build issue ([50f5056](https://github.com/InboundPlatform/global-esim/commit/50f5056e6e90c54159e837a5e6a8a77060db0d47))

## [2.10.18](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.17...api-v2.10.18) (2024-09-25)


### Bug Fixes

* remove app env ([1376a6a](https://github.com/InboundPlatform/global-esim/commit/1376a6a7a126c2edc1310a1f0b2ab77cb3a20522))
* remove app env dev ([a40b864](https://github.com/InboundPlatform/global-esim/commit/a40b864b964727853d07fa86615c2ad3e3ece2b9))

## [2.10.17](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.16...api-v2.10.17) (2024-09-25)


### Bug Fixes

* queue back ([360aff0](https://github.com/InboundPlatform/global-esim/commit/360aff0d7970badd8b10e1c6eae0b05224f968be))

## [2.10.16](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.15...api-v2.10.16) (2024-09-25)


### Bug Fixes

* queue back ([0b4481c](https://github.com/InboundPlatform/global-esim/commit/0b4481c64208f59a211360ab986f6c17125ac592))

## [2.10.15](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.14...api-v2.10.15) (2024-09-25)


### Bug Fixes

* build issues ([71b94a1](https://github.com/InboundPlatform/global-esim/commit/71b94a1b53d5737255bbe827993cfecfdc27a2e5))

## [2.10.14](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.13...api-v2.10.14) (2024-09-25)


### Bug Fixes

* redeploy ([35614b0](https://github.com/InboundPlatform/global-esim/commit/35614b089f8196acba3b9e1a3c44700bd4b1b69d))

## [2.10.13](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.12...api-v2.10.13) (2024-09-24)


### Bug Fixes

* rand ([5d3da08](https://github.com/InboundPlatform/global-esim/commit/5d3da08bf2994ab11232b4ad4ce74228d7f23285))

## [2.10.12](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.11...api-v2.10.12) (2024-09-24)


### Bug Fixes

* app env ([c82a4b4](https://github.com/InboundPlatform/global-esim/commit/c82a4b49ca414cce1f7d4c15965ed027170bfb19))

## [2.10.11](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.10...api-v2.10.11) (2024-09-24)


### Bug Fixes

* handle error ([d3297bc](https://github.com/InboundPlatform/global-esim/commit/d3297bc13cf42d58142def4a8c358fa47cd766f7))

## [2.10.10](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.9...api-v2.10.10) (2024-09-22)


### Bug Fixes

* remove queue for direct payment as well ([63433f7](https://github.com/InboundPlatform/global-esim/commit/63433f71cd2555aa5ccfcfed079d45baef4c86cb))

## [2.10.9](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.8...api-v2.10.9) (2024-09-22)


### Bug Fixes

* make it 15secs ([dbe2292](https://github.com/InboundPlatform/global-esim/commit/dbe22927d57276b1b2f85057c9623f812a50b4ef))

## [2.10.8](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.7...api-v2.10.8) (2024-09-22)


### Bug Fixes

* cache issue re enabled ([04cdcd1](https://github.com/InboundPlatform/global-esim/commit/04cdcd11f8937a0d0d47b6686abe4046b047dea5))

## [2.10.7](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.6...api-v2.10.7) (2024-09-22)


### Bug Fixes

* remove queue at all ([176fd53](https://github.com/InboundPlatform/global-esim/commit/176fd53fb6b21a94eb5af2f6b7cf210a1e1151b4))

## [2.10.6](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.5...api-v2.10.6) (2024-09-21)


### Bug Fixes

* do all work on in single queue ([6b4ee5d](https://github.com/InboundPlatform/global-esim/commit/6b4ee5d69037ca977c055192cab15815df23be90))

## [2.10.5](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.4...api-v2.10.5) (2024-09-20)


### Bug Fixes

* api cleanup with logs ([ba63634](https://github.com/InboundPlatform/global-esim/commit/ba636348e2627fa3fe291595ed7f67b559e32859))
* code cleanup ([2fa2af7](https://github.com/InboundPlatform/global-esim/commit/2fa2af766dab0d03edc63f3b7e935c9e3084213f))

## [2.10.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.3...api-v2.10.4) (2024-09-20)


### Bug Fixes

* restrict user who dont have email address ([f9ea209](https://github.com/InboundPlatform/global-esim/commit/f9ea209ed9237a984bc498a48064af087f8170df))

## [2.10.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.2...api-v2.10.3) (2024-09-20)


### Bug Fixes

* mail formatter ([0b60426](https://github.com/InboundPlatform/global-esim/commit/0b60426667b9226f6e3650eff48cc3cd1f07d46f))

## [2.10.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.1...api-v2.10.2) (2024-09-20)


### Bug Fixes

* minor fixes for email ([7fed71e](https://github.com/InboundPlatform/global-esim/commit/7fed71e76ce75b6bdbc4c716646f2dde2cbcb609))
* minor fixes for email ([214ac90](https://github.com/InboundPlatform/global-esim/commit/214ac907a62f1848b57009e8d103addc6b7d2a88))

## [2.10.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.10.0...api-v2.10.1) (2024-09-20)


### Bug Fixes

* handle lgu token expiry ([4112efe](https://github.com/InboundPlatform/global-esim/commit/4112efe0128b6f12eb2563404561dcad291d6150))
* handle undefined access of esim for lgu provider ([2b30370](https://github.com/InboundPlatform/global-esim/commit/2b303707987d637321306cc32d581558235f7181))
* update user access ([038a7c1](https://github.com/InboundPlatform/global-esim/commit/038a7c122d497310cbb2fbc115d891f2d2e20cf4))
* user id checking wrong order ([b36a84a](https://github.com/InboundPlatform/global-esim/commit/b36a84a8111b999fa013cbf7eff74e0e60a25da2))

## [2.10.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.9.2...api-v2.10.0) (2024-09-12)


### Features

* get iccid on complete page for guestc ([5e35c70](https://github.com/InboundPlatform/global-esim/commit/5e35c702c3bf0c9c3e50865ec35781ee85866e6a))
* remove iccid on thank you page ([456c6e3](https://github.com/InboundPlatform/global-esim/commit/456c6e327c0f6471c7a715d8df8cffe1363eee20))
* reset guest user password ([1d66447](https://github.com/InboundPlatform/global-esim/commit/1d66447ac94056f6bee167c1bf094d42d8ac9e72))
* reset password for guest ([e6a53c1](https://github.com/InboundPlatform/global-esim/commit/e6a53c1b4ccdc345606b645b36b5eed0852f4030))


### Bug Fixes

* reset email logic fixes ([6199079](https://github.com/InboundPlatform/global-esim/commit/619907971b14fe50fa1c125afe26b71f5354dee9))

## [2.9.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.9.1...api-v2.9.2) (2024-09-11)


### Bug Fixes

* urocomm campaign key mis matched ([6568873](https://github.com/InboundPlatform/global-esim/commit/65688732a8c6185258e17fe45aa8e5fa630a3de2))

## [2.9.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.9.0...api-v2.9.1) (2024-09-09)


### Bug Fixes

* minor fixes ([a41dbb5](https://github.com/InboundPlatform/global-esim/commit/a41dbb5e3364327edddb287679417431d7eafd48))

## [2.9.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.8.3...api-v2.9.0) (2024-09-09)


### Features

* rakuten order email template updates ([119a9bd](https://github.com/InboundPlatform/global-esim/commit/119a9bd5c5542295749330757e18f91713776ec8))

## [2.8.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.8.2...api-v2.8.3) (2024-09-07)


### Bug Fixes

* api hot fixes ([14a74d9](https://github.com/InboundPlatform/global-esim/commit/14a74d99dd61ce61a00566884d9ef30e5f2c7cc9))

## [2.8.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.8.1...api-v2.8.2) (2024-09-07)


### Bug Fixes

* api and queue issue ([e1d516d](https://github.com/InboundPlatform/global-esim/commit/e1d516de7a578a514679a0b5f8432f98950ad05e))
* email of undefined ([545917c](https://github.com/InboundPlatform/global-esim/commit/545917c75246ae497440f126efb0712581e34c15))
* stable redis connection ([8e82cf9](https://github.com/InboundPlatform/global-esim/commit/8e82cf9f4651fc4205a3524261abfe5baab1e54e))

## [2.8.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.8.0...api-v2.8.1) (2024-09-04)


### Bug Fixes

* queue issue again ([2fd832f](https://github.com/InboundPlatform/global-esim/commit/2fd832f012f69a7f7f72850c592298a69b5bea86))

## [2.8.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.8...api-v2.8.0) (2024-09-04)


### Features

* remove recaptcha skip for mobile users on subscribe ([1ae16d3](https://github.com/InboundPlatform/global-esim/commit/1ae16d3fee2166b366d8cb4120e13a5b3b005f4b))

## [2.7.8](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.7...api-v2.7.8) (2024-08-30)


### Bug Fixes

* disable too busy ([047c469](https://github.com/InboundPlatform/global-esim/commit/047c469de74140f99e60351ed0ad5f1c684a2ef1))
* handle max 10 worker ([8a1362d](https://github.com/InboundPlatform/global-esim/commit/8a1362d077e89ccee668a9cfa3cac6a3c4d71111))
* queue refactor ([87569f9](https://github.com/InboundPlatform/global-esim/commit/87569f927bf85e3c1c183428a21c7c61b4d12d0e))
* two at a time ([107dd7c](https://github.com/InboundPlatform/global-esim/commit/107dd7c95092ba1b044b82609785b8c4ab1353b3))

## [2.7.7](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.6...api-v2.7.7) (2024-08-29)


### Bug Fixes

* webhook in same queue ([cf79afe](https://github.com/InboundPlatform/global-esim/commit/cf79afef0b468639a5956c24b9db6412f2129036))

## [2.7.6](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.5...api-v2.7.6) (2024-08-29)


### Bug Fixes

* queue hotfix ([89a4ed0](https://github.com/InboundPlatform/global-esim/commit/89a4ed0663a5a72d94337f38777b3613a735708b))

## [2.7.5](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.4...api-v2.7.5) (2024-08-29)


### Bug Fixes

* add log from new queue processor ([ed9bdb6](https://github.com/InboundPlatform/global-esim/commit/ed9bdb6f57a0e91ba86a12b086d173bf24844901))
* api ([18ea82e](https://github.com/InboundPlatform/global-esim/commit/18ea82ef72ac8a4fbfbd4e96c2fb1b0483daad18))
* db index ([4a78c70](https://github.com/InboundPlatform/global-esim/commit/4a78c70d11c6e63e77c7c38ee2b676d67126da02))

## [2.7.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.3...api-v2.7.4) (2024-08-29)


### Bug Fixes

* log error data and messages ([c710f5a](https://github.com/InboundPlatform/global-esim/commit/c710f5a7aad74bb7eae739c39efd5e7f10e0c9ba))

## [2.7.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.2...api-v2.7.3) (2024-08-29)


### Bug Fixes

* if lgu access token is corrupted then try for new token ([50e0f62](https://github.com/InboundPlatform/global-esim/commit/50e0f62d33d487f5ee96a00d2a364f5e89b48ef9))
* log error for some special cases ([50e0f62](https://github.com/InboundPlatform/global-esim/commit/50e0f62d33d487f5ee96a00d2a364f5e89b48ef9))
* some task ([71ea4a3](https://github.com/InboundPlatform/global-esim/commit/71ea4a3be73741152c405dd6c550c2d48c4e2318))

## [2.7.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.1...api-v2.7.2) (2024-08-26)


### Bug Fixes

* force usage update ([0f13bed](https://github.com/InboundPlatform/global-esim/commit/0f13bedd244bba0d7b917803a144f0e2648fa9a9))
* usage and queue hotifxes ([e32e61f](https://github.com/InboundPlatform/global-esim/commit/e32e61f793aa6c2e26a57c6410b6cc7b31fcf7bd))

## [2.7.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.7.0...api-v2.7.1) (2024-08-23)


### Bug Fixes

* migration issue with lgu ([216e0e9](https://github.com/InboundPlatform/global-esim/commit/216e0e9b42ce932a0e5b663866451e07200cde74))

## [2.7.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.6.0...api-v2.7.0) (2024-08-23)


### Features

* add recaptcha to signup ([3d896e2](https://github.com/InboundPlatform/global-esim/commit/3d896e2f87d2692c36324ddc0e59f639e20957c3))


### Bug Fixes

* add lgu order temps schema on table ([0128501](https://github.com/InboundPlatform/global-esim/commit/0128501ebeaabdc02a6ea0c1dc3da61081e8cb48))

## [2.6.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.5.1...api-v2.6.0) (2024-08-22)


### Features

* text update, setup page link update ([b879013](https://github.com/InboundPlatform/global-esim/commit/b8790135e92fb65eccc1f57f324c354547d2c9f7))
* update airtrip order email ([dac657e](https://github.com/InboundPlatform/global-esim/commit/dac657edeed6d8489bc87df2e1ea9aea5eb23eec))


### Bug Fixes

* lgu usage fixes ([687c474](https://github.com/InboundPlatform/global-esim/commit/687c474632352de61a17543e8d73f9385f0f629d))
* plans order fix ([b27fe2a](https://github.com/InboundPlatform/global-esim/commit/b27fe2a788abca55744c27ad9aeed82d8be49235))
* plans order fix ([b27fe2a](https://github.com/InboundPlatform/global-esim/commit/b27fe2a788abca55744c27ad9aeed82d8be49235))
* plans order fix ([b27fe2a](https://github.com/InboundPlatform/global-esim/commit/b27fe2a788abca55744c27ad9aeed82d8be49235))
* plans order fix ([b27fe2a](https://github.com/InboundPlatform/global-esim/commit/b27fe2a788abca55744c27ad9aeed82d8be49235))
* plans order fix ([b27fe2a](https://github.com/InboundPlatform/global-esim/commit/b27fe2a788abca55744c27ad9aeed82d8be49235))
* plans order fix ([c9f2544](https://github.com/InboundPlatform/global-esim/commit/c9f254430420058a4762888532e01aee3bd0fe30))
* usage fixes for LGU ([5aa6879](https://github.com/InboundPlatform/global-esim/commit/5aa6879d164c2a0c23810d1995f1a94faf2e400d))

## [2.5.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.5.0...api-v2.5.1) (2024-08-16)


### Bug Fixes

* build issues api ([94d5b12](https://github.com/InboundPlatform/global-esim/commit/94d5b12a8207371273d628280f8b90279b12155e))

## [2.5.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.4.0...api-v2.5.0) (2024-08-16)


### Features

* add airtrip campaign email template ([2fe407d](https://github.com/InboundPlatform/global-esim/commit/2fe407d44bf6a775dc510672f1c4b87ca082f618))
* add airtrip campaign email template ([2fe407d](https://github.com/InboundPlatform/global-esim/commit/2fe407d44bf6a775dc510672f1c4b87ca082f618))
* airtrip campaign email subject ([fc2ab57](https://github.com/InboundPlatform/global-esim/commit/fc2ab57eadd79e7ea5cb0262d907b01122dd2393))
* airtrip campaign email subject ([3a7ec86](https://github.com/InboundPlatform/global-esim/commit/3a7ec861f0f279d2e6214537894ee32f108e42a4))
* change airtrip logo to png ([f276a91](https://github.com/InboundPlatform/global-esim/commit/f276a91e3572d6b3b542c2651e192a1dbf7a564a))


### Bug Fixes

* booking no clash fixes ([93bb9cc](https://github.com/InboundPlatform/global-esim/commit/93bb9ccbf916539072f886d5b8cc548b43a778a3))
* build errors ([1a4dc51](https://github.com/InboundPlatform/global-esim/commit/1a4dc51e4f9958c9db54e57f8fa0e9ccae39e7a9))
* update allowed user ([e48fac4](https://github.com/InboundPlatform/global-esim/commit/e48fac4dc1191437aa252378495e33b8c2ffb6ba))

## [2.4.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.3.2...api-v2.4.0) (2024-08-15)


### Features

* add campaign text to rakuten order mail ([b8892bf](https://github.com/InboundPlatform/global-esim/commit/b8892bf62b2e12aa2a926b17d969dd8a97928580))
* add campaign text to rakuten order mail ([d23624b](https://github.com/InboundPlatform/global-esim/commit/d23624be1219bdf879ea06b93fb7426bb957ca63))
* add order activation details to mail ([066b474](https://github.com/InboundPlatform/global-esim/commit/066b474f5afe19c9ec4c5ad9f31fceb49c34f01b))
* add setup page link to rakuten order mail ([f5645ac](https://github.com/InboundPlatform/global-esim/commit/f5645ac550bf94f0e6a3dd17d63a6a414a3d00fb))


### Bug Fixes

* add new urocmm campaign provider ([dd741a5](https://github.com/InboundPlatform/global-esim/commit/dd741a501693bd211912ebd9de1e05c1080e83d5))
* add vendor as new column ([631ae2f](https://github.com/InboundPlatform/global-esim/commit/631ae2fc1e2db1acb35b7b4aa1cea22d4ce9c742))
* order mail translation fixes ([69690da](https://github.com/InboundPlatform/global-esim/commit/69690da6a32e698eba7f329a94316ad8258dd52b))
* order mail translation fixes ([0e9eed8](https://github.com/InboundPlatform/global-esim/commit/0e9eed89fc85974cd4207685d554c4b662f713d5))
* rakuten order mail translation fixes ([1d980f9](https://github.com/InboundPlatform/global-esim/commit/1d980f9f89d25c0f79e3c635db86e108261e372e))

## [2.3.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.3.1...api-v2.3.2) (2024-08-07)


### Bug Fixes

* change payment details for stripe ([34b7f5d](https://github.com/InboundPlatform/global-esim/commit/34b7f5ddcddb090ba7c2308f108e7b6785a8d61b))

## [2.3.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.3.0...api-v2.3.1) (2024-08-07)


### Bug Fixes

* api build ([dffa6dc](https://github.com/InboundPlatform/global-esim/commit/dffa6dc57455891930f2609e9425d6bff17484be))

## [2.3.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.2.1...api-v2.3.0) (2024-08-07)


### Features

* 50945 login after sms verification ([66ac929](https://github.com/InboundPlatform/global-esim/commit/66ac929da8ccb464348e9a703abe679b87157023))
* add iccid to order complete page and order confirm mail ([695d8f8](https://github.com/InboundPlatform/global-esim/commit/695d8f8d5f598065ae07487dfcb183cbb3632511))


### Bug Fixes

* hide social login button on campaign page ([15292b9](https://github.com/InboundPlatform/global-esim/commit/15292b9d9768a91972890a1a3ad680163feb8833))
* improve error message on campaign page ([0829f2d](https://github.com/InboundPlatform/global-esim/commit/0829f2d7352cfad73fd768ed0e14676fd0478e7a))
* social login and authentication issues ([007aba9](https://github.com/InboundPlatform/global-esim/commit/007aba974462e20d9278202a74ff1d072854a6cb))
* social login issues ([be158d2](https://github.com/InboundPlatform/global-esim/commit/be158d29ccc8972182354848a68ff0c9d3bb2b1e))
* south africa translation fix ([cf74570](https://github.com/InboundPlatform/global-esim/commit/cf74570c59f2071abdee7cd3ea41cae977959c31))

## [2.2.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.2.0...api-v2.2.1) (2024-07-29)


### Bug Fixes

* remove spaces in country names ([8c73ab1](https://github.com/InboundPlatform/global-esim/commit/8c73ab197496cd3f9c076e09ebd806b07c61ed2e))

## [2.2.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.1.0...api-v2.2.0) (2024-07-26)


### Features

* 50998 recaptcha be ([6ea80d1](https://github.com/InboundPlatform/global-esim/commit/6ea80d1ba1cf665939511ce49d28073960df96b1))
* skip app recaptcha validation ([1a3e4e6](https://github.com/InboundPlatform/global-esim/commit/1a3e4e60e3b236146ffb20d6218b3867cf5183ef))


### Bug Fixes

* added todo, and values fixes ([c3d8b4f](https://github.com/InboundPlatform/global-esim/commit/c3d8b4f6fbed5647c705cb10c633384d5eba4683))

## [2.1.0](https://github.com/InboundPlatform/global-esim/compare/api-v2.0.4...api-v2.1.0) (2024-07-23)


### Features

* add rakuten custom email template and subject ([3ac83f4](https://github.com/InboundPlatform/global-esim/commit/3ac83f4ed05df7fe9f89d83c5fdb367a195fcc53))
* added migration for apps metadata ([de412ce](https://github.com/InboundPlatform/global-esim/commit/de412ce305ad9948e804f50507d82ad4ca590b48))
* update apps.rakuten metadata ([dae9b93](https://github.com/InboundPlatform/global-esim/commit/dae9b93bd2eb93b0c435719f65f92a0b20efc59d))


### Bug Fixes

* apps metadata values update ([bed1ef8](https://github.com/InboundPlatform/global-esim/commit/bed1ef87b9257904bc3d2be9cd7dfa0541faf943))
* send channel name as per apps id ([8ccf1c7](https://github.com/InboundPlatform/global-esim/commit/8ccf1c75879ea40b68acad4e1e308f78211b3eca))
* send missing countryCode while purchased through web ([8ccf1c7](https://github.com/InboundPlatform/global-esim/commit/8ccf1c75879ea40b68acad4e1e308f78211b3eca))

## [2.0.4](https://github.com/InboundPlatform/global-esim/compare/api-v2.0.3...api-v2.0.4) (2024-07-19)


### Bug Fixes

* activation code lgu ([6f86954](https://github.com/InboundPlatform/global-esim/commit/6f86954019cada60f19957dde3cd8675ecb1d5a8))

## [2.0.3](https://github.com/InboundPlatform/global-esim/compare/api-v2.0.2...api-v2.0.3) (2024-07-19)


### Bug Fixes

* language, dob and email subject issue ([088b3f0](https://github.com/InboundPlatform/global-esim/commit/088b3f0c11d685924b074e32a1a4be9c46daa155))

## [2.0.2](https://github.com/InboundPlatform/global-esim/compare/api-v2.0.1...api-v2.0.2) (2024-07-19)


### Bug Fixes

* mandatory dob issues ([2ccedcb](https://github.com/InboundPlatform/global-esim/commit/2ccedcb880a27a4ff4be01488bc051aa854b1749))

## [2.0.1](https://github.com/InboundPlatform/global-esim/compare/api-v2.0.0...api-v2.0.1) (2024-07-19)


### Bug Fixes

* hotfix email dispatcher issuer ([9402c2c](https://github.com/InboundPlatform/global-esim/commit/9402c2c70b26e0568d694977cab7316dda7f6292))

## [2.0.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.8.2...api-v2.0.0) (2024-07-19)


### ⚠ BREAKING CHANGES

* API v2

### Bug Fixes

* add service provider and validity days infornmation on master sheet export ([ebfb997](https://github.com/InboundPlatform/global-esim/commit/ebfb997a3a0eca5f067c3c7014ec0096946e4184))
* change order details information as request and to fit with unlimited plans on confirmation email ([1b0fedf](https://github.com/InboundPlatform/global-esim/commit/1b0fedf72e17036e8be84243875720b6f05f07ca))
* make expiry days 90 for korea and 30 for rest ([1b0fedf](https://github.com/InboundPlatform/global-esim/commit/1b0fedf72e17036e8be84243875720b6f05f07ca))
* no user found issue ([e5e9e85](https://github.com/InboundPlatform/global-esim/commit/e5e9e857769a979e2693dcb8d3d1ff5fb268d1e8))
* send subject in multilingual from only english ([1b0fedf](https://github.com/InboundPlatform/global-esim/commit/1b0fedf72e17036e8be84243875720b6f05f07ca))

## [1.8.2](https://github.com/InboundPlatform/global-esim/compare/api-v1.8.1...api-v1.8.2) (2024-07-18)


### Bug Fixes

* email dispatcher issue ([09f3aea](https://github.com/InboundPlatform/global-esim/commit/09f3aea3315489e1937ae9fa4aeb6373d7929c39))

## [1.8.1](https://github.com/InboundPlatform/global-esim/compare/api-v1.8.0...api-v1.8.1) (2024-07-18)


### Bug Fixes

* add auth signin/signup back to webjp ([b0590d6](https://github.com/InboundPlatform/global-esim/commit/b0590d67279f58bd7bff9096a69a96f097dfc3be))
* add support email in bcc for confirmation ([e46c32d](https://github.com/InboundPlatform/global-esim/commit/e46c32ddec21ebd24466b57b144774e65972f113))
* corporate permission issue ([1a99659](https://github.com/InboundPlatform/global-esim/commit/1a99659494b8335c88f463fe63715faf4839b109))
* email country text translation fix ([2560ff4](https://github.com/InboundPlatform/global-esim/commit/2560ff4203867e1063fb0a7f24673c1bc58404b2))
* email country text translation fix ([2560ff4](https://github.com/InboundPlatform/global-esim/commit/2560ff4203867e1063fb0a7f24673c1bc58404b2))
* email order translation fix ([57d82f2](https://github.com/InboundPlatform/global-esim/commit/57d82f29ee7123a0ef4780fc66d2dc49ee6fd2e1))
* issue on email verification ([fded479](https://github.com/InboundPlatform/global-esim/commit/fded4791243a293fe634de1548f5bca52d3893e8))
* qr code information extraction from lgu provider ([7b47c52](https://github.com/InboundPlatform/global-esim/commit/7b47c526f84ce8f74f8cb0dd4f719dd0e299b546))
* wip ([095d7c3](https://github.com/InboundPlatform/global-esim/commit/095d7c35e51ce02c5fee2bad706dd86edb28943c))

## [1.8.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.7.4...api-v1.8.0) (2024-07-11)


### Features

* add cmlink register link for hongkong taiwan order mail ([f408635](https://github.com/InboundPlatform/global-esim/commit/f408635a652bc9fc5e6085a41cd59e05cfede6a0))


### Bug Fixes

* feedback fixes 07/11 ([c816c1b](https://github.com/InboundPlatform/global-esim/commit/c816c1bc907b40e338b1748b3a150ad941f70aa5))
* missing cmlink registration email link ([9f24c87](https://github.com/InboundPlatform/global-esim/commit/9f24c8789f873e62c8ded192e162d02031c57c68))
* missing cmlink registration email link ([9f24c87](https://github.com/InboundPlatform/global-esim/commit/9f24c8789f873e62c8ded192e162d02031c57c68))
* missing cmlink registration email link ([9f24c87](https://github.com/InboundPlatform/global-esim/commit/9f24c8789f873e62c8ded192e162d02031c57c68))
* missing cmlink registration email link ([9f24c87](https://github.com/InboundPlatform/global-esim/commit/9f24c8789f873e62c8ded192e162d02031c57c68))

## [1.7.4](https://github.com/InboundPlatform/global-esim/compare/api-v1.7.3...api-v1.7.4) (2024-07-03)


### Bug Fixes

* 50839 set api first name and last name min length to 1 ([38c81bc](https://github.com/InboundPlatform/global-esim/commit/38c81bcad2ecac8e8deafebe7c7afeaeda329317))

## [1.7.3](https://github.com/InboundPlatform/global-esim/compare/api-v1.7.2...api-v1.7.3) (2024-06-28)


### Bug Fixes

* change way of updating plans and standardize the process of handling plans by business team only ([d0d52a4](https://github.com/InboundPlatform/global-esim/commit/d0d52a49ab418e404f8b105b8062cef35fbdd47b))
* code cleanup ([d5c93c2](https://github.com/InboundPlatform/global-esim/commit/d5c93c2daad36ecb1eef6ead5ffd3291105d2457))
* forgot password api error catch ([9eb67b8](https://github.com/InboundPlatform/global-esim/commit/9eb67b815c6ca2c022ba025aaed7c87772736750))

## [1.7.2](https://github.com/InboundPlatform/global-esim/compare/api-v1.7.1...api-v1.7.2) (2024-06-28)


### Bug Fixes

* change way of updating plans and standardize the process of handling plans by business team only ([d0d52a4](https://github.com/InboundPlatform/global-esim/commit/d0d52a49ab418e404f8b105b8062cef35fbdd47b))
* esim web fixes ([eaccd80](https://github.com/InboundPlatform/global-esim/commit/eaccd8006d2d9783b6364a78f4eca17824567be8))
* forgot password api error catch ([9eb67b8](https://github.com/InboundPlatform/global-esim/commit/9eb67b815c6ca2c022ba025aaed7c87772736750))

## [1.7.2](https://github.com/InboundPlatform/global-esim/compare/api-v1.7.1...api-v1.7.2) (2024-06-24)


### Bug Fixes

* esim web fixes ([eaccd80](https://github.com/InboundPlatform/global-esim/commit/eaccd8006d2d9783b6364a78f4eca17824567be8))

## [1.7.1](https://github.com/InboundPlatform/global-esim/compare/api-v1.7.0...api-v1.7.1) (2024-06-24)


### Bug Fixes

* global plans unlimited add ([c6b350b](https://github.com/InboundPlatform/global-esim/commit/c6b350bf5a1db8465a847ceaf0b6b606f2c276e5))

## [1.7.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.6.1...api-v1.7.0) (2024-06-24)


### Features

* add number of countries on featured plans list ([4836b25](https://github.com/InboundPlatform/global-esim/commit/4836b2572dbcdce85cee21b9736f6e21fb15e088))


### Bug Fixes

* add new unlimited plans for different countries ([d3045d5](https://github.com/InboundPlatform/global-esim/commit/d3045d540c4fb18e62e92203a8bb4874c071b425))
* add user source in the users table to identify their source of login ([97cfb05](https://github.com/InboundPlatform/global-esim/commit/97cfb05f8dd8d3d6ab7e66a5168195aa82c3e338))
* auth redirection fixes for airtrip esim ([7b86521](https://github.com/InboundPlatform/global-esim/commit/7b86521a6c9821ddf362b37d311e01c1f1084869))
* feedback fixes 06/19 ([79d7dc6](https://github.com/InboundPlatform/global-esim/commit/79d7dc6fbd9fa17cb14514bb37a54b68606c08a9))
* japan plans 3 gb price updates ([d3045d5](https://github.com/InboundPlatform/global-esim/commit/d3045d540c4fb18e62e92203a8bb4874c071b425))
* take param airtrip on signup ([7b86521](https://github.com/InboundPlatform/global-esim/commit/7b86521a6c9821ddf362b37d311e01c1f1084869))

## [1.6.1](https://github.com/InboundPlatform/global-esim/compare/api-v1.6.0...api-v1.6.1) (2024-06-14)


### Bug Fixes

* build issues ([bc6175b](https://github.com/InboundPlatform/global-esim/commit/bc6175bfa9d4017372577918fb33743d28b32ad5))

## [1.6.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.5.1...api-v1.6.0) (2024-06-14)


### Features

* add countries code in the api of plans ([75463ce](https://github.com/InboundPlatform/global-esim/commit/75463ce54a2e10e1b3d65af3ef58fc2bf24e7fbc))
* add payment method in the subscribe api ([75463ce](https://github.com/InboundPlatform/global-esim/commit/75463ce54a2e10e1b3d65af3ef58fc2bf24e7fbc))


### Bug Fixes

* 2 gb chore plan updates ([5e36741](https://github.com/InboundPlatform/global-esim/commit/5e36741d2cb60415c37f354284c4c71dfc12db87))

## [1.5.1](https://github.com/InboundPlatform/global-esim/compare/api-v1.5.0...api-v1.5.1) (2024-06-07)


### Bug Fixes

* update 3gb plans pricing ([c6316e6](https://github.com/InboundPlatform/global-esim/commit/c6316e6d7fd1a9b31026a31317553ab5e539ac20))

## [1.5.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.4.2...api-v1.5.0) (2024-06-06)


### Features

* add customer-sheet api for mobile app ([bfd833e](https://github.com/InboundPlatform/global-esim/commit/bfd833ec3c461750ffb4c7164098ae23ce11cc3c))
* add email check api endpoint ([eb7d31d](https://github.com/InboundPlatform/global-esim/commit/eb7d31d0ef732cb8e0e483f64105570f9aa17b69))
* add new zealand, canada and guam esim plan ([a8d3f38](https://github.com/InboundPlatform/global-esim/commit/a8d3f3887a4da927648dc0d82b84f0b03a202263))


### Bug Fixes

* renable some disabled countries ([b5e70b0](https://github.com/InboundPlatform/global-esim/commit/b5e70b060d448ad6170563ee81d993c3d4bfa346))
* transform iccid to string because of urocomm api changes ([7c4d1c9](https://github.com/InboundPlatform/global-esim/commit/7c4d1c9519663449a5be0e9388e7e1f683442b8a))

## [1.4.2](https://github.com/InboundPlatform/global-esim/compare/api-v1.4.1...api-v1.4.2) (2024-05-28)


### Bug Fixes

* urocomm hot fix ([582d347](https://github.com/InboundPlatform/global-esim/commit/582d347d76f4456fa07f3177f604e1ba91e0e122))

## [1.4.1](https://github.com/InboundPlatform/global-esim/compare/api-v1.4.0...api-v1.4.1) (2024-05-24)


### Bug Fixes

* language issues ([e5d6d7b](https://github.com/InboundPlatform/global-esim/commit/e5d6d7b8b8b92e7d5de82240c4e76201bb176949))
* not redirected to proper service after email verification ([e208b32](https://github.com/InboundPlatform/global-esim/commit/e208b3243aa58984c905cb867276585615568e1b))
* redirect to correct locale page after verification ([3d5db29](https://github.com/InboundPlatform/global-esim/commit/3d5db292f39b02acd082ecea78173713d28fb6cd))
* redirect to correct service after email verification ([6830e05](https://github.com/InboundPlatform/global-esim/commit/6830e05da0531e731cc3a4c8bdfbe7480235f0f8))

## [1.4.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.3.0...api-v1.4.0) (2024-05-15)


### Features

* add locales in public so can accessed for mobile app ([6bf6d84](https://github.com/InboundPlatform/global-esim/commit/6bf6d841ed1a354752accee798d39e2c9d45c1c3))
* add new compaitbility json public for mobile app ([6bf6d84](https://github.com/InboundPlatform/global-esim/commit/6bf6d841ed1a354752accee798d39e2c9d45c1c3))


### Bug Fixes

* add error codes for login api ([6bf6d84](https://github.com/InboundPlatform/global-esim/commit/6bf6d841ed1a354752accee798d39e2c9d45c1c3))
* disable sending ukomi email once order is completed ([6bf6d84](https://github.com/InboundPlatform/global-esim/commit/6bf6d841ed1a354752accee798d39e2c9d45c1c3))
* wip ([023bffc](https://github.com/InboundPlatform/global-esim/commit/023bffcf059c0de971bececcc860bcbbe3bf071b))

## [1.3.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.2.2...api-v1.3.0) (2024-05-09)


### Features

* add new fixed plans for japan ([fc225fc](https://github.com/InboundPlatform/global-esim/commit/fc225fc0161dac7065b2443db5a9158f8cca54c1))


### Bug Fixes

* remove '&lt;br/&gt;' from description and remove region ([00a4f59](https://github.com/InboundPlatform/global-esim/commit/00a4f59cfe6c93fd30e33aca7ec0d16300594ea7))
* update test plan id for dev environment ([5705225](https://github.com/InboundPlatform/global-esim/commit/5705225f6a049e06a08bfc610f49ef39e00c8472))

## [1.2.2](https://github.com/InboundPlatform/global-esim/compare/api-v1.2.1...api-v1.2.2) (2024-05-01)


### Bug Fixes

* featured price rounded to nearest value instead of default 100 ([8a13e8c](https://github.com/InboundPlatform/global-esim/commit/8a13e8cccdb2c89a2cfa7455eaae268c7f47f4db))
* hard code plans for featured plans ([1a434f0](https://github.com/InboundPlatform/global-esim/commit/1a434f0bd906c26f49a295ff93da27093b8fae58))
* remove duplicate 500mb japan esim plans ([4b40c88](https://github.com/InboundPlatform/global-esim/commit/4b40c88bf15ddc95cd0ff1bb316b9bf41a5b6061))
* update network qos value to standard and consistent ([4b40c88](https://github.com/InboundPlatform/global-esim/commit/4b40c88bf15ddc95cd0ff1bb316b9bf41a5b6061))
* update price of plans shown on homepage, make it more cheaper with given formula ([4b40c88](https://github.com/InboundPlatform/global-esim/commit/4b40c88bf15ddc95cd0ff1bb316b9bf41a5b6061))

## [1.2.1](https://github.com/InboundPlatform/global-esim/compare/api-v1.2.0...api-v1.2.1) (2024-04-26)


### Bug Fixes

* add missing plans of usa,korea and asia ([8b9e25b](https://github.com/InboundPlatform/global-esim/commit/8b9e25ba32a8a7b8f38d76a817c62a5f2b8bae99))

## [1.2.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.1.0...api-v1.2.0) (2024-04-25)


### Features

* now jpy prices are round up ([9830d93](https://github.com/InboundPlatform/global-esim/commit/9830d93f079994dc30d9e4ea1651049688a608c2))


### Bug Fixes

* activation code not reflected correctly ([0552e7d](https://github.com/InboundPlatform/global-esim/commit/0552e7d3bb87f16a96c0a6c7fb6636b4eea46c3c))
* make github actions production ready ([9830d93](https://github.com/InboundPlatform/global-esim/commit/9830d93f079994dc30d9e4ea1651049688a608c2))
* merge issues ([f601270](https://github.com/InboundPlatform/global-esim/commit/f6012700eef02cbb83cb5f9e9f535746098e1344))
* new urocomm plans ([9d7eb4f](https://github.com/InboundPlatform/global-esim/commit/9d7eb4f4a34281eaec20924a2c3b5b1956d4a084))
* payload is not defined ([a3460ff](https://github.com/InboundPlatform/global-esim/commit/a3460ff79167ee0f0c4daa91f634f719641216e6))

## [1.1.0](https://github.com/InboundPlatform/global-esim/compare/api-v1.0.0...api-v1.1.0) (2024-04-18)


### Features

* api documentation integrated on swagger ([5ac3fdc](https://github.com/InboundPlatform/global-esim/commit/5ac3fdc65d6a83c96dc17cadadbe6cf1e9a09a04))
* api documentation integrated on swagger ([4aee7ba](https://github.com/InboundPlatform/global-esim/commit/4aee7bada1c25c0267a7822926e14a078d69d35e))
* swagger url change to docs and add basic auth ([5fe772e](https://github.com/InboundPlatform/global-esim/commit/5fe772ec372882fb89639f9d1a16aa7b11abe17c))


### Bug Fixes

* add countries as object in sub countries api and make frontend work as per changes ([2b20112](https://github.com/InboundPlatform/global-esim/commit/2b201123f6a82a8ce2a1175d81e1804b86ac7846))
* add countries as object in sub countries api and make frontend work as per changes ([eef360a](https://github.com/InboundPlatform/global-esim/commit/eef360a4a04fd9cbd8d19e23feea4f0c7bc4e168))
* allow all ([e92450a](https://github.com/InboundPlatform/global-esim/commit/e92450aa4764329ba71460d4b288ab4588020fce))
* app env ([29124f3](https://github.com/InboundPlatform/global-esim/commit/29124f32d728aded61983bf717ab2f351a638e2e))
* change prisma script of package json ([2a20bd6](https://github.com/InboundPlatform/global-esim/commit/2a20bd621f09633531a8523aa63dbb55ddfe0427))
* cors allow all headers ([367b8fb](https://github.com/InboundPlatform/global-esim/commit/367b8fb44d9901de5fe4c7195bca488eeed8d72c))
* cors for gmobile ([8a03973](https://github.com/InboundPlatform/global-esim/commit/8a0397363a6382d4fe358a7f90343d6a1434c170))
* cors set to true ([5ec5e24](https://github.com/InboundPlatform/global-esim/commit/5ec5e24f843a0f26d2d645b38a76443d90dd7fa0))
* not able to login debugg ([542c657](https://github.com/InboundPlatform/global-esim/commit/542c657d57de1a32b35b406a132499d330952558))
* work on adding release please ([7de674b](https://github.com/InboundPlatform/global-esim/commit/7de674b20876823e76a5a075775a34f40c762b00))
