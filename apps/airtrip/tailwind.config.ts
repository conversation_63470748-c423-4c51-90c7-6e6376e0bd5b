import type { Config } from "tailwindcss";

const config: Config = {
  important: "#app",
  content: [
    "./src/app/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/[locale]/**/*.{js,ts,jsx,tsx,mdx}",
    "../../packages/ui/src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      xs: "480px",
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
    },
    extend: {
      animation: {
        shake: "shake 0.3s ease-in-out",
      },
      keyframes: {
        shake: {
          "0%, 100%": { transform: "translateX(0)" },
          "25%": { transform: "translateX(-5px)" },
          "50%": { transform: "translateX(5px)" },
          "75%": { transform: "translateX(-5px)" },
        },
      },
      colors: {
        primary: "#196FB9",
        secondary: "#E7F3F9",
        neutral: "#777777",
        goronaviInactive: "#666",
        action: "#fb8501",
        actionGradient: "#fb6b01",
        defaultGrey: "#202020",
      },
      fontSize: {
        xxs: "0.6rem",
      },
    },
  },
};
export default config;
