# Changelog

## [1.40.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.40.2...airtrip-v1.40.3) (2025-07-11)


### Bug Fixes

* airtrip paypay banner on checkout page ([6fc2080](https://github.com/InboundPlatform/global-esim/commit/6fc2080dca2041186632085938ef9a4ee925bc49))

## [1.40.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.40.1...airtrip-v1.40.2) (2025-07-11)


### Bug Fixes

* enable 5G plans for lgu in APIs ([6d7a6e4](https://github.com/InboundPlatform/global-esim/commit/6d7a6e439b7ae3d799f80d95a91aaef5404570e5))

## [1.40.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.40.0...airtrip-v1.40.1) (2025-07-11)


### Bug Fixes

* build issues ([24f66e3](https://github.com/InboundPlatform/global-esim/commit/24f66e3d95cbdca6b83d4c3932380150c0fec99a))

## [1.40.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.39.1...airtrip-v1.40.0) (2025-07-10)


### Features

* add paypay banner ([ad0bd0c](https://github.com/InboundPlatform/global-esim/commit/ad0bd0c3b8c96bbd18774c6e7e87169f1f48bbe8))

## [1.39.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.39.0...airtrip-v1.39.1) (2025-07-07)


### Bug Fixes

* **region-h1-tag:** change region page h1 hidden tag text ([8be9645](https://github.com/InboundPlatform/global-esim/commit/8be96451503f58dcd9ccc2a153c3e2f9433ddcb9))

## [1.39.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.38.0...airtrip-v1.39.0) (2025-07-04)


### Features

* **ges-267:** add review cards on GM/AT landing page ([bec5463](https://github.com/InboundPlatform/global-esim/commit/bec546336421b29f5762974118822f4decf516e1))
* **gm-airtrip-seo:** add hidden h1 text 日本用eSIM - エアトリeSIM in region page for seo ([ec3f4da](https://github.com/InboundPlatform/global-esim/commit/ec3f4da0782fe1f7465911173822d698f5d410f7))
* **gm-airtrip-seo:** airtrip - remove h1 tag from auth/signup, auth/signin, setup page ([59e76ae](https://github.com/InboundPlatform/global-esim/commit/59e76aeb72ce78daabc0cedf143b8e59bb1ccdd2))
* **gm-airtrip-seo:** airtrip - remove unwanted noLogoText variable ([1dc65d2](https://github.com/InboundPlatform/global-esim/commit/1dc65d2e16643a77117b3652b8636fdaa0a09264))
* **gm-airtrip-seo:** airtrip - seo changes ([33143cb](https://github.com/InboundPlatform/global-esim/commit/33143cb73d05abd724af291da3416fe695e2c4e6))
* **gm-airtrip-seo:** airtrip - seo changes ([910ef5d](https://github.com/InboundPlatform/global-esim/commit/910ef5dcd8fc20430367f92280d40a9363a22678))
* **gm-airtrip-seo:** remove h1 tag from eSIM使うなら、エアトリeSIM in region page ([57cc160](https://github.com/InboundPlatform/global-esim/commit/57cc160d58ac758d5d2d9b87cb0390e93fa0af3d))
* **gm-airtrip-seo:** rename the variable logoTextIsTitle -&gt; isLogoTextTitle ([17b369f](https://github.com/InboundPlatform/global-esim/commit/17b369f9834300a02ae06b927654f469b9e496f0))

## [1.38.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.37.2...airtrip-v1.38.0) (2025-06-30)


### Features

* update free esim campaign banners ([d83ecd4](https://github.com/InboundPlatform/global-esim/commit/d83ecd46ca39b950542e38206a85b196cc2987ca))

## [1.37.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.37.1...airtrip-v1.37.2) (2025-06-27)


### Bug Fixes

* ui flickering ([d2bca33](https://github.com/InboundPlatform/global-esim/commit/d2bca334d6d41d69a08dfefa5af98a7910aff7e3))

## [1.37.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.37.0...airtrip-v1.37.1) (2025-06-27)


### Bug Fixes

* do nothing postOrder throws and enable session replay ([b9dc034](https://github.com/InboundPlatform/global-esim/commit/b9dc034dbc49bee77c3e916fa4062b6ef45fcd35))
* trigger deploy ([198dc1c](https://github.com/InboundPlatform/global-esim/commit/198dc1c55b9f6819d2c1ba4c4f1a18c93b5a9dcd))
* trigger deploy ([b950f5c](https://github.com/InboundPlatform/global-esim/commit/b950f5c15219e780f6564fe3fa8ed7fee887aadb))

## [1.37.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.36.0...airtrip-v1.37.0) (2025-06-23)


### Features

* **seo-optimization:** aritrip - change the faq.json and home.json to use {{region}} as in translation sheet ([9b0a359](https://github.com/InboundPlatform/global-esim/commit/9b0a3599742d206e16279d1158f5b2b0df1e99fa))
* **seo-optimization:** aritrip - update the translations json with the sheet ([59d0c56](https://github.com/InboundPlatform/global-esim/commit/59d0c569ffc4918264d00f352777a3b4c3d25809))
* **seo-optimization:** webjp - add the faq, how it works and region about-esim for webjp as well same as airtrip ([d3c39a6](https://github.com/InboundPlatform/global-esim/commit/d3c39a69db228ae15786566e4726d2313e1eb01e))
* **seo-optimization:** webjp & airtrip sync translations with google sheets ([554b0be](https://github.com/InboundPlatform/global-esim/commit/554b0be984297b56367b3a3609d62d41eefea072))

## [1.36.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.35.0...airtrip-v1.36.0) (2025-06-19)


### Features

* lgu topup ([c1c40d9](https://github.com/InboundPlatform/global-esim/commit/c1c40d9731c7ee971d28134d567af1e495806d4a))

## [1.35.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.34.4...airtrip-v1.35.0) (2025-06-18)


### Features

* **paypay:** add translations for paypay airtrip and gm esim jp page ([6413f66](https://github.com/InboundPlatform/global-esim/commit/6413f66faebdefda53e908a4611f8ee5c49da71b))
* **seo-optimization:** change the meta title tag for the airtrip ([caa78be](https://github.com/InboundPlatform/global-esim/commit/caa78be6b67e3ae45c5594f377dc446183c51928))
* **seo-optimization:** reformat the meta description text ([467ef24](https://github.com/InboundPlatform/global-esim/commit/467ef240850cab4e0d88601ff7859a6694384252))


### Bug Fixes

* update sentry dsn ([14725e2](https://github.com/InboundPlatform/global-esim/commit/14725e2f80dd92cd5c7c3dde48747a9ef07fde81))

## [1.34.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.34.3...airtrip-v1.34.4) (2025-06-12)


### Bug Fixes

* trigger release ([03f4714](https://github.com/InboundPlatform/global-esim/commit/03f47146121f186aef31bb6c290b181404c0aed2))

## [1.34.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.34.2...airtrip-v1.34.3) (2025-06-11)


### Bug Fixes

* add logger for request ([eefc635](https://github.com/InboundPlatform/global-esim/commit/eefc6356eeae47a976c6de6dd7cb35af2d92733f))
* improved logging for frontend and middle ware cookies ([a7c423e](https://github.com/InboundPlatform/global-esim/commit/a7c423e9be7ad1ca886415897199d181ef8afe24))

## [1.34.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.34.1...airtrip-v1.34.2) (2025-06-09)


### Bug Fixes

* trigger release ([a0265f6](https://github.com/InboundPlatform/global-esim/commit/a0265f646c58e0fabc345bd88a0eeede67aa56cd))

## [1.34.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.34.0...airtrip-v1.34.1) (2025-06-09)


### Bug Fixes

* disable phone for verification ([f191f26](https://github.com/InboundPlatform/global-esim/commit/f191f26a010b90b6211b1f277a71b65f5215939a))

## [1.34.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.33.1...airtrip-v1.34.0) (2025-06-06)


### Features

* **seo-optimization:** add metadata title tags for campaign and destination path airtrip ([703b7b1](https://github.com/InboundPlatform/global-esim/commit/703b7b17da0a533ebd6d1a9d06032a50ec994d39))


### Bug Fixes

* update auth token and project name for sentry ([b8e4372](https://github.com/InboundPlatform/global-esim/commit/b8e4372f875a6be1ebeaa0a5c54260e11c03d24a))

## [1.33.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.33.0...airtrip-v1.33.1) (2025-06-05)


### Bug Fixes

* add update sentry token for airtrip ([e40d249](https://github.com/InboundPlatform/global-esim/commit/e40d249811aee6f05c8e9d5eaf481432e8d696d2))
* **airtrip:** update sentry project ([6636837](https://github.com/InboundPlatform/global-esim/commit/66368376eb14414944a0793e33349357be329222))
* ireland image update ([79a5188](https://github.com/InboundPlatform/global-esim/commit/79a5188e4fd9aa1be79d8c30488e3f6e41fffb58))
* remove error page for sentry verification ([9428e68](https://github.com/InboundPlatform/global-esim/commit/9428e6899c9462c5fff504cab9053e95e1d7e30d))

## [1.33.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.32.1...airtrip-v1.33.0) (2025-05-27)


### Features

* invoke gmocp url ([e7d0bca](https://github.com/InboundPlatform/global-esim/commit/e7d0bca54649232e0b62400513cee7d3f0c1eecf))


### Bug Fixes

* gmocp free esim fixes ([9164f61](https://github.com/InboundPlatform/global-esim/commit/9164f618ceeafe4bdc597a173840afd1a6e60bf4))
* trigger release ([5f5f158](https://github.com/InboundPlatform/global-esim/commit/5f5f158c3c135bb3e356c0d8d7fd91233de48dfd))

## [1.32.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.32.0...airtrip-v1.32.1) (2025-05-19)


### Bug Fixes

* remove duplicate product review ([e47934f](https://github.com/InboundPlatform/global-esim/commit/e47934f3890645216802991dd85bcf68d1986485))
* reset password not being sent ([466c4d6](https://github.com/InboundPlatform/global-esim/commit/466c4d6668033459997dcf3d517f6a920cb6224a))

## [1.32.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.8...airtrip-v1.32.0) (2025-05-08)


### Features

* add json ld reviews for airtrip and gm jp ([bab8c4d](https://github.com/InboundPlatform/global-esim/commit/bab8c4d4e37b79c668446f1b803aa7d6aeb441d2))
* service specific coupon ([8fca803](https://github.com/InboundPlatform/global-esim/commit/8fca8031632062e49493b1a20c0d14dcb860df97))


### Bug Fixes

* **airtrip:** update variable reference ([f63f93a](https://github.com/InboundPlatform/global-esim/commit/f63f93ae522009fdb9b5b732e5132711c0546140))
* remove lgu text for non-lgu korea plan ([110d667](https://github.com/InboundPlatform/global-esim/commit/110d667b77bf62087056dcc6e1263733d3a993cc))

## [1.31.8](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.7...airtrip-v1.31.8) (2025-04-24)


### Bug Fixes

* trigger release ([1876e0d](https://github.com/InboundPlatform/global-esim/commit/1876e0d18ce21e056ccfa2db7916d56094ecaf07))

## [1.31.7](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.6...airtrip-v1.31.7) (2025-04-24)


### Bug Fixes

* build issue airtrip ([0f07ba0](https://github.com/InboundPlatform/global-esim/commit/0f07ba0a15f8707c29e872a925428f7cb31d78fe))

## [1.31.6](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.5...airtrip-v1.31.6) (2025-04-22)


### Bug Fixes

* trigger deployments ([#1637](https://github.com/InboundPlatform/global-esim/issues/1637)) ([30842ef](https://github.com/InboundPlatform/global-esim/commit/30842ef4a7c1c47775c8ef23723cd91afdc902ad))

## [1.31.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.4...airtrip-v1.31.5) (2025-04-18)


### Bug Fixes

* trigger release ([01935b6](https://github.com/InboundPlatform/global-esim/commit/01935b6232ec1b9a1aa6dda960d9cab1c8619f0e))

## [1.31.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.3...airtrip-v1.31.4) (2025-04-18)


### Bug Fixes

* trigger release ([824a856](https://github.com/InboundPlatform/global-esim/commit/824a8562764c59a54695233ec89fcb6a63165578))

## [1.31.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.2...airtrip-v1.31.3) (2025-04-17)


### Bug Fixes

* allow all agents in checkout ([#1608](https://github.com/InboundPlatform/global-esim/issues/1608)) ([33c67e9](https://github.com/InboundPlatform/global-esim/commit/33c67e94170fe213e0b8590441cf61e42ce3e5f3))
* chasy tag for gmesim ([#1607](https://github.com/InboundPlatform/global-esim/issues/1607)) ([b5e1305](https://github.com/InboundPlatform/global-esim/commit/b5e13057096329261acf62adafb6b805f7405cc1))

## [1.31.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.1...airtrip-v1.31.2) (2025-04-14)


### Bug Fixes

* enable phone in campaign form ([ce04662](https://github.com/InboundPlatform/global-esim/commit/ce04662dca8ef49574766610e34d8837d2e4d1ca))

## [1.31.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.31.0...airtrip-v1.31.1) (2025-04-14)


### Bug Fixes

* gmo campaign form ([158872a](https://github.com/InboundPlatform/global-esim/commit/158872a7446aecbdb536842e5d5291b83e220671))
* refactor top page to ssg airtrip ([b9a5091](https://github.com/InboundPlatform/global-esim/commit/b9a50919ec28c80f0d524d34b29f3102d0b071df))

## [1.31.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.7...airtrip-v1.31.0) (2025-04-10)


### Features

* add gmocp agent ([#1585](https://github.com/InboundPlatform/global-esim/issues/1585)) ([f5168af](https://github.com/InboundPlatform/global-esim/commit/f5168af0944eb92140d52aef48fe7fefe9f318dc))
* add to specification table ([#1571](https://github.com/InboundPlatform/global-esim/issues/1571)) ([552deb9](https://github.com/InboundPlatform/global-esim/commit/552deb90f25aa34562c6ee9b83b7bfe9ea65e388))


### Bug Fixes

* catch error on all routes ([b8c262c](https://github.com/InboundPlatform/global-esim/commit/b8c262c8a9558bb7977241b9dc5423bc5c43da5b))
* set proper id at top of complete page ([#1572](https://github.com/InboundPlatform/global-esim/issues/1572)) ([9c88195](https://github.com/InboundPlatform/global-esim/commit/9c881955adfd2bf7b3274615a2b49afd1e26bf33))
* update translations ([#1588](https://github.com/InboundPlatform/global-esim/issues/1588)) ([93c41f5](https://github.com/InboundPlatform/global-esim/commit/93c41f5a5fdbb3836f86439449a211f81b6aa39c))
* **webjp&airtrip:** import not found component dynamically with no ssr ([32477a0](https://github.com/InboundPlatform/global-esim/commit/32477a0cb1f486444ecf7fd683f9d4626ba52709))

## [1.30.7](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.6...airtrip-v1.30.7) (2025-04-08)


### Bug Fixes

* trigger release ([4321152](https://github.com/InboundPlatform/global-esim/commit/43211527b7039b039ed20676506184d2f252a1ec))

## [1.30.6](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.5...airtrip-v1.30.6) (2025-04-02)


### Bug Fixes

* trigger deploy ([8c604b6](https://github.com/InboundPlatform/global-esim/commit/8c604b6153933a23d66917e265afc8f76d701fd0))

## [1.30.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.4...airtrip-v1.30.5) (2025-04-01)


### Bug Fixes

* update free esim campaign ([#1557](https://github.com/InboundPlatform/global-esim/issues/1557)) ([#1559](https://github.com/InboundPlatform/global-esim/issues/1559)) ([7e2c0d9](https://github.com/InboundPlatform/global-esim/commit/7e2c0d9cad20daf12bd0331fb226c0f1ec13a300))

## [1.30.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.3...airtrip-v1.30.4) (2025-03-28)


### Bug Fixes

* faq correct links ([#1546](https://github.com/InboundPlatform/global-esim/issues/1546)) ([6f0191e](https://github.com/InboundPlatform/global-esim/commit/6f0191e0a37c08d30791b3a1ac47bafb94786227))
* faq missing content ([#1549](https://github.com/InboundPlatform/global-esim/issues/1549)) ([f7a853d](https://github.com/InboundPlatform/global-esim/commit/f7a853df921a16481354e2bb2a70d2388be35060))

## [1.30.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.2...airtrip-v1.30.3) (2025-03-27)


### Bug Fixes

* trigger release ([47b8a2d](https://github.com/InboundPlatform/global-esim/commit/47b8a2df46e5e181c6cb2254a02d4dae2517180e))

## [1.30.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.1...airtrip-v1.30.2) (2025-03-27)


### Bug Fixes

* basic auth ([9937659](https://github.com/InboundPlatform/global-esim/commit/9937659b6aec279fb915df707e8fc84e9a558f07))

## [1.30.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.30.0...airtrip-v1.30.1) (2025-03-27)


### Bug Fixes

* translation changes for airtrip mobile app ([0ece45a](https://github.com/InboundPlatform/global-esim/commit/0ece45ab2573cff1431e94ecffbb9da1923659b3))

## [1.30.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.9...airtrip-v1.30.0) (2025-03-27)


### Features

* add country param airtrip ([9956673](https://github.com/InboundPlatform/global-esim/commit/9956673ed0102a3dfb5165cd69bfe310465e6d87))
* add instant esim pdf for airtrip in jp ([a76c111](https://github.com/InboundPlatform/global-esim/commit/a76c1113f1c57920468d3e3af4b9c9d4bbfa60d2))
* add reset password with code and email for lost users ([6605a1e](https://github.com/InboundPlatform/global-esim/commit/6605a1eb4bb6b7a1812f1f639117aa6d8513ee01))
* Airtrip esim basket ([e566756](https://github.com/InboundPlatform/global-esim/commit/e566756234ddeb7825eab86d590a8a7a0b691260))
* apply default coupon on checkout page by default ([c996d4e](https://github.com/InboundPlatform/global-esim/commit/c996d4ee51c97d2fe1a195e92ec0608dd42f01a0))
* basket flow completed for airtrip ([e3df90c](https://github.com/InboundPlatform/global-esim/commit/e3df90ce420ad962441d20013339c2209d5ed7d5))
* basket in  webjp ([c6dfd57](https://github.com/InboundPlatform/global-esim/commit/c6dfd5792db30f8ce6504a5735800721e2647ad6))
* great refactor ([8168f92](https://github.com/InboundPlatform/global-esim/commit/8168f929a598d6e06042de37918665ec74f69d6b))
* region page SEO improvements ([a8d77f7](https://github.com/InboundPlatform/global-esim/commit/a8d77f7493e2a7a698f29fd927612f4ad64b0e48))
* topup api and ui with flow ([a4945cd](https://github.com/InboundPlatform/global-esim/commit/a4945cd6001339fb275df6a50dde1263fe049ce6))
* ukomi review api ([#1369](https://github.com/InboundPlatform/global-esim/issues/1369)) ([883dfd3](https://github.com/InboundPlatform/global-esim/commit/883dfd34955bf34ca5093da6c4085d2d10906931))


### Bug Fixes

* add coupon currency with default to USD ([d4a03da](https://github.com/InboundPlatform/global-esim/commit/d4a03dac96e9edec89c0e3b120b08b532030668e))
* add line break for mobile view ([d180dfa](https://github.com/InboundPlatform/global-esim/commit/d180dfa5a57889efe1e2b18a170aa8168344c52a))
* add missing faq ([#1465](https://github.com/InboundPlatform/global-esim/issues/1465)) ([d56ce85](https://github.com/InboundPlatform/global-esim/commit/d56ce850bc91fdd417a29591c5f5d341e9f2b0a1))
* add utm_source agent ([7997d54](https://github.com/InboundPlatform/global-esim/commit/7997d54d814892f9fca625cb3b51b697c0d1d748))
* airtrip japan page improvements ([037303b](https://github.com/InboundPlatform/global-esim/commit/037303b829dcd9f2ff3d432dceaa2bcdcd1e597c))
* airtrip japan region header ([#1489](https://github.com/InboundPlatform/global-esim/issues/1489)) ([e03d5a1](https://github.com/InboundPlatform/global-esim/commit/e03d5a1273765ade25509a2712719f66486ba4af))
* airtrip payment button fix ([153d071](https://github.com/InboundPlatform/global-esim/commit/153d071f241b5c3f0634eb53096ddd27e69ed348))
* airtrip payment button fix ([1187971](https://github.com/InboundPlatform/global-esim/commit/11879712bb7d369e12ccf58ac017cbc105613bee))
* **airtrip:** update translations ([132525c](https://github.com/InboundPlatform/global-esim/commit/132525c72a393a398426dcbde06ab540820ba53c))
* allow lost user to continue campaign if they abandon in middle ([2d0df3c](https://github.com/InboundPlatform/global-esim/commit/2d0df3c4ce25c142ad7d507bab6880fcc7449264))
* aside menu translation for airtrip ([d4bb1c3](https://github.com/InboundPlatform/global-esim/commit/d4bb1c35cd3bd6a8c8c5207d0943ea7f4b9b6335))
* basket coupon enhancement with download pdf ([dab59f7](https://github.com/InboundPlatform/global-esim/commit/dab59f7886525a84608f4a84dc4dfaabfd9385d8))
* basket follow up issues fixes ([1969ecc](https://github.com/InboundPlatform/global-esim/commit/1969eccceb9a15e04815e37757b0031c9257c885))
* basket hotfixes ([ed10a81](https://github.com/InboundPlatform/global-esim/commit/ed10a81767de78b1df0a62bc2dee8bef1c57cc3b))
* cart color not showing on mobile landing page ([5a40772](https://github.com/InboundPlatform/global-esim/commit/5a40772a61e819182cb4fc44bcc0888d13b73dae))
* convert png images to webp airtrip ([8ef6ea8](https://github.com/InboundPlatform/global-esim/commit/8ef6ea81e6f7b4456ed3cf6f17032cc4e22b2ff8))
* ges 113 update faq ([#1453](https://github.com/InboundPlatform/global-esim/issues/1453)) ([6524072](https://github.com/InboundPlatform/global-esim/commit/6524072442cdd27bb53028a7f4060e9f76849327))
* ges-149 utm parameter bug fix ([a8d6d5d](https://github.com/InboundPlatform/global-esim/commit/a8d6d5dd3bf8c08948cc3d7e84421c0ae26b30a9))
* hyperlinks and translation consumption ([4810d09](https://github.com/InboundPlatform/global-esim/commit/4810d09f05e314a9d55182039266a55038fd911c))
* increase z index of navbar menu item ([f0b77f2](https://github.com/InboundPlatform/global-esim/commit/f0b77f27dbfb90cdba819bd45e77f91cffdaf68d))
* language update for airtrip, missing locale for qatar, turkey and republic of south africa ([165dd55](https://github.com/InboundPlatform/global-esim/commit/165dd550380927e55d013780dd468a7aa4ea48d2))
* language, mobile app locale addition for airtrip mobile app ([bb53ec1](https://github.com/InboundPlatform/global-esim/commit/bb53ec1c0068bd414ac839495d3ef140183e6143))
* misc fixes ([907830c](https://github.com/InboundPlatform/global-esim/commit/907830c29baa6425232f824cfda5778ac0b5b042))
* missing user source for social login users ([4581a7d](https://github.com/InboundPlatform/global-esim/commit/4581a7d04609db3a7520383cbb8e458b014085c3))
* network spec for korea plan ([#1399](https://github.com/InboundPlatform/global-esim/issues/1399)) ([5df89be](https://github.com/InboundPlatform/global-esim/commit/5df89bef3e2530580262948ba0cf250f067fac5c))
* random issues ([37b9c57](https://github.com/InboundPlatform/global-esim/commit/37b9c5758b8b51e19e1574ef4a80a1668bfa6db7))
* refactor aside menu and update menu links ([#1416](https://github.com/InboundPlatform/global-esim/issues/1416)) ([8b48385](https://github.com/InboundPlatform/global-esim/commit/8b48385c7712ac4811ffb6ba58779d6443a2a001))
* region page ekyc text correction ([433688d](https://github.com/InboundPlatform/global-esim/commit/433688da6756fa7ba839229a4005071e3b80e270))
* region translations and button color ([#1530](https://github.com/InboundPlatform/global-esim/issues/1530)) ([0a39ec2](https://github.com/InboundPlatform/global-esim/commit/0a39ec2d3bddbf2decd3ba52683e29f0dd3251d7))
* remove airtrip 20 off ([#1520](https://github.com/InboundPlatform/global-esim/issues/1520)) ([5f09041](https://github.com/InboundPlatform/global-esim/commit/5f090415e09848b8ce38fb8549940245e39a8e9b))
* remove app breadcrumb from checkout page ([#1412](https://github.com/InboundPlatform/global-esim/issues/1412)) ([3aa9f00](https://github.com/InboundPlatform/global-esim/commit/3aa9f007630bcbb08dc5bb516b05eb8affb2f7ed))
* remove basepath fro esim airtrip from env ([067fce7](https://github.com/InboundPlatform/global-esim/commit/067fce7673ed95834079e16d400622b6816d8800))
* show qr on the esim details accordion ([624719d](https://github.com/InboundPlatform/global-esim/commit/624719d48bd81c6f0ec1561ae2900f8638636c40))
* style override from ukomi ([6a50f66](https://github.com/InboundPlatform/global-esim/commit/6a50f66af92a7640548d24248665b5012ea64e62))
* tab translation and unique key error ([#1343](https://github.com/InboundPlatform/global-esim/issues/1343)) ([#1344](https://github.com/InboundPlatform/global-esim/issues/1344)) ([ee7951d](https://github.com/InboundPlatform/global-esim/commit/ee7951d94a09a47896b606ee984b2b689084bec9))
* translation fixes for error sheet ([97c7a35](https://github.com/InboundPlatform/global-esim/commit/97c7a357199eced4de2122b017780be1e4a42e87))
* trigger deploy ([e7d6263](https://github.com/InboundPlatform/global-esim/commit/e7d6263062b8568c815694f6b02a5297d702249e))
* trigger deploys for airtrip and web ([9de8b8b](https://github.com/InboundPlatform/global-esim/commit/9de8b8bf7c016f6047a107857a2cecec565cf86a))
* trigger release ([4f0922f](https://github.com/InboundPlatform/global-esim/commit/4f0922facff6a1982eb15ab9639d442e4769f841))
* trigger release ([406e797](https://github.com/InboundPlatform/global-esim/commit/406e797cb65021158d000b26dbaea4525d140e92))
* trigger release ([c2f2bca](https://github.com/InboundPlatform/global-esim/commit/c2f2bca59651c121067933d8fc72332a6b64f2be))
* trigger release ([3f0d2da](https://github.com/InboundPlatform/global-esim/commit/3f0d2da30d42a5ad4b16c05b1f19eab76d71fb82))
* un-hide campaign banners ([52a97f3](https://github.com/InboundPlatform/global-esim/commit/52a97f3edfbf1ca388b217615e580f22b2c1336a))
* update LINE banner links for airtrip ([#1452](https://github.com/InboundPlatform/global-esim/issues/1452)) ([a14bda6](https://github.com/InboundPlatform/global-esim/commit/a14bda68e5e5542e4ac43b0873a481900de3a1bb))
* update spec list for lgu ([#1397](https://github.com/InboundPlatform/global-esim/issues/1397)) ([421cc05](https://github.com/InboundPlatform/global-esim/commit/421cc05c797ee082226a37d6c3d7c6d9996d9fcb))
* update speclist network section ([#1464](https://github.com/InboundPlatform/global-esim/issues/1464)) ([fa36642](https://github.com/InboundPlatform/global-esim/commit/fa3664259581520ba0295ed320f106068acd9d00))
* update text and ui ([f24d4de](https://github.com/InboundPlatform/global-esim/commit/f24d4de3f17212bd8e523320bfef4d4e0eaa1798))
* update text, button text ([1a61c46](https://github.com/InboundPlatform/global-esim/commit/1a61c46d02e83f516be3742992e553e43aa34607))
* update translations and add faq link ([#1417](https://github.com/InboundPlatform/global-esim/issues/1417)) ([6c8d6af](https://github.com/InboundPlatform/global-esim/commit/6c8d6af055570d5688339262771d8804797063ad))
* wip basket ([5c502cf](https://github.com/InboundPlatform/global-esim/commit/5c502cfc665d3a947f49fd751b911c9f40c61500))

## [1.29.8](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.7...airtrip-v1.29.8) (2025-03-26)


### Bug Fixes

* region translations and button color ([#1530](https://github.com/InboundPlatform/global-esim/issues/1530)) ([0a39ec2](https://github.com/InboundPlatform/global-esim/commit/0a39ec2d3bddbf2decd3ba52683e29f0dd3251d7))
* remove airtrip 20 off ([#1520](https://github.com/InboundPlatform/global-esim/issues/1520)) ([5f09041](https://github.com/InboundPlatform/global-esim/commit/5f090415e09848b8ce38fb8549940245e39a8e9b))

## [1.29.7](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.6...airtrip-v1.29.7) (2025-03-25)


### Bug Fixes

* basket follow up issues fixes ([1969ecc](https://github.com/InboundPlatform/global-esim/commit/1969eccceb9a15e04815e37757b0031c9257c885))

## [1.29.6](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.5...airtrip-v1.29.6) (2025-03-24)


### Bug Fixes

* trigger release ([4f0922f](https://github.com/InboundPlatform/global-esim/commit/4f0922facff6a1982eb15ab9639d442e4769f841))

## [1.29.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.4...airtrip-v1.29.5) (2025-03-24)


### Bug Fixes

* trigger release ([406e797](https://github.com/InboundPlatform/global-esim/commit/406e797cb65021158d000b26dbaea4525d140e92))

## [1.29.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.3...airtrip-v1.29.4) (2025-03-24)


### Bug Fixes

* remove basepath fro esim airtrip from env ([067fce7](https://github.com/InboundPlatform/global-esim/commit/067fce7673ed95834079e16d400622b6816d8800))

## [1.29.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.2...airtrip-v1.29.3) (2025-03-24)


### Bug Fixes

* trigger release ([c2f2bca](https://github.com/InboundPlatform/global-esim/commit/c2f2bca59651c121067933d8fc72332a6b64f2be))

## [1.29.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.1...airtrip-v1.29.2) (2025-03-24)


### Bug Fixes

* trigger release ([3f0d2da](https://github.com/InboundPlatform/global-esim/commit/3f0d2da30d42a5ad4b16c05b1f19eab76d71fb82))

## [1.29.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.29.0...airtrip-v1.29.1) (2025-03-24)


### Bug Fixes

* language update for airtrip, missing locale for qatar, turkey and republic of south africa ([165dd55](https://github.com/InboundPlatform/global-esim/commit/165dd550380927e55d013780dd468a7aa4ea48d2))
* language, mobile app locale addition for airtrip mobile app ([bb53ec1](https://github.com/InboundPlatform/global-esim/commit/bb53ec1c0068bd414ac839495d3ef140183e6143))

## [1.29.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.11...airtrip-v1.29.0) (2025-03-19)


### Features

* add country param airtrip ([9956673](https://github.com/InboundPlatform/global-esim/commit/9956673ed0102a3dfb5165cd69bfe310465e6d87))


### Bug Fixes

* airtrip japan page improvements ([037303b](https://github.com/InboundPlatform/global-esim/commit/037303b829dcd9f2ff3d432dceaa2bcdcd1e597c))
* convert png images to webp airtrip ([8ef6ea8](https://github.com/InboundPlatform/global-esim/commit/8ef6ea81e6f7b4456ed3cf6f17032cc4e22b2ff8))
* translation fixes for error sheet ([97c7a35](https://github.com/InboundPlatform/global-esim/commit/97c7a357199eced4de2122b017780be1e4a42e87))

## [1.28.11](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.10...airtrip-v1.28.11) (2025-03-14)


### Bug Fixes

* airtrip japan region header ([#1489](https://github.com/InboundPlatform/global-esim/issues/1489)) ([e03d5a1](https://github.com/InboundPlatform/global-esim/commit/e03d5a1273765ade25509a2712719f66486ba4af))

## [1.28.10](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.9...airtrip-v1.28.10) (2025-03-14)


### Bug Fixes

* increase z index of navbar menu item ([f0b77f2](https://github.com/InboundPlatform/global-esim/commit/f0b77f27dbfb90cdba819bd45e77f91cffdaf68d))

## [1.28.9](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.8...airtrip-v1.28.9) (2025-03-14)


### Bug Fixes

* **airtrip:** update translations ([132525c](https://github.com/InboundPlatform/global-esim/commit/132525c72a393a398426dcbde06ab540820ba53c))
* hyperlinks and translation consumption ([4810d09](https://github.com/InboundPlatform/global-esim/commit/4810d09f05e314a9d55182039266a55038fd911c))
* update text, button text ([1a61c46](https://github.com/InboundPlatform/global-esim/commit/1a61c46d02e83f516be3742992e553e43aa34607))

## [1.28.8](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.7...airtrip-v1.28.8) (2025-03-06)


### Bug Fixes

* add line break for mobile view ([d180dfa](https://github.com/InboundPlatform/global-esim/commit/d180dfa5a57889efe1e2b18a170aa8168344c52a))
* show qr on the esim details accordion ([624719d](https://github.com/InboundPlatform/global-esim/commit/624719d48bd81c6f0ec1561ae2900f8638636c40))
* update LINE banner links for airtrip ([#1452](https://github.com/InboundPlatform/global-esim/issues/1452)) ([a14bda6](https://github.com/InboundPlatform/global-esim/commit/a14bda68e5e5542e4ac43b0873a481900de3a1bb))

## [1.28.7](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.6...airtrip-v1.28.7) (2025-02-28)


### Bug Fixes

* ges-149 utm parameter bug fix ([a8d6d5d](https://github.com/InboundPlatform/global-esim/commit/a8d6d5dd3bf8c08948cc3d7e84421c0ae26b30a9))

## [1.28.6](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.5...airtrip-v1.28.6) (2025-02-27)


### Bug Fixes

* trigger deploys for airtrip and web ([9de8b8b](https://github.com/InboundPlatform/global-esim/commit/9de8b8bf7c016f6047a107857a2cecec565cf86a))

## [1.28.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.4...airtrip-v1.28.5) (2025-02-26)


### Bug Fixes

* basket hotfixes ([ed10a81](https://github.com/InboundPlatform/global-esim/commit/ed10a81767de78b1df0a62bc2dee8bef1c57cc3b))

## [1.28.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.3...airtrip-v1.28.4) (2025-02-17)


### Bug Fixes

* network spec for korea plan ([#1399](https://github.com/InboundPlatform/global-esim/issues/1399)) ([5df89be](https://github.com/InboundPlatform/global-esim/commit/5df89bef3e2530580262948ba0cf250f067fac5c))
* update spec list for lgu ([#1397](https://github.com/InboundPlatform/global-esim/issues/1397)) ([421cc05](https://github.com/InboundPlatform/global-esim/commit/421cc05c797ee082226a37d6c3d7c6d9996d9fcb))

## [1.28.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.2...airtrip-v1.28.3) (2025-02-14)


### Bug Fixes

* add utm_source agent ([7997d54](https://github.com/InboundPlatform/global-esim/commit/7997d54d814892f9fca625cb3b51b697c0d1d748))

## [1.28.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.1...airtrip-v1.28.2) (2025-02-07)


### Bug Fixes

* missing user source for social login users ([4581a7d](https://github.com/InboundPlatform/global-esim/commit/4581a7d04609db3a7520383cbb8e458b014085c3))
* style override from ukomi ([6a50f66](https://github.com/InboundPlatform/global-esim/commit/6a50f66af92a7640548d24248665b5012ea64e62))

## [1.28.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.28.0...airtrip-v1.28.1) (2025-02-06)


### Bug Fixes

* trigger deploy ([e7d6263](https://github.com/InboundPlatform/global-esim/commit/e7d6263062b8568c815694f6b02a5297d702249e))

## [1.28.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.27.5...airtrip-v1.28.0) (2025-02-06)


### Features

* ukomi review api ([#1369](https://github.com/InboundPlatform/global-esim/issues/1369)) ([883dfd3](https://github.com/InboundPlatform/global-esim/commit/883dfd34955bf34ca5093da6c4085d2d10906931))

## [1.27.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.27.4...airtrip-v1.27.5) (2025-02-05)


### Bug Fixes

* airtrip payment button fix ([153d071](https://github.com/InboundPlatform/global-esim/commit/153d071f241b5c3f0634eb53096ddd27e69ed348))

## [1.27.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.27.3...airtrip-v1.27.4) (2025-02-03)


### Bug Fixes

* un-hide campaign banners ([52a97f3](https://github.com/InboundPlatform/global-esim/commit/52a97f3edfbf1ca388b217615e580f22b2c1336a))

## [1.27.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.27.2...airtrip-v1.27.3) (2025-01-31)


### Bug Fixes

* tab translation and unique key error ([#1343](https://github.com/InboundPlatform/global-esim/issues/1343)) ([#1344](https://github.com/InboundPlatform/global-esim/issues/1344)) ([ee7951d](https://github.com/InboundPlatform/global-esim/commit/ee7951d94a09a47896b606ee984b2b689084bec9))

## [1.27.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.27.1...airtrip-v1.27.2) (2025-01-30)


### Bug Fixes

* hide region banner ([2f4a525](https://github.com/InboundPlatform/global-esim/commit/2f4a525ce486d57d00d8c86b054f343c2c9ee416))

## [1.27.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.27.0...airtrip-v1.27.1) (2025-01-30)


### Bug Fixes

* hide top banner campaign ([9655865](https://github.com/InboundPlatform/global-esim/commit/9655865635ff39cf9cc35b8fd869f39aadceac84))

## [1.27.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.7...airtrip-v1.27.0) (2025-01-30)


### Features

* enhanced authentication flow ([ca7a64c](https://github.com/InboundPlatform/global-esim/commit/ca7a64c2c712241ab2c12ffffb0b3e496e2b2c2a))
* refactor airtrip campaign and form ([93845fd](https://github.com/InboundPlatform/global-esim/commit/93845fd7fd914b07854b2574aa7676dc2ba82df0))


### Bug Fixes

* misc fixes ([6f2bbae](https://github.com/InboundPlatform/global-esim/commit/6f2bbaecac3b93fb70e9574b3100c0134cf4deb8))
* show social message error ([027a8c8](https://github.com/InboundPlatform/global-esim/commit/027a8c86958225e0a2ef01d15b36b3a52ee7c8a3))
* social login email conflict message ([dda5342](https://github.com/InboundPlatform/global-esim/commit/dda534242c37e333a391837199f228d9e724b229))

## [1.26.7](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.6...airtrip-v1.26.7) (2025-01-28)


### Bug Fixes

* quotation request not being sent on coupon ([6ae758d](https://github.com/InboundPlatform/global-esim/commit/6ae758d987b9b054d0c1fd1617d7079ac9e7b96a))

## [1.26.6](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.5...airtrip-v1.26.6) (2025-01-27)


### Bug Fixes

* env issues for dockerfile ([de79151](https://github.com/InboundPlatform/global-esim/commit/de79151d23de9626c8ac909911345c854b0d9215))
* trigger fresh deploys ([4e9ea54](https://github.com/InboundPlatform/global-esim/commit/4e9ea54cef6dd7e94cf706dcbdb56419e1fee90e))

## [1.26.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.4...airtrip-v1.26.5) (2025-01-24)


### Bug Fixes

* trigger deploys ([d511ce7](https://github.com/InboundPlatform/global-esim/commit/d511ce732e2efef9a994ec120cd787015afb75c5))

## [1.26.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.3...airtrip-v1.26.4) (2025-01-24)


### Bug Fixes

* dev deploy ([57390a1](https://github.com/InboundPlatform/global-esim/commit/57390a1008e0b70f25d43b3dc851fb37db490752))
* disable kddi plan selection ([e75e4d1](https://github.com/InboundPlatform/global-esim/commit/e75e4d15ae3998f3292be98e668b2653fe623b1e))
* replace free esim campaign banner ([4a4f316](https://github.com/InboundPlatform/global-esim/commit/4a4f316db616fcd8f29231f5afaec8548bb7ba97))

## [1.26.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.2...airtrip-v1.26.3) (2025-01-23)


### Bug Fixes

* prevent duplicate plans ([87020dc](https://github.com/InboundPlatform/global-esim/commit/87020dc5e493d921dcd24f1248a6521be0ce9c6d))

## [1.26.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.1...airtrip-v1.26.2) (2025-01-23)


### Bug Fixes

* trigger airtrip deploy ([1335952](https://github.com/InboundPlatform/global-esim/commit/133595230af8e83dbb3a926b38a7da39e83b391a))

## [1.26.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.26.0...airtrip-v1.26.1) (2025-01-22)


### Bug Fixes

* broken cover image for some countries ([a99c630](https://github.com/InboundPlatform/global-esim/commit/a99c63033eea0a0e47f1596026001785270c0d54))

## [1.26.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.25.5...airtrip-v1.26.0) (2025-01-16)


### Features

* add request origin on each api request ([db4d898](https://github.com/InboundPlatform/global-esim/commit/db4d8987c3c2e88e00e3a8ffb55572d5a7be9058))
* new kv ui for airtrip ([ff2de3a](https://github.com/InboundPlatform/global-esim/commit/ff2de3a9994f150834fa030d0cacb64ff2d3cd75))
* new kv ui for airtrip ([3920db7](https://github.com/InboundPlatform/global-esim/commit/3920db72501cd96b107e08983154420a1fda1bbe))


### Bug Fixes

* airtrip esim region title tag ([#1272](https://github.com/InboundPlatform/global-esim/issues/1272)) ([5661466](https://github.com/InboundPlatform/global-esim/commit/566146662c224ae016d71809636403ccd192810a))
* region page title tags ([#1269](https://github.com/InboundPlatform/global-esim/issues/1269)) ([67d4377](https://github.com/InboundPlatform/global-esim/commit/67d437787c20c06860e382a20161f4afcd907018))
* trigger deploy ([e0095de](https://github.com/InboundPlatform/global-esim/commit/e0095dea1df2c745d09a53482881b30ddf4ff766))
* update footer copyright date ([68ad172](https://github.com/InboundPlatform/global-esim/commit/68ad1722e5e0664eb9a8019c24e3091da1be9ed9))
* update footer copyright date ([a3978e4](https://github.com/InboundPlatform/global-esim/commit/a3978e4216797847c50bf6a7825a403af20907d5))
* update meta title for korean region page ([#1266](https://github.com/InboundPlatform/global-esim/issues/1266)) ([88cc8fa](https://github.com/InboundPlatform/global-esim/commit/88cc8fa45b22faa3abc540e043d1511781ab2c12))

## [1.25.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.25.4...airtrip-v1.25.5) (2025-01-15)


### Bug Fixes

* missing translations on all destinations page ([#1261](https://github.com/InboundPlatform/global-esim/issues/1261)) ([38201b8](https://github.com/InboundPlatform/global-esim/commit/38201b856b9b0a86575c7b548ba426464471bea2))
* translations and top page ([#1259](https://github.com/InboundPlatform/global-esim/issues/1259)) ([c356dc9](https://github.com/InboundPlatform/global-esim/commit/c356dc9e67956d1d484c31d982af63c35b98bda2))

## [1.25.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.25.3...airtrip-v1.25.4) (2025-01-14)


### Bug Fixes

* build issue, server components issue ([098cc9c](https://github.com/InboundPlatform/global-esim/commit/098cc9cebc2d625add742c96a9dfeb0928adc722))
* build issues ([48fccd2](https://github.com/InboundPlatform/global-esim/commit/48fccd2e95908074a7f99a101d79999461f589b7))
* password length ([#1251](https://github.com/InboundPlatform/global-esim/issues/1251)) ([7c8e8c3](https://github.com/InboundPlatform/global-esim/commit/7c8e8c36a9338c5f6f352288e3320b0cde99a423))
* remove nikkei top campaign banner ([8773369](https://github.com/InboundPlatform/global-esim/commit/87733695a1591e38d11be329fd77ea8e292606d0))
* trigger deploy ([27b9f02](https://github.com/InboundPlatform/global-esim/commit/27b9f025a51fe53f88e5037ac66df613704f9566))

## [1.25.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.25.2...airtrip-v1.25.3) (2024-12-27)


### Bug Fixes

* airtrip campaign url ([c02019c](https://github.com/InboundPlatform/global-esim/commit/c02019c1e0f892d9617d85f697d63eca822e4de5))

## [1.25.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.25.1...airtrip-v1.25.2) (2024-12-27)


### Bug Fixes

* default plan information, new link ([2e1e42b](https://github.com/InboundPlatform/global-esim/commit/2e1e42b2d8c14f320c96d807b0f9e6adca53e5f3))
* new compatibility information ([07af2fc](https://github.com/InboundPlatform/global-esim/commit/07af2fcdd671d3e39a9245b3180654b2dc40407c))
* us virgin islands misspell ([22910ec](https://github.com/InboundPlatform/global-esim/commit/22910ec7aefdf1f7e01fd64490489041c9284f70))
* virgin island image name ([412d4e7](https://github.com/InboundPlatform/global-esim/commit/412d4e744735ed047e7cc41a283a96b0806d81cb))

## [1.25.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.25.0...airtrip-v1.25.1) (2024-12-25)


### Bug Fixes

* content fixes ([7da27ca](https://github.com/InboundPlatform/global-esim/commit/7da27ca97bd0e96ebebb375e656a5ffb79a097a3))

## [1.25.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.13...airtrip-v1.25.0) (2024-12-23)


### Features

* add 15 off image on home page ([babce7b](https://github.com/InboundPlatform/global-esim/commit/babce7b14fbb07dfa0d0532d96a2540394a30a71))
* add gmo affiliate tags ([#1058](https://github.com/InboundPlatform/global-esim/issues/1058)) ([1fa9939](https://github.com/InboundPlatform/global-esim/commit/1fa9939510bf15c36faa95739e58374da709076f))
* add instant eSIM ([#1012](https://github.com/InboundPlatform/global-esim/issues/1012)) ([d776496](https://github.com/InboundPlatform/global-esim/commit/d776496384265260d042128a982a3c941368c585))
* base price plus airtrip upsell ([59f3092](https://github.com/InboundPlatform/global-esim/commit/59f30927271dff041c6e24187badd5dedce48ac6))
* discount on destination page ([12bd145](https://github.com/InboundPlatform/global-esim/commit/12bd145a62aed5adc6bbe5438208303c0395e948))
* discount on top page, check user phone number on register ([bd7b146](https://github.com/InboundPlatform/global-esim/commit/bd7b146e4cb57c3f9561a2b425db4ab7e954d9aa))


### Bug Fixes

* add 1 day free esim banner ([#1108](https://github.com/InboundPlatform/global-esim/issues/1108)) ([84e082b](https://github.com/InboundPlatform/global-esim/commit/84e082b1e15952bc92e0fbcc3cea0744408fc210))
* add gmo script to landing page ([#1076](https://github.com/InboundPlatform/global-esim/issues/1076)) ([531128d](https://github.com/InboundPlatform/global-esim/commit/531128d7c8c7282f87e41471a23d7e11d09b60c1))
* add google-site-verification ([292a7f3](https://github.com/InboundPlatform/global-esim/commit/292a7f3c36d85831005038fc424402b7875fd3d0))
* add name registration guide ([#1138](https://github.com/InboundPlatform/global-esim/issues/1138)) ([dd006be](https://github.com/InboundPlatform/global-esim/commit/dd006be069b3d49b9b8064e25e64f13af59affa3))
* add sms authentication in the korea campaign flow ([a5b8620](https://github.com/InboundPlatform/global-esim/commit/a5b86208959f1651e18dc302fb7e8ef4386a5e17))
* airtrip campaign free esim ([346c8a1](https://github.com/InboundPlatform/global-esim/commit/346c8a1710dd20670c23cec36434e1b10e46e08d))
* airtrip fix ([d7f5cb9](https://github.com/InboundPlatform/global-esim/commit/d7f5cb91d24a13bd4a83fcec62c1c01df1c7022c))
* airtrip iphone compatibility update ([302403a](https://github.com/InboundPlatform/global-esim/commit/302403acde1d50197bb60203fd61ebd66e7bbe98))
* airtrip merge issues, code removed ([992cf41](https://github.com/InboundPlatform/global-esim/commit/992cf417c0b268fc4e89858db04c5f1639dcaeac))
* airtrip side menu fix ([8f13a9b](https://github.com/InboundPlatform/global-esim/commit/8f13a9bca807e57b655a1a445d0859ada5f84825))
* build error with sessionstorage ([813ec76](https://github.com/InboundPlatform/global-esim/commit/813ec763243f00e264f3ddfc57c7bde1b0b39301))
* build issue ([ff4315b](https://github.com/InboundPlatform/global-esim/commit/ff4315b51f88c19ccd596c04f52afaec1c125e65))
* build issue ([#1041](https://github.com/InboundPlatform/global-esim/issues/1041)) ([2c382e1](https://github.com/InboundPlatform/global-esim/commit/2c382e1811465862ed0e1e59be88dda97ae25a47))
* build issues ([e7a25db](https://github.com/InboundPlatform/global-esim/commit/e7a25db9be13aba674c05b2d4053a5502ab68283))
* build issues ([64c2f5b](https://github.com/InboundPlatform/global-esim/commit/64c2f5b5929028c3e50bf16403de40b8276fdf5b))
* build issues ([1595600](https://github.com/InboundPlatform/global-esim/commit/1595600ef412dd4a833c2be1bc39732f1ad285a5))
* build issues ([3116055](https://github.com/InboundPlatform/global-esim/commit/3116055f08c7359d8de52b8b9eb4bf92131f7e88))
* build issues ([53b560e](https://github.com/InboundPlatform/global-esim/commit/53b560e673291551e2642ea5a22112be3ccec74a))
* build issues ([ff72911](https://github.com/InboundPlatform/global-esim/commit/ff7291156ce7824741df8fa88d89b48a1ace5c39))
* campaign prices for lgu korea ([e7ef024](https://github.com/InboundPlatform/global-esim/commit/e7ef024ee08b6a9ee89cdc3e8587b950a1f0fb24))
* campaign should be all caps ([328ccb7](https://github.com/InboundPlatform/global-esim/commit/328ccb7757d7f3b6be323a240ccb4066bdd7d4c7))
* change hero input logic on blur event ([c13af9c](https://github.com/InboundPlatform/global-esim/commit/c13af9c3ca63cfbd61b6563e808379a22cf02b62))
* change phone already exists to japanese on airtrip ([aa6e77f](https://github.com/InboundPlatform/global-esim/commit/aa6e77f672e19947eb5c04f0f461a7ebd0a29169))
* commit message ([#1037](https://github.com/InboundPlatform/global-esim/issues/1037)) ([8288443](https://github.com/InboundPlatform/global-esim/commit/828844369c14f425b51e7ecc647c5375bb94fc45))
* content files update ([697abfd](https://github.com/InboundPlatform/global-esim/commit/697abfdd71b77710a6fab86cb85326579783fe91))
* debounce value needs to be 100 ([af3ef51](https://github.com/InboundPlatform/global-esim/commit/af3ef519ef2d1fb42c2acf8f964db780d62ec0d8))
* design fixes for airtrip modal of coupon excess use ([72cdffb](https://github.com/InboundPlatform/global-esim/commit/72cdffb839b3b1b70209148f88ed2f17e1b9641e))
* enable phone ([ff0478d](https://github.com/InboundPlatform/global-esim/commit/ff0478d45ef68b529c42860e7fec978cf5d8056a))
* esim counter korea image change ([b5c95ac](https://github.com/InboundPlatform/global-esim/commit/b5c95ac47971b747e484122a2de87962b9df2184))
* exclude gmo from discount ([#1068](https://github.com/InboundPlatform/global-esim/issues/1068)) ([8eea29f](https://github.com/InboundPlatform/global-esim/commit/8eea29f864d99a842d21d9ec1f3ab9aa8ab08982))
* fixes for translations ([98f9c00](https://github.com/InboundPlatform/global-esim/commit/98f9c004497431488fcaca5743348257b8da8c2e))
* fixes for translations component ([1b3716b](https://github.com/InboundPlatform/global-esim/commit/1b3716be43522ee0d39e3117eba451ffe7972f4d))
* fixes for ui ([4076490](https://github.com/InboundPlatform/global-esim/commit/407649080278a0c1581fbcca4e624abd31455904))
* gmo condition hook ([813ec76](https://github.com/InboundPlatform/global-esim/commit/813ec763243f00e264f3ddfc57c7bde1b0b39301))
* increase padding of qr code ([#1030](https://github.com/InboundPlatform/global-esim/issues/1030)) ([7401f61](https://github.com/InboundPlatform/global-esim/commit/7401f61bedb0b07bc5bc5ffb5adeb5367a37b0fd))
* kv size fix ([8de6661](https://github.com/InboundPlatform/global-esim/commit/8de666109b4b5bf22e56e15246c5e53bbbbcfae6))
* kv size fix ([b905a71](https://github.com/InboundPlatform/global-esim/commit/b905a7169a1ae25c7ce2c97cf1efee3450ba7958))
* kv sizing updates ([8922805](https://github.com/InboundPlatform/global-esim/commit/8922805fe3892a6b949e549659a38b930b6e6666))
* manual fixes for pdf ([2be75d3](https://github.com/InboundPlatform/global-esim/commit/2be75d301852b0d9008a44987de3cee112a60b03))
* misc ([a733c41](https://github.com/InboundPlatform/global-esim/commit/a733c41ab3c62d57a6e525084c541f12b2578584))
* more centered error message for coupon usage ([9e2014f](https://github.com/InboundPlatform/global-esim/commit/9e2014f4bdbc935def71813628f21a1dc6049705))
* negative pricing on featured plans ([#1121](https://github.com/InboundPlatform/global-esim/issues/1121)) ([08465ab](https://github.com/InboundPlatform/global-esim/commit/08465ab5a26e2859533b90c7e6d6f71227082992))
* new ui for gm and airtrip ([cb0a227](https://github.com/InboundPlatform/global-esim/commit/cb0a2271a87f99fabac8a0c509f4dc98ca9153b7))
* pass params through url and link ([5e7702c](https://github.com/InboundPlatform/global-esim/commit/5e7702c8187704d51404c37e1cf85bbd31efa1af))
* price updates ([d5945d1](https://github.com/InboundPlatform/global-esim/commit/d5945d13840fadb183dcfccc7538c58f2ad864fd))
* redeploy ([c2a5e21](https://github.com/InboundPlatform/global-esim/commit/c2a5e214ab5ba59057bb367d99528b40b02ae581))
* reduce z index of qr code overlay ([#1088](https://github.com/InboundPlatform/global-esim/issues/1088)) ([7733af9](https://github.com/InboundPlatform/global-esim/commit/7733af9ab170dcea5a5a1583d8cf3818ac29763e))
* remove 'isGM' property ([#1002](https://github.com/InboundPlatform/global-esim/issues/1002)) ([71a4dd8](https://github.com/InboundPlatform/global-esim/commit/71a4dd8232bab7475b62e72de661998bc89d7f44))
* remove comment ([#1039](https://github.com/InboundPlatform/global-esim/issues/1039)) ([70505c3](https://github.com/InboundPlatform/global-esim/commit/70505c3512c56cdf62a3c9842708648bb4282755))
* remove consoles ([71d79c7](https://github.com/InboundPlatform/global-esim/commit/71d79c7496e42ea10e0778b92ff60ec4cf1074b0))
* removed line button from the order page ([4e22dd7](https://github.com/InboundPlatform/global-esim/commit/4e22dd7607be9259f778e74ffd8baf2624b9afe1))
* replace array with the variable ([198e2ca](https://github.com/InboundPlatform/global-esim/commit/198e2ca61684a6d46f6c5d18cda8802520665210))
* revert qr padding change ([#1033](https://github.com/InboundPlatform/global-esim/issues/1033)) ([79254f4](https://github.com/InboundPlatform/global-esim/commit/79254f412e9779c89c5f71fcae309e1b9d515686))
* revert qr padding change ([#1033](https://github.com/InboundPlatform/global-esim/issues/1033)) ([#1036](https://github.com/InboundPlatform/global-esim/issues/1036)) ([62b75c5](https://github.com/InboundPlatform/global-esim/commit/62b75c5e1ec857b1de8f8c02012ec52970cc4dd1))
* revert qr padding change ([#1034](https://github.com/InboundPlatform/global-esim/issues/1034)) ([cfd8a09](https://github.com/InboundPlatform/global-esim/commit/cfd8a09cf5f9510b0dae2f0429c88e2803af9cc4))
* SEO content update ([33ce216](https://github.com/InboundPlatform/global-esim/commit/33ce216a06e7c92417500e1f8db6492ac86bfd54))
* show total gb for fixed plans on checkout ([#1177](https://github.com/InboundPlatform/global-esim/issues/1177)) ([c1b7785](https://github.com/InboundPlatform/global-esim/commit/c1b7785d082a6ee97affd0f9c13100c755991cc6))
* translation for gm and airtrip ([7b471c1](https://github.com/InboundPlatform/global-esim/commit/7b471c1c2d985fd445a71dc582a3555fae59f344))
* update dockerfile with migration ([f302ee0](https://github.com/InboundPlatform/global-esim/commit/f302ee0806908d21ac4ddfb5578b8c8b1120f84f))
* update plan images ([#1022](https://github.com/InboundPlatform/global-esim/issues/1022)) ([1387554](https://github.com/InboundPlatform/global-esim/commit/1387554e43d6af6a394defb7609823db3aa3fec5))
* update spec list for korean plans ([#1005](https://github.com/InboundPlatform/global-esim/issues/1005)) ([3bab37b](https://github.com/InboundPlatform/global-esim/commit/3bab37b067d6f4982c80ce6856490d48a636f2e8))
* wallet payment issue and checkout page improvement ([52afbdb](https://github.com/InboundPlatform/global-esim/commit/52afbdb023a0dc1fb15c132bf52044964dc95259))

## [1.24.13](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.12...airtrip-v1.24.13) (2024-12-19)


### Bug Fixes

* show total gb for fixed plans on checkout ([#1177](https://github.com/InboundPlatform/global-esim/issues/1177)) ([c1b7785](https://github.com/InboundPlatform/global-esim/commit/c1b7785d082a6ee97affd0f9c13100c755991cc6))

## [1.24.12](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.11...airtrip-v1.24.12) (2024-12-19)


### Bug Fixes

* add sms authentication in the korea campaign flow ([a5b8620](https://github.com/InboundPlatform/global-esim/commit/a5b86208959f1651e18dc302fb7e8ef4386a5e17))
* enable phone ([ff0478d](https://github.com/InboundPlatform/global-esim/commit/ff0478d45ef68b529c42860e7fec978cf5d8056a))

## [1.24.11](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.10...airtrip-v1.24.11) (2024-12-18)


### Bug Fixes

* design fixes for airtrip modal of coupon excess use ([72cdffb](https://github.com/InboundPlatform/global-esim/commit/72cdffb839b3b1b70209148f88ed2f17e1b9641e))
* misc ([a733c41](https://github.com/InboundPlatform/global-esim/commit/a733c41ab3c62d57a6e525084c541f12b2578584))
* more centered error message for coupon usage ([9e2014f](https://github.com/InboundPlatform/global-esim/commit/9e2014f4bdbc935def71813628f21a1dc6049705))

## [1.24.10](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.9...airtrip-v1.24.10) (2024-12-17)


### Bug Fixes

* airtrip campaign free esim ([346c8a1](https://github.com/InboundPlatform/global-esim/commit/346c8a1710dd20670c23cec36434e1b10e46e08d))

## [1.24.9](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.8...airtrip-v1.24.9) (2024-12-13)


### Bug Fixes

* add name registration guide ([#1138](https://github.com/InboundPlatform/global-esim/issues/1138)) ([dd006be](https://github.com/InboundPlatform/global-esim/commit/dd006be069b3d49b9b8064e25e64f13af59affa3))
* fixes for translations ([98f9c00](https://github.com/InboundPlatform/global-esim/commit/98f9c004497431488fcaca5743348257b8da8c2e))
* fixes for translations component ([1b3716b](https://github.com/InboundPlatform/global-esim/commit/1b3716be43522ee0d39e3117eba451ffe7972f4d))
* fixes for ui ([4076490](https://github.com/InboundPlatform/global-esim/commit/407649080278a0c1581fbcca4e624abd31455904))
* removed line button from the order page ([4e22dd7](https://github.com/InboundPlatform/global-esim/commit/4e22dd7607be9259f778e74ffd8baf2624b9afe1))

## [1.24.8](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.7...airtrip-v1.24.8) (2024-12-11)


### Bug Fixes

* negative pricing on featured plans ([#1121](https://github.com/InboundPlatform/global-esim/issues/1121)) ([08465ab](https://github.com/InboundPlatform/global-esim/commit/08465ab5a26e2859533b90c7e6d6f71227082992))

## [1.24.7](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.6...airtrip-v1.24.7) (2024-12-11)


### Bug Fixes

* build issues ([64c2f5b](https://github.com/InboundPlatform/global-esim/commit/64c2f5b5929028c3e50bf16403de40b8276fdf5b))
* pass params through url and link ([5e7702c](https://github.com/InboundPlatform/global-esim/commit/5e7702c8187704d51404c37e1cf85bbd31efa1af))

## [1.24.6](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.5...airtrip-v1.24.6) (2024-12-11)


### Bug Fixes

* add 1 day free esim banner ([#1108](https://github.com/InboundPlatform/global-esim/issues/1108)) ([84e082b](https://github.com/InboundPlatform/global-esim/commit/84e082b1e15952bc92e0fbcc3cea0744408fc210))
* airtrip fix ([d7f5cb9](https://github.com/InboundPlatform/global-esim/commit/d7f5cb91d24a13bd4a83fcec62c1c01df1c7022c))
* airtrip merge issues, code removed ([992cf41](https://github.com/InboundPlatform/global-esim/commit/992cf417c0b268fc4e89858db04c5f1639dcaeac))
* build issues ([1595600](https://github.com/InboundPlatform/global-esim/commit/1595600ef412dd4a833c2be1bc39732f1ad285a5))
* campaign should be all caps ([328ccb7](https://github.com/InboundPlatform/global-esim/commit/328ccb7757d7f3b6be323a240ccb4066bdd7d4c7))
* debounce value needs to be 100 ([af3ef51](https://github.com/InboundPlatform/global-esim/commit/af3ef519ef2d1fb42c2acf8f964db780d62ec0d8))
* remove consoles ([71d79c7](https://github.com/InboundPlatform/global-esim/commit/71d79c7496e42ea10e0778b92ff60ec4cf1074b0))
* update dockerfile with migration ([f302ee0](https://github.com/InboundPlatform/global-esim/commit/f302ee0806908d21ac4ddfb5578b8c8b1120f84f))

## [1.24.5](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.4...airtrip-v1.24.5) (2024-12-09)


### Bug Fixes

* build error with sessionstorage ([813ec76](https://github.com/InboundPlatform/global-esim/commit/813ec763243f00e264f3ddfc57c7bde1b0b39301))
* gmo condition hook ([813ec76](https://github.com/InboundPlatform/global-esim/commit/813ec763243f00e264f3ddfc57c7bde1b0b39301))

## [1.24.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.3...airtrip-v1.24.4) (2024-12-06)


### Bug Fixes

* build issues ([3116055](https://github.com/InboundPlatform/global-esim/commit/3116055f08c7359d8de52b8b9eb4bf92131f7e88))

## [1.24.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.2...airtrip-v1.24.3) (2024-12-05)


### Bug Fixes

* build issue ([ff4315b](https://github.com/InboundPlatform/global-esim/commit/ff4315b51f88c19ccd596c04f52afaec1c125e65))

## [1.24.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.1...airtrip-v1.24.2) (2024-12-02)


### Bug Fixes

* build issues ([53b560e](https://github.com/InboundPlatform/global-esim/commit/53b560e673291551e2642ea5a22112be3ccec74a))

## [1.24.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.24.0...airtrip-v1.24.1) (2024-11-20)


### Bug Fixes

* build issues ([ff72911](https://github.com/InboundPlatform/global-esim/commit/ff7291156ce7824741df8fa88d89b48a1ace5c39))

## [1.24.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.23.4...airtrip-v1.24.0) (2024-11-15)


### Features

* add instant eSIM ([#1012](https://github.com/InboundPlatform/global-esim/issues/1012)) ([d776496](https://github.com/InboundPlatform/global-esim/commit/d776496384265260d042128a982a3c941368c585))


### Bug Fixes

* manual fixes for pdf ([2be75d3](https://github.com/InboundPlatform/global-esim/commit/2be75d301852b0d9008a44987de3cee112a60b03))
* remove 'isGM' property ([#1002](https://github.com/InboundPlatform/global-esim/issues/1002)) ([71a4dd8](https://github.com/InboundPlatform/global-esim/commit/71a4dd8232bab7475b62e72de661998bc89d7f44))
* update plan images ([#1022](https://github.com/InboundPlatform/global-esim/issues/1022)) ([1387554](https://github.com/InboundPlatform/global-esim/commit/1387554e43d6af6a394defb7609823db3aa3fec5))
* update spec list for korean plans ([#1005](https://github.com/InboundPlatform/global-esim/issues/1005)) ([3bab37b](https://github.com/InboundPlatform/global-esim/commit/3bab37b067d6f4982c80ce6856490d48a636f2e8))

## [1.23.4](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.23.3...airtrip-v1.23.4) (2024-11-06)


### Bug Fixes

* airtrip iphone compatibility update ([302403a](https://github.com/InboundPlatform/global-esim/commit/302403acde1d50197bb60203fd61ebd66e7bbe98))

## [1.23.3](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.23.2...airtrip-v1.23.3) (2024-11-05)


### Bug Fixes

* esim counter korea image change ([b5c95ac](https://github.com/InboundPlatform/global-esim/commit/b5c95ac47971b747e484122a2de87962b9df2184))

## [1.23.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.23.1...airtrip-v1.23.2) (2024-11-01)


### Bug Fixes

* price updates ([d5945d1](https://github.com/InboundPlatform/global-esim/commit/d5945d13840fadb183dcfccc7538c58f2ad864fd))

## [1.23.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.23.0...airtrip-v1.23.1) (2024-10-31)


### Bug Fixes

* redeploy ([c2a5e21](https://github.com/InboundPlatform/global-esim/commit/c2a5e214ab5ba59057bb367d99528b40b02ae581))

## [1.23.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.22.1...airtrip-v1.23.0) (2024-10-31)


### Features

* discount on destination page ([12bd145](https://github.com/InboundPlatform/global-esim/commit/12bd145a62aed5adc6bbe5438208303c0395e948))
* discount on top page, check user phone number on register ([bd7b146](https://github.com/InboundPlatform/global-esim/commit/bd7b146e4cb57c3f9561a2b425db4ab7e954d9aa))

## [1.22.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.22.0...airtrip-v1.22.1) (2024-10-29)


### Bug Fixes

* kv size fix ([8de6661](https://github.com/InboundPlatform/global-esim/commit/8de666109b4b5bf22e56e15246c5e53bbbbcfae6))
* kv size fix ([b905a71](https://github.com/InboundPlatform/global-esim/commit/b905a7169a1ae25c7ce2c97cf1efee3450ba7958))
* kv sizing updates ([8922805](https://github.com/InboundPlatform/global-esim/commit/8922805fe3892a6b949e549659a38b930b6e6666))

## [1.22.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.21.1...airtrip-v1.22.0) (2024-10-24)


### Features

* 51485 added cospakorea fiit campaign ([9f60e9b](https://github.com/InboundPlatform/global-esim/commit/9f60e9b01e2579b06d8070a3a1f9cc5e39ae07cb))

## [1.21.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.21.0...airtrip-v1.21.1) (2024-10-22)


### Bug Fixes

* changes ([61453d9](https://github.com/InboundPlatform/global-esim/commit/61453d979cb6eb7990e95a901bc07a4bf2e59c10))

## [1.21.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.20.0...airtrip-v1.21.0) (2024-10-09)


### Features

* rebuild region page cache ([7ca1099](https://github.com/InboundPlatform/global-esim/commit/7ca10996ee9b689ed73b288a34846698ee578577))


### Bug Fixes

* show error message on order page airtrip ([9ce0937](https://github.com/InboundPlatform/global-esim/commit/9ce0937f430e7c28d4748beab37615bc857cb3a4))

## [1.20.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.19.0...airtrip-v1.20.0) (2024-10-03)


### Features

* 51375 airtrip 20% off ([3201d18](https://github.com/InboundPlatform/global-esim/commit/3201d180f0e3f0d2c2871eb3741209981c1c1e7e))
* send coupon to be ([98bffc6](https://github.com/InboundPlatform/global-esim/commit/98bffc6ee737589d119399f12e2645da59b6d589))
* send coupon to be ([dc92a08](https://github.com/InboundPlatform/global-esim/commit/dc92a08160564e97b1107de1413cb3408f2bc005))

## [1.19.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.18.0...airtrip-v1.19.0) (2024-09-27)


### Features

* lgu fiit banners ([64fc3e5](https://github.com/InboundPlatform/global-esim/commit/64fc3e515f4e11c531f5f32829a6e890f699ccdb))

## [1.18.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.17.0...airtrip-v1.18.0) (2024-09-27)


### Features

* 51292 airtrip affiliate ([cd2424a](https://github.com/InboundPlatform/global-esim/commit/cd2424a4255092f996e24e93e5d24d296f48e449))
* change country page to static ([d625a5b](https://github.com/InboundPlatform/global-esim/commit/d625a5b4d47039d303414184e8cd0c06f290bd36))
* remove get discount api ([4178539](https://github.com/InboundPlatform/global-esim/commit/41785390fd9ccd8ddca11e1dcb95e017d67c11c1))

## [1.17.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.16.0...airtrip-v1.17.0) (2024-09-19)


### Features

* remove old rewardful tags ([7b08faa](https://github.com/InboundPlatform/global-esim/commit/7b08faab788a72f05bb5c664f441c423201b26b6))

## [1.16.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.15.1...airtrip-v1.16.0) (2024-09-12)


### Features

* 51262 airtrip rewardful ([119eeb6](https://github.com/InboundPlatform/global-esim/commit/119eeb6a35f1399b2df3ee9cd19a949ccd6bcbfb))


### Bug Fixes

* login error translation fix ([ca9b890](https://github.com/InboundPlatform/global-esim/commit/ca9b89003d39e7d13048853f9787306d59135759))

## [1.15.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.15.0...airtrip-v1.15.1) (2024-09-05)


### Bug Fixes

* add 404 redirects to non-existent countries ([7767e3c](https://github.com/InboundPlatform/global-esim/commit/7767e3c7a361607993b2b156f7dc876a7eac0f94))
* airtrip change color ([a5f2c9a](https://github.com/InboundPlatform/global-esim/commit/a5f2c9a52e8e2f51c274cdf61c64ec0dd8d15a05))

## [1.15.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.14.1...airtrip-v1.15.0) (2024-08-23)


### Features

* add recaptcha to signup ([3d896e2](https://github.com/InboundPlatform/global-esim/commit/3d896e2f87d2692c36324ddc0e59f639e20957c3))


### Bug Fixes

* add recaptcha to callback ([fbd0ebb](https://github.com/InboundPlatform/global-esim/commit/fbd0ebb9c25d75a7ff6d2d6ba2d4b6ff141d3930))
* signup error message fix ([a54a96d](https://github.com/InboundPlatform/global-esim/commit/a54a96d498e27c366804e4b86934a24bbceb1639))
* translation fixes ([872db33](https://github.com/InboundPlatform/global-esim/commit/872db3302a86ce19d03c0dd6e0485f38da502954))

## [1.14.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.14.0...airtrip-v1.14.1) (2024-08-16)


### Bug Fixes

* build issues ([537abbb](https://github.com/InboundPlatform/global-esim/commit/537abbb4b27a7394591c10e95d9040994e166482))

## [1.14.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.13.0...airtrip-v1.14.0) (2024-08-15)


### Features

* 50839 feedback updates 8/14 ([001c2b1](https://github.com/InboundPlatform/global-esim/commit/001c2b11954a40fde3209db6bb827e06789e3b3f))
* 51080 setup page ([e78ae66](https://github.com/InboundPlatform/global-esim/commit/e78ae6636c5b5a083ece984011c975b5adc55889))
* 51116 data usage note ([8d296f9](https://github.com/InboundPlatform/global-esim/commit/8d296f9f1ff21fd3ab7a315e7416b83a7d7316d2))


### Bug Fixes

* 51080 reset carrousel on tab change ([e667add](https://github.com/InboundPlatform/global-esim/commit/e667add9474154d7c797e64b4026cffd9fd53be1))

## [1.13.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.12.1...airtrip-v1.13.0) (2024-08-08)


### Features

* 08/08 feedbacks ([d8423d4](https://github.com/InboundPlatform/global-esim/commit/d8423d489274ea7b97d097485ba6236cea70b5cf))

## [1.12.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.12.0...airtrip-v1.12.1) (2024-08-07)


### Bug Fixes

* deploy fix airtrip ([dacdf76](https://github.com/InboundPlatform/global-esim/commit/dacdf76e7468800192486e30ffac404b7b5ed51c))

## [1.12.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.11.0...airtrip-v1.12.0) (2024-08-07)


### Features

* 50839 feedback fixes ([a733ee4](https://github.com/InboundPlatform/global-esim/commit/a733ee45a757239b6726bd51946a34c95bfc4fce))
* 50839 feedback fixes ([8e44a17](https://github.com/InboundPlatform/global-esim/commit/8e44a17922ab78b58bd7e34ccf702e2465bf747c))
* 50839 feedback updates ([e518ec1](https://github.com/InboundPlatform/global-esim/commit/e518ec13f11d5423e3cc240c2fe667e31f7c7ee6))
* 50945 avoid redirection on campaign order success ([d7d9f9f](https://github.com/InboundPlatform/global-esim/commit/d7d9f9f903f951097a6e08d484b13f34a1cf4fb2))
* 50945 login after sms verification ([66ac929](https://github.com/InboundPlatform/global-esim/commit/66ac929da8ccb464348e9a703abe679b87157023))
* 51010 destination page ([36343dc](https://github.com/InboundPlatform/global-esim/commit/36343dce393a7bb984135053f624f85d06066fa1))
* add iccid to gm esim complete page ([56ab054](https://github.com/InboundPlatform/global-esim/commit/56ab0542200ca093fea87a7ff9e86480dd83cb5e))
* add iccid to order complete page and order confirm mail ([695d8f8](https://github.com/InboundPlatform/global-esim/commit/695d8f8d5f598065ae07487dfcb183cbb3632511))
* increase sms resend timer ([6c72e7c](https://github.com/InboundPlatform/global-esim/commit/6c72e7c87815069f16b83310d9caa2db7e806177))


### Bug Fixes

* 50945 stop rerender on login ([8ee9fb5](https://github.com/InboundPlatform/global-esim/commit/8ee9fb57f240f4ab16bcc6b10e53e1b2876eeccf))
* 50945 stop rerender on login ([3958fc8](https://github.com/InboundPlatform/global-esim/commit/3958fc85d67aa6c21eaf2912d8dcee0ec7ad0460))
* 51010 uk search fix ([426641a](https://github.com/InboundPlatform/global-esim/commit/426641aa61f26bf5b2910e232ed38567332c55e5))
* airtrip esim comparison image fix ([21169cd](https://github.com/InboundPlatform/global-esim/commit/21169cd84604c806d6d1a4e5d1e5661d662fc27e))
* image fixes, cherry pick fixes ([aa6567a](https://github.com/InboundPlatform/global-esim/commit/aa6567a74951b7379ccbca520b126890dbec3810))

## [1.11.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.10.1...airtrip-v1.11.0) (2024-08-01)


### Features

* 50839 feedback fixes ([72ba8eb](https://github.com/InboundPlatform/global-esim/commit/72ba8eb1f1a5d68961c1182836c0a88a4d9a8a4c))
* 50948 search module ([f8e160a](https://github.com/InboundPlatform/global-esim/commit/f8e160ae539c55f39d33a52acdbb22bba7027237))
* 50964 add compatible function ([2be2fef](https://github.com/InboundPlatform/global-esim/commit/2be2fefd36f318de4780bb8e9dea3e11aad34621))
* gm esim search module ([d7fa8ae](https://github.com/InboundPlatform/global-esim/commit/d7fa8ae36d1c9bc8119c0f9ab327fffd5560a096))
* search by upper case ([0fa3fe5](https://github.com/InboundPlatform/global-esim/commit/0fa3fe52f0d2df531b612c5607e9914c90d80aae))


### Bug Fixes

* 50948 text updates ([5588f76](https://github.com/InboundPlatform/global-esim/commit/5588f7634d7131c97ea0c34120e7cdd604ca8274))
* 50964 translation fixes, image fixes ([eb6cc31](https://github.com/InboundPlatform/global-esim/commit/eb6cc313c6d613f62d5818864b27f5ad89711a91))
* image fixes, cherry pick fixes ([ba4626c](https://github.com/InboundPlatform/global-esim/commit/ba4626cff4137683e8d903d602f380e3b7ec6c44))
* missing flag fix ([b3995d1](https://github.com/InboundPlatform/global-esim/commit/b3995d1f150140a6d1cdd19876b02b8d446da164))
* search module missing image fix ([fa031c4](https://github.com/InboundPlatform/global-esim/commit/fa031c4c041da815c13dc32a97efa69ad52981d7))

## [1.10.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.10.0...airtrip-v1.10.1) (2024-07-29)


### Bug Fixes

* remove spaces in country names ([8c73ab1](https://github.com/InboundPlatform/global-esim/commit/8c73ab197496cd3f9c076e09ebd806b07c61ed2e))
* top page sort fix ([637bfe6](https://github.com/InboundPlatform/global-esim/commit/637bfe6edc86b6f72acb3172dba82aaffce84d06))

## [1.10.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.9.2...airtrip-v1.10.0) (2024-07-26)


### Features

* added recaptcha for airtrip fe ([48855de](https://github.com/InboundPlatform/global-esim/commit/48855deb4b273b92156f1ad75d428bf780b561d1))


### Bug Fixes

* add loading indicator for signin signup buttons ([9a58ad6](https://github.com/InboundPlatform/global-esim/commit/9a58ad613f10b7b0645674f5f54bedf2224ec44c))
* login error translations ([7720fb3](https://github.com/InboundPlatform/global-esim/commit/7720fb34769856bb82befa8f1b5123e9568fb957))

## [1.9.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.9.1...airtrip-v1.9.2) (2024-07-26)


### Bug Fixes

* image fix ([b39d3b8](https://github.com/InboundPlatform/global-esim/commit/b39d3b89e6dc45c9d4793572f077d9f3b603892c))

## [1.9.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.9.0...airtrip-v1.9.1) (2024-07-25)


### Bug Fixes

* missing images and api parameter fix ([f358950](https://github.com/InboundPlatform/global-esim/commit/f358950aa677c240267de70f3aea3992ebceaa8e))

## [1.9.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.8.1...airtrip-v1.9.0) (2024-07-25)


### Features

* 50973 add new countries ([b830ee7](https://github.com/InboundPlatform/global-esim/commit/b830ee79cb9d04eb167b2cfd4346ab5cbdc674e8))
* 50973 updated countries price, country images ([b6b6d7d](https://github.com/InboundPlatform/global-esim/commit/b6b6d7dea667e4f074d6633c7a951bbfd324c004))
* countries translation updates, mobile ui updates ([ff483dd](https://github.com/InboundPlatform/global-esim/commit/ff483dd16abdc6cf2232ca9e32d47a349481641e))


### Bug Fixes

* region page currency symbol fix ([0e00ead](https://github.com/InboundPlatform/global-esim/commit/0e00ead32b28eb42a55d28a106d26531ffb4b3cb))

## [1.8.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.8.0...airtrip-v1.8.1) (2024-07-19)


### Bug Fixes

* feature plan daily price ([#607](https://github.com/InboundPlatform/global-esim/issues/607)) ([bc12f3b](https://github.com/InboundPlatform/global-esim/commit/bc12f3bd5ba44794176e6a1e0a0abf0d567d0ae7))
* lgu feedback fixes ([#605](https://github.com/InboundPlatform/global-esim/issues/605)) ([624aebf](https://github.com/InboundPlatform/global-esim/commit/624aebf7dc8a0ebe69c6efd2158d7d12b336a4c0))
* update link to order list ([cdf05cc](https://github.com/InboundPlatform/global-esim/commit/cdf05cc3e4485e73f2ed5518a2dde215ae1cdf6a))

## [1.8.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.7.0...airtrip-v1.8.0) (2024-07-18)


### Features

* 50839 lgu updates ([545b72d](https://github.com/InboundPlatform/global-esim/commit/545b72d93fd050c5638c38f2963bf78e1855f4d0))
* 50839 show LGU and ui updates ([46e1caa](https://github.com/InboundPlatform/global-esim/commit/46e1caad56b0716d254e5a95241ff2319d1b0a85))
* airtrip ui/ux updates, passport form updates ([56bb006](https://github.com/InboundPlatform/global-esim/commit/56bb0067b4137eb079ec3dd2a5d346d7ce944e66))
* traffic data text updates, jp scroll to plan ([05f4d4e](https://github.com/InboundPlatform/global-esim/commit/05f4d4ea33050f6d5d491cf7740280c463d0c0f6))


### Bug Fixes

* build error fixes ([a149743](https://github.com/InboundPlatform/global-esim/commit/a14974312c57bc5561b4487492445c9a6b01dbc0))
* get plans api parameter fix ([16388f1](https://github.com/InboundPlatform/global-esim/commit/16388f161116480a23672b420ff1e4e3d491066a))
* lgu fixes ([8f89d23](https://github.com/InboundPlatform/global-esim/commit/8f89d23bf918a4d801debea213fa224e88c74db7))
* qr code src fix ([5f56cd3](https://github.com/InboundPlatform/global-esim/commit/5f56cd378e69b9addbd425dd5013515da01f6131))
* resend verification email link ([dcefa1a](https://github.com/InboundPlatform/global-esim/commit/dcefa1a5c01a1c2a5e173669658d65834f815a99))
* text and redirection fixes ([ed52b02](https://github.com/InboundPlatform/global-esim/commit/ed52b028e89ac4ab5e488e8051659b6bb380562b))
* text and translation fixes ([edf22fa](https://github.com/InboundPlatform/global-esim/commit/edf22fa2a38a3e6b8c8871cf68c8d6086134096f))
* wip ([095d7c3](https://github.com/InboundPlatform/global-esim/commit/095d7c35e51ce02c5fee2bad706dd86edb28943c))

## [1.7.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.6.1...airtrip-v1.7.0) (2024-07-11)


### Features

* 50839 50932 ui/ux updates ([2ac3050](https://github.com/InboundPlatform/global-esim/commit/2ac30506725de33bff032df27e2cdf26434d6e1b))
* 50881 passport form ui/ux updates ([5062562](https://github.com/InboundPlatform/global-esim/commit/5062562af78448baaa28a6adfb32ab42f4f77982))
* 50931 added traffic data ui ([ee1a61e](https://github.com/InboundPlatform/global-esim/commit/ee1a61e49f7990f73ced42819653daa78b506180))
* added new countries tawain hongkong ([42ef3e9](https://github.com/InboundPlatform/global-esim/commit/42ef3e9a91214cacc522c4fc996012a1bbf09285))
* airtrip hongkong taiwan, moved plan api call to client ([0cf53b8](https://github.com/InboundPlatform/global-esim/commit/0cf53b85c9425375a1497b497be33d708da77686))
* bg updates, scroll to traffic data ([058dfdb](https://github.com/InboundPlatform/global-esim/commit/058dfdb56aa033badb1590ad741a74179436f9c7))
* gm taiwan hongkong ([1410eb0](https://github.com/InboundPlatform/global-esim/commit/1410eb08e6da831f95893ad422c59d288f93dbae))
* order form appeal, text fixes ([3a0e9c1](https://github.com/InboundPlatform/global-esim/commit/3a0e9c157d28bef5eb14aa7532aa677efe29c8d2))
* top page country sort and price updates ([1b8edc0](https://github.com/InboundPlatform/global-esim/commit/1b8edc02ee4f3b6f4fa30bfd8fc103b95296ed18))
* update loading ui ([bbca723](https://github.com/InboundPlatform/global-esim/commit/bbca723dcf6ef4b46df25f9d9eaa8d23061d595d))


### Bug Fixes

* airtrip region header parameter fix ([3df102e](https://github.com/InboundPlatform/global-esim/commit/3df102e1e9f3922592b62db792caebedaf7c9291))
* complete page message fix ([4272689](https://github.com/InboundPlatform/global-esim/commit/4272689225fb4d4780e1aa9323d6c44e773430f1))
* feedback fixes ([f0fb6c7](https://github.com/InboundPlatform/global-esim/commit/f0fb6c7aa52eee78fb5b6cd209a0600f1ab28ed3))
* feedback fixes 07/11 ([c816c1b](https://github.com/InboundPlatform/global-esim/commit/c816c1bc907b40e338b1748b3a150ad941f70aa5))
* header myesim link fix ([970ee21](https://github.com/InboundPlatform/global-esim/commit/970ee211c0d77e06c28b6eb187d4d77daaaf954b))
* need registration session fix ([16d550e](https://github.com/InboundPlatform/global-esim/commit/16d550e2bf19ba3db761bfa0e7a49965aab6beb8))
* text fixes ([f863083](https://github.com/InboundPlatform/global-esim/commit/f863083f8d53182cc66f01737b373a71aeb82479))

## [1.6.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.6.0...airtrip-v1.6.1) (2024-07-08)


### Bug Fixes

* redeploy static page ([122737b](https://github.com/InboundPlatform/global-esim/commit/122737b7a7234196572ef4f614d7313ddb0e4cff))

## [1.6.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.5.1...airtrip-v1.6.0) (2024-07-05)


### Features

* add line button, re-add guam ([d38edfd](https://github.com/InboundPlatform/global-esim/commit/d38edfd16322d3c2c9b18c622d60d045ee95fe01))

## [1.5.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.5.0...airtrip-v1.5.1) (2024-07-04)


### Bug Fixes

* remove guam temporarily ([07a48f4](https://github.com/InboundPlatform/global-esim/commit/07a48f48be4a8cc91bcb54b00e6807064ec8cae7))

## [1.5.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.4.1...airtrip-v1.5.0) (2024-07-04)


### Features

* 50449 activation page updates ([0565a8d](https://github.com/InboundPlatform/global-esim/commit/0565a8d505b03758689df7e8af6d3d4607909672))
* 50449 android guide ([b96edf4](https://github.com/InboundPlatform/global-esim/commit/b96edf444263a81cba73a2aae4fff8b3310208a5))
* 50449 android guide ([c204631](https://github.com/InboundPlatform/global-esim/commit/c204631223c82f6601a281d306e5c48833bdd021))
* 50449 android guide images ([46b4473](https://github.com/InboundPlatform/global-esim/commit/46b44735294ab261477af689d54c40087ce9a853))
* 50449 gm esim activation page updates ([5590470](https://github.com/InboundPlatform/global-esim/commit/5590470b638a9208152e8c09f33f2fa6e407df56))
* 50567 compatible page redesign ([0909b82](https://github.com/InboundPlatform/global-esim/commit/0909b820d578e219bf9b70eb252689f1956ad7ae))
* 50567 removed modal for future redesign ([5db9f8c](https://github.com/InboundPlatform/global-esim/commit/5db9f8c9139d97fe6568ce975fd494c0dbe42927))
* 50759 faq page updates for airtrip and gm ([3f66850](https://github.com/InboundPlatform/global-esim/commit/3f668504a34fec94066f8f2d70dd4c1db6b9d627))
* 50845 order form redesign ([1bb6c6c](https://github.com/InboundPlatform/global-esim/commit/1bb6c6c4587a53459a9e88a41560e94cbcc0a798))
* activation carrousel updates ([7ea6811](https://github.com/InboundPlatform/global-esim/commit/7ea68112f699c55d853c4f5b47b4a3cd7d14ae6c))
* add new countries to airtrip ([fabd5f9](https://github.com/InboundPlatform/global-esim/commit/fabd5f95b639d08879db0700e85b263eb808520e))
* added new countries to gm esim ([20e88e6](https://github.com/InboundPlatform/global-esim/commit/20e88e601d6e323f9052df7947f7393dc6e44ff8))
* handle unlimited plans ([266ba44](https://github.com/InboundPlatform/global-esim/commit/266ba44209f824dbac697f126b44bbaa7d2fba54))
* new countries updated price ([93b02c8](https://github.com/InboundPlatform/global-esim/commit/93b02c8f7523af9d8ac0d782924d8e6bf8ad19b1))
* new faq page ([f41af7f](https://github.com/InboundPlatform/global-esim/commit/f41af7f13c5b03dbf47e9d1b9d938b04fb1d11f1))


### Bug Fixes

* 50839 gm scroll fix, ui updates ([ec9deb9](https://github.com/InboundPlatform/global-esim/commit/ec9deb98a13b139aedcfbd84c6abdfc276ae12ec))
* 50839 setup guide fixes ([b2bf6b9](https://github.com/InboundPlatform/global-esim/commit/b2bf6b954378266fff2bbde24ab81a73cb675f4e))
* 50839 text updates, validation updates ([edb2262](https://github.com/InboundPlatform/global-esim/commit/edb226231370178fb7e970af67161a63c1f136eb))
* airtrip faq links ([0245a06](https://github.com/InboundPlatform/global-esim/commit/0245a060f8d0527e1ae71a8411a929d2dda0ab7f))
* airtrip icon fixes ([12057aa](https://github.com/InboundPlatform/global-esim/commit/12057aab354ea9be37568c7a8e5c3dd3486765b4))
* button size fixes, text size fixes, link fixes ([34d64d3](https://github.com/InboundPlatform/global-esim/commit/34d64d3a4662744bd2549fd2d2420374f0916f9b))
* change bg color ([c0c092d](https://github.com/InboundPlatform/global-esim/commit/c0c092dd59b0317f05c4b93cb546eeade9b959cb))
* icons, style fixes ([b2a68e2](https://github.com/InboundPlatform/global-esim/commit/b2a68e29ee3f433a09a34cbf53d07088db4ded61))
* login error message update ([626e5d2](https://github.com/InboundPlatform/global-esim/commit/626e5d22dcb0e3ac9de61afbac1eac0a0389e549))
* missing hawaii translation ([826e6e0](https://github.com/InboundPlatform/global-esim/commit/826e6e091c1175b06cbf6bff160fb25eeaf1c8da))
* url update ([d567e6f](https://github.com/InboundPlatform/global-esim/commit/d567e6f1b361ac82fa0a4889dc01060c77f0dcdc))

## [1.4.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.4.0...airtrip-v1.4.1) (2024-06-30)


### Bug Fixes

* changed name char limit to 1, update error messages to jp ([2aa461c](https://github.com/InboundPlatform/global-esim/commit/2aa461cb704eb468dc8f993a8ba3003043337e02))

## [1.4.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.3.0...airtrip-v1.4.0) (2024-06-27)


### Features

* 50839 add esim to goronavi ([195e232](https://github.com/InboundPlatform/global-esim/commit/195e2329d8f99e465680204d51d363a668f92432))
* added reset password, resend email pages ([157c1fd](https://github.com/InboundPlatform/global-esim/commit/157c1fd167bf48d862ab144b2280ca1664847971))


### Bug Fixes

* 50839 added no user error message ([7f8623e](https://github.com/InboundPlatform/global-esim/commit/7f8623ede3ace3ca8343c3afea848137e9efa594))
* goronavi text update ([e459607](https://github.com/InboundPlatform/global-esim/commit/e45960705e2a78d9da02ae65b5f45c055d70cd9f))

## [1.3.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.2.0...airtrip-v1.3.0) (2024-06-26)


### Features

* add apple pay domain association file ([890c1a4](https://github.com/InboundPlatform/global-esim/commit/890c1a430d9baef72e20c72bfd85735bb2c48d7b))

## [1.2.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.1.2...airtrip-v1.2.0) (2024-06-26)


### Features

* sp kv updates ([f045019](https://github.com/InboundPlatform/global-esim/commit/f0450197569b798198bc6a59abfbd721ade2da2c))


### Bug Fixes

* button size fixes ([7693340](https://github.com/InboundPlatform/global-esim/commit/7693340c26b39fba95fe35defe242b09f893792d))
* feedback fixes 06/25 ([7585bad](https://github.com/InboundPlatform/global-esim/commit/7585badc5f18d13bd93bc4ee5c560cd154c07d06))
* feedback ui fixes ([8aa54b5](https://github.com/InboundPlatform/global-esim/commit/8aa54b55934210e7cd629a1ef806aaf024941ea2))
* feedback ui fixes ([5e65996](https://github.com/InboundPlatform/global-esim/commit/5e65996c6a79eedf16a134c2b6b03b60bbc2bd23))
* feedback ui fixes ([411b7f7](https://github.com/InboundPlatform/global-esim/commit/411b7f70c51b58ec8a73acd1456064c5001da3cd))
* image size fixes ([d68442d](https://github.com/InboundPlatform/global-esim/commit/d68442d0dfcbefc0e6be161ba73900a95802cfa7))
* links, ui, manual fixes ([6ec439a](https://github.com/InboundPlatform/global-esim/commit/6ec439acca95056336f068439825f68f21c6a80a))
* responsive fixes, img fixes ([f70822e](https://github.com/InboundPlatform/global-esim/commit/f70822edf4c41ba0d74750f14b75a6eee35eb2e6))
* sp esim/wifi navigation ui fixes ([1ae8013](https://github.com/InboundPlatform/global-esim/commit/1ae80136958b5799721794f11154974065d30cb8))
* sp top kv tab color fixes ([a293640](https://github.com/InboundPlatform/global-esim/commit/a2936408931242ced1c564971bc2ea942caeac5b))
* spacing fixes ([00c33b7](https://github.com/InboundPlatform/global-esim/commit/00c33b7d99c00e26b3c403a1307cc9e3e132ea9e))
* spacing fixes ([0ac9d8d](https://github.com/InboundPlatform/global-esim/commit/0ac9d8d07fa38196eb59c46807e1368e1788c013))
* spacing fixes ([0505be3](https://github.com/InboundPlatform/global-esim/commit/0505be377c5759ff6ba41272a0411e2fd41b4ea8))

## [1.1.2](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.1.1...airtrip-v1.1.2) (2024-06-24)


### Bug Fixes

* global esim fixes ([cc99589](https://github.com/InboundPlatform/global-esim/commit/cc995897d4e41ff85b9979b1b5b9603921caaa47))

## [1.1.1](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.1.0...airtrip-v1.1.1) (2024-06-24)


### Bug Fixes

* build issues of airtrip ([554f0ad](https://github.com/InboundPlatform/global-esim/commit/554f0adff602f2cddfe5f628633527bd13a56a81))

## [1.1.0](https://github.com/InboundPlatform/global-esim/compare/airtrip-v1.0.0...airtrip-v1.1.0) (2024-06-24)


### Features

* add airtrip to deploy workflow ([5bfb751](https://github.com/InboundPlatform/global-esim/commit/5bfb751bf66c95ff483a094fec1673a473d90a36))
* add new airtrip esim project ([ce7a53a](https://github.com/InboundPlatform/global-esim/commit/ce7a53acf757b407d8df4fd31e4cdd8f951e13d9))
* added line button, removed lgu contents ([4fd7f11](https://github.com/InboundPlatform/global-esim/commit/4fd7f11c642e89f1699e7d3245bd7f368970b736))
* contact page and help page updates ([7c7f858](https://github.com/InboundPlatform/global-esim/commit/7c7f8588219528bc5a7ea2edafacca08e66880b0))
* init airtrip kv ([37ce23b](https://github.com/InboundPlatform/global-esim/commit/37ce23bbba2d33c467bb376184715e8749c9b049))
* update airtrip register api ([2322082](https://github.com/InboundPlatform/global-esim/commit/2322082357fdae2ff24851f1873f130f2cc14761))
* update korea banners to lgu ([b9e1cee](https://github.com/InboundPlatform/global-esim/commit/b9e1cee9fecae313717194b52fb3b9db22b7a8bc))


### Bug Fixes

* added terms and conditions page ([ee7d68c](https://github.com/InboundPlatform/global-esim/commit/ee7d68cd48fdf794012affb915f9f10b55b131f4))
* airtrip link fixes, korea counter details, lgu compatiblity ([6ac7ec0](https://github.com/InboundPlatform/global-esim/commit/6ac7ec0d280eb365b1b81195edda1cf235d3c5ef))
* feedback fixes ([2ff26a6](https://github.com/InboundPlatform/global-esim/commit/2ff26a672afd17be780ff0c5b389889c729b824f))
* feedback fixes 06/19 ([79d7dc6](https://github.com/InboundPlatform/global-esim/commit/79d7dc6fbd9fa17cb14514bb37a54b68606c08a9))
* image url fix ([a2c253b](https://github.com/InboundPlatform/global-esim/commit/a2c253b06d927102373af0300718e3642b3d97a1))
* line button position fix ([3a21094](https://github.com/InboundPlatform/global-esim/commit/3a2109410eddaf17bb8aa81b939f926b6193e941))
* z index fixes ([9bb8243](https://github.com/InboundPlatform/global-esim/commit/9bb8243748ed41488d94e020ae1db6544f1038a3))
