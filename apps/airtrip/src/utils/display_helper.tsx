import {
  IconCoinPound,
  IconCoinYen,
  IconCoinEuro,
  IconCoin,
  IconCoinYuan
} from "@tabler/icons-react";

export const getCurrencyIcon = (code: string) => {
  if (["HKD", "TWD", "CAD", "AUD", "USD"].includes(code)) {
    return <IconCoin size={24} color="#666" />;
  } else if (code === "JPY") {
    return <IconCoinYen size={24} color="#666" />;
  } else if (code === "GBP") {
    return <IconCoinPound size={24} color="#666" />;
  } else if (code === "CNY") {
    return <IconCoinYuan size={24} color="#666" />;
  } else if (code === "EUR") {
    return <IconCoinEuro size={24} color="#666" />;
  } else {
    return null
  }
}