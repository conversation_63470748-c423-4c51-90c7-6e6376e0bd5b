import { get, set as immutableSet } from "dot-prop-immutable";

import { getCookie, setCookie } from "./cookies";

type StorageType = "local" | "cookie";

class Storage {
  static INSTANCE: Storage;
  static NAME = "ri-values";
  public values: { [key: string]: any } = {};
  private storageType: StorageType;

  constructor(storageType: StorageType = "local") {
    this.storageType = storageType;
    this.loadValues();
  }

  setValues(values: any) {
    this.values = values;
  }

  loadValues() {
    if (typeof window === "undefined") return;

    let loadedValues = null;

    // Try to load from the configured storage type
    if (this.storageType === "cookie") {
      loadedValues = getCookie(Storage.NAME);
    } else {
      loadedValues = window.localStorage.getItem(Storage.NAME);
    }

    // Parse the loaded values
    try {
      this.values = loadedValues ? JSON.parse(loadedValues) : {};
    } catch (error) {
      this.values = {};
    }

    // Initialize storage if empty
    if (
      Object.keys(this.values).length === 0 &&
      typeof window !== "undefined"
    ) {
      const emptyValues = "{}";
      window.localStorage.setItem(Storage.NAME, emptyValues);
      setCookie(Storage.NAME, emptyValues, { expires: 30 });
      this.values = {};
    }
  }

  getFromCookie(key: string) {
    const cookieValue = getCookie(key);
    if (!cookieValue) {
      return null;
    }

    try {
      return JSON.parse(cookieValue);
    } catch (e) {
      return cookieValue;
    }
  }

  get(key: string, defaultValue = null) {
    this.loadValues();

    // First try to get from our stored values
    const valueFromStorage = get(this.values, key, defaultValue);
    if (valueFromStorage !== null && valueFromStorage !== undefined) {
      return valueFromStorage;
    }

    // Fall back to checking cookies directly
    return this.getFromCookie(key) || defaultValue;
  }

  set(
    key: string,
    value: any,
    options: {
      storageType?: StorageType;
      expiresAt?: number | Date;
    } = {}
  ) {
    // Update the in-memory values
    this.values = immutableSet(this.values, key, value);
    const valueString = JSON.stringify(this.values);

    const targetStorageType = options.storageType || this.storageType;

    if (typeof window !== "undefined") {
      window.localStorage.setItem(Storage.NAME, valueString);
    }

    // Set in cookie if using cookie storage
    if (targetStorageType === "cookie") {
      setCookie(Storage.NAME, valueString, {
        expires: options.expiresAt || 30,
      });
    }
  }

  setStorageType(storageType: StorageType) {
    this.storageType = storageType;
    this.loadValues();
  }

  static getInstance(storageType: StorageType = "local"): Storage {
    if (!Storage.INSTANCE) {
      Storage.INSTANCE = new Storage(storageType || "local");
    } else if (storageType && Storage.INSTANCE.storageType !== storageType) {
      // Update the storage type if it's different from the current one
      Storage.INSTANCE.setStorageType(storageType);
    }

    return Storage.INSTANCE;
  }
}

export default Storage;
