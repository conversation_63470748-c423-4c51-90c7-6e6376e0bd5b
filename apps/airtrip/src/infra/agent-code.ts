import { apiClient } from "@/utils/api-client";

interface CheckExistAgentCodeParamsI {
  agent_code: string;
  branch?: string;
  tour?: string;
  code?: string;
}
interface CheckExistAgentCodeResponseI {
  is_exists: number;
}

export async function checkExistAgentCode(
  params: CheckExistAgentCodeParamsI
): Promise<CheckExistAgentCodeResponseI> {
  const { data } = await apiClient.post<{ data: CheckExistAgentCodeResponseI }>(
    "/api/gm/agents/check-agent",
    params,
    { baseURL: process.env.NEXT_PUBLIC_ORIGIN_URL }
  );

  return data.data;
}
