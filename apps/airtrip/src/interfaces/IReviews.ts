export interface IGetReviewsItems {
  title: string;
  content: string;
  score: string;
  created_at: string;
  name: string;
  product_name: string;
}

export interface IGetReviewsList {
  review: IGetReviewsItems[];
}
export interface IGetReviewsSummary {
  total_reviews: string;
  average_ratings: number;
}

export interface IGetReviewsResponse {
  ukomiReviews: IGetReviewsList;
  ukomiSumary: IGetReviewsSummary;
}

export interface IReviews {
  reviews: IGetReviewsItems[];
  page: number;
}
