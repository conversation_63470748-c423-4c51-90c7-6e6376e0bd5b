import Link from "next/link";
import React, { useCallback } from "react";

import { Group, Text } from "@mantine/core";

import { useAuth } from "@ri/fe-auth";
import { jwtDecode } from "jwt-decode";

import { withBasePath } from "@/utils";
import Storage from "@/utils/storage";

import { ApiService } from "@/api";

import { AUTH_TOKEN_NAME, ERR_PASSWORD_NOT_MATCHED } from "@/app/constants";
import { useMessageStore } from "@/store/MessageStore";

const useLogin = () => {
  const auth = useAuth();
  const [setGlobalMessage, setGlobalLoading, isLoading] = useMessageStore(
    (s) => [s.setMessage, s.toggleLoading, s.isLoading]
  );
  const onToken = useCallback((data: { accessToken: string }) => {
    Storage.getInstance().set(AUTH_TOKEN_NAME, data);
    const decoded = jwtDecode(data.accessToken) as {
      email: string;
      user_id: string;
      name: string;
      companyId: string;
    };
    auth?.startSession({
      id: decoded.user_id,
      displayName: decoded.name,
      email: decoded.email,
      data: {
        companyId: "",
      },
    });
  }, []);
  const logout = React.useCallback(() => {
    auth?.endSession();
    Storage.getInstance().set(AUTH_TOKEN_NAME, "");
    setTimeout(() => {
      //Buffer time for state clearance
      window.location.href = withBasePath("auth/signin");
    }, 100);
  }, []);

  const login = React.useCallback(
    (payload: { email: string; password: string }) => {
      setGlobalLoading(true);
      ApiService.login(payload)
        .then(({ data }) => {
          onToken(data.data);
        })
        .catch((err) => {
          let errorMessage =
            err?.response?.data?.message || ERR_PASSWORD_NOT_MATCHED;
          if (
            err?.response?.data?.message ===
              "Incorrect username or password." ||
            err?.response?.data?.message ===
              "The provided username and password combination does not match."
          ) {
            errorMessage = (
              <Text size={"sm"} c="app-dark">
                メールアドレスまたは、パスワードが違います。
                <br />
                ご確認お願い致します。
              </Text>
            );
          }
          // TODO: only check errorCode once all API error codes are set
          if (
            err?.response?.data?.message === "User is not confirmed." ||
            err?.response?.data?.errorCode === 4007
          ) {
            errorMessage = (
              <Group className="gap-0">
                <Text size={"sm"} c="app-dark">
                  ユーザーは確認されていません。
                </Text>
                <Link href={"/account/resend-email"}>
                  <Text size={"sm"}>メールを再送</Text>
                </Link>
              </Group>
            );
          }
          if (err?.response?.data?.message === "No user found") {
            errorMessage = "ユーザーが見つかりません ";
          }
          setGlobalMessage(<>{errorMessage}</>);
        })
        .finally(() => {
          setGlobalLoading(false);
        });
    },
    []
  );

  return {
    isLoading,
    login,
    logout,
    onToken,
  };
};
export default useLogin;
