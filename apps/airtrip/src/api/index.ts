import axios from "axios";



import Storage from "@/utils/storage";



import { IPostOrderPayload } from "@/interfaces/IPostOrderPayload";
import { IProfile } from "@/interfaces/IProfile";
import { IGetReviewsResponse } from "@/interfaces/IReviews";



import { AUTH_TOKEN_NAME } from "@/app/constants";





const baseURL = process.env.NEXT_PUBLIC_API_HOST;

export const httpClient = axios.create({
  baseURL,
});

const instantEsim = (payload: { secret: string;[key: string]: string }) =>
  httpClient.post("/esim/instant-esim", payload);

const resendVerificationEmail = (
  email: string | undefined,
  phone_number?: number | undefined
) =>
  httpClient.get("/auth/resend-verification", {
    params: {
      email,
      phone_number: phone_number && +phone_number,
    },
  });

const resendPhoneVerificationCode = (
  email: string | undefined,
  phone_number: number | undefined
) =>
  httpClient.post("/auth/resend-phone-verification", {
    email,
    phone_number: phone_number && +phone_number,
  });
export const privateHttpClient = axios.create({
  baseURL,
  validateStatus: (status) => status >= 200 && status < 300, // default,
});

httpClient.interceptors.request.use((config) => {
  config.headers["x-user-pool"] = `AIRTRIP`;
  config.headers["x-service-name"] = `GLOBAL_ESIM_AIRTRIP`;
  return config;
});
privateHttpClient.interceptors.request.use((config) => {
  const token = Storage.getInstance().get(AUTH_TOKEN_NAME);
  config.headers["x-user-pool"] = `AIRTRIP`;
  config.headers["x-service-name"] = `GLOBAL_ESIM_AIRTRIP`;

  if (config.headers && token?.accessToken)
    config.headers["Authorization"] = `Bearer ${token?.accessToken || ""}`;

  return config;
});

const login = (payload: object) => httpClient.post("/auth/login", payload);
const register = (payload: object) =>
  httpClient.post("/auth/register", {
    ...payload,
    website: "airtrip",
  });

const getProfile = (query: object) =>
  privateHttpClient.get("/auth/profile", {
    params: query,
  });

const getPlans = (query: object) =>
  httpClient.get("/plans", {
    params: query,
  });
const getQuotation = (query: object) =>
  httpClient.post("/esim/order/quotation", query);

export async function getUkomiReviews(
  params: object
): Promise<IGetReviewsResponse> {
  const { data } = await httpClient.post<{ data: IGetReviewsResponse }>(
    "/api/gm/ukomi/get-reviews",
    params,
    {
      baseURL: process.env.NEXT_PUBLIC_UKOMI_REVIEWS_BASE_URL,
      timeout: 30000, // 30 seconds
    }
  );

  return data?.data;
}

const redeemCoupon = (query: object) =>
  httpClient.post("/coupons/redeem-guest", query);
const redeemCouponGuest = (query: object) =>
  httpClient.post("/coupons/redeem", query);
const getCoupon = (query: object) =>
  httpClient.get("/coupons", {
    params: query,
  });

const getPlanById = (planId: string) => httpClient.get("/plans/" + planId, {});

const getClientSecret = (payload: {}) => {
  return privateHttpClient.post("/payment/setup", payload);
};
const purchasePlan = (plan: object) =>
  privateHttpClient.post("/esim/subscribe", plan);

const purchasePlanAsGuest = (plan: object) =>
  privateHttpClient.post("/esim/subscribe/guest", plan);

const updateProfile = (profile: Pick<IProfile, "firstName" | "lastName">) =>
  privateHttpClient.post("/auth/profile", profile);

const getOrders = (options: object) =>
  privateHttpClient.get("/esim/orders", {
    params: options,
  });
const getOrder = (orderId: string, options?: object) =>
  privateHttpClient.get("/esim/orders/" + orderId, {
    params: options,
  });

const forgotPassword = (email: string) =>
  httpClient.post("/auth/forgot-password", {
    email,
  });

const resetPassword = (payload: {
  confirmationCode: string;
  newPassword: string;
  email: string;
}) => httpClient.post("/auth/confirm-password", payload);

const saveUserFirebaseToken = (token: string) => {
  return privateHttpClient.post("/notifications/register-token", {
    token,
  });
};

const contactUs = (payload: any) => {
  return httpClient.post("/emails/contact-us", payload);
};

const verifyConfirmationCode = (
  username: string,
  code: string,
  email: null | string,
  query: object
) => {
  console.log(email)
  return httpClient.post(
    "/auth/confirm",
    {
      username,
      code,
      email
    },
    {
      params: query,
    }
  );
};
export const postOrderAsGuest = (payload: IPostOrderPayload) => {
  return httpClient.post("/esim/order/guest", payload);
};

export const postOrder = (payload: IPostOrderPayload) => {
  return privateHttpClient.post("/esim/order", payload);
};
export const ApiService = {
  getUkomiReviews,
  postOrderAsGuest,
  postOrder,
  instantEsim,
  saveUserFirebaseToken,
  resetPassword,
  getOrder,
  forgotPassword,
  getOrders,
  purchasePlan,
  purchasePlanAsGuest,
  getClientSecret,
  login,
  getPlanById,
  getProfile,
  register,
  getPlans,
  updateProfile,
  resendVerificationEmail,
  verifyConfirmationCode,
  contactUs,
  resendPhoneVerificationCode,
  getQuotation,
  redeemCoupon,
  redeemCouponGuest,
  getCoupon,
};