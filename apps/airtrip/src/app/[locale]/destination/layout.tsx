import type { Metadata } from "next";

import TranslationsProvider from "@/app/components/TranslationProvider";

import initTranslations from "@/i18n";
import { locales } from "@/i18n/settings";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "countries",
  "region",
  "payment",
];

export const metadata: Metadata = {
  title: "エアトリeSIMの最新キャンペーン情報まとめ - 今だけの特典をチェック！",
};

const AuthRootLayout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default AuthRootLayout;
