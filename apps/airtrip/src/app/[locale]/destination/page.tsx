"use server";

/*
 * import modules and libraries
 */
import { Suspense } from "react";
import { Text } from "@mantine/core"
import sortBy from "lodash/sortBy";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import Countries from "@/app/components/destination/Countries";
/*
 * import constants and utils
 */
import featuredPlans from "@repo/ui/src/constants/featured-plans.json";
import { REGIONS } from "@repo/ui/src/constants/regions";
import { COUNTRY_SORT } from "@/app/constants";
import { countryAlias } from "@/utils";
/*
 * import interfaces
 */
import type { IPlan } from "@/interfaces/IPlan";

async function getServerSideProps() {
  let formatCountryPlans: IPlan[] = [];
  const plans: {
    regions: IPlan[];
    countries: IPlan[];
  } = featuredPlans.data as {
    countries: any;
    regions: any;
  };

  try {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries.map((item: IPlan) => ({
        ...item,
        order: COUNTRY_SORT.get(item.country) || null,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      })),
      ["order"]
    );

    /* @ts-ignore */
    plans.regions = plans.regions.map((item: IPlan) => ({
      ...item,
      originalName: item.country,
      country: {
        name: countryAlias(item.country as unknown as string),
      },
    }));
  } catch (err) {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries.map((item: IPlan) => ({
        ...item,
        order: COUNTRY_SORT.get(item.country) || null,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      })),
      ["order"]
    );
  }

  return {
    plans,
    formatCountryPlans,
  };
}

export default async function Destination() {
  const props = await getServerSideProps();

  const getRegionCountries = (region: string) => {
    if (!props.formatCountryPlans) {
      return [];
    }

    return props.formatCountryPlans.filter((item: IPlan) => item.region === region);
  }

  return (
    <AppLayout contentBg="#fff" isLogoTextTitle={false}>
      <SectionContent mainTitle={"海外旅行の行き先からeSIMを検索"} usePrimaryColor={true}>
        <Text
          component="p"
          className="font-bold text-sm md:text-base text-center"
        >
          世界<span className="text-primary">149か国</span>所以上で利用可能な海外eSIMを検索できます。<br />
          <span className="text-primary">ヨーロッパ、アジア、世界周遊など、2か国以上をカバーする周遊プランや、<br className="hidden md:inline-block"/>
          韓国、台湾、ハワイ、イタリア、中国、アメリカ、</span><br className="hidden md:inline-block"/>
          などの人気観光地で無制限のデータ通信をご利用いただけます。<br />
          海外eSIMを利用して、もっと気軽に海外旅行を楽しみましょう。
        </Text>
        <Suspense>
          <Countries
            data={[
              ...REGIONS.map((item) => (
                {
                  name: `region:${item.name}`,
                  code: item.code,
                  plans: getRegionCountries(item.code),
                }
              )),
              {
                name: "common:regional-esim",
                code: "regional",
                plans: props.plans.regions,
              },
            ]}
            allData={[
              ...props.formatCountryPlans,
              ...props.plans.regions,
            ]}
          />
        </Suspense>
      </SectionContent>
    </AppLayout>
  );
}
