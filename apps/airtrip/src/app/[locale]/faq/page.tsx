"use client";

import { useMemo, useState } from "react";



import { Grid, em } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";



import { useTranslation } from "react-i18next";



import CategoriesBox from "@repo/ui/src/faq/CategoriesBox";
import FAQHero from "@repo/ui/src/faq/FAQHero";
import FAQSearch from "@repo/ui/src/faq/FAQSearch";
import QuestionAnswerList from "@repo/ui/src/faq/QuestionAnswerList";



import AppLayout from "@/app/components/common/AppLayout";



import { isDev } from "@/utils";





const baseUrl =
  typeof window !== "undefined"
    ? `${window?.location?.origin}/`
    : isDev()
      ? "http://localhost:3000/"
      : "https://esim.airtrip.jp/";

export default function Page() {
  const { t } = useTranslation();
  const [selectedQuestion, setSelectedQuestion] = useState<string>("");
  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });

  const FAQ_DATA = useMemo(
    () => [
      {
        id: "whatisglobalesim",
        category: t("faq:category.whatisglobalesim"),
        qa: [
          {
            id: "whatisglobalesimquestion1",
            q: t("faq:whatisglobalesim.question.1"),
            a: t("faq:whatisglobalesim.answer.1"),
          },
          {
            id: "whatisglobalesimquestion2",
            q: t("faq:whatisglobalesim.question.2"),
            a: t("faq:whatisglobalesim.answer.2"),
          },
          {
            id: "whatisglobalesimquestion3",
            q: t("faq:whatisglobalesim.question.3"),
            a: t("faq:whatisglobalesim.answer.3"),
          },
          {
            id: "whatisglobalesimquestion4",
            q: t("faq:whatisglobalesim.question.4"),
            a: t("faq:whatisglobalesim.answer.4"),
          },
          {
            id: "whatisglobalesimquestion5",
            q: t("faq:whatisglobalesim.question.5"),
            a: t("faq:whatisglobalesim.answer.5"),
          },
          {
            id: "whatisglobalesimquestion6",
            q: t("faq:whatisglobalesim.question.6"),
            a: t("faq:whatisglobalesim.answer.6"),
          },
          {
            id: "whatisglobalesimquestion7",
            q: t("faq:whatisglobalesim.question.7"),
            a: t("faq:whatisglobalesim.answer.7"),
            link: `${baseUrl}compatible-devices`,
          },
          {
            id: "whatisglobalesimquestion8",
            q: t("faq:whatisglobalesim.question.8"),
            a: t("faq:whatisglobalesim.answer.8"),
          },
        ],
      },
      {
        id: "planprice",
        category: t("faq:category.planprice"),
        qa: [
          {
            id: "planpricequestion1",
            q: t("faq:planprice.question.1"),
            a: t("faq:planprice.answer.1"),
          },
          {
            id: "planpricequestion2",
            q: t("faq:planprice.question.2"),
            a: t("faq:planprice.answer.2"),
          },
          {
            id: "planpricequestion3",
            q: t("faq:planprice.question.3"),
            a: t("faq:planprice.answer.3"),
            link: `${baseUrl}support/help`,
          },
          {
            id: "planpricequestion4",
            q: t("faq:planprice.question.4"),
            a: t("faq:planprice.answer.4"),
          },
          {
            id: "planpricequestion5",
            q: t("faq:planprice.question.5"),
            a: t("faq:planprice.answer.5"),
          },
          {
            id: "planpricequestion6",
            q: t("faq:planprice.question.6"),
            a: t("faq:planprice.answer.6"),
            link: `${baseUrl}support/contactus`,
          },
        ],
      },
      {
        id: "howtosetup",
        category: t("faq:category.howtosetup"),
        qa: [
          {
            id: "howtosetupquestion1",
            q: t("faq:howtosetup.question.1"),
            a: t("faq:howtosetup.answer.1"),
          },
          {
            id: "howtosetupquestion2",
            q: t("faq:howtosetup.question.2"),
            a: t("faq:howtosetup.answer.2"),
          },
          {
            id: "howtosetupquestion3",
            q: t("faq:howtosetup.question.3"),
            a: t("faq:howtosetup.answer.3"),
            link: `${baseUrl}support/help`,
          },
          {
            id: "howtosetupquestion4",
            q: t("faq:howtosetup.question.4"),
            a: t("faq:howtosetup.answer.4"),
          },
          {
            id: "howtosetupquestion5",
            q: t("faq:howtosetup.question.5"),
            a: t("faq:howtosetup.answer.5"),
          },
          {
            id: "howtosetupquestion6",
            q: t("faq:howtosetup.question.6"),
            a: t("faq:howtosetup.answer.6"),
          },
          {
            id: "howtosetupquestion7",
            q: t("faq:howtosetup.question.7"),
            a: t("faq:howtosetup.answer.7"),
          },
          {
            id: "howtosetupquestion8",
            q: t("faq:howtosetup.question.8"),
            a: t("faq:howtosetup.answer.8"),
          },
          {
            id: "howtosetupquestion9",
            q: t("faq:howtosetup.question.9"),
            a: t("faq:howtosetup.answer.9"),
          },
          {
            id: "howtosetupquestion10",
            q: t("faq:howtosetup.question.10"),
            a: t("faq:howtosetup.answer.10"),
          },
          {
            id: "howtosetupquestion11",
            q: t("faq:howtosetup.question.11"),
            a: t("faq:howtosetup.answer.11"),
          },
        ],
      },
      {
        id: "troubleshoot",
        category: t("faq:category.troubleshoot"),
        qa: [
          {
            id: "troubleshoot1",
            q: t("faq:troubleshoot.question.1"),
            a: t("faq:troubleshoot.answer.1"),
          },
          {
            id: "troubleshoot2",
            q: t("faq:troubleshoot.question.2"),
            a: t("faq:troubleshoot.answer.2"),
          },
          {
            id: "troubleshoot3",
            q: t("faq:troubleshoot.question.3"),
            a: t("faq:troubleshoot.answer.3"),
          },
          {
            id: "troubleshoot4",
            q: t("faq:troubleshoot.question.4"),
            a: t("faq:troubleshoot.answer.4"),
          },
          {
            id: "troubleshoot5",
            q: t("faq:troubleshoot.question.5"),
            a: t("faq:troubleshoot.answer.5"),
          },
          {
            id: "troubleshoot6",
            q: t("faq:troubleshoot.question.6"),
            a: t("faq:troubleshoot.answer.6"),
          },
          {
            id: "troubleshoot7",
            q: t("faq:troubleshoot.question.7"),
            a: t("faq:troubleshoot.answer.7"),
          },
          {
            id: "troubleshoot8",
            q: t("faq:troubleshoot.question.8"),
            a: t("faq:troubleshoot.answer.8"),
          },
          {
            id: "troubleshoot9",
            q: t("faq:troubleshoot.question.9"),
            a: t("faq:troubleshoot.answer.9"),
          },
          {
            id: "troubleshoot10",
            q: t("faq:troubleshoot.question.10"),
            a: t("faq:troubleshoot.answer.10"),
          },
          {
            id: "troubleshoot11",
            q: t("faq:troubleshoot.question.11"),
            a: t("faq:troubleshoot.answer.11"),
            link: `${baseUrl}documents/jp/name-registration-guide.pdf`,
            link2: "https://global.cmlink.com/store/realname?LT=en",
          },
        ],
      },
    ],
    []
  );

  return (
    <AppLayout isLogoTextTitle={false}>
      <FAQHero title={t("faq:title")} />
      <div className="mt-10 flex justify-center px-2">
        <FAQSearch setSelectedValue={setSelectedQuestion} faqData={FAQ_DATA} />
      </div>
      <Grid
        grow
        classNames={{
          root: "mx-3 mt-12 md:mt-20 mb-24 lg:mx-16",
          inner: "gap-2 lg:flex-row flex-col",
        }}
      >
        <Grid.Col span={1} className="mb-8 md:mb-0">
          <CategoriesBox
            isMobile={isMobileSize}
            data={FAQ_DATA}
            title={t("faq:category.title")}
          />
        </Grid.Col>
        <Grid.Col span={7}>
          <QuestionAnswerList
            selectedValue={selectedQuestion}
            data={FAQ_DATA}
            service="エアトリ"
          />
        </Grid.Col>
      </Grid>
    </AppLayout>
  );
}