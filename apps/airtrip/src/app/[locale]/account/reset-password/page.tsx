"use client";

/*
 * import modules and libraries
 */
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback } from "react";

import { But<PERSON>, Stack, Text } from "@mantine/core";
import { useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";

import { useTranslation } from "react-i18next";

import SectionContent from "@repo/ui/src/common/SectionContent";

/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";

/*
 * import api
 */
import { ApiService } from "@/api";

/*
 * import constants and helpers
 */
import useLogin from "@/hooks/useLogin";
import { useMessageStore } from "@/store/MessageStore";

export default function ResetPassword() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useLogin();
  const { t } = useTranslation();
  const code = searchParams.get("code");
  const email = searchParams.get("email");

  const [loading, toggleLoading, setMessage] = useMessageStore((s) => [
    s.isLoading,
    s.toggleLoading,
    s.setMessage,
  ]);
  const form = useForm({
    initialValues: {
      confirmPassword: "",
      password: "",
      email: "",
      code: "",
    },

    validate: {
      password: (v) =>
        v.length >= 8 ? null : "パスワードは英数字8文字以上です。",
      confirmPassword: (v) =>
        v.length >= 8 ? null : "パスワードは英数字8文字以上です。",
    },
  });

  const handlePasswordReset = useCallback(
    async (values: typeof form.values) => {
      if (values.password !== values.confirmPassword) {
        showNotification({
          color: "red",
          message: t("password:validation.mustbesame"),
        });
        return;
      }
      toggleLoading(true);
      try {
        await ApiService.resetPassword({
          newPassword: values.password,
          email: email || values.email,
          confirmationCode: code || values.code,
        });
        login({
          email: email as string,
          password: values.password,
        });
        showNotification({
          message: t("password:validation.changesuccess"),
          color: "green",
        });
        router.push("/app");
      } catch (err) {
        toggleLoading(false);
        showNotification({
          message: t("password:validation.changefailure"),
          color: "red",
        });
      }
    },
    [code, email, form.values]
  );

  return (
    <AppLayout contentBg="#fff">
      <SectionContent small title={t("password:reset-password.title")}>
        <form onSubmit={form.onSubmit(handlePasswordReset)}>
          <Stack>
            <Text
              className="text-center text-sm"
              dangerouslySetInnerHTML={{
                __html: "新しいパスワードを入力してください。",
              }}
            />
            {!email && (
              <AppTextInput
                withAsterisk
                type="text"
                placeholder={
                  t("password:forgotpassword.email.placeholder") as string
                }
                label={t("password:forgotpassword.email.label") as string}
                {...form.getInputProps("email")}
              />
            )}
            {!code && (
              <AppTextInput
                withAsterisk
                type="text"
                placeholder={"Code"}
                label={"Code" as string}
                {...form.getInputProps("code")}
              />
            )}
            <AppTextInput
              withAsterisk
              type="password"
              placeholder={
                t("password:reset-password.greaterthan8characters") as string
              }
              label={t("password:reset-password.password.label")}
              {...form.getInputProps("password")}
            />
            <AppTextInput
              type="password"
              withAsterisk
              placeholder={
                t("password:reset-password.confirmation.label") as string
              }
              label={t("password:reset-password.confirmation.label")}
              {...form.getInputProps("confirmPassword")}
            />

            <Button loading={loading} w={"100%"} type="submit" color="app-dark">
              {t("password:reset-password.btn.txt", "RESET PASSWORD")}
            </Button>
          </Stack>
        </form>
      </SectionContent>
    </AppLayout>
  );
}
