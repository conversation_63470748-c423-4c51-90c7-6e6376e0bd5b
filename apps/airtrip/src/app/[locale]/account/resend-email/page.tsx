"use client";
/*
 * import modules and libraries
 */
import { showNotification } from '@mantine/notifications';
import { useForm } from "@mantine/form";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Stack,
  Text,
} from "@mantine/core";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";
/*
 * import constants and helpers
 */
import { useMessageStore } from "@/store/MessageStore";
/*
 * import api
 */
import { ApiService } from "@/api";

export default function ResendEmail() {
  const { t } = useTranslation();
  const [loading, toggleLoading, setMessage] = useMessageStore((s) => [
    s.isLoading,
    s.toggleLoading,
    s.setMessage,
  ]);
  const form = useForm({
    initialValues: {
      email: "",
    },

    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Invalid email"),
    },
  });

  const handleForgotPassword = (values: typeof form.values) => {
    toggleLoading(true);
    ApiService.resendVerificationEmail(values.email)
      .then(() => {
        form.setValues({
          email: "",
        });
        showNotification({
          message: "新しい確認メールを送信しました。",
          color: "green",
        });
      })
      .catch(() => {
        showNotification({
          color: "red",
          message: "確認メールを送信できません。もう一度お試しください。",
        });
      })
      .finally(() => {
        toggleLoading(false);
      });
  };

  return (
    <AppLayout contentBg="#fff">
      <SectionContent
        small 
        title="確認メールを再送"
      >
        <form onSubmit={form.onSubmit(handleForgotPassword)}>
          <Stack>
            <Text
              className="text-sm text-center"
              dangerouslySetInnerHTML={{
                __html: "アカウントに関連付けられているメールアドレスを入力すると、確認メールが送信されます。",
              }}
            />
            <AppTextInput
              withAsterisk
              placeholder={
                t("password:forgotpassword.email.placeholder") as string
              }
              label={t("password:forgotpassword.email.placeholder") as string}
              {...form.getInputProps("email")}
            />
            <Button
              loading={loading}
              w={"100%"}
              type="submit"
              color="app-dark"
            >
              確認メールを再送
            </Button>
          </Stack>
        </form>
      </SectionContent>
    </AppLayout>
  );
}
