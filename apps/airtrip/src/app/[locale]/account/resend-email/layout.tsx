/*
 * import modules and libraries
 */
import type { Metadata } from "next";
/*
 * import components
 */
import TranslationsProvider from "@/app/components/TranslationProvider";
/*
 * import constants and helpers
 */
import { locales } from "@/i18n/settings";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "password",
  "login",
  "compatibility,"
];

export const metadata: Metadata = {
  title: "パスワードリセット - エアトリeSIM",
  robots: {
    index: false,
  }
};

const ResendEmailLayout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale} 
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default ResendEmailLayout;
 