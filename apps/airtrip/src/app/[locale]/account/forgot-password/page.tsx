"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";

import { Button, Group, Stack, Text } from "@mantine/core";
import { useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";

import { useTranslation } from "react-i18next";

import SectionContent from "@repo/ui/src/common/SectionContent";

/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";

/*
 * import api
 */
import { ApiService } from "@/api";

/*
 * import constants and helpers
 */
import { useMessageStore } from "@/store/MessageStore";

export default function ForgotPassword() {
  const { t } = useTranslation();
  const [loading, toggleLoading, setMessage] = useMessageStore((s) => [
    s.isLoading,
    s.toggleLoading,
    s.setMessage,
  ]);
  const form = useForm({
    initialValues: {
      email: "",
    },

    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Invalid email"),
    },
  });

  const handleForgotPassword = (values: typeof form.values) => {
    toggleLoading(true);
    ApiService.forgotPassword(values.email)
      .then(() => {
        form.setValues({
          email: "",
        });
        showNotification({
          message: "パスワードリセットリンクを送信しました。",
          color: "green",
        });
      })
      .catch((err: any) => {
        let errorMessage =
          "パスワードリセットメールを送信できません。もう一度お試しください。";
        if (err.response?.data?.errorCode === 4008) {
          errorMessage =
            "ソーシャルログインを使用してアカウントが作成されました。ソーシャルログインを使用してサインインしてください。";
        }
        if (err.response?.data?.message === "User not found.") {
          errorMessage =
            "ユーザーが見つかりませんでした。まず登録してください。";
        }

        showNotification({
          color: "red",
          message: errorMessage,
        });
      })
      .finally(() => {
        toggleLoading(false);
      });
  };

  return (
    <AppLayout contentBg="#fff">
      <SectionContent
        small
        title={t("password:forgotpassword.title") as string}
      >
        <form onSubmit={form.onSubmit(handleForgotPassword)}>
          <Stack>
            <Text
              className="text-center text-sm"
              dangerouslySetInnerHTML={{
                __html: t("password:forgotpassword.notice"),
              }}
            />
            <AppTextInput
              withAsterisk
              placeholder={
                t("password:forgotpassword.email.placeholder") as string
              }
              label={t("password:forgotpassword.email.placeholder") as string}
              {...form.getInputProps("email")}
            />
            <Stack gap={1} align="end">
              <Group justify="end">
                <Text size="xs">
                  {t(
                    "password:account.keyword.alreadyhaveacode",
                    "Already have a code?"
                  )}{" "}
                  <Link href={"/account/reset-password"}>
                    {t(
                      "password:account.keyword.resetpassword",
                      "Reset Password"
                    )}
                  </Link>
                </Text>
              </Group>
              <Text className="justify-self-end" size="xs">
                {t(
                  "password:account.keyword.resendverification",
                  "Resend verification?"
                )}{" "}
                <Link href={"/account/resend-email"}>
                  {t("password:account.keyword.resendemail", "Resend Email")}
                </Link>
              </Text>
            </Stack>
            <Button loading={loading} w={"100%"} type="submit" color="app-dark">
              {t("password:forgotpassword.btn.sendresetlink")}
            </Button>
          </Stack>
        </form>
      </SectionContent>
    </AppLayout>
  );
}
