"use client";

import { usePathname } from "next/navigation";

import { useLogger } from "@gmesim/logger/lib/axiom/client";

import NotFoundComponent from "@repo/ui/src/NotFoundComponent";

import AppLayout from "@/app/components/common/AppLayout";

export default function NotFound() {
  const pathname = usePathname();
  const log = useLogger();

  log.log("error", "Page not found", {
    request: {
      host: typeof window !== "undefined" && window.location.href,
      path: pathname,
      statusCode: status,
    },
  });

  return (
    <>
      <AppLayout contentBg="#fff">
        <NotFoundComponent />
      </AppLayout>
    </>
  );
}
