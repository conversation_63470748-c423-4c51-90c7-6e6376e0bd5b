"use server";

import Link from "next/link";

import { Box, Button, Stack } from "@mantine/core";

import Appeal from "@repo/ui/src/campaign/Appeal";
import CountryList from "@repo/ui/src/campaign/CountryList";
import Description from "@repo/ui/src/campaign/Description";
import EsimWifiComparison from "@repo/ui/src/campaign/EsimWifiComparison";
import SectionContent from "@repo/ui/src/common/SectionContent";

import Hero from "@/app/components/campaign/Hero";
import HowItWorks from "@/app/components/campaign/HowItWorks";
import AppLayout from "@/app/components/common/AppLayout";
import AgentScript from "@/app/components/script/agent";

export default async function CampaignPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const agent = searchParams?.via;

  return (
    <>
      <AgentScript agent={agent as string} />
      <AppLayout contentBg="#f3f3f3" isLogoTextTitle={false}>
        <Hero
          campaignGetButton={
            <Button
              component={Link}
              href="/campaign/form"
              size="md"
              color="app-action.4"
              className="mx-auto w-full max-w-sm"
            >
              無料eSIMをGET！
            </Button>
          }
          campaignPeriodText="2025年7月1日(火)～2025年8月31日(日)"
        />
        <Box className="bg-secondary">
          <SectionContent noHeader noFooter>
            <Stack className="gap-8">
              <Description />
            </Stack>
          </SectionContent>
        </Box>
        <SectionContent title={"このキャンペーンはこんな方におすすめ"}>
          <Appeal />
        </SectionContent>
        <Box className="bg-white">
          <SectionContent title="お試し利用可能国">
            <CountryList />
          </SectionContent>
          <SectionContent title={"home:howitworks.titleairtrip"}>
            <HowItWorks />
          </SectionContent>
          <SectionContent title="eSIM、SIM、WiFiの違い">
            <EsimWifiComparison />
          </SectionContent>
        </Box>
      </AppLayout>
    </>
  );
}
