"use server";

/*
 * import modules and libraries
 */
import { Box, Stack } from "@mantine/core";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import HowItWorks from "@/app/components/top/HowItWorks";
import Appeal from "@repo/ui/src/campaign/Appeal";
import Description from "@repo/ui/src/campaign/Description";
import CountryList from "@repo/ui/src/campaign/CountryList";
import EsimWifiComparison from "@repo/ui/src/campaign/EsimWifiComparison";
import CTAButton from "@/app/components/common/CTAButton";

export default async function CampaignPage() {
  return (
    <AppLayout
      contentBg="#f3f3f3"
      ctaButton={
        <Box className="py-2 bg-white border-t z-[999]">
          <Box className="max-w-[1080px] mx-auto px-4">
            <CTAButton customUrl="/campaign/202408/form" />
          </Box>
        </Box>
      }
    >
      <Box className="bg-secondary">
        <SectionContent noHeader noFooter>
          <Stack className="gap-8">
            <Description />
          </Stack>
        </SectionContent>
      </Box>
      <SectionContent title={"このキャンペーンはこんな方におすすめ"}>
        <Appeal />
      </SectionContent>
      <Box className="bg-white">
        <SectionContent title="お試し利用可能国">
          <CountryList />
        </SectionContent>
        <SectionContent title={"home:howitworks.title"}>
          <HowItWorks customUrl="/campaign/202408/form" />
        </SectionContent>
        <SectionContent title="eSIM、SIM、WiFiの違い">
          <EsimWifiComparison />
        </SectionContent>
      </Box>
    </AppLayout>
  );
}
