"use client";

import dynamic from "next/dynamic";
import { usePathname } from "next/navigation";

import { useLogger } from "@gmesim/logger/lib/axiom/client";

import AppLayout from "@/app/components/common/AppLayout";

const NotFoundComponent = dynamic(
  () => import("@repo/ui/src/NotFoundComponent"),
  { ssr: false }
);

export default function CatchAll() {
  const pathname = usePathname();
  const log = useLogger();

  log.log("error", "Page not found", {
    request: {
      host: typeof window !== "undefined" && window.location.href,
      path: pathname,
      statusCode: 404,
    },
  });

  return (
    <AppLayout contentBg="#fff">
      <NotFoundComponent />
    </AppLayout>
  );
}
