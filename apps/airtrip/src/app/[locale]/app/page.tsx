"use client";

/*
 * import modules and libraries
 */
import { useSearchParams } from "next/navigation";
import { useCallback, useState } from "react";

import { Box } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";

import { useTranslation } from "react-i18next";

import { DATA_PACKAGE_TYPE, IOrder } from "@gmesim/fe-interfaces/src";

import { TopupModal } from "@repo/ui/src/TopupModal";
import SectionContent from "@repo/ui/src/common/SectionContent";
import MyEsimContainer from "@repo/ui/src/my-esim/MyEsimContainer";

/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import AppTabs from "@/app/components/common/AppTabs/AppTabs";

export default function Page() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const tabQuery = searchParams?.get("search");

  const [activeTab, setActiveTab] = useState<string | null>(
    (tabQuery as string) || "active"
  );

  const [topupModalOpened, { open, close }] = useDisclosure();
  const [topupOrder, setTopupOrder] = useState<IOrder | undefined>();

  // For Korea unlimited plans to differentiate between fixed and unlimited plans
  const [topupPackageType, setTopupPackageType] = useState<DATA_PACKAGE_TYPE>(
    DATA_PACKAGE_TYPE.FIXED_DAY
  );
  const [isTopupLgu, setIsTopupLgu] = useState(false);

  const handleTopupClick = useCallback((order: IOrder) => {
    if (order.plan.network.name === "LGUPLUS") {
      setTopupPackageType(DATA_PACKAGE_TYPE.PER_DAY);
      setIsTopupLgu(true);
    } else {
      setTopupPackageType(DATA_PACKAGE_TYPE.FIXED_DAY);
      setIsTopupLgu(false);
    }
    open();
    setTopupOrder(order);
  }, []);

  const headers = useCallback(
    (activeTab: string) => [
      {
        name: t("myesim:tab.purchased"),
        code: "active",
        children: (
          <SectionContent noHeader small>
            <Box className="pt-4">
              <MyEsimContainer
                onTopupClick={handleTopupClick}
                isActive={activeTab === "active"}
                status="active"
              />
            </Box>
          </SectionContent>
        ),
      },
      {
        name: t("myesim:tab.expired"),
        code: "expired",
        children: (
          <SectionContent noHeader small>
            <Box className="pt-4">
              <MyEsimContainer
                onTopupClick={handleTopupClick}
                isActive={activeTab === "expired"}
                status="expired"
              />
            </Box>
          </SectionContent>
        ),
      },
    ],
    []
  );

  return (
    <>
      <TopupModal
        topupOrderId={topupOrder?.orderId}
        country={topupOrder?.plan.country?.name}
        initialState={topupModalOpened}
        onClose={close}
        topupPackageType={topupPackageType}
        isLgu={isTopupLgu}
      />
      <AppLayout contentBg="#f3f3f3">
        <AppTabs
          activeTab={activeTab}
          defaultValue={activeTab}
          headers={headers(activeTab || "")}
          onTabChange={setActiveTab}
        />
      </AppLayout>
    </>
  );
}
