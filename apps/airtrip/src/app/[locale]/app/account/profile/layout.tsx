import TranslationsProvider from "@/app/components/TranslationProvider";
import { locales } from "@/i18n/settings";
import { Metadata } from "next";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = ["signup", "profile", "common", "currencies", "home"];

export const metadata: Metadata = {
  title: "アカウント設定 - エアトリeSIM",
  description:
    "アカウント設定でお名前（姓・名）を簡単に設定。お客様の利便性を最優先に考えた設計でeSIMサービスをパーソナライズしていて、世界中で安心して利用可能です。",
};

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
}
