"use client";

/*
 * import modules and libraries
 */
import { useMemo } from "react";

import { Box, Paper, Stack, Table, Title } from "@mantine/core";

import { AxiosError } from "axios";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

import SectionContent from "@repo/ui/src/common/SectionContent";

/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import EsimProgressChart from "@/app/components/my-esim/EsimProgressChart";
import MyEsimList from "@/app/components/my-esim/MyEsimContainer/MyEsimList";

import { formatDate } from "@/utils";

/*
 * import api
 */
import { ApiService } from "@/api";

/*
 * import interfaces
 */
import { IOrder } from "@/interfaces/IOrder";
import { IUsage } from "@/interfaces/IUsage";

export default function Page({
  params,
}: {
  params: {
    orderId: string;
    os: string;
  };
}) {
  const { t } = useTranslation();

  const { data, error, isLoading } = useQuery(
    `orders-${params.orderId}`,
    async () => {
      const response = await ApiService.getOrder(params.orderId + "", {
        type: "usage",
      });
      if (response instanceof AxiosError) throw new Error("Order not found.");
      return response;
    },
    {
      enabled: !!params.orderId,
    }
  );

  const orderInfo = useMemo<{ order: IOrder; usage: IUsage }>(
    () => data?.data?.data,
    [data?.data?.data]
  );

  const rows = useMemo(() => {
    if (!orderInfo?.order?.childOrders?.length) {
      return (
        <Table.Tr>
          <Table.Td colSpan={4}>
            {t("common:topup.history.nodata", "Order not found")}
          </Table.Td>
        </Table.Tr>
      );
    }

    return orderInfo.order.childOrders.map((order) => (
      <Table.Tr key={order.id}>
        <Table.Td>{order.orderId}</Table.Td>
        <Table.Td>{order.plan.dataId}</Table.Td>
        <Table.Td>{order.plan.validityDays}</Table.Td>
        <Table.Td>{formatDate(order.createdAt)}</Table.Td>
      </Table.Tr>
    ));
  }, [orderInfo, t]);

  return (
    <AppLayout>
      <Box className="bg-[#f3f3f3] pb-8">
        <SectionContent title={t("region:checkusage", "Check Usage")} small>
          <Stack>
            {orderInfo && (
              <MyEsimList
                ignoreCardRenderLogic
                esimCardProps={{
                  hideButton: true,
                }}
                accordionProps={{
                  defaultValue: orderInfo?.order?.orderId,
                }}
                fetchNextPage={() => {}} // TODO: fetch next page function
                items={data ? [[orderInfo.order]] : []}
                status={"active"}
                renderWrapper={(_, children) => <>{children}</>}
              />
            )}
            {orderInfo && (
              <EsimProgressChart
                country={orderInfo?.order?.plan?.country?.name}
                endDate={orderInfo?.order?.expireTime}
                startDate={orderInfo?.order?.activateDate}
                totalData={orderInfo?.order?.plan?.dataId}
                usedData={orderInfo?.usage?.topup?.usage}
                validitiyDays={orderInfo?.order?.plan?.validityDays}
              />
            )}
            <Paper p={"md"} radius={"md"}>
              <Stack>
                <Title order={4} fw={500}>
                  {t("common:topup.history.title", "Topup History")}
                </Title>
                <Table
                  striped
                  highlightOnHover
                  withTableBorder
                  withColumnBorders
                >
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>
                        {t("common:topup.history.table.order-id", "Order ID")}
                      </Table.Th>
                      <Table.Th>
                        {t(
                          "common:topup.history.table.datavolume",
                          "Data Volume"
                        )}
                      </Table.Th>
                      <Table.Th>
                        {t(
                          "common:topup.history.table.validitydays",
                          "Validity Days"
                        )}{" "}
                      </Table.Th>
                      <Table.Th>
                        {t(
                          "common:topup.history.table.purchasedon",
                          "Purchased On"
                        )}
                      </Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>{rows}</Table.Tbody>
                </Table>
              </Stack>
            </Paper>
          </Stack>
        </SectionContent>
      </Box>
    </AppLayout>
  );
}
