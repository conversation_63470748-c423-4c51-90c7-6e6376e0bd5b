"use client";

/*
 * import modules and libraries
 */
import { useQuery } from "react-query";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import ActivateContainer from "@/app/components/activate/ActivateContainer";
/*
 * import api
 */
import { ApiService } from "@/api";
/*
 * import constants
 */
import { getIosGuide } from "./ios-guide";
import { getAndroidGuide } from "./android-guide";

export default function Page({
  params,
}: {
  params: {
    orderId: string;
    os: string;
  };
}) {
  const { error, data: orderInfo, isLoading } = useQuery(
    `orders-${params.orderId}`,
    async () => {
      const order = await ApiService.getOrder(params.orderId + "", {
        // type: "usage",
      });
      const data = order?.data?.data;
      if (!data) throw new Error("Product not found.")
      return data
    },
    {
      enabled: !!params.orderId,
    }
  );

  return (
    <AppLayout contentBg="#f3f3f3">
      <ActivateContainer
        guide={params.os === "ios" ?getIosGuide : getAndroidGuide}
        error={error}
        isLoading={isLoading}
        os={params.os}
        orderInfo={{
          order: {
            qrCodeImgUrl: orderInfo?.order?.qrCodeImgUrl,
            downloadLink: orderInfo?.order?.downloadLink,
            activateCode: orderInfo?.order?.activateCode,
            smdp: orderInfo?.order?.response?.products?.[0]?.smdp
          }
        }}
      />
    </AppLayout>
  )
}
