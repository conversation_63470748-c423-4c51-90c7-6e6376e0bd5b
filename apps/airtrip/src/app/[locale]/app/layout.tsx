import type { Metadata } from "next";

import TranslationsProvider from "@/app/components/TranslationProvider";

import initTranslations from "@/i18n";
import { locales } from "@/i18n/settings";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "myesim",
  "countries",
  "region",
  "chooseplan",
];

export const metadata: Metadata = {
  title: "購入履歴 - エアトリeSIM",
  description:
    "これまでに購入したeSIM一覧と、使用済みeSIMを一覧で確認。安心して次の旅行を計画しましょう。世界から信頼されるサービスで旅の続きを何度でも。",
};

const AppRootLayout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default AppRootLayout;
