import TranslationsProvider from "@/app/components/TranslationProvider";
import { locales } from "@/i18n/settings";
import { Metadata } from "next";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = ["home", "common"];

export const metadata: Metadata = {
  title: "返金ポリシー エアトリeSIM",
  description:
    "はじめてeSIMをご利用されるお客様の”インストールや設定”、”現地通信”などのご不安にお応えするために、初回購入者様向けの返金制度を設けております",
};

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
}
