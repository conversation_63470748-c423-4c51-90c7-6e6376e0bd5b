import Link from "next/link";

import { Box, Paper } from "@mantine/core";

import sortBy from "lodash/sortBy";
import { ProductJsonLd } from "next-seo";

import { getUkomiReviewsWithAllSummary } from "@gmesim/fe-apis/src";

import BannerCarousel from "@repo/ui/src/BannerCarousel";
import CountrySelector from "@repo/ui/src/CountrySelector";
import EsimWifiComparison from "@repo/ui/src/campaign/EsimWifiComparison";
import SectionContent from "@repo/ui/src/common/SectionContent";
import featuredPlans from "@repo/ui/src/constants/featured-plans.json";
import { REGIONS } from "@repo/ui/src/constants/regions";

import AppLayout from "@/app/components/common/AppLayout";
import GoroNavi from "@/app/components/common/airtrip-goronavi/GoroNavi";
import AgentScript from "@/app/components/script/agent";
import Features from "@/app/components/top/Features";
import Hero from "@/app/components/top/Hero";
import HomeFAQ from "@/app/components/top/HomeFAQ";
import HowItWorks from "@/app/components/top/HowItWorks";
import WhatIsEsim from "@/app/components/top/WhatIsEsim";

import { bannerSlides, countryAlias } from "@/utils";

import { IPlan } from "@/interfaces/IPlan";

import ReviewSection from "../components/top/ReviewSection";
import { COUNTRY_SORT } from "../constants";

export const dynamic = "force-static";

async function getPlans() {
  let formatCountryPlans: IPlan[] = [];
  const plans: {
    regions: IPlan[];
    countries: IPlan[];
  } = featuredPlans.data as {
    countries: any;
    regions: any;
  };

  try {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries.map((item: IPlan) => ({
        ...item,
        order: COUNTRY_SORT.get(item.country) || null,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      })),
      ["order"]
    );
    /* @ts-ignore */
    plans.regions = plans.regions.map((item: IPlan) => ({
      ...item,
      originalName: item.country,
      country: {
        name: countryAlias(item.country as unknown as string),
      },
    }));
  } catch (err) {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries.map((item: IPlan) => ({
        ...item,
        order: COUNTRY_SORT.get(item.country) || null,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      })),
      ["order"]
    );
  }

  return {
    formatCountryPlans,
    plans,
  };
}

const getUkomiReviewsWithSumary = async () => {
  try {
    const ukomiReviewsWithSummary = await getUkomiReviewsWithAllSummary();
    return {
      ukomiReviewsWithSummary,
    };
  } catch (error) {
    console.log(error);
    return {
      ukomiReviewsWithSummary: null,
    };
  }
};

export default async function Page({
  searchParams,
}: {
  searchParams: Record<string, string | string[]>;
}) {
  const { formatCountryPlans, plans } = await getPlans();
  const { ukomiReviewsWithSummary } = await getUkomiReviewsWithSumary();
  const agent = searchParams?.via;

  const getRegionCountries = (region: string) => {
    if (!formatCountryPlans) {
      return [];
    }

    return formatCountryPlans.filter((item: IPlan) => item.region === region);
  };

  const countrySelectorData = [
    ...REGIONS.map((item) => ({
      name: `region:${item.name}`,
      code: item.code,
      plans: getRegionCountries(item.code),
    })),
    {
      name: "common:regional-esim",
      code: "regional",
      plans: plans.regions,
    },
  ];

  return (
    <>
      <AgentScript agent={agent as string} />
      {ukomiReviewsWithSummary && ukomiReviewsWithSummary.summary && (
        <ProductJsonLd
          productName="エアトリeSIM"
          aggregateRating={{
            ratingValue: ukomiReviewsWithSummary?.summary?.average_ratings,
            ratingCount: ukomiReviewsWithSummary?.summary?.total_reviews,
          }}
          useAppDir={true}
        />
      )}
      <AppLayout
        ukomiSumary={ukomiReviewsWithSummary?.summary ?? undefined}
        contentBg="#fff"
        noShadowHeader
        isInvertColorHeader
        isShowCountryButton
      >
        <div className="top-[63px] z-[50] hidden md:sticky md:block">
          <GoroNavi />
        </div>
        <Hero
          countrySelector={
            <Paper className="w-full rounded-lg md:hidden">
              <div className="mb-2 max-w-4xl rounded-lg py-3">
                <BannerCarousel slides={bannerSlides} linkComponent={Link} />
              </div>
              <Box className="w-full bg-white px-4 pb-10 pt-0">
                {/* @ts-ignore */}
                <CountrySelector data={countrySelectorData} />
              </Box>
            </Paper>
          }
        />
        <div className="relative hidden bg-zinc-200 py-4 md:block">
          <div className="m-auto max-w-6xl">
            <BannerCarousel slides={bannerSlides} linkComponent={Link} />
          </div>
        </div>
        <Box className="hidden w-full rounded-lg px-4 pb-14 pt-10 md:block">
          {/* @ts-ignore */}
          <CountrySelector data={countrySelectorData} />
        </Box>
        {ukomiReviewsWithSummary && (
          <SectionContent>
            <ReviewSection ukomiReviewsWithSummary={ukomiReviewsWithSummary} />
          </SectionContent>
        )}
        <SectionContent
          title={"home:benefit.title"}
          caption={"home:benefit.description"}
        >
          <WhatIsEsim />
        </SectionContent>
        <SectionContent title={"home:features.titleairtrip"}>
          <Features />
        </SectionContent>
        <SectionContent title="eSIM、SIM、WiFiの違い">
          <EsimWifiComparison />
        </SectionContent>
        <SectionContent title={"home:howitworks.titleairtrip"}>
          <HowItWorks />
        </SectionContent>
        <Box className="bg-secondary">
          <SectionContent title={"faq:home.title"}>
            <HomeFAQ />
          </SectionContent>
        </Box>
      </AppLayout>
    </>
  );
}
