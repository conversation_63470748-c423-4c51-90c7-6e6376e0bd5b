import TranslationsProvider from "@/app/components/TranslationProvider";
import initTranslations from "@/i18n";

const i18nNamespaces = ["login"];

const ActivateLayout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default ActivateLayout;
