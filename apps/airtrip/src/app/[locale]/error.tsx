"use client";

import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import {
  Button,
  Center,
  Group,
  Paper,
  Text,
  ThemeIcon,
  Title,
  rem,
} from "@mantine/core";

import * as Sentry from "@sentry/nextjs";
import { IconExclamationCircle } from "@tabler/icons-react";

import { useLogger } from "@gmesim/logger/lib/axiom/client";

import AppLayout from "../components/common/AppLayout";

export default function Error({
  error,
}: {
  error: Error & { digest?: string };
}) {
  const router = useRouter();
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  const pathname = usePathname();
  const log = useLogger();
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push("/");
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  let status = error.message == "Invalid URL" ? 404 : 500;

  log.log("error", error.message, {
    error: error.name,
    cause: error.cause,
    stack: error.stack,
    digest: error.digest,
    request: {
      host: window.location.href,
      path: pathname,
      statusCode: status,
    },
  });

  return (
    <AppLayout contentBg="#fff">
      <div className="-mt-16 flex min-h-[65vh] flex-col items-center justify-center px-4">
        <Paper
          shadow="xl"
          radius="lg"
          p="xl"
          withBorder
          className="text-center"
        >
          <Center mb="md">
            <ThemeIcon size={rem(80)} radius="xl" color="primary">
              <IconExclamationCircle size={rem(50)} />
            </ThemeIcon>
          </Center>

          <Title order={2} mb="md" className="text-primary">
            エラーが発生しました。
          </Title>

          <Text c="dimmed" mb="lg">
            ご不便をおかけして申し訳ありません。エラーは報告されており、現在対応中です。
          </Text>

          <Text size="sm" c="dimmed">
            <Text span fw={500}>
              {countdown}
            </Text>{" "}
            秒後にホームページへ移動します。
          </Text>

          <Group justify="center" mt="xl">
            <Button radius="md" size="md" onClick={() => router.push("/")}>
              ホームページに戻る
            </Button>
          </Group>
        </Paper>
      </div>
    </AppLayout>
  );
}
