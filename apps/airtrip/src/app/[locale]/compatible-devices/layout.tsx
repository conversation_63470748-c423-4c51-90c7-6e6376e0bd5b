import TranslationsProvider from "@/app/components/TranslationProvider";
import { locales } from "@/i18n/settings";
import { Metadata } from "next";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "countries",
  "region",
  "help",
  "profile",
];

export const metadata: Metadata = {
  title: "対応端末・機種一覧 - エアトリeSIM",
  description:
    "幅広い端末・機種に対応！100か国以上で利用可能なeSIMサービス。申込みは簡単、設定も迅速。信頼性と利便性を兼ね備えた理想的な選択です。",
};

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale} 
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
}
