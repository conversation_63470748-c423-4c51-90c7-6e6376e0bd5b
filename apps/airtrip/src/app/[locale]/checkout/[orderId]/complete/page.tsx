"use client";

import Link from "next/link";
import { useEffect, useRef, useState } from "react";

import { Box, Button, Flex, Stack, Text, Title } from "@mantine/core";

import Lottie from "lottie-react";
import { useTranslation } from "react-i18next";

import { useCartClear } from "@gmesim/cart-package/hooks/useCartClear";

import InstantEsimContainerNew from "@repo/ui/src/InstantEsimContainerNew";
import SectionContent from "@repo/ui/src/common/SectionContent";
import { useAdmaneScript } from "@repo/ui/src/hooks/useAdmaneScript";

import AppLayout from "@/app/components/common/AppLayout";

import { useMessageStore } from "@/store/MessageStore";
import { useProfile } from "@/store/UserStore";

import CheckLottie from "./check-lottie.json";

export default function Page({
  params,
}: {
  params: {
    orderId: string;
  };
}) {
  const { t } = useTranslation();
  const lottieRef = useRef();
  const profile = useProfile((s) => s.profile);
  const [isGlobalLoading, setGlobalLoading] = useMessageStore((s) => [
    s.isLoading,
    s.toggleLoading,
  ]);
  const [isProcessing, setIsProcessing] = useState(true);
  const [firstOrderId, setFirstOrderId] = useState("");
  const [isNeedRegistrationChecked, setIsNeedRegistrationChecked] =
    useState("false");

  const { admaneHtmlScript, admaneScript } = useAdmaneScript({
    variant: "airtrip",

    agentCode:
      (typeof sessionStorage !== "undefined" &&
        (sessionStorage.getItem("agent") as string)) ||
      "",
    totalAmount:
      (typeof sessionStorage !== "undefined" &&
        Number(sessionStorage.getItem("amount"))) ||
      0,

    orderId: params.orderId,
  });
  useCartClear();

  useEffect(() => {
    setGlobalLoading(true);
    setIsNeedRegistrationChecked(
      sessionStorage.getItem("isNeedRegistrationChecked") as string
    );
    const timeout = setTimeout(() => {
      setIsProcessing(true);
      setGlobalLoading(false);
    }, 1000);
    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsProcessing(false);
    }, 5000);
    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    if (!profile?.email) return;
    //@ts-ignore
    if (profile?.email && typeof window.rewardful !== "undefined")
      //@ts-ignore
      rewardful("convert", { email: profile?.email });
  }, [profile?.email]);

  return (
    <>
      {admaneScript}
      {admaneHtmlScript}
      {profile?.email && (
        <script
          dangerouslySetInnerHTML={{
            __html: `
            rewardful('convert', { email: '${profile?.email}' });
           `,
          }}
        />
      )}
      <AppLayout contentBg="#f3f3f3">
        <SectionContent small noHeader>
          <Flex h={290} justify="center">
            <Lottie lottieRef={lottieRef.current} animationData={CheckLottie} />
          </Flex>

          <Stack
            className="mt-[-50px] gap-4"
            align={"center"}
            justify={"center"}
          >
            <Box className="text-center">
              <Title order={5} className="font-bold">
                {t("region:thankyoufororder", "THANK YOU FOR YOUR ORDER!")}
              </Title>
              <Text className="text-sm font-bold">
                {t("region:orderid", "Order ID")}: {firstOrderId}
              </Text>
            </Box>
            <Text
              className="text-center text-sm"
              dangerouslySetInnerHTML={{
                __html:
                  isNeedRegistrationChecked === "true"
                    ? t("payment:hongkongtaiwancomplete")
                    : "",
              }}
            />
            <InstantEsimContainerNew
              getFirstOrderId={setFirstOrderId}
              orderId={params.orderId}
              showDownloadButton
            />
          </Stack>

          <Stack className="mt-6">
            <Button
              component={Link}
              href={"/app"}
              loading={isProcessing}
              color="app-dark"
              w={"100%"}
            >
              {isProcessing
                ? t("region:processing", "PROCESSING")
                : t("region:activateesimnow", "ACTIVATE eSIM NOW")}
            </Button>
            <Button
              component={Link}
              href={"/"}
              loading={isProcessing}
              variant="outline"
              color="app-dark"
              className="bg-white"
              w={"100%"}
            >
              {t("region:gotohome", "GO TO HOME")}
            </Button>
          </Stack>
        </SectionContent>
      </AppLayout>
    </>
  );
}
