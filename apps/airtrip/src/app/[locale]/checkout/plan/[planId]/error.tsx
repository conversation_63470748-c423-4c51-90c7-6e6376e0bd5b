"use client";

import { useEffect } from "react";

import * as Sen<PERSON> from "@sentry/nextjs";

import NotFoundComponent from "@repo/ui/src/NotFoundComponent";

import AppLayout from "@/app/components/common/AppLayout";

export default function Error({ error }: { error: any }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);
  return (
    <AppLayout contentBg="#fff">
      <NotFoundComponent />
    </AppLayout>
  );
}
