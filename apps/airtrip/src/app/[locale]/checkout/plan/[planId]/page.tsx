"use client";

import { useParams, useSearchParams } from "next/navigation";
import { Suspense, useCallback, useEffect, useState } from "react";

import { Box } from "@mantine/core";
import { useDebouncedValue } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";

import { IconMoodSad } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

import BasketOrderDetails from "@gmesim/cart-package/components/BasketOrderDetails";
import { ApiService } from "@gmesim/fe-apis/src";
import { IQuotation } from "@gmesim/fe-interfaces/src";

import CheckoutContainer from "@repo/ui/src/CheckoutContainer";
import CheckoutPageSkeleton from "@repo/ui/src/CheckoutPageSkeleton";

import AppLayout from "@/app/components/common/AppLayout";

import { useOrderStore } from "@/store";
import { useMessageStore } from "@/store/MessageStore";
import { useProfile } from "@/store/UserStore";

export default function Page() {
  const { t } = useTranslation();
  const [loading, setIsLoading] = useState(false);
  const toggleCompatibilityModalOpen: () => void = useOrderStore(
    (state) => state.toggleCompatibilityModalOpen
  );
  const searchParams = useSearchParams();
  const [via, setVia] = useState(
    searchParams?.has("via") && searchParams?.get("via")
      ? searchParams.get("via")
      : null
  );
  const toggleLoading = useMessageStore((state) => state.toggleLoading);
  const profile = useProfile((store) => store.profile);
  const { planId } = useParams() || {};
  const [quotation, setQuotation] = useState<IQuotation>();

  const {
    data: quotationQuery,
    isLoading,
    isFetching,
  } = useQuery(
    "quotation" + planId + via,
    async () => {
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
      const planResponse = await ApiService.getQuotationNew(
        {
          recaptha: "",
          couponId: via,
          products: Array(
            searchParams?.has("count")
              ? //@ts-expect-error
                (+searchParams?.get("count") as unknown as string)
              : 1
          )
            .fill("true")
            .map((item) => ({
              optionId: planId + "",
            })),
        },
        {
          requestOriginServiceName: via as string,
        }
      );
      return planResponse.data?.data;
    },
    {
      enabled: !!planId,
      keepPreviousData: true,
      // cacheTime: 0,
    }
  );

  useEffect(() => {
    setQuotation(quotationQuery);
  }, [quotationQuery]);

  const handleCouponChange = useCallback((coupon?: string | null) => {
    setIsLoading(true);
    setVia(coupon || null);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, []);

  const [quotationQueryDebounced] = useDebouncedValue(quotationQuery, 1000);
  useEffect(() => {
    const { errors } = quotationQueryDebounced || {};
    if (!errors?.length) return;

    errors.forEach((element: any) => {
      if (!element) return;
      const couponEl = document.getElementById("coupon-code");
      if (couponEl) {
        couponEl.classList.add("border-red-500");
        couponEl.classList.add("text-red-500");
      }
      notifications.show({
        id: `notification-${element.code}`, // Unique ID
        icon: <IconMoodSad />,
        message: t(
          `error:coupons.${element.code}`,
          t(`error:coupons.selfonlycoupon`)
        ) as string,
      });
    });
  }, [quotationQueryDebounced]);

  return (
    <AppLayout contentBg="#f3f3f3" isLoading={loading}>
      <Box className="pb-8">
        <Suspense
          fallback={
            <CheckoutPageSkeleton name="When Checkout container is not loaded" />
          }
        >
          <CheckoutContainer
            origin="airtrip"
            onCouponChange={handleCouponChange}
            stripePublishableKey={
              process.env
                .NEXT_PUBLIC_STRIPE_AIRTRIP_ESIM_PUBLISHABLE_KEY as string
            }
            source="single"
            toggleLoading={toggleLoading}
            quotation={quotation}
            profile={profile}
            renderBasketCheckout={(props) => (
              <BasketOrderDetails
                //@ts-expect-error
                discount={props.discount}
                quotation={props.quotation}
                source="single"
                renderCheckoutButton={() => <></>}
              />
            )}
          />
        </Suspense>
      </Box>
    </AppLayout>
  );
}
