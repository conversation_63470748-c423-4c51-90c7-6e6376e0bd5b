"use client";


import { useSearchParams } from "next/navigation";
import { Suspense, useEffect } from "react";

import { Box } from "@mantine/core";

import BasketOrderDetails from "@gmesim/cart-package/components/BasketOrderDetails";

import CheckoutContainer from "@repo/ui/src/CheckoutContainer";
import CheckoutPageSkeleton from "@repo/ui/src/CheckoutPageSkeleton";

import AppLayout from "@/app/components/common/AppLayout";

import { useOrderStore, useShoppingCart } from "@/store";
import { useMessageStore } from "@/store/MessageStore";
import { useProfile } from "@/store/UserStore";

export default function Page() {
  const [quotation, setCoupon] = useShoppingCart((s) => [
    s.quotation,
    s.setCoupon,
  ]);
  const toggleCompatibilityModalOpen: () => void = useOrderStore(
    (state) => state.toggleCompatibilityModalOpen
  );
  const searchParams = useSearchParams();
  const via =
    searchParams?.has("via") && searchParams?.get("via")
      ? searchParams.get("via")
      : null;
  const toggleLoading = useMessageStore((state) => state.toggleLoading);
  const profile = useProfile((store) => store.profile);
  useEffect(() => {
    setCoupon(via as string);
  }, []);
  return (
    <AppLayout contentBg="#f3f3f3">
      <Box className="pb-8">
        <Suspense fallback={<CheckoutPageSkeleton />}>
          <CheckoutContainer
            origin="airtrip"
            stripePublishableKey={
              process.env
                .NEXT_PUBLIC_STRIPE_AIRTRIP_ESIM_PUBLISHABLE_KEY as string
            }
            source="multiple"
            onCouponChange={(coupon) => {
              setCoupon(coupon as string);
            }}
            toggleLoading={toggleLoading}
            quotation={quotation}
            profile={profile}
            renderBasketCheckout={(props) => (
              <BasketOrderDetails
                //@ts-expect-error
                discount={props.discount}
                quotation={props.quotation}
                source="multiple"
                renderCheckoutButton={() => <></>}
              />
            )}
          />
        </Suspense>
      </Box>
    </AppLayout>
  );
}
