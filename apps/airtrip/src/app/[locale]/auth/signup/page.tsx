"use server";

/*
 * import modules and libraries
 */
import type { Metadata } from "next";
import { Suspense } from "react";

/*
 * import components
 */
import AuthLayout from "@/app/components/auth/AuthLayout";
import SignupFormContainer from "@/app/components/auth/SignupFormContainer";
import AppLayout from "@/app/components/common/AppLayout";

export async function metadata() {
  const metadata: Metadata = {
    title: "新規会員登録 - エアトリeSIM",
    description:
      "新規登録が簡単！100か国以上で使える信頼のeSIMサービス。eSIMがはじめての方から旅行や出張が多い方まですべての方におすすめ。スムーズな回線開通とシンプルな操作で、いつでもどこでも快適に。",
  };

  return metadata;
}

export default async function Page() {
  return (
    <AppLayout contentBg="#fff" isLogoTextTitle={false}>
      <Suspense>
        <AuthLayout>
          <SignupFormContainer />
        </AuthLayout>
      </Suspense>
    </AppLayout>
  );
}
