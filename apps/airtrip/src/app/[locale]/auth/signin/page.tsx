"use server";

/*
 * import modules and libraries
 */
import type { Metadata } from "next";
import { Suspense } from "react";

/*
 * import components
 */
import AuthLayout from "@/app/components/auth/AuthLayout";
import SigninFormContainer from "@/app/components/auth/SigninFormContainer";
import AppLayout from "@/app/components/common/AppLayout";

export async function metadata() {
  const metadata: Metadata = {
    title: "ログイン - エアトリeSIM",
    description:
      "世界100か国以上で信頼されるeSIMサービス！申込みから瞬時に回線開通、設定も簡単。旅行好きなあなたに最適なシンプルで使いやすいWebアプリ。",
  };

  return metadata;
}

export default async function SignIn() {
  return (
    <AppLayout contentBg="#fff" isLogoTextTitle={false}>
      <Suspense>
        <AuthLayout>
          <SigninFormContainer />
        </AuthLayout>
      </Suspense>
    </AppLayout>
  );
}
