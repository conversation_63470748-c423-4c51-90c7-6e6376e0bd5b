/*
 * import constants and helpers
 */
/*
 * import constants
 */
import { countryMetaTags } from "@repo/ui/src/constants/regions";

/*
 * import components
 */
import TranslationsProvider from "@/app/components/TranslationProvider";

import initTranslations from "@/i18n";
import { locales } from "@/i18n/settings";

/*
 * import styles
 */
import "./PlanForm.css";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "countries",
  "region",
  "profile",
  "chooseplan",
];

export async function generateMetadata(context: {
  params: {
    country: string;
  };
}) {
  const country = context.params.country;
  const region = countryMetaTags[country];

  return {
    title: `${region} eSIM ｜安くてすぐ使える！${region}旅行に最適なエアトリeSIM`,

    description: `${region} eSIMを選ぶならエアトリ！無制限プランや短期旅行向けプランが充実。オンライン購入後すぐに使える簡単設定。日本語サポートで安心。`,
  };
}

export const dynamic = "force-dynamic";

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
}
