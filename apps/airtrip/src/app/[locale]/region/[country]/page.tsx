"use server";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import { Box, Title } from "@mantine/core";

import MetaTags from "@repo/ui/src/MetaTags";
import SectionContent from "@repo/ui/src/common/SectionContent";
import { countryMetaTags } from "@repo/ui/src/constants/regions";
import type { IPlan } from "@repo/ui/src/interfaces/IPlan";

import AppLayout from "@/app/components/common/AppLayout";
import PlanContainer from "@/app/components/region/PlanContainer";
import RegionNotes from "@/app/components/region/RegionNotes";
import AgentScript from "@/app/components/script/agent";
import HomeFAQ from "@/app/components/top/HomeFAQ";
import HowItWorks from "@/app/components/top/HowItWorks";

import { countryNameException, reverseKebabCase } from "@/utils";
import { AGENT } from "@/utils/agent-handler";

import { ApiService } from "@/api";

import type { INetwork } from "@/interfaces/INetwork";

import { PURCHASE_STEPS } from "@/app/constants";

const WhatIsEsim = dynamic(() => import("@/app/components/top/WhatIsEsim"));
const Features = dynamic(() => import("@/app/components/top/Features"));
const HeroJp = dynamic(() => import("@/app/components/region/HeroJp"));
const GoroNavi = dynamic(
  () => import("@/app/components/common/airtrip-goronavi/GoroNavi")
);
const KoreaCounterSection = dynamic(
  () => import("@repo/ui/src/KoreaCounterSection")
);
const EsimWifiComparison = dynamic(
  () => import("@repo/ui/src/campaign/EsimWifiComparison")
);

const DynamicPurchaseFlowStepper = dynamic(
  () => import("@repo/ui/src/PurchaseFlowStepper")
);

async function getServerSideProps(context: {
  params: {
    country: string;
  };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  try {
    const countryException = countryNameException(context.params?.country);
    const country = reverseKebabCase(countryException);
    const params = { country, provider: undefined };
    if (
      country.toLowerCase() === "korea" &&
      context.searchParams?.via?.includes?.(AGENT.AIRTRIP_KOREA)
    ) {
      //@ts-expect-error
      params.provider = "LGUPLUS_AIRTRIP_CAMPAIGN";
    }
    const response = await ApiService.getPlans(params);
    const network: INetwork[] = response?.data?.data.network || {};
    const profile = response?.data?.data.country;

    return {
      network,
      countryProfile: profile || null,
      plans: response.data.data.plans,
    };
  } catch (err) {
    console.log(err);
    return null;
  }
}

export default async function Region(context: {
  params: {
    country: string;
  };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  if (!countryMetaTags[context.params.country as string]) {
    return notFound();
  }

  const props: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  } | null = await getServerSideProps(context);

  const agent = context.searchParams?.via as string;
  const country = context.params.country as string;
  const isLGU = country === "korea";
  const isJapan = country === "japan";
  const isNeedRegistration = ["taiwan", "hongkong"].includes(country);

  return (
    <>
      <AgentScript agent={agent} />
      <MetaTags
        brand="エアトリeSIM"
        country={countryMetaTags[country]}
        logo="https://esim.airtrip.jp/favicon.ico"
        siteUrl="https://esim.airtrip.jp"
        review={{
          author: "エアトリeSIM",
          comment: "",
          rating: 4.5,
        }}
      />
      <AppLayout
        contentBg="#f3f3f3"
        noShadowHeader={isJapan}
        isInvertColorHeader={isJapan}
        isShowCountryButton
        isLogoTextTitle={false}
      >
        {isJapan && (
          <>
            <div className="top-[63px] z-[50] hidden md:sticky md:block">
              <GoroNavi />
            </div>
            <HeroJp />
          </>
        )}
        {!isJapan && (
          <>
            <DynamicPurchaseFlowStepper
              currentStep={0}
              steps={PURCHASE_STEPS}
            />
            <div className="mb-[10px]"></div>
          </>
        )}
        <Box className="relative">

           <Title order={1} className="hidden">
            {`${countryMetaTags[country]}用eSIM - エアトリeSIM`}
          </Title>
          <PlanContainer
            agent={agent}
            isLGU={isLGU}
            isJapan={isJapan}
            isNeedRegistration={isNeedRegistration}
            country={context.params.country}
            //@ts-expect-error
            regionData={props}
          />
        </Box>
        <Box className="bg-white">
          {isJapan && (
            <>
              <SectionContent
                title={"home:benefit.title"}
                caption={"home:benefit.description"}
              >
                <WhatIsEsim />
              </SectionContent>
              <SectionContent title={"home:features.titleairtrip"}>
                <Features />
              </SectionContent>
              <SectionContent title="eSIM、SIM、WiFiの違い">
                <EsimWifiComparison />
              </SectionContent>
            </>
          )}
          {isLGU && <KoreaCounterSection />}
          <SectionContent title={"home:howitworks.titleairtrip"}>
            <HowItWorks
              isLGU={isLGU}
              country={countryMetaTags[context.params.country]}
            />
          </SectionContent>
          <SectionContent title={"home:note.caution.title"}>
            <RegionNotes
              isShowLGUCompatible={isLGU}
              isNeedRegistration={isNeedRegistration}
            />
          </SectionContent>
        </Box>
        <Box className="bg-secondary pb-6">
          <SectionContent
            dynamicContent={{
              prefix: `${countryMetaTags[context.params.country]}eSIMの`,
            }}
            dynamicTitle={"faq:home.title"}
          >
            <HomeFAQ
              country={countryMetaTags[context.params.country] as string}
            />
          </SectionContent>
        </Box>
      </AppLayout>
    </>
  );
}
