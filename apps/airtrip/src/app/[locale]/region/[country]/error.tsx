"use client";

import Error from "next/error";
import { useEffect } from "react";

import * as Sentry from "@sentry/nextjs";

import NotFoundComponent from "@repo/ui/src/NotFoundComponent";

import AppLayout from "@/app/components/common/AppLayout";

export default function ErrorPage({ error }: { error: Error }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);
  return (
    <AppLayout contentBg="#fff">
      <NotFoundComponent />
    </AppLayout>
  );
}
