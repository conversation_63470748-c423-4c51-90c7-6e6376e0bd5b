import TranslationsProvider from "@/app/components/TranslationProvider";
import { locales } from "@/i18n/settings";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "home",
  "faq",
  "signup",
  "signin",
  "login",
  "compatibility",
  "contact",
  "currencies",
  "help"
];

const ContactRootLayout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale} 
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default ContactRootLayout;