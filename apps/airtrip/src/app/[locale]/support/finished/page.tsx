"use server";
import type { Metadata } from "next";
import AppLayout from "@/app/components/common/AppLayout";
import { Suspense } from "react";
import Finished from "@/app/components/support/Finished";

export async function metadata() {
  const metadata: Metadata = {
    title: "問い合わせありがとうございます - エアトリeSIM",
    description:
      "世界100か国以上で信頼されるeSIMサービス！申込みから瞬時に回線開通、設定も簡単。旅行好きなあなたに最適なシンプルで使いやすいWebアプリ。",
  };

  return metadata;

}

export default async function Page() {

  return (
    <AppLayout>
      <Suspense>
        <Finished />
      </Suspense>
    </AppLayout>
  );
}