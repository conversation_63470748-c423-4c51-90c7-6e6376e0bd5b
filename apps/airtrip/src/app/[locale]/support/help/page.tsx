"use server";
import type { Metada<PERSON> } from "next";
import AppLayout from "@/app/components/common/AppLayout";
import { Suspense } from "react";
import ContactLayout from "@/app/components/support/ContactLayout";
import Help from "@/app/components/support/Help";

export async function metadata() {
  const metadata: Metadata = {
    title: "ヘルプ - エアトリeSIM",
    description:
      "eSIMの設定や対応端末に関する疑問を解消！初めての方も安心の動画説明やガイド付き。100か国以上で活用される信頼性no高いeSIMサービス。",
  };

  return metadata;
}

export default async function Page() {
  return (
    <AppLayout isLogoTextTitle={false}>
      <Suspense>
        <ContactLayout>
          <Help />
        </ContactLayout>
      </Suspense>
    </AppLayout>
  );
}