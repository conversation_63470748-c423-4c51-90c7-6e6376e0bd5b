import type { Metadata } from "next";
import { Inter } from "next/font/google";
import Script from "next/script";
import { Suspense } from "react";

import "@mantine/carousel/styles.css";
import { ColorSchemeScript, MantineProvider } from "@mantine/core";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css";
import { Notifications } from "@mantine/notifications";
import "@mantine/notifications/styles.css";

import { dir } from "i18next";

import TranslationsProvider from "@/app/components/TranslationProvider";
import RootAuthProvider from "@/app/components/common/RootAuthProvider";

import "@/app/globals.css";
import { theme } from "@/app/theme";
import initTranslations from "@/i18n";
import { locales } from "@/i18n/settings";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const inter = Inter({ subsets: ["latin"], display: "swap" });

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "countries",
  "region",
  "destination",
  "payment",
  "paypay-error",
];

export const metadata: Metadata = {
  title: "エアトリeSIM - 海外旅行向けのeSIMサービス",
  description:
    "世界100か国以上で利用されているeSIMサービス！すぐに回線開通、使い勝手の良いシンプルな操作で、旅行もビジネスも快適に。安心してご利用いただけるサポート体制を完備。",
};

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <html lang={locale} dir={dir(locale)}>
      <head>
        <ColorSchemeScript />
        <meta
          name="google-site-verification"
          content="9nlt4Wh0oUSTtlR-31UyO9KB6xIiH9qiGDmAcJC1bDE"
        />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        {process.env.NODE_ENV !== "development" && (
          <>
            <Script
              strategy="afterInteractive"
              src="https://www.googletagmanager.com/gtag/js?id=G-PFB0VTCESN"
            ></Script>
            <Script
              id="google-analytics"
              strategy="afterInteractive"
              dangerouslySetInnerHTML={{
                __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-PFB0VTCESN');
          `,
              }}
            ></Script>
          </>
        )}
        <Script
          id="jquery"
          src="https://code.jquery.com/jquery-3.7.0.min.js"
          integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g="
          crossOrigin="anonymous"
        ></Script>
        <Script
          type="text/javascript"
          id="ukommi-reviews"
          dangerouslySetInnerHTML={{
            __html: `
           (function u(){var u=document.createElement("script");u.type="text/javascript",u.async=true,u.src="//api.u-komi.com/760019e323d255f9aab5fc769cdffed1aa5765f66808288933e34c66ca381e43/widget.js";var k=document.getElementsByTagName("script")[0];k.parentNode.insertBefore(u,k)})();
          `,
          }}
        ></Script>
      </head>
      <body className={inter.className} id="app">
        <Script
          id="jquery"
          src="https://code.jquery.com/jquery-3.7.0.min.js"
          integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g="
          crossOrigin="anonymous"
        ></Script>
        <Script
          id="script-1"
          dangerouslySetInnerHTML={{
            __html: `
            (function(w,r)\{w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful')
            `,
          }}
        ></Script>

        {process.env.NODE_ENV !== "development" && (
          <Script
            type="text/javascript"
            id="script-3"
            dangerouslySetInnerHTML={{
              __html: `
              (function(w,d,s,l,i){w[l] = w[l] || [];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-TBGT3KJ');
              `,
            }}
          ></Script>
        )}
        {process.env.NODE_ENV !== "development" && (
          <div
            dangerouslySetInnerHTML={{
              __html: `
            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TBGT3KJ"
            height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
            <!-- End Google Tag Manager (noscript) -->
            `,
            }}
          ></div>
        )}
        <TranslationsProvider
          namespaces={i18nNamespaces}
          locale={locale}
          resources={resources}
        >
          <MantineProvider theme={theme}>
            <Notifications position="top-right" />
            <Suspense>
              <RootAuthProvider>{children}</RootAuthProvider>
            </Suspense>
          </MantineProvider>
        </TranslationsProvider>
        <Script
          id="rewardful-870521"
          src="https://r.wdfl.co/rw.js"
          data-rewardful="870521"
        ></Script>
        <Script id="rewardful-queue" strategy="beforeInteractive">
          {`(function(w,r){w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful');`}
        </Script>
      </body>
    </html>
  );
}
