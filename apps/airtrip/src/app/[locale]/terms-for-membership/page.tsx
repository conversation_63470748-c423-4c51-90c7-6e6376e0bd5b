"use client";

/*
 * import modules and libraries
 */
import { Box, Text } from "@mantine/core";

import { useTranslation } from "react-i18next";

/*
 * import components
 */
import SectionContent from "@repo/ui/src/common/SectionContent";

import AppLayout from "@/app/components/common/AppLayout";

export default function TermsAndConditions() {
  const { t } = useTranslation();

  return (
    <AppLayout contentBg="#f3f3f3" isLogoTextTitle={false}>
      <SectionContent
        small
        mainTitle="home:termsformembership"
        usePrimaryColor={true}
      >
        <Box className="bg-white p-4">
          <Text
            dangerouslySetInnerHTML={{
              __html: t("termsformembership:termsformembership"),
            }}
          />
        </Box>
      </SectionContent>
    </AppLayout>
  );
}
