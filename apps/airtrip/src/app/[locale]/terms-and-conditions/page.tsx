"use client";

/*
 * import modules and libraries
 */
import { useTranslation } from "react-i18next";
import { Text, Box, } from "@mantine/core";
/*
 * import components
 */
import SectionContent from "@repo/ui/src/common/SectionContent";
import AppLayout from "@/app/components/common/AppLayout";
/*
 * import constants
 */
import jpTOS from "@/app/constants/jp-tos";

export default function TermsAndConditions() {
  const { t } = useTranslation();

  return (
    <AppLayout contentBg="#f3f3f3">
      <SectionContent
        small
        title={t("home:termsandconditions")}
      >
        <Box className="bg-white p-4">
          <Text
            dangerouslySetInnerHTML={{
                __html: jpTOS.replaceAll(/\n/g, "<br/>")
            }}
          />
        </Box>
      </SectionContent>
    </AppLayout>
  );
}
