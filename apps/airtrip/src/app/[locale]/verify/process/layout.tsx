import type { Metadata } from "next";
import TranslationsProvider from "@/app/components/TranslationProvider";
import { locales } from "@/i18n/settings";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "signup",
  "signin",
  "login",
  "verify",
];

export const metadata: Metadata = {
  title: "エアトリeSIM - 海外旅行向けのeSIMサービス",
  description:
  "世界100か国以上で信頼されるeSIMサービス！申込みから瞬時に回線開通、設定も簡単。旅行好きなあなたに最適なシンプルで使いやすいWebアプリ。",
  robots: {
    index: false,
  },
};

const Layout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale} 
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default Layout;
