{"v": "5.5.7", "meta": {"g": "LottieFiles AE 0.1.20", "a": "", "k": "", "d": "", "tc": ""}, "fr": 29.9700012207031, "ip": 0, "op": 60.0000024438501, "w": 320, "h": 200, "nm": "404", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "mark Outlines", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [19.9, 19.295, 0], "ix": 2}, "a": {"a": 0, "k": [2.503, 12.271, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.244, 0], [0, 0], [0, 1.244], [-1.244, 0], [0, -1.244]], "o": [[0, 0], [-1.244, 0], [0, -1.244], [1.244, 0], [0, 1.244]], "v": [[0, 2.253], [0, 2.253], [-2.253, -0.001], [0, -2.253], [2.253, -0.001]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.503, 22.04], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.244, 0], [0, 0], [0, 1.244], [0, 0], [-1.244, 0], [0, -1.244], [0, 0]], "o": [[0, 0], [-1.244, 0], [0, 0], [0, -1.244], [1.244, 0], [0, 0], [0, 1.244]], "v": [[0, 8.26], [0, 8.26], [-2.253, 6.007], [-2.253, -6.008], [0, -8.26], [2.253, -6.008], [2.253, 6.007]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.503, 8.51], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 3 Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [0]}, {"t": 17.0000006924242, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [198.645, -21.677, 0], "to": [0, 13.167, 0], "ti": [0, -13.167, 0]}, {"t": 17.0000006924242, "s": [198.645, 57.323, 0]}], "ix": 2, "x": "var $bm_rt;\nvar _0x9233 = [\n        'BOUNCr overSHOOT+',\n        'Amplitude',\n        'Frequency',\n        'Decay',\n        'Floor',\n        'index',\n        'time',\n        'frameDuration',\n        'PI',\n        'sin',\n        'exp',\n        'abs'\n    ];\ntry {\n    var effecto = effect(_0x9233[0]);\n    var amp = $bm_div(effecto(_0x9233[1]), 1000);\n    var freq = effecto(_0x9233[2]);\n    var decay = effecto(_0x9233[3]);\n    var floor = effecto(_0x9233[4]);\n    var n, numkeys, v, t;\n    if (floor != true) {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time)[_0x9233[5]];\n            if (key(n)[_0x9233[6]] > time) {\n                n--;\n            }\n        }\n        ;\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n)[_0x9233[6]]);\n        }\n        ;\n        if (n > 0) {\n            v = velocityAtTime($bm_sub(key(n)[_0x9233[6]], $bm_div(thisComp[_0x9233[7]], 10)));\n            $bm_rt = $bm_sum(value, $bm_mul($bm_mul(v, amp), $bm_div(Math[_0x9233[9]]($bm_mul($bm_mul($bm_mul(freq, t), 2), Math[_0x9233[8]])), Math[_0x9233[10]]($bm_mul(decay, t)))));\n        } else {\n            $bm_rt = value;\n        }\n    } else {\n        $bm_rt = n = 0;\n        if (numKeys > 0) {\n            $bm_rt = n = nearestKey(time)[_0x9233[5]];\n            if (key(n)[_0x9233[6]] > time) {\n                n--;\n            }\n        }\n        ;\n        if (n == 0) {\n            $bm_rt = t = 0;\n        } else {\n            $bm_rt = t = $bm_sub(time, key(n)[_0x9233[6]]);\n        }\n        ;\n        if (n > 0) {\n            v = velocityAtTime($bm_sub(key(n)[_0x9233[6]], $bm_div(thisComp[_0x9233[7]], 10)));\n            $bm_rt = $bm_sum(value, $bm_mul($bm_mul(v, amp), $bm_neg($bm_div(Math[_0x9233[11]](Math[_0x9233[9]]($bm_mul($bm_mul($bm_mul(freq, t), 2), Math[_0x9233[8]]))), Math[_0x9233[10]]($bm_mul(decay, t))))));\n        } else {\n            $bm_rt = value;\n        }\n    }\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [19.9, 19.899, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "BOUNCr overSHOOT+", "np": 8, "mn": "Pseudo/pse bouncR overSHOOT+", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/pse bouncR overSHOOT+-0001", "ix": 1, "v": {"a": 0, "k": 65, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/pse bouncR overSHOOT+-0002", "ix": 2, "v": {"a": 0, "k": 3, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/pse bouncR overSHOOT+-0003", "ix": 3, "v": {"a": 0, "k": 6, "ix": 3}}, {"ty": 0, "nm": "Floor", "mn": "Pseudo/pse bouncR overSHOOT+-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "Ã¯Â½Â©2018 pixelbot - BOUNCr_v1.1", "mn": "Pseudo/pse bouncR overSHOOT+-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "BOUNCr overSHOOT+", "mn": "Pseudo/pse bouncR overSHOOT+-0006", "ix": 6, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -10.852], [10.852, 0], [0, 10.852], [-10.852, 0]], "o": [[0, 10.852], [-10.852, 0], [0, -10.852], [10.852, 0]], "v": [[19.65, 0], [0.001, 19.649], [-19.65, 0], [0.001, -19.649]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.949, 0.345, 0.306, 0.5, 0.969, 0.249, 0.269, 1, 0.99, 0.153, 0.232], "ix": 9}}, "s": {"a": 0, "k": [0.919, -19.305], "ix": 5}, "e": {"a": 0, "k": [1.461, 19.954], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.9, 19.899], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "shadow Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [59.474]}, {"t": 34.0000013848484, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [192.874, 67.919, 0], "ix": 2}, "a": {"a": 0, "k": [10.821, 15.161, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.518, 0.466], [0.611, 2.446], [1.717, 3.21], [3, 2.924], [0.416, 0.36], [0, -5.597], [-9.025, 0]], "o": [[-0.422, -2.528], [-1.032, -4.229], [-1.743, -3.26], [-0.387, -0.377], [-4.29, 2.946], [0, 9.026], [1.671, 0]], "v": [[10.572, 14.192], [9.033, 6.724], [4.888, -4.486], [-2.26, -13.806], [-3.464, -14.911], [-10.572, -1.432], [5.771, 14.911]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.286549407361, 0.552295520259, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.821, 15.161], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 5 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [160.035, 100, 0], "to": [0, 0.917, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [160.035, 105.5, 0], "to": [0, 0, 0], "ti": [0, 0.917, 0]}, {"t": 22.0000008960784, "s": [160.035, 100, 0]}], "ix": 2}, "a": {"a": 0, "k": [45.07, 56.17, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.946, -4.134], [1.675, -1.603], [2.103, 0], [2.364, 4.894], [0, 12.516], [-1.917, 4.368], [-3.475, 0], [-1.795, -3.644], [0, -16.272]], "o": [[-0.862, 3.772], [-1.554, 1.484], [-2.009, 0], [-2.094, -4.339], [0, -12.412], [1.605, -3.657], [3.443, 0], [1.436, 2.914], [0, 8.997]], "v": [[9.154, 20.45], [5.328, 28.55], [-0.105, 30.756], [-7.423, 25.295], [-10.579, -0.105], [-7.691, -25.394], [-0.245, -30.756], [7.432, -25.414], [10.579, 0.662]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.955, 7.82], [1.717, 3.21], [3, 2.923], [4.787, 1.84], [7.226, 0], [7.565, -8.395], [0, -20.411], [-1.408, -6.709], [-2.646, -4.04], [-5.755, -2.978], [-9.056, 0], [-6.851, 9.387], [0, 19.134]], "o": [[-1.032, -4.229], [-1.743, -3.26], [-3.071, -2.996], [-4.746, -1.823], [-15.154, 0], [-7.59, 8.422], [0, 8.085], [1.443, 6.876], [3.669, 5.731], [5.711, 2.956], [16.3, 0], [6.636, -9.094], [0, -8.49]], "v": [[41.871, -25.357], [37.727, -36.567], [30.579, -45.886], [18.738, -53.173], [0.697, -55.92], [-33.54, -43.269], [-44.82, -0.42], [-42.697, 21.875], [-36.527, 38.34], [-22.324, 51.465], [-0.07, 55.92], [34.818, 41.773], [44.82, -0.768]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.090000002992, 0.528999956916, 0.933000033509, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.07, 56.17], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 6 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [255.368, 99.127, 0], "ix": 2}, "a": {"a": 0, "k": [47.967, 55.297, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[4.087, 7.857], [-13.799, 7.857], [4.087, -13.19]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.657, 0], [0, 0], [0, 0], [1.657, 0], [0, 0], [0.57, -0.677], [0, 0], [0, -0.707], [0, 0], [-1.657, 0], [0, 0], [0, 0], [-1.657, 0], [0, 0], [0, 1.658], [0, 0], [0, 0], [0, 1.657], [0, 0]], "o": [[0, 0], [0, 0], [0, -1.657], [0, 0], [-0.886, 0], [0, 0], [-0.455, 0.541], [0, 0], [0, 1.657], [0, 0], [0, 0], [0, 1.658], [0, 0], [1.657, 0], [0, 0], [0, 0], [1.657, 0], [0, 0], [0, -1.657]], "v": [[44.716, 7.857], [34.871, 7.857], [34.871, -52.047], [31.871, -55.047], [7.087, -55.047], [4.79, -53.978], [-47.012, 7.598], [-47.716, 9.53], [-47.716, 32.919], [-44.716, 35.919], [4.087, 35.919], [4.087, 52.047], [7.087, 55.047], [31.871, 55.047], [34.871, 52.047], [34.871, 35.919], [44.716, 35.919], [47.716, 32.919], [47.716, 10.857]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.090000002992, 0.528999956916, 0.933000033509, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.966, 55.297], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Layer 7 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64.633, 99.127, 0], "ix": 2}, "a": {"a": 0, "k": [47.966, 55.297, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[4.087, 7.857], [-13.799, 7.857], [4.087, -13.19]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.657, 0], [0, 0], [0, 0], [1.657, 0], [0, 0], [0.57, -0.677], [0, 0], [0, -0.707], [0, 0], [-1.657, 0], [0, 0], [0, 0], [-1.657, 0], [0, 0], [0, 1.658], [0, 0], [0, 0], [0, 1.657], [0, 0]], "o": [[0, 0], [0, 0], [0, -1.657], [0, 0], [-0.886, 0], [0, 0], [-0.455, 0.541], [0, 0], [0, 1.657], [0, 0], [0, 0], [0, 1.658], [0, 0], [1.657, 0], [0, 0], [0, 0], [1.657, 0], [0, 0], [0, -1.657]], "v": [[44.717, 7.857], [34.87, 7.857], [34.87, -52.047], [31.87, -55.047], [7.087, -55.047], [4.79, -53.978], [-47.012, 7.598], [-47.717, 9.53], [-47.717, 32.919], [-44.717, 35.919], [4.087, 35.919], [4.087, 52.047], [7.087, 55.047], [31.87, 55.047], [34.87, 52.047], [34.87, 35.919], [44.717, 35.919], [47.717, 32.919], [47.717, 10.857]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.090000002992, 0.528999956916, 0.933000033509, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.966, 55.297], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}], "markers": []}