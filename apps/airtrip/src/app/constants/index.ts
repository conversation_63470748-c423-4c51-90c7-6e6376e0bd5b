export const ESIM_API_PATH = process.env.NEXT_PUBLIC_API_HOST;
export const ERR_GENERIC_REQUIRED = "この項目は必須です。";
export const REGEX_STRONG_PASSWORD = /^(?=.*[A-Z]).{8,}$/;
export const ERR_MIN_PASSWORD_LENGTH =
  "パスワードは8文字以上でなければなりません。";
export const ERR_STRONG_PASSWORD =
  "パスワードは 8 文字以上で、少なくとも 1 つの大文字を含める必要があります。";
export const AUTH_TOKEN_NAME = "gesim";
export const ERR_PASSWORD_NOT_MATCHED =
  "ユーザー名とパスワードの組み合わせが一致しませんでした。";
export const google_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Google`;
export const fb_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Facebook`;
export const FIIT_AFFILIATE = ["fiit01", "fiit02", "fiit03", "cospakorea"];

const countries = [
  "Japan",
  "Korea",
  "Taiwan",
  "China",
  "Macau",
  "HongKong",
  "USA",
  "Hawaii",
  "Canada",
  "Philippines",
  "Thailand",
  "Singapore",
  "Vietnam",
  "Malaysia",
  "Indonesia",
  "Cambodia",
  "Laos",
  "Italy",
  "France",
  "Germany",
  "UK",
  "Australia",
  "New Zealand",
  "Guam",
];
export const COUNTRY_SORT = new Map();
// Sorting countries
for (let x = 0; x < countries.length; x += 1) {
  const country_name = countries[x];
  COUNTRY_SORT.set(country_name.toLocaleLowerCase(), x + 1);
}

export const PURCHASE_STEPS = ["プラン選択", "内容確認・決済", "注文完了"];
