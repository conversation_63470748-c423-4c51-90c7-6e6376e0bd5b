@layer tailwind {
  @tailwind base;
}
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

a {
  color: -webkit-link;
  text-decoration: underline;
}

ul {
  list-style-type: disc;
}

.text-hour {
  font-size: 9px;
  line-height: 14px;
}
.text-phone {
  line-height: 21px;
}

.grecaptcha-badge {
  /* bottom: 150px !important; */
  display: none !important;
}

.success-esim {
  background-image: url("/assets/icon/circle.png");
  background-color:rgba(255,255,255,0.8);
  background-blend-mode:lighten;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.partial-esim {
  background-image: url("/assets/icon/triangle.png");
  background-color:rgba(255,255,255,0.8);
  background-blend-mode:lighten;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.fail-esim {
  background-image: url("/assets/icon/cross.png");
  background-color:rgba(255,255,255,0.8);
  background-blend-mode:lighten;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.linear-bg {
  background: linear-gradient(118.01deg, rgba(231, 243, 249, 0.85) 13.05%, rgba(25, 111, 185, 0.50) 93.79%);
}
