"use client";

import dynamic from "next/dynamic";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  ComponentPropsWithoutRef,
  Dispatch,
  ForwardedRef,
  SetStateAction,
  forwardRef,
  useCallback,
  useEffect,
  useState,
} from "react";

import {
  Accordion,
  Alert,
  Anchor,
  Avatar,
  Checkbox,
  Group,
  Modal,
  Radio,
  Stack,
  Text,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";

import { useElements, useStripe } from "@stripe/react-stripe-js";
import { IconCreditCard, IconInfoCircle } from "@tabler/icons-react";
import { Trans, useTranslation } from "react-i18next";

import { getCDNUrl, simpleStringEncode } from "@/utils";
import { AGENT } from "@/utils/agent-handler";

import { ApiService } from "@/api";

import { IPlan } from "@/interfaces/IPlan";

import { useOrderStore } from "@/store";
import { useMessageStore } from "@/store/MessageStore";
import { useProfile } from "@/store/UserStore";

import InternalLink from "../../common/InternalLink";
import CreditCardCheckoutForm from "../CreditCardCheckoutForm";
import styles from "./checkoutForm.module.css";

const DynamicLGUCompatibility = dynamic(
  () => import("@repo/ui/src/modals/LGUCompatibility")
);
interface ItemProps extends ComponentPropsWithoutRef<"div"> {
  image: string;
  label: string;
  description: string;
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(
  ({ image, label, description, ...others }: ItemProps, ref) => (
    <div ref={ref} {...others}>
      <Group wrap="nowrap">
        <Avatar src={image} size={"xs"} />
        <div>
          <Text size="sm">{label}</Text>
        </div>
      </Group>
    </div>
  )
);

SelectItem.displayName = "SelectItem";

const CheckoutForm = forwardRef(
  (
    {
      isAgreementError,
      planId,
      plan,
      clientSecret,
      price,
      isLGU,
      form,
      isNeedRegistration,
      isCompatibilityChecked,
      setIsCompatibilityChecked,
      isTermsChecked,
      setIsTermsChecked,
      isNeedRegistrationChecked,
      setIsNeedRegistrationChecked,
      isCheckoutDisabled,
      setIsCheckoutDisabled,
      recaptcha,
      setCaptchaResetKey,
      agent,
      isFREEeSIM,
      ...props
    }: {
      planId: string;
      plan: IPlan;
      clientSecret: string;
      price: number;
      isAgreementError: boolean;
      setPaymentRequest: (pr: any) => void;
      setPaymentMethod: (paymentMethod: string) => void;
      isCompatibilityChecked: boolean;
      setIsCompatibilityChecked: Dispatch<SetStateAction<boolean>>;
      isTermsChecked: boolean;
      setIsTermsChecked: Dispatch<SetStateAction<boolean>>;
      isNeedRegistrationChecked: boolean;
      setIsNeedRegistrationChecked: Dispatch<SetStateAction<boolean>>;
      isCheckoutDisabled: { [key: string]: boolean };
      setIsCheckoutDisabled: Dispatch<
        SetStateAction<{ number: boolean; expiry: boolean; cvc: boolean }>
      >;
      isLGU?: boolean;
      form?: any;
      isNeedRegistration?: boolean;
      recaptcha?: string;
      setCaptchaResetKey: (key: number) => void;
      isFREEeSIM?: boolean;
      agent: string | null;
    },
    ref: ForwardedRef<HTMLFormElement>
  ) => {
    const router = useRouter();
    const { profile } = useProfile();
    const { t, i18n } = useTranslation();
    const stripe = useStripe();
    const elements = useElements();
    let buttonList = ["credit"];

    const toggleCompatibilityModalOpen: () => void = useOrderStore(
      (state) => state.toggleCompatibilityModalOpen
    );
    const toggleLoading = useMessageStore((state) => state.toggleLoading);
    const [activePaymentMethod, setActivePaymentMethod] = useState("credit");
    const [paymentRequest, setPaymentRequest] = useState(null);
    const [canApplePay, setCanApplePay] = useState(false);
    const [canGooglePay, setCanGooglePay] = useState(false);
    const [cardErrMessage, setCardErrMessage] = useState<string | null>(null);
    const [checkoutAgreeError, setCheckoutAgreeError] = useState<string | null>(
      null
    );
    const [referral, setReferral] = useState(null);
    const [lguModal, setLguModal] = useDisclosure();
    const [opened, { open, close }] = useDisclosure(false);

    const handleCreditCardPaymentMethod = async (
      ev: any,
      onSuccess?: () => void,
      onError?: () => void,
      isWallet = false
    ) => {
      setCardErrMessage("");
      if (!isCompatibilityChecked) {
        return;
      }
      if (!isTermsChecked) {
        return;
      }
      if (!stripe || !elements) {
        return;
      }

      toggleLoading();
      let result;
      if (planId) {
        let couponDetails = null;
        try {
          if (agent && agent !== AGENT.AIRTRIP_GMO) {
            couponDetails = await ApiService.redeemCoupon({
              couponId: agent,
              planId: planId,
            });
          }
        } catch (err) {
          console.log(err);
          setCaptchaResetKey(Date.now());
          setCardErrMessage("エラーが発生しました。もう一度お試しください");
          toggleLoading(false);
        }

        try {
          const response = await (profile
            ? ApiService.purchasePlan({
                sessionSecret: clientSecret,
                locale: i18n.language || "en",
                recaptcha,
                products: {
                  planId,
                  qty: 1,
                },
                referral,
                couponId: couponDetails?.data?.data?.code,
                couponReferenceId: couponDetails?.data?.data?.referenceId,
              })
            : ApiService.purchasePlanAsGuest({
                sessionSecret: clientSecret,
                locale: i18n.language || "en",
                recaptcha,
                products: {
                  planId,
                  qty: 1,
                },
                couponId: couponDetails?.data?.data?.code,
                couponReferenceId: couponDetails?.data?.data?.referenceId,
                email: form?.values?.email,
                firstName: form?.values?.firstName,
                lastName: form?.values?.lastName,
                source: "airtrip",
              }));
          result = response.data?.data;
        } catch (err) {
          setCaptchaResetKey(Date.now());
          toggleLoading(false);
          if (
            //@ts-expect-error
            err?.response?.data?.message.includes(
              "Coupon code is only usable once"
            )
          ) {
            open();
            return;
          } else {
            setCardErrMessage("エラーが発生しました。もう一度お試しください");
          }
        }

        let error, paymentIntent;
        if (!isFREEeSIM) {
          const cardResponse = await stripe.confirmCardPayment(
            result.payment.client_secret,
            {
              payment_method: isWallet
                ? ev.paymentMethod.id
                : {
                    card: elements?.getElement("cardNumber"),
                  },
            },
            isWallet ? { handleActions: false } : {}
          );
          error = cardResponse.error;
          paymentIntent = cardResponse.paymentIntent;
        }
        toggleLoading(false);
        if (error) {
          // This point will only be reached if there is an immediate error when
          // confirming the payment. Show error to your customer (for example, payment
          // details incomplete)
          setCaptchaResetKey(Date.now());
          onError?.();
          setCardErrMessage(
            error?.message || "エラーが発生しました。もう一度お試しください"
          );
          toggleLoading(false);
        } else if (
          paymentIntent?.status === "succeeded" ||
          (isFREEeSIM && result.instantEsimSecret)
        ) {
          onSuccess?.();
          sessionStorage.setItem(
            "isNeedRegistrationChecked",
            isNeedRegistrationChecked.toString()
          );
          sessionStorage.setItem("amount", result.payment.amount);
          router.replace(
            window.location.protocol +
              "//" +
              window.location.host +
              `/checkout/${
                result.orderId
              }/complete?transaction=${simpleStringEncode(
                Date.now() + ""
              )}&event=complete&uid=${simpleStringEncode(
                result.instantEsimSecret
              )}&country=${plan?.country?.name}`
          );
        }
      }
    };

    const AdditionalPaymentOption = (props: {
      payment: "google" | "apple";
    }) => (
      <Accordion.Item value={props.payment}>
        <Accordion.Control>
          <Group align="center">
            <Radio
              readOnly
              color={"app-pink.4"}
              checked={isActivePaymentMethod(props.payment)}
            />
            <Group gap={5} align="center">
              <Image
                src={getCDNUrl(
                  `/assets/payments/${props.payment}_pay_logo.png`
                )}
                width={35}
                height={35}
                alt={`${props.payment} Pay Logo`}
              />
              <Text tt="capitalize" fw={600}>
                {props.payment} Pay
              </Text>
            </Group>
          </Group>
        </Accordion.Control>
      </Accordion.Item>
    );

    const AgreementCheckboxes = () => {
      return (
        <Stack className="mt-4 gap-4">
          <Checkbox
            error={checkoutAgreeError || isAgreementError}
            onChange={(event) =>
              setIsCompatibilityChecked(event.currentTarget.checked)
            }
            checked={isCompatibilityChecked}
            className="items-center justify-center"
            label={
              <Text c={"#555555"}>
                {t(
                  "region:checkoutagreement",
                  "Before completing this order, please confirm your device is eSIM comptabile and network-unlocked."
                )}
                <button
                  className="text-[#0000ee] underline decoration-black"
                  onClick={
                    isLGU ? setLguModal.open : toggleCompatibilityModalOpen
                  }
                >
                  {t("region:checkcompatibility", "Check Compatibility")}
                </button>
              </Text>
            }
          />
          {isNeedRegistration && (
            <Checkbox
              onChange={(event) =>
                setIsNeedRegistrationChecked(event.currentTarget.checked)
              }
              checked={isNeedRegistrationChecked}
              className="items-center justify-center"
              label={
                <Text
                  c={"#555555"}
                  dangerouslySetInnerHTML={{
                    __html: t("payment:hongkongtaiwannote"),
                  }}
                ></Text>
              }
            />
          )}
          <Checkbox
            error={isAgreementError}
            checked={isTermsChecked}
            onChange={(event) => setIsTermsChecked(event.currentTarget.checked)}
            label={
              <Text c={"#555555"}>
                <Trans i18nKey="signup:agreement.txt">
                  I agree to グロモバ&apos;s
                  <InternalLink
                    underline
                    target="_blank"
                    defaultColor
                    href={
                      i18n?.language === "jp"
                        ? "https://www.gmobile.biz/terms-of-service/?value=esim"
                        : "/terms-and-conditions"
                    }
                  >
                    Terms and Conditions
                  </InternalLink>{" "}
                  <InternalLink
                    underline
                    target="_blank"
                    defaultColor
                    href={
                      i18n?.language === "jp"
                        ? "https://www.inbound-platform.com/privacy/"
                        : "https://www.inbound-platform.com/en/privacy/"
                    }
                  >
                    Privacy Policy
                  </InternalLink>{" "}
                  <InternalLink
                    underline
                    target="_blank"
                    defaultColor
                    href="/terms-for-membership"
                  >
                    Terms for membership.
                  </InternalLink>
                </Trans>
              </Text>
            }
          />
        </Stack>
      );
    };

    const isActivePaymentMethod = (
      paymentMethodType: "credit" | "apple" | "google"
    ) => {
      return activePaymentMethod === paymentMethodType;
    };

    useEffect(() => {
      setCheckoutAgreeError(null);
    }, [isCompatibilityChecked]);

    useEffect(() => {
      if (cardErrMessage) {
        showNotification({
          message: cardErrMessage,
          color: "red",
        });
        setCardErrMessage("");
      }
    }, [cardErrMessage]);

    const handlePaymentMethodClick = useCallback(
      (value: string | null) => {
        setActivePaymentMethod(value || "credit");
        props.setPaymentMethod(value || "credit");
      },
      [props, isCompatibilityChecked]
    );

    useEffect(() => {
      if (!paymentRequest) return;
      //@ts-ignore
      const handler = async (ev) => {
        handleCreditCardPaymentMethod(
          ev,
          () => {
            ev.complete("success");
          },
          () => {
            ev.complete("fail");
          },
          true
        );
      };

      // @ts-expect-error
      paymentRequest.on("paymentmethod", handler);

      props.setPaymentRequest(paymentRequest);

      return () => {
        // @ts-expect-error
        paymentRequest.off("paymentmethod", handler);
      };
    }, [paymentRequest, handleCreditCardPaymentMethod, recaptcha]);

    useEffect(() => {
      if (stripe) {
        if (!Number(price || 0)) {
          // router.back();
          return;
        }
        const pr = stripe.paymentRequest({
          country: "JP",
          currency: "jpy",
          total: {
            label: "",
            amount: Number(price),
          },
          requestPayerName: true,
          requestPayerEmail: true,
        });

        const checkCanMakePayment = async () => {
          const canMakePaymentObject = await pr.canMakePayment();
          if (canMakePaymentObject) {
            const { applePay, googlePay } = canMakePaymentObject;
            if (applePay) {
              setCanApplePay(true);
              if (!buttonList.includes("apple")) {
                buttonList.push("apple");
              }
            }

            if (googlePay) {
              setCanGooglePay(true);
              if (!buttonList.includes("google")) {
                buttonList.push("google");
              }
            }
            //@ts-ignore
            setPaymentRequest(pr);
          }
        };
        checkCanMakePayment();
      }
    }, [stripe, price]);

    useEffect(() => {
      //@ts-ignore
      window?.rewardful?.("ready", () => {
        //@ts-ignore
        if (window.Rewardful.referral) {
          //@ts-ignore
          setReferral(window.Rewardful.referral);
        }
      });
    }, []);

    const handleSelfLinkClick = useCallback(
      (e: any) => {
        e.preventDefault();
        sessionStorage.removeItem("agent");
        window.location.href = "/";
      },
      [agent]
    );

    return (
      <>
        <Modal
          opened={opened}
          onClose={close}
          className="rounded-md"
          radius={"lg"}
          centered
          transitionProps={{
            transition: "fade",
            duration: 500,
            timingFunction: "linear",
          }}
          classNames={{
            body: "relative z-[9999] p-10 ",
          }}
        >
          <Text ta={"center"} className="-mt-16">
            すでにキャンペーンをご利用済みです。
            <br />
            <br />
            追加のご利用は{" "}
            <Anchor
              href="https://esim.airtrip.jp/region/korea"
              onClick={handleSelfLinkClick}
            >
              こちらのサイト{" "}
            </Anchor>
            より、 <br />
            お申込みくださいませ。
          </Text>
        </Modal>
        {referral ? (
          <input type="hidden" name="referral" value={referral} />
        ) : null}
        <div style={{ display: isFREEeSIM ? "none" : "block" }}>
          <Accordion
            classNames={{ chevron: styles["chevron"] }}
            value={activePaymentMethod}
            onChange={handlePaymentMethodClick}
            variant="contained"
            defaultValue="credit"
          >
            <Accordion.Item value="credit">
              <Accordion.Control>
                <Group align="center">
                  <Radio
                    readOnly
                    color={"app-pink.4"}
                    checked={isActivePaymentMethod("credit")}
                  />
                  <Group gap={5} align="center">
                    <IconCreditCard size={35} strokeWidth={1} color={"black"} />
                    <Text fw={600}>
                      {t("payment:paymentform.creditcard.title")}
                    </Text>
                  </Group>
                </Group>
              </Accordion.Control>
              <Accordion.Panel>
                {isActivePaymentMethod("credit") && (
                  <CreditCardCheckoutForm
                    isFREEeSIM={isFREEeSIM}
                    formRef={ref}
                    clientSecret={clientSecret}
                    planId={planId}
                    handlePaymentMethod={handleCreditCardPaymentMethod}
                    isCheckoutDisabled={isCheckoutDisabled}
                    setIsCheckoutDisabled={setIsCheckoutDisabled}
                  />
                )}
              </Accordion.Panel>
            </Accordion.Item>
            {canApplePay && <AdditionalPaymentOption payment={"apple"} />}
            {canGooglePay && <AdditionalPaymentOption payment={"google"} />}
          </Accordion>
        </div>

        {cardErrMessage && (
          <Alert
            className="my-4"
            color="red"
            title="エラー"
            icon={<IconInfoCircle />}
          >
            {cardErrMessage}
          </Alert>
        )}
        <AgreementCheckboxes />
        {isLGU && (
          <DynamicLGUCompatibility
            size="lg"
            title="LGU⁺ eSIM対応機種について"
            opened={lguModal}
            onClose={setLguModal.close}
          />
        )}
      </>
    );
  }
);
CheckoutForm.displayName = "CheckoutForm";
export default CheckoutForm;
