"use client";

import { useCallback } from "react";

import { Box, Group, Stack, Text, Title } from "@mantine/core";

import { useTranslation } from "react-i18next";

import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";

const UserInfo = ({
  form,
}: {
  form: any;
}) => {
  const { t } = useTranslation();

  const handleSubmit = useCallback(
    async (values: typeof form.values) => {
      try {
      } catch (err: any) {}
    },
    [form]
  );

  return (
    // TODO: add text to translation sheet
    <form onSubmit={form.onSubmit(handleSubmit)}>
      <Stack className="gap-4">
        <Box>
          <Title order={4}>ご契約者情報</Title>
        </Box>
        <Stack className="gap-4 border border-solid border-[#dee2e6] bg-[#f8f9fa] p-4">
          <AppTextInput
            id="app-text-email-input-airtrip"
            withAsterisk
            label={t("login:email.title")}
            placeholder={t("signup:formfield.email.placeholder") as string}
            className="flex-1"
            {...form.getInputProps("email")}
          />
          <Box className="mb-[10px]">
            <Text className="mb-[10px] text-sm">
              名前{" "}
              <Text component="span" c="red.6" className="text-sm">
                *
              </Text>
            </Text>
            <Group className="items-start">
              <AppTextInput
                id="app-text-last-name-input-airtrip"
                withAsterisk
                placeholder="YAMADA（姓）"
                className="flex-1"
                {...form.getInputProps("lastName")}
              />
              <AppTextInput
                id="app-text-first-name-input-airtrip"
                withAsterisk
                placeholder="HANAKO（名）"
                className="flex-1"
                {...form.getInputProps("firstName")}
              />
            </Group>
          </Box>
        </Stack>
      </Stack>
    </form>
  );
};

export default UserInfo;
