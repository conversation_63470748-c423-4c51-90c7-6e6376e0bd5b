"use client";

/*
 * import modules and libraries
 */
import {
  MantineProvider,
  BackgroundImage,
  Badge,
  Box,
  Image,
  Loader,
  Overlay,
  Paper,
  Stack,
  Text,
  Alert,
} from "@mantine/core";
import Link from "next/link";
import { AxiosError } from "axios";
import { useQuery } from "react-query";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { IconQrcode } from "@tabler/icons-react";
/*
 * import components
 */
import RingLoader from "@/app/components/common/RingLoader";
/*
 * import constants and helpers
 */
import { simpleStringDecode } from "@/utils";
import QRSamplePNG from "./qr-sample.webp";
/*
 * import api
 */
import { ApiService } from "@/api";
/*
 * import interfaces
 */
import type { IPlan } from "@/interfaces/IPlan";

const InstantEsim = ({ orderId }: { orderId: string }) => {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const [order, setOrder] = useState<
    | {
        orderId: string;
        iccid: string;
        qr: string;
        plan: IPlan;
      }
    | undefined
  >(undefined);
  const [isEsimInstantFailed, setIsEsimInstantFailed] = useState(false);

  const sessionSecret = useMemo(() => {
    try {
      return simpleStringDecode(searchParams.get("uid") + "");
    } catch (err) {
      console.log(err);
      return "";
    }
  }, []);

  const isAllEsimFetched = useMemo(() => {
    return !!order?.qr;
  }, [order]);

  const payload = {
    secret: sessionSecret,
    transaction: searchParams.get("transaction") + "",
    timestamp: Date.now() + "",
    orderId: orderId + "",
  };
  const { data, error } = useQuery(
    "instant-esim" + orderId,
    async () => {
      const response = await ApiService.instantEsim(payload);
      return response.data;
    },
    {
      refetchInterval: 10000, // Poll every 10 seconds
      enabled: !isEsimInstantFailed && !isAllEsimFetched,
    }
  );

  useEffect(() => {
    setOrder(data?.data?.order);
  }, [data]);

  useEffect(() => {
    if (
      error instanceof AxiosError &&
      error.response &&
      error.response?.status > 399
    ) {
      setIsEsimInstantFailed(true);
    }
  }, [error]);

  const planDescription = useCallback((plan: IPlan, type: string, t: any) => {
    const isUnlimited = plan.name === "unlimited";

    return (
      <Text className="text-sm font-bold">
        {isUnlimited ? (
          <span className="text-black">無制限</span>
        ) : (
          <span className="text-black">
            {plan.dataVolume}
            {plan.dataUnit}/{t("common:siminfo-card.perday.unit")}
          </span>
        )}{" "}
        -{" "}
        {t("common:siminfo-card.validity.unit", {
          count: plan.validityDays, // Get the usage plan days without unit
          defaultValue: "{{ count }} day",
          defaultValue_other: "{{ count }} days",
        })}
      </Text>
    );
  }, []);

  return (
    <>
      {isEsimInstantFailed ? (
        <Alert
          variant="light"
          color="red"
          title="eSIM 生成エラー"
          icon={<IconQrcode />}
        >
          eSIMの作成中に問題が発生しているようです。詳細については、
          <Link href="/support/contactus">お問い合わせ</Link>ください。
        </Alert>
      ) : (
        <>
          {order && (
            <Paper className="rounded-lg shadow-lg p-3">
              <Badge
                color="app-pink.4"
                variant="light"
                className=" animate__animated animate__fadeIn mb-2"
              >
                {planDescription(order?.plan, "regular", t)}
              </Badge>
              {order?.iccid && (
                <Text className="text-sm font-bold mb-2">
                  ICCID: {order.iccid}
                </Text>
              )}
              <Box className="relative h-80 w-80 mx-auto mb-2">
                {order?.qr ? (
                  <Image
                    src={order?.qr}
                    className="w-full h-full animate__animated animate__fadeIn"
                    alt="Image for QR code."
                  />
                ) : (
                  <>
                    <BackgroundImage
                      src={QRSamplePNG.src}
                      radius="sm"
                      className="w-full h-full"
                    />
                    <Overlay className="z-0" backgroundOpacity={0.6} blur={5} />
                    <Stack className="z-[200] px-4 absolute inset-0 items-center justify-center gap-2">
                      <MantineProvider
                        theme={{
                          components: {
                            Loader: Loader.extend({
                              defaultProps: {
                                loaders: {
                                  ...Loader.defaultLoaders,
                                  ring: RingLoader,
                                },
                                type: "ring",
                              },
                            }),
                          },
                        }}
                      >
                        <Loader color="white" size={60} />
                      </MantineProvider>
                      <Text className="text-white text-base font-bold text-center">
                        QR コードを生成しています。
                      </Text>
                      <Text className="text-white text-sm text-center">
                        少々時間がかかる場合があります。
                      </Text>
                    </Stack>
                  </>
                )}
              </Box>
            </Paper>
          )}
        </>
      )}
    </>
  );
};

export default InstantEsim;
