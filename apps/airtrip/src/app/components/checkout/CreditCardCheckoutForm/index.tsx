/*
 * import modules and libraries
 */
import { FormEvent, ForwardedRef, useCallback, useState } from "react";
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
} from "@stripe/react-stripe-js";
import {
  StripeCardCvcElementChangeEvent,
  StripeCardExpiryElementChangeEvent,
} from "@stripe/stripe-js";

/*
 * import components
 */
import { SimpleGrid } from "@mantine/core";

/*
 * import styles
 */
import styles from "./creditCardCheckoutForm.module.css";

type FormProps = {
  formRef: ForwardedRef<HTMLFormElement>;
  clientSecret?: string | undefined;
  planId?: string | undefined;
  message?: string;
  isCheckoutDisabled: {[key: string]: boolean;};
  setIsCheckoutDisabled: any;
  handlePaymentMethod: (e: any) => void;
  isFREEeSIM?: boolean;
};

const baseStyle = {
  color: "#32325d",
  fontSmoothing: "antialiased",
  fontSize: "17px",
  "::placeholder": {
    color: "#aab7c4",
  },
};
const CreditCardCheckoutForm: React.FC<FormProps> = ({
  message,
  isCheckoutDisabled,
  setIsCheckoutDisabled,
  handlePaymentMethod,
  ...props
}) => {

  const allCardStatus = Object.values(isCheckoutDisabled).every(
    (value) => value === true
  );

  const onSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    event.stopPropagation();
    if (!props.isFREEeSIM) {
      if (!allCardStatus) {
        return false;
      }
    }
    handlePaymentMethod(event);
  };

  const handleElementChange = useCallback(
    (input: "number" | "cvc" | "expiry") => {
      return (
        e:
          | StripeCardCvcElementChangeEvent
          | StripeCardExpiryElementChangeEvent
          | any
      ) => {
        setIsCheckoutDisabled((isCheckoutDisabled :{[key: string]: boolean}) => ({
          ...isCheckoutDisabled,
          [input]: e.complete,
        }));
      };
    },
    []
  );
  return (
    <>
      <form ref={props.formRef} onSubmit={onSubmit}>
        <div>
          <div className={styles["card-input-container"]}>
            <CardNumberElement
              onChange={handleElementChange("number")}
              options={{
                showIcon: true,
                style: {
                  base: baseStyle,
                },
              }}
            />
          </div>
          <SimpleGrid cols={2}>
            <div className={styles["card-input-container"]}>
              <CardExpiryElement
                onChange={handleElementChange("expiry")}
                options={{
                  style: {
                    base: baseStyle,
                  },
                }}
              />
            </div>
            <div className={styles["card-input-container"]}>
              <CardCvcElement
                onChange={handleElementChange("cvc")}
                options={{
                  style: {
                    base: baseStyle,
                  },
                }}
              />
            </div>
          </SimpleGrid>
        </div>
      </form>
    </>
  );
};

export default CreditCardCheckoutForm;
