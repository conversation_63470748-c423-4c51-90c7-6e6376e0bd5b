/*
 * import components
 */
import {
  Box,
  Divider,
  Flex,
  Image,
  List,
  Paper,
  Text,
  ThemeIcon,
  Title,
} from "@mantine/core";
/*
 * import modules and libraries
 */
import React from "react";
import { Trans, useTranslation } from "react-i18next";
/*
 * import interfaces
 */
import { IPlan } from "@/interfaces/IPlan";
import type { IDiscount } from "@repo/ui/src/interfaces/ICoupon";
/*
 * import constants and utils
 */
import { countryAlias, getCDNUrl } from "@/utils";
import { Currency } from "@/utils/currency";
import { NetworkGenerationType } from "@repo/ui/src/constants/plans";

const PlanConfirmationCard = (props: { plan: IPlan; discount?: IDiscount }) => {
  const { t } = useTranslation();
  const selectedCode = React.useMemo(() => Currency.getSelectedCurrency(), []);
  const is5GPlan =
    props?.plan?.network?.networkGeneration === NetworkGenerationType.FIVE_G;

  return (
    <Box>
      <Paper
        pos={"relative"}
        p={"2rem"}
        bg={"linear-gradient(324.8deg, #082699 0%, #2B3D82 100%)"}
      >
        <Flex direction={"column"} gap={"sm"} c="white">
          <Flex>
            <Flex direction={"column"} w="100%">
              <Text c="white">{props.plan.planProvider} eSIM</Text>
              <Title className="capitalize" order={4} c="white">
                <Trans
                  i18nKey={`countries:${props.plan.country?.name
                    ?.toLocaleLowerCase()
                    .replaceAll(" ", "")}`}
                >
                  {/* @ts-ignore */}
                  {countryAlias(props.plan.country?.name || props.plan.country)}
                </Trans>
              </Title>
            </Flex>
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <Flex w="100%" justify={"flex-start"} align={"center"}>
              <ThemeIcon color={"transparent"} size={"sm"} radius="xl" mr={5}>
                <Image src={getCDNUrl(`/assets/data.png`)} />
              </ThemeIcon>
              <Flex w="100%" justify={"space-between"} align={"center"}>
                <Text c="white" fw={"600"}>
                  {t("region:data")}
                </Text>
                {is5GPlan ? (
                  <Text c="white" fw={"600"}>
                    無制限 5G
                  </Text>
                ) : props.plan?.name?.toLowerCase() === "unlimited" ? (
                  <Text c="white" fw={"600"}>
                    無制限
                  </Text>
                ) : (
                  <Text c="white" fw={"600"}>
                    {props.plan.dataVolume}
                    {props.plan.dataUnit}
                  </Text>
                )}
              </Flex>
            </Flex>
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <Flex w="100%" justify={"flex-start"} align={"center"}>
              <ThemeIcon color={"transparent"} size={18} radius="xl" mr={5}>
                <Image src={getCDNUrl(`/assets/validity.png`)} />
              </ThemeIcon>
              <Flex w="100%" justify={"space-between"} align={"center"}>
                <Text c="white" fw={"600"}>
                  {t("region:validity")}
                </Text>
                <Text c="white" fw={"600"}>
                  {t("common:siminfo-card.validity.unit", {
                    count: props.plan.validityDays, // Get the usage plan days without unit
                    defaultValue: "{{ count }} day",
                    defaultValue_other: "{{ count }} days",
                  })}
                </Text>
              </Flex>
            </Flex>
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <Flex w="100%" justify={"flex-start"} align={"center"}>
              <Flex w="100%" justify={"space-between"} align={"center"}>
                <Flex justify={"center"} align={"center"} gap={5}>
                  <Text c="white" fw={"600"}>
                    {/* @ts-ignore */}
                    {props.plan.country?.name === "japan" ||
                    //@ts-expect-error
                    props.plan.country === "japan"
                      ? t("payment:price.label")
                      : t("payment:price.labelwithouttax")}
                  </Text>
                </Flex>
                <Flex direction={"column"} align={"end"}>
                  <Text className="font-semibold text-white">
                    {selectedCode?.code}
                    <span
                      className={
                        props.discount?.discount
                          ? "font-normal text-gray-400 mx-1 line-through text-sm"
                          : ""
                      }
                    >
                      {Currency.formatToSelected({
                        xe: props.plan.xe,
                        price: +props.plan.price,
                      })}
                    </span>
                    {props.discount?.discount && (
                      <>
                        <span>
                          {Currency.formatToSelected({
                            xe: props.discount.xe,
                            price: +props.discount.total,
                          })}
                        </span>
                      </>
                    )}
                  </Text>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
          <Divider color="#3E53A3" />
          <Flex>
            <List c="white" size={"xs"}>
              <List.Item>
                <Trans
                  i18nKey="payment:descriptions2"
                  components={{
                    b: <span className="font-bold"></span>,
                  }}
                />
              </List.Item>
            </List>
          </Flex>
        </Flex>
      </Paper>
    </Box>
  );
};
export default PlanConfirmationCard;
