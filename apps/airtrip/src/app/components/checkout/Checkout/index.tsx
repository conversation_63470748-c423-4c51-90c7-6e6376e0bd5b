"use client";

import dynamic from "next/dynamic";
import Image from "next/image";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import {
  Box,
  Button,
  Center,
  LoadingOverlay,
  Stack,
  Title,
} from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import { useDebouncedValue, useInterval } from "@mantine/hooks";

import { Elements, PaymentRequestButtonElement } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import isEmpty from "lodash/isEmpty";
import { GoogleReCaptcha } from "react-google-recaptcha-v3";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

import SectionContent from "@repo/ui/src/common/SectionContent";

import PaymentCheckoutBox from "@/app/components/checkout/PaymentCheckoutBox";
import PlanConfirmationCard from "@/app/components/checkout/PlanConfirmationCard";
import StickyContainer from "@/app/components/common/StickyContainer";

import { getCDNUrl } from "@/utils";
import { AGENT } from "@/utils/agent-handler";

import { ApiService } from "@/api";

import { ERR_GENERIC_REQUIRED, PURCHASE_STEPS } from "@/app/constants";
import { z } from "@/i18n/ja-zod";
import { useMessageStore } from "@/store/MessageStore";
import { useProfile } from "@/store/UserStore";

const DynamicPurchaseFlowStepper = dynamic(
  () => import("@repo/ui/src/PurchaseFlowStepper")
);

const DynamicUserInfo = dynamic(
  () => import("@/app/components/checkout/UserInfo")
);

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_AIRTRIP_ESIM_PUBLISHABLE_KEY as string
);

const schema = z.object({
  email: z.string().email("メールアドレスが無効です"),
  firstName: z
    .string()
    .min(1, ERR_GENERIC_REQUIRED)
    .regex(/^[a-zA-Z. ]*$/, "ローマ字で記入してください"),
  lastName: z
    .string()
    .min(1, ERR_GENERIC_REQUIRED)
    .regex(/^[a-zA-Z. ]*$/, "ローマ字で記入してください"),
});

const currentStep = 1;

const Checkout = () => {
  const { planId } = useParams();
  const { profile } = useProfile();
  const searchParams = useSearchParams();
  const agent =
    searchParams.has("via") && searchParams.get("via")
      ? searchParams.get("via")
      : null;
  const formRef = useRef<HTMLFormElement>(null);
  const [recaptcha, setRecaptcha] = useState<string | null>("");
  const [captchaResetKey, setCaptchaResetKey] = useState(Date.now());
  const [paymentMethod, setPaymentMethod] = useState("credit");
  const [paymentRequest, setPaymentRequest] = useState();
  const [isErrorOnAgreement, setIsErrorOnAgreement] = useState(false);
  const [currentStepLGU, setCurrentStepLGU] = useState(0);
  const [isCheckoutDisabled, setIsCheckoutDisabled] = useState({
    number: false,
    expiry: false,
    cvc: false,
  });
  const planIdParam = useMemo(() => planId, [planId]);
  const toggleLoading = useMessageStore((state) => state.toggleLoading);
  const isLoading = useMessageStore((state) => state.isLoading);
  const router = useRouter();

  const { data, isLoading: isPlanLoading } = useQuery(
    "plan" + planIdParam,
    () => ApiService.getPlanById(planIdParam as string),
    {
      enabled: !!planIdParam,
    }
  );
  const discountKey = useDebouncedValue("discount" + planIdParam + agent, 100);
  const discount = useQuery(
    discountKey,
    () =>
      ApiService.getQuotation({
        products: [
          {
            optionId: planIdParam as string,
          },
        ],
        couponId: agent,
      }),
    {
      enabled: !!planIdParam && agent !== AGENT.AIRTRIP_GMO,
    }
  )?.data?.data?.data;
  const isLGU = useMemo(
    () => data?.data.data.country?.name.toLowerCase() === "korea",
    [data]
  );
  const isNeedRegistration = useMemo(
    () =>
      ["taiwan", "hongkong"].includes(data?.data.data.country?.name as string),
    [data]
  );

  const form = useForm({
    initialValues: {
      email: "",
      firstName: "",
      lastName: "",
    },
    validate: zodResolver(schema),
    validateInputOnBlur: true,
  });

  const [isCompatibilityChecked, setIsCompatibilityChecked] =
    useState<boolean>(false);
  const [isTermsChecked, setIsTermsChecked] = useState<boolean>(false);
  const [isUserInfoValid, setIsUserInfoValid] = useState<boolean>(
    profile ? true : false
  );
  const [isCardDataValid, setIsCardDataValid] = useState<boolean>(
    Object.values(isCheckoutDisabled).every((value) => value === true)
      ? true
      : false
  );
  const [isNeedRegistrationChecked, setIsNeedRegistrationChecked] =
    useState<boolean>(false);

  const StickyPaymentButton = () => (
    <>
      {paymentMethod === "credit" && (
        <Box className="sticky bottom-0 w-full bg-white py-2">
          <Button
            loading={isLoading}
            size="lg"
            onClick={handleCheckout}
            disabled={
              !recaptcha?.length ||
              !isCompatibilityChecked ||
              !isTermsChecked ||
              !isUserInfoValid ||
              !isCardDataValid ||
              (isNeedRegistration && !isNeedRegistrationChecked)
            }
            fullWidth
            color="app-action.4"
            classNames={{
              root: "disabled:bg-gray-300",
            }}
          >
            {t("payment:pay-now")}
          </Button>
        </Box>
      )}
      {paymentRequest && paymentMethod !== "credit" && (
        <StickyContainer>
          <PaymentRequestButtonElement
            onClick={(e) => {
              if (
                !isCompatibilityChecked ||
                (isNeedRegistration && !isNeedRegistrationChecked)
              ) {
                setIsErrorOnAgreement(true);
                e.preventDefault();
                return;
              }
              sessionStorage.setItem(
                "isNeedRegistrationChecked",
                isNeedRegistrationChecked.toString()
              );
              setIsErrorOnAgreement(false);
            }}
            options={{
              paymentRequest,
              style: {
                paymentRequestButton: {
                  height: "50px", // same as "Pay Now" button
                },
              },
            }}
          />
        </StickyContainer>
      )}
    </>
  );

  const handleCheckout = useCallback(() => {
    if (!recaptcha) {
      alert(`Please complete reCAPTCHA before proceeding`);
      return;
    }

    formRef.current?.dispatchEvent(
      new Event("submit", {
        bubbles: true,
        cancelable: true,
      })
    );
  }, [formRef, recaptcha]);

  const onVerify = useCallback((token: string) => {
    setRecaptcha(token);
  }, []);

  const { t } = useTranslation();
  const isFREEeSIM = useMemo(() => {
    return discount?.total < 1;
  }, [discount]);

  const { data: setUpIntentQuery, isLoading: isSetupIntentLoading } = useQuery(
    "setupIntent",
    () => {
      return ApiService.getClientSecret({
        planId,
        source: "airtrip",
        userPool: "AIRTRIP",
      });
    },
    {
      enabled: !isFREEeSIM,
    }
  );

  useEffect(() => {
    toggleLoading(isSetupIntentLoading);
  }, [isSetupIntentLoading]);

  useEffect(() => {
    if (
      profile ||
      (isEmpty(form.errors) &&
        form.values.email &&
        form.values.firstName &&
        form.values.lastName)
    ) {
      setIsUserInfoValid(true);
    } else {
      setIsUserInfoValid(false);
    }
  }, [form, profile]);

  useEffect(() => {
    setIsCardDataValid(
      Object.values(isCheckoutDisabled).every((value) => value === true)
        ? true
        : false
    );
  }, [isCheckoutDisabled]);

  const pathname = usePathname();
  useEffect(() => {
    if (
      isLGU &&
      !profile?.email &&
      searchParams.get("via") === "KOREAFREECAMPAIGN"
    ) {
      router.replace("/campaign/airtrip/form?via=KOREAFREECAMPAIGN");
      sessionStorage.setItem("returnUrl", pathname);
    }
  }, [isLGU, pathname, profile]);

  const interval = useInterval(() => {
    setCaptchaResetKey(Date.now());
    return interval.stop;
  }, 30000);

  return (
    <>
      <LoadingOverlay
        visible={isSetupIntentLoading || isPlanLoading || discount?.isLoading}
      />
      <Center>
        <GoogleReCaptcha
          onVerify={onVerify}
          refreshReCaptcha={captchaResetKey}
        />
      </Center>
      <DynamicPurchaseFlowStepper
        currentStep={currentStep}
        steps={PURCHASE_STEPS}
      />
      <div className="mb-[10px]"></div>
      <SectionContent small noHeader isWhiteBg noFooter>
        <Stack className="gap-6 pt-6">
          {data?.data && !isPlanLoading && !isSetupIntentLoading && (
            <PlanConfirmationCard plan={data?.data.data} discount={discount} />
          )}
          {!profile && !isPlanLoading && <DynamicUserInfo form={form} />}

          {!isPlanLoading && (
            <Stack className="gap-4">
              {isFREEeSIM ? null : (
                <>
                  <Title order={4}>{t("payment:method.title")}</Title>
                  <Box>
                    <Image
                      height={23}
                      width={220}
                      src={getCDNUrl("/assets/payment.png")}
                      alt="payment image."
                    />
                  </Box>
                </>
              )}
              {setUpIntentQuery?.data?.data && (
                <Elements
                  stripe={stripePromise}
                  options={{
                    clientSecret: setUpIntentQuery?.data?.data.client_secret,
                    appearance: {
                      theme: "flat",
                      rules: {
                        ".Input": {
                          padding: "14px",
                          border: "1px solid #DDDDDD",
                          borderRadius: "4px",
                          backgroundColor: "white",
                        },
                        ".Input::placeholder": {
                          color: "#AAAAAA",
                          fontSize: "14px",
                          lineHeight: "21px",
                        },
                        ".Label": {
                          color: "#555555",
                          fontSize: "12px",
                          lineHeight: "18px",
                        },
                      },
                    },
                  }}
                >
                  <Box>
                    {planId && data?.data.data && (
                      <PaymentCheckoutBox
                        plan={data?.data.data}
                        isFREEeSIM={isFREEeSIM}
                        isAgreementError={isErrorOnAgreement}
                        clientSecret={
                          setUpIntentQuery?.data?.data.client_secret
                        }
                        setPaymentMethod={setPaymentMethod}
                        setPaymentRequest={setPaymentRequest}
                        price={
                          discount
                            ? discount["xe"]["JPY"]
                            : data?.data.data["xe"]["JPY"]
                        }
                        planId={planId as string}
                        ref={formRef}
                        // TODO: should move this to state management or refactor checkout components
                        isCompatibilityChecked={isCompatibilityChecked}
                        setIsCompatibilityChecked={setIsCompatibilityChecked}
                        isTermsChecked={isTermsChecked}
                        setIsTermsChecked={setIsTermsChecked}
                        isCheckoutDisabled={isCheckoutDisabled}
                        setIsCheckoutDisabled={setIsCheckoutDisabled}
                        isNeedRegistrationChecked={isNeedRegistrationChecked}
                        setIsNeedRegistrationChecked={
                          setIsNeedRegistrationChecked
                        }
                        //
                        isLGU={isLGU}
                        form={form}
                        isNeedRegistration={isNeedRegistration}
                        recaptcha={recaptcha ? recaptcha : undefined}
                        setCaptchaResetKey={setCaptchaResetKey}
                        agent={agent}
                      />
                    )}
                  </Box>
                  {!isFREEeSIM && planId && data?.data.data && (
                    <StickyPaymentButton />
                  )}
                </Elements>
              )}
              {isFREEeSIM && (
                <Box className="sticky bottom-0 w-full bg-white py-2">
                  <Button
                    loading={isLoading}
                    size="lg"
                    onClick={handleCheckout}
                    disabled={
                      !recaptcha?.length ||
                      !isCompatibilityChecked ||
                      !isTermsChecked ||
                      !isUserInfoValid ||
                      (isNeedRegistration && !isNeedRegistrationChecked)
                    }
                    fullWidth
                    color="app-action.4"
                    classNames={{
                      root: "disabled:bg-gray-300",
                    }}
                  >
                    eSIMをGET！
                  </Button>
                </Box>
              )}
            </Stack>
          )}
        </Stack>
      </SectionContent>
    </>
  );
};
export default Checkout;
