"use client";

/*
 * import modules and libraries
 */
import { Box } from "@mantine/core";
import { useTranslation } from "react-i18next";
/*
 * import components
 */
import WorkGuide from "@repo/ui/src/WorkGuide";
/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";

const HowItWorks = () => {
  const { t } = useTranslation();

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Box className="">
          <WorkGuide
            count="1"
            iconURl={getCDNUrl("/assets/check-compatibility.webp")}
            header={t("home:work-step1.title")}
            content={<></>}
            alwaysShowBg
          />
        </Box>
        <Box className="">
          <WorkGuide
            count="2"
            iconURl={getCDNUrl("/assets/get-your-esim.webp")}
            header={t("home:work-step2.title")}
            content={<></>}
            alwaysShowBg
          />
        </Box>
        <Box className="">
          <WorkGuide
            count="3"
            iconURl={getCDNUrl("/assets/install-your-esim.webp")}
            header={t("home:work-step3.title")}
            content={<></>}
            alwaysShowBg
          />
        </Box>
        <Box className="">
          <WorkGuide
            count="4"
            iconURl={getCDNUrl("/assets/install-your-esim.webp")}
            header={`
              注文完了メールでeSIMを受領！<br/>
              <span class="text-primary">同じアカウントログインで<br/>
              アプリでも確認可能！</span>
            `}
            content={<></>}
            alwaysShowBg
          />
        </Box>
      </div>
    </>
  );
};
export default HowItWorks;
