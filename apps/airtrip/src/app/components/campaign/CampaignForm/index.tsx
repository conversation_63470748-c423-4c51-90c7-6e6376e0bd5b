"use client";

import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";

import { Button, Container, Image, Stack, Stepper, Text } from "@mantine/core";
import { useDebouncedState } from "@mantine/hooks";

import { useTranslation } from "react-i18next";

import { useAdmaneScript } from "@repo/ui/src/hooks/useAdmaneScript";

import CodeVerificationForm from "@/app/components/CodeVerificationForm";
import SignupForm from "@/app/components/auth/SignupFormContainer";
import AlertMessage from "@/app/components/common/AlertMessage";

import { ApiService } from "@/api";

import useLogin from "@/hooks/useLogin";

const CompletePage = ({
  via,
  campaignCode,
  orderId,
}: {
  via: string | null;
  campaignCode?: string | undefined;
  orderId?: string;
}) => {
  const { t } = useTranslation();
  const { admaneHtmlScript, admaneScript } = useAdmaneScript({
    variant: "airtrip",
    agentCode: via || "",
    totalAmount: 0,
    orderId: orderId || "",
  });
  return (
    <>
      {admaneScript}
      {admaneHtmlScript}
      <Stack className="mt-6">
        <Text className="text-center font-bold">
          アカウント作成が完了しました。
        </Text>
        <Text className="text-center font-bold">
          マイページまたは、メールにて
          <br />
          無料eSIMをご確認ください。
        </Text>
        <Text className="text-primary text-center font-bold">
          ※マイページでeSIMが表示されない場合は <br />
          ページをリフレッシュしてください。
        </Text>
        <Button component={Link} href={"/app"} color="app-dark" w={"100%"}>
          マイページへ
        </Button>
        <Button
          component={Link}
          href={`/`}
          variant="outline"
          color="app-dark"
          className="bg-white"
          w={"100%"}
        >
          {t("region:gotohome", "GO TO HOME")}
        </Button>
      </Stack>
    </>
  );
};

export default function CampaignForm({ campaignId }: { campaignId?: string }) {
  const { login } = useLogin();
  const router = useRouter();
  const searchParams = useSearchParams();
  const via =
    typeof sessionStorage !== "undefined" && sessionStorage?.getItem("agent")
      ? sessionStorage?.getItem("agent")
      : null;

  const [active, setActive] = useState(
    searchParams.get("step")
      ? searchParams.get("step")
      : searchParams.get("success") === "true"
        ? 2
        : 0
  );
  const [campaignCode, setCampaignCode] = useState<string | undefined>();
  const [orderId, setOrderId] = useState<string | undefined>(
    searchParams.get("order_id") as string
  );

  useEffect(() => {
    if (searchParams.get("order_id")) {
      setOrderId(searchParams.get("order_id") as string);
    }
  }, [searchParams]);

  const [username, setUsername] = useState<string | undefined>();
  const [phoneNumber, setPhoneNumber] = useState<string | undefined>(
    searchParams.get("phone_number") as string
  );
  const [email, setEmail] = useState<string | undefined>(
    searchParams.get("email") as string
  );
  const [loadingSubmitCode, setLoadingSubmitCode] = useState<boolean>(false);
  const [loadingSendCode, setLoadingSendCode] = useState<boolean>(false);
  const [password, setPassword] = useState<string | undefined>();
  const [showCompleted, setShowCompleted] = useDebouncedState(false, 1000);
  const handleRegisterSuccess = useCallback(
    (registerPayload: {
      username?: string;
      phoneNumber: string;
      password: string;
      email: string;
    }) => {
      setActive(1);
      setPassword(registerPayload.password as string);
      setUsername(registerPayload.username);
      setPhoneNumber(registerPayload.phoneNumber);
      setEmail(registerPayload.email);
    },
    []
  );

  const shouldShowOTP = useMemo(() => {
    return (
      (searchParams.get("step") as string) &&
      searchParams.get("email") &&
      searchParams.get("phone_number")
    );
  }, []);

  const handleCodeVerification = useCallback(
    async (code: string) => {
      setLoadingSubmitCode(true);
      try {
        let email = searchParams.get("email");
        if (email) {
          email = email.replaceAll(" ", "+");
        }
        const response = await ApiService.verifyConfirmationCode(
          username as string,
          code,
          email,
          {
            campaign: via || "airtrip",
          }
        );

        login({
          email: email as string,
          password: password as string,
        });
        setCampaignCode(response.data.data.campaignCode);
        if (response.data.data?.type === "REDIRECT") {
          router.replace(
            `/campaign${
              campaignId ? `/${campaignId}` : ""
            }/form?success=true&code=${
              response.data.data.campaignCode
            }&redirect=` +
              `/region/korea?via=` +
              response.data.data.campaignCode +
              `${via ? `&via=${via}` : ""}`
          );
        } else {
          router.replace(
            `/campaign${campaignId ? `/${campaignId}` : ""}/form?success=true${via ? `&via=${via}` : ""}`
          );
          setActive(2);
        }
      } catch (err) {
        console.log(err);
        alert("コードを検証できません。");
      }
      setLoadingSubmitCode(false);
    },
    [username, searchParams]
  );

  const handleResendVerificationCode = useCallback(async () => {
    setLoadingSendCode(true);
    try {
      if (phoneNumber && email) {
        await ApiService.resendPhoneVerificationCode(email, +phoneNumber);
      } else {
        alert("確認メールを再送信できませんでした。");
      }
    } catch (err) {
      alert("確認メールを再送信できませんでした。");
    }
    setLoadingSendCode(false);
  }, [phoneNumber, email]);

  useEffect(() => {
    if (searchParams.has("success") && searchParams.has("redirect")) {
      setTimeout(() => {
        const returnUrl = sessionStorage.getItem("returnUrl");
        router.replace(
          returnUrl
            ? returnUrl + "?via=" + searchParams.get("code")
            : searchParams.get("redirect") || "/"
        );
      }, 1000);
    }
  }, [searchParams]);

  useEffect(() => {
    setShowCompleted(searchParams.has("redirect") ? false : true);
  }, [searchParams]);

  return (
    <>
      <Container size={700} className="py-12">
        {via && via === "KOREAFREECAMPAIGN" && (
          <Image
            src="https://esim.airtrip.jp/assets/campaign/free-esim-campaign.webp"
            className="-mt-5 mb-5"
          />
        )}
        <Stepper active={+(active || 0)} onStepClick={setActive} size="xs">
          <Stepper.Step label="アカウント作成" disabled>
            <AlertMessage />
            <SignupForm
              defaultTab="signup"
              enablePhone
              onSuccess={handleRegisterSuccess}
              hideSocialLogin
            />
          </Stepper.Step>
          <Stepper.Step label="内容確認" disabled>
            <CodeVerificationForm
              onSuccess={handleCodeVerification}
              onResendCode={handleResendVerificationCode}
              loadingSendCode={loadingSendCode}
              loadingSubmitCode={loadingSubmitCode}
            />
          </Stepper.Step>
          <Stepper.Step label="購入" disabled>
            {showCompleted ? (
              <CompletePage via={via} orderId={orderId || campaignCode} />
            ) : (
              "数秒後にキャンペーン専用ページにリダイレクトされます"
            )}
          </Stepper.Step>
        </Stepper>
      </Container>
    </>
  );
}
