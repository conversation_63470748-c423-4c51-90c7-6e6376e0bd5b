"use server";

/*
 * import modules and libraries
 */
import { ReactNode } from "react";

import { Stack, Text } from "@mantine/core";

import { IconWifi } from "@tabler/icons-react";

/*
 * import styles
 */
import styles from "./hero.module.css";

export default async function Hero({
  campaignPeriodText,
  campaignGetButton,
}: {
  campaignPeriodText: string;
  campaignGetButton: ReactNode;
}) {
  return (
    <>
      <Stack
        className={`${styles["hero-bg"]} bg-primary mx-auto items-center justify-center gap-0 px-4 py-8`}
      >
        <IconWifi className="text-white" size={100} stroke={2.5} />
        <Stack className="items-center justify-center">
          <Text
            component="h1"
            className="xxs:text-4xl text-center text-3xl font-black text-white md:text-6xl"
          >
            無料データプレゼント
            <br />
            キャンペーン
          </Text>
          <Text className="flex items-center text-base font-black text-white md:text-xl">
            {campaignPeriodText}
          </Text>
          {campaignGetButton}
        </Stack>
      </Stack>
    </>
  );
}
