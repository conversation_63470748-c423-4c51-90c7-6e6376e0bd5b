import {
  em,
  <PERSON>,
  Button,
  Center,
  Group,
  PinInput,
  Stack,
  Text,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useCallback, useEffect, useState } from "react";
import { useInterval } from "@mantine/hooks";

const CodeVerificationForm = (props: {
  loadingSendCode: boolean;
  loadingSubmitCode: boolean;
  onResendCode: () => void;
  onSuccess: (code: string) => void;
}) => {
  const [code, setCode] = useState("");
  const [seconds, setSeconds] = useState(0);
  const interval = useInterval(() => setSeconds((s) => s + 1), 1000);
    // TODO: can be move to utils
  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });

  const handleCodeSubmit = useCallback(
    (event: any) => {
      event.preventDefault();
      props.onSuccess(code);
    },
    [code]
  );

  useEffect(() => {
    interval.start();
    return interval.stop;
  }, []);

  const countDown = 60 * 1 - seconds;

  useEffect(() => {
    if (countDown < 1) {
      interval.toggle();
    }
  }, [countDown]);

  const handleResendCode = useCallback(async () => {
    try {
      interval.toggle();
      setSeconds(0);
      props.onResendCode();
    } catch (err) {
      //@ts-expect-error
      alert(err.message);
    }
  }, [interval]);

  return (
    <form onSubmit={handleCodeSubmit}>
      <Stack>
        <Group justify="space-between" className="gap-0">
          SMSで受信したコードを入力してください。
          <Box>
            {countDown > 0 ? (
              <Text p={"sm"}>
                00:{countDown < 9 ? `0${countDown}` : countDown}
              </Text>
            ) : (
              <Button
                variant="transparent"
                className="p-0"
                loading={props.loadingSendCode || props.loadingSubmitCode}
                onClick={handleResendCode}
                
              >
                コードを再送する
              </Button>
            )}
          </Box>
        </Group>
        <Center>
          <PinInput
            value={code}
            type="number"
            onChange={(e: any) => setCode(e)}
            size={isMobileSize ? "md" : "xl"}
            length={6}
          />
        </Center>
        <Button
          className="rounded-[15px] text-2xl mt-4"
          size="md"
          w={"100%"}
          type="submit"
          color="app-action.4"
          loading={props.loadingSubmitCode}
        >
          確認する
        </Button>
      </Stack>
    </form>
  );
};
export default CodeVerificationForm;
