"use client";

import Link from "next/link";

import {
  Accordion,
  AccordionControl,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Stack,
  Text,
  Title,
} from "@mantine/core";

import { IconLetterQ } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import styles from "./homefaq.module.css";

const HomeFAQ = ({ country }: { country?: string }) => {
  const { t } = useTranslation();

  const region = country ? t(`countries:${country}`) : "";

  return (
    <>
      <Box>
        <Accordion
          variant="separated"
          classNames={{
            control:
              "bg-white rounded-md border-0 ease-in-out duration-300 md:hover:scale-105",
            item: "bg-transparent border-0",
            content: "text-sm md:p-4",
          }}
        >
          <AccordionItem value="item1">
            <AccordionControl
              className="md:px-14"
              icon={
                <Box className="bg-primary rounded-full p-1">
                  <IconLetterQ color="white" stroke={3} />
                </Box>
              }
            >
              <Title order={3} className="text-sm font-normal md:text-lg">
                {t("faq:home.item1.q", { region })}
              </Title>
            </AccordionControl>
            <AccordionPanel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item1.a"),
                }}
              />
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem value="item2">
            <AccordionControl
              className="md:px-14"
              icon={
                <Box className="bg-primary rounded-full p-1">
                  <IconLetterQ color="white" stroke={3} />
                </Box>
              }
            >
              <Title order={3} className="text-sm font-normal md:text-lg">
                {t("faq:home.item2.q", { region })}
              </Title>
            </AccordionControl>
            <AccordionPanel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item2.a", { region }),
                }}
              />
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem value="item3">
            <AccordionControl
              className="md:px-14"
              icon={
                <Box className="bg-primary rounded-full p-1">
                  <IconLetterQ color="white" stroke={3} />
                </Box>
              }
            >
              <Title order={3} className="text-sm font-normal md:text-lg">
                {t("faq:home.item3.q")}
              </Title>
            </AccordionControl>
            <AccordionPanel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item3.a") as string,
                }}
              />

              <Box className="mt-4">
                <Text
                  component="p"
                  dangerouslySetInnerHTML={{
                    __html: t("faq:home.item3.list.title") as string,
                  }}
                />
                <Text component="div">
                  <ul
                    className={styles["list"]}
                    dangerouslySetInnerHTML={{
                      __html: t("faq:home.item3.list.items") as string,
                    }}
                  />
                </Text>
              </Box>

              <Box className="mt-4">
                <Text
                  component="p"
                  dangerouslySetInnerHTML={{
                    __html: t("faq:home.item3.manual.title") as string,
                  }}
                />
                <Text component="div">
                  <ul
                    className={styles["list"]}
                    dangerouslySetInnerHTML={{
                      __html: t("faq:home.item3.manual.items") as string,
                    }}
                  />
                </Text>
              </Box>
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem value="item4">
            <AccordionControl
              className="md:px-14"
              icon={
                <Box className="bg-primary rounded-full p-1">
                  <IconLetterQ color="white" stroke={3} />
                </Box>
              }
            >
              <Title order={3} className="text-sm font-normal md:text-lg">
                {t("faq:home.item4.q")}
              </Title>
            </AccordionControl>
            <AccordionPanel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item4.a"),
                }}
              />
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem value="item5">
            <AccordionControl
              className="md:px-14"
              icon={
                <Box className="bg-primary rounded-full p-1">
                  <IconLetterQ color="white" stroke={3} />
                </Box>
              }
            >
              <Title order={3} className="text-sm font-normal md:text-lg">
                {t("faq:home.item5.q", { region })}
              </Title>
            </AccordionControl>
            <AccordionPanel>
              <Text
                component="p"
                dangerouslySetInnerHTML={{
                  __html: t("faq:home.item5.a"),
                }}
              />
            </AccordionPanel>
          </AccordionItem>
        </Accordion>
        <Stack align="center">
          <Button
            size="lg"
            radius="md"
            variant="outline"
            color="app-pink.4"
            bg="white"
            component={Link}
            href={"/faq"}
            target="_blank"
            className="mt-8 md:w-[500px]"
          >
            <Text className="font-bold text-black">{t("faq:viewmore")}</Text>
          </Button>
        </Stack>
      </Box>
    </>
  );
};
export default HomeFAQ;
