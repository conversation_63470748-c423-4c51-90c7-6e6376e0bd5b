"use client";
/**
 * 
 * Componet removed from all pages - 2024.11.17
 * 
*/



/*
 * import modules and libraries
 */
import {
  Stack,
  Title,
  Image,
  Text,
  List,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconX } from "@tabler/icons-react";
/*
 * import modules and libraries
 */
import CTAButton from "@/app/components/common/CTAButton";
/*
 * import modules and libraries
 */
import { getCDNUrl } from "@/utils";

/**
 * @deprecated Componet removed from all pages, but can be used if necessarily - 2024.11.17
*/
export default function Issues() {
  const { t } = useTranslation();
  const items = [
    {
      content: t("home:issues.content.one"),
    },
    {
      content: t("home:issues.content.two"),
    },
    {
      content: t("home:issues.content.three"),
    },
  ];
  return (
    <Stack>
      <Stack className="bg-secondary p-4" align="center">
        <Title
          order={2}
          className="text-primary text-center text-2xl md:text-3xl"
        >
          {t("home:issues.content.title")}
        </Title>
        <Image
          className="w-[300px] md:w-[800px]"
          src={getCDNUrl("/assets/gm-issues.webp")}
          alt="レンタルWifiやSIMカードのデメリット"
          loading="lazy"
        />
        <List className="text-center">
          {items.map((item, index) => (
            <List.Item
              classNames={{
                itemIcon: "mr-1",
              }}
              key={index}
              icon={
                // <Image
                //   className="w-[25px] h-[25px] md:w-[36px] md:h-[36px]"
                //   src={getCDNUrl("/assets/x-icon.svg")}
                //   alt="eSIMのメリット"
                // />
                <IconX className="text-primary w-[25px] h-[25px] md:w-[36px] md:h-[36px]" />
              }
            >
              <Title className="text-base" order={4}>
                {item.content}
              </Title>
            </List.Item>
          ))}
        </List>
      </Stack>
      <Text
        dangerouslySetInnerHTML={{
          __html: t("home:issues.content.description")
        }}
        className="self-center text-center py-2"
        component="p"
      />
      <CTAButton />
    </Stack>
  );
}
