"use client";

/*
 * import components
 */
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";

import {
  CloseButton,
  Combobox,
  ComboboxDropdown,
  ComboboxEmpty,
  ComboboxOption,
  ComboboxOptions,
  ComboboxTarget,
  ScrollAreaAutosize,
  Stack,
  TextInput,
  useCombobox,
} from "@mantine/core";

import { IconSearch, IconTrash } from "@tabler/icons-react";

import CountrySelectorTabs from "@repo/ui/src/CountrySelectorTabs";
/*
 * import constants and utils
 */
import { COUNTRIES } from "@repo/ui/src/constants/regions";

/*
 * import modules and libraries
 */
import { getCDNUrl } from "@/utils";

/*
 * import interfaces
 */
import { IPlan } from "@/interfaces/IPlan";

export default function CountrySelector({
  data,
}: {
  data: {
    name: string;
    code?: string;
    plans: IPlan[];
  }[];
}) {
  const [search, setSearch] = useState("");
  const [searchCountry, setSearchCountry] = useState("");
  const router = useRouter();

  const combobox = useCombobox({
    onDropdownClose: () => {
      combobox.resetSelectedOption();
    },
  });

  const shouldFilterOptions = !COUNTRIES.some(
    (item) =>
      item.value === searchCountry.toLowerCase() &&
      item.jp === search.toLowerCase()
  );
  const filteredOptions = shouldFilterOptions
    ? COUNTRIES.filter(
        (item) =>
          item.jp.includes(search.trim().replaceAll(" ", "").toLowerCase()) ||
          item.value.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          ) ||
          item.hiragana.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          ) ||
          item.katakana.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          )
      )
    : COUNTRIES;

  const options = useMemo(
    () =>
      filteredOptions.map((item) => (
        <ComboboxOption
          value={item.value}
          key={item.value}
          className="flex items-center gap-2 border-t border-slate-500 py-4 text-center last:mb-10 last:border-b-0 hover:text-black md:py-2 md:text-left last:md:mb-0"
        >
          <Image
            src={getCDNUrl(`/assets/flags/svg/${item.code.toLowerCase()}.svg`)}
            width={24}
            height={24}
            alt={item.code}
          />
          {item.jp}
        </ComboboxOption>
      )),
    [filteredOptions]
  );

  return (
    <Stack className="gap-2" align="center">
      <Combobox
        middlewares={{ flip: false }}
        store={combobox}
        withinPortal={true}
        position="bottom"
        onOptionSubmit={(val) => {
          setSearchCountry(val);
          setSearch(COUNTRIES.find((item) => item.value === val)?.jp || "");
          combobox.closeDropdown();

          try {
            const textField = document.getElementById("country-input");
            setTimeout(() => {
              textField?.blur();
              if (val) router.push("/region/" + val);
            }, 1000);
          } catch (err) {
            console.log(err);
          }
        }}
      >
        <ComboboxTarget>
          <TextInput
            id="country-input"
            size="lg"
            label="利用する国や地域を選んでeSIMをかんたん購入！"
            placeholder="入力・選択してください"
            className="flex-1"
            leftSection={<IconSearch className="text-primary" />}
            rightSection={
              search !== "" && (
                <CloseButton
                  className="text-primary"
                  size="sm"
                  onMouseDown={(event) => event.preventDefault()}
                  onClick={() => setSearch("")}
                  aria-label="Clear value"
                  icon={<IconTrash />}
                />
              )
            }
            value={search}
            onChange={(event: any) => {
              combobox.openDropdown();
              combobox.updateSelectedOptionIndex();
              setSearch(event.currentTarget.value);
            }}
            classNames={{
              root: "w-full max-w-[800px] text-center",
              label: "text-sm md:text-xl font-bold mb-2",
            }}
            onClick={() => combobox.openDropdown()}
            onFocus={() => combobox.openDropdown()}
            onBlur={() => {
              combobox.closeDropdown();
              if (searchCountry) {
                setSearch(
                  COUNTRIES.find((item) => item.value === searchCountry)?.jp ||
                    ""
                );
              }
            }}
          />
        </ComboboxTarget>
        <ComboboxDropdown
          hidden={search.trim().length === 0}
          className="z-10 border-0 bg-black bg-opacity-85 text-white shadow-lg"
        >
          {COUNTRIES.length !== 0 && (
            <ComboboxOptions>
              <ScrollAreaAutosize mah={200} type="scroll">
                {options.length > 0 ? (
                  options
                ) : (
                  <ComboboxEmpty>国のデータが見つかりません。</ComboboxEmpty>
                )}
              </ScrollAreaAutosize>
            </ComboboxOptions>
          )}
        </ComboboxDropdown>
      </Combobox>
      {/* @ts-ignore */}
      <CountrySelectorTabs data={data} />
    </Stack>
  );
}
