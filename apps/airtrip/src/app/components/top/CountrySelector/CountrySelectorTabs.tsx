/*
 * import interfaces
 */
import Image from "next/image";
import Link from "next/link";
import { useMemo, useState } from "react";

/*
 * import components
 */
import { Carousel } from "@mantine/carousel";
import {
  SimpleGrid,
  Skeleton,
  Tabs,
  TabsList,
  TabsPanel,
  TabsTab,
} from "@mantine/core";

import { TFunction } from "i18next";
import { Trans, useTranslation } from "react-i18next";

/*
 * import modules and libraries
 */
import { getCDNUrl, normalizeAndKebabCase } from "@/utils";

import { IPlan } from "@/interfaces/IPlan";

type TabData = {
  name: string;
  code?: string | undefined;
  plans: IPlan[];
}[];

const CountriesSkeleton = () => ({
  ...Array.from(Array(12).keys()).map((_, index) => (
    <Skeleton
      key={index}
      height={60}
      className="flex flex-nowrap items-center gap-2 text-sm text-black no-underline"
      radius="md"
    />
  )),
});

const PillTab = ({
  code,
  name,
  t,
}: {
  code: string | undefined;
  name: string;
  t: TFunction;
}) => (
  <TabsTab key={code || name} value={code || name} color="app-pink.4">
    {t(name)}
  </TabsTab>
);

const CountriesTabList = ({
  activeTab,
  countryTabData,
  regionalTabData,
  t,
}: {
  activeTab: string | null;
  countryTabData: TabData;
  regionalTabData: TabData;
  t: TFunction;
}) => (
  <TabsList>
    <Carousel
      dragFree
      withControls={false}
      align={"start"}
      classNames={{
        root: "w-full bg-gray-100 rounded-md p-1",
        container: "md:justify-center",
        viewport: "md:flex md:justify-center",
      }}
      slideSize="0%"
      slideGap={"xs"}
      controlsOffset={0}
      containScroll="trimSnaps"
    >
      <Carousel.Slide>
        <TabsTab value={"popular"} key={"popular"} color="app-pink.4">
          人気国
        </TabsTab>
      </Carousel.Slide>
      {activeTab === "countries"
        ? countryTabData.map((item, index) => (
            <Carousel.Slide key={index}>
              <PillTab code={item.code} name={item.name} t={t} />
            </Carousel.Slide>
          ))
        : regionalTabData.map((item, index) => (
            <Carousel.Slide key={index}>
              <PillTab code={item.code} name={item.name} t={t} />
            </Carousel.Slide>
          ))}
    </Carousel>
  </TabsList>
);

const CountriesTabPanel = ({
  regionalTabData,
  data,
  t,
  activeTab,
  activeSubTab,
  setActiveSubTab,
  countryTabData,
  popularData,
}: {
  regionalTabData: TabData;
  data: TabData;
  t: TFunction;
  activeTab: string | null;
  setActiveSubTab: ((value: string | null) => void) | undefined;
  activeSubTab: string | null;
  countryTabData: TabData;
  popularData: IPlan[];
}) => (
  <Tabs.Panel value="countries">
    <Tabs
      id="select-plan"
      w={"100%"}
      value={activeSubTab}
      onChange={setActiveSubTab}
      color="app-pink.4"
      variant="pills"
      classNames={{
        tab: "transition-colors duration-100 ease-in py-[10px] px-[16px] border-2 data-[active=true]: border-solid bg-gray-300 rounded-full data-[active=true]:bg-white data-[active=true]:border-primary border-gray-300 text-primary",
        root: "mt-2 rounded-md",
        tabLabel: "text-[13px] data-[selected=true]:text-primary",
      }}
    >
      <CountriesTabList
        activeTab={activeTab}
        countryTabData={countryTabData}
        regionalTabData={regionalTabData}
        t={t}
      />
      {activeSubTab === "popular" ? (
        <PopularTabsPanel popularData={popularData} />
      ) : !data ? (
        <SimpleGrid
          classNames={{
            root: "bg-white mt-6 grid-cols-2 md:grid-cols-4",
          }}
        >
          {...Array.from(Array(12).keys()).map(() => (
            <Skeleton
              height={60}
              className="flex flex-nowrap items-center gap-2 text-sm text-black no-underline"
              radius="md"
            />
          ))}
        </SimpleGrid>
      ) : (
        countryTabData.map((item) =>
          item.plans ? (
            <TabsPanel
              key={item.code + item.name}
              value={item.code || item.name}
            >
              <SimpleGrid
                classNames={{
                  root: "bg-white mt-6 grid-cols-2 md:grid-cols-4",
                }}
              >
                {item.plans?.map?.((plan, index) => (
                  <div
                    key={index}
                    className="inline-block transform rounded-lg bg-gray-100 p-4 shadow-md transition-transform duration-300 ease-in-out hover:scale-110 hover:shadow-lg"
                  >
                    <Link
                      className="flex flex-nowrap items-center gap-2 text-xs text-black no-underline"
                      href={`/region/${normalizeAndKebabCase(
                        plan.originalName || plan.country.name
                      )}`}
                    >
                      <Image
                        src={getCDNUrl(
                          `/assets/flags/svg/${plan.countryCode?.toLowerCase()}.svg`
                        )}
                        width={38}
                        height={25}
                        alt={plan.countryCode || ""}
                      />
                      <span>
                        <Trans
                          i18nKey={`countries:${
                            plan.originalName || plan.country.name
                          }`}
                        >
                          {plan.originalName || plan.country.name}
                        </Trans>
                      </span>
                    </Link>
                  </div>
                ))}
              </SimpleGrid>
            </TabsPanel>
          ) : null
        )
      )}
    </Tabs>
  </Tabs.Panel>
);

const RegionalTabsPanel = ({
  regionalTabData,
  data,
  t,
}: {
  regionalTabData: TabData;
  data: TabData;
  t: TFunction;
}) => (
  <Tabs.Panel value="regional">
    <Tabs
      id="select-plan"
      w={"100%"}
      value={"regional"}
      color="app-pink.4"
      variant="pills"
      classNames={{
        tab: "transition-colors duration-100 ease-in py-[10px] px-[16px] border-2 data-[active=true]: border-solid bg-gray-300 rounded-full data-[active=true]:bg-white data-[active=true]:border-primary border-gray-300 text-primary",
        root: "mt-2 rounded-md",
        tabLabel: "text-[13px] data-[selected=true]:text-primary",
      }}
    >
      <TabsList>
        <Carousel
          dragFree
          withControls={false}
          align={"start"}
          classNames={{
            root: "w-full bg-gray-100 rounded-md p-1",
            container: "md:justify-center",
            viewport: "md:flex md:justify-center",
          }}
          slideSize="0%"
          slideGap={"xs"}
          controlsOffset={0}
        >
          {regionalTabData.map((item, index) => (
            <PillTab
              code={item.code}
              name={item.name}
              t={t}
              key={index + item.name}
            />
          ))}
        </Carousel>
      </TabsList>
      {!data ? (
        <SimpleGrid
          classNames={{
            root: "bg-white mt-6 grid-cols-2 md:grid-cols-4",
          }}
        >
          <CountriesSkeleton />
        </SimpleGrid>
      ) : (
        regionalTabData.map((item) =>
          item.plans ? (
            <TabsPanel
              key={item.code + item.name}
              value={item.code || item.name}
            >
              <SimpleGrid
                classNames={{
                  root: "bg-white mt-6 grid-cols-2 md:grid-cols-4",
                }}
              >
                {item.plans?.map?.((plan, index) => (
                  <div
                    key={index}
                    className="inline-block transform rounded-lg bg-gray-100 p-4 shadow-md transition-transform duration-300 ease-in-out hover:scale-110 hover:shadow-lg"
                  >
                    <Link
                      className="flex flex-nowrap items-center gap-2 text-xs text-black no-underline"
                      href={`/region/${normalizeAndKebabCase(
                        plan.originalName || plan.country.name
                      )}`}
                    >
                      <Image
                        src={getCDNUrl(
                          `/assets/flags/svg/${plan.countryCode?.toLowerCase()}.svg`
                        )}
                        width={38}
                        height={25}
                        alt={plan.countryCode || ""}
                      />
                      <span>
                        <Trans
                          i18nKey={`countries:${
                            plan.originalName || plan.country.name
                          }`}
                        >
                          {plan.originalName || plan.country.name}
                        </Trans>
                      </span>
                    </Link>
                  </div>
                ))}
              </SimpleGrid>
            </TabsPanel>
          ) : null
        )
      )}
    </Tabs>
  </Tabs.Panel>
);

const PopularTabsPanel = ({ popularData }: { popularData: IPlan[] }) => (
  <TabsPanel value="popular">
    <SimpleGrid
      classNames={{
        root: "bg-white mt-6 grid-cols-2 md:grid-cols-4",
      }}
    >
      {!popularData ? (
        <>
          <CountriesSkeleton />
        </>
      ) : (
        popularData.map((item, index) => (
          <div
            className="inline-block transform rounded-lg bg-gray-100 p-4 shadow-md transition-transform duration-300 ease-in-out hover:scale-110 hover:shadow-lg"
            key={index}
          >
            <Link
              className="flex flex-nowrap items-center gap-2 text-sm text-black no-underline"
              href={`/region/${normalizeAndKebabCase(
                item.originalName || item.country.name
              )}`}
            >
              <Image
                src={getCDNUrl(
                  `/assets/flags/svg/${item.countryCode?.toLowerCase()}.svg`
                )}
                width={38}
                height={25}
                alt={item.originalName || item.country.name}
              />
              <span>
                <Trans
                  i18nKey={`countries:${
                    item.originalName || item.country.name
                  }`}
                >
                  {item.originalName || item.country.name}
                </Trans>
              </span>
            </Link>
          </div>
        ))
      )}
    </SimpleGrid>
  </TabsPanel>
);

export default function CountrySelectorTabs({ data }: { data: TabData }) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string | null>("countries");
  const [activeSubTab, setActiveSubTab] = useState<string | null>("popular");
  const TOP_COUNTRIES = [
    "KR",
    "JP",
    "US",
    "TW",
    "HI",
    "VN",
    "TH",
    "IT",
    "HK",
    "AU",
    "ES",
    "UK",
    "SG",
    "FR",
  ];

  const popularData = useMemo(
    () =>
      data.flatMap((item) =>
        item.plans.filter((plan) =>
          TOP_COUNTRIES.includes(plan.countryCode as string)
        )
      ),
    []
  );

  const countryTabData = useMemo(
    () => data.filter((item) => item.code !== "regional"),
    []
  );

  const regionalTabData = useMemo(
    () => data.filter((item) => item.code === "regional"),
    []
  );

  return (
    <Tabs
      variant="none"
      value={activeTab}
      onChange={setActiveTab}
      classNames={{
        panel: "w-full max-w-[900px]",
        root: "w-full flex flex-col md:items-center",
        list: "w-full bg-gray-300 rounded-md max-w-[800px]",
        tab: "my-1 rounded-md border-2 hover:bg-gray-300 border-gray-300 border-solid transition-colors duration-100 ease-in",
        tabLabel: "font-bold",
      }}
      defaultValue="countries"
    >
      <Tabs.List grow>
        <Tabs.Tab
          className="data-[active=true]:border-primary ml-1 data-[active=true]:bg-white"
          value="countries"
        >
          国・地域
        </Tabs.Tab>
        <Tabs.Tab
          className="data-[active=true]:border-primary mr-1 data-[active=true]:bg-white"
          value="regional"
        >
          複数国・周遊
        </Tabs.Tab>
      </Tabs.List>
      <CountriesTabPanel
        regionalTabData={regionalTabData}
        data={data}
        t={t}
        activeTab={activeTab}
        setActiveSubTab={setActiveSubTab}
        activeSubTab={activeSubTab}
        countryTabData={countryTabData}
        popularData={popularData}
      />
      <RegionalTabsPanel regionalTabData={regionalTabData} data={data} t={t} />
    </Tabs>
  );
}
