"use client";

/*
 * import modules and libraries
 */
import dynamic from "next/dynamic";
import Link from "next/link";

import { Button, Image, Stack, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";

import { Trans, useTranslation } from "react-i18next";

import SetupGuide from "@repo/ui/src/SetupGuide";
/*
 * import constants and helpers
 */
import WorkGuide from "@repo/ui/src/WorkGuide";

import { countryAlias, getCDNUrl, reverseKebabCase } from "@/utils";

import { useOrderStore } from "@/store";

import CTAButton from "../../common/CTAButton";

/*
 * import dynamic components
 */
const DynamicLGUCompatibility = dynamic(
  () => import("@repo/ui/src/modals/LGUCompatibility")
);

const HowItWorks = ({
  isLGU,
  customUrl,
  country,
}: {
  isLGU?: boolean;
  customUrl?: string;
  country?: string;
}) => {
  const { t } = useTranslation();
  const [lguModal, setLguModal] = useDisclosure();
  const toggleCompatibilityModalOpen: () => void = useOrderStore(
    (state) => state.toggleCompatibilityModalOpen
  );

  const region = country ? t(`countries:${country}`) : "";

  return (
    <>
      <Stack className="gap-8">
        <WorkGuide
          count="1"
          iconURl={getCDNUrl("/assets/check-compatibility.webp")}
          header={t("home:work-step1.title")}
          content={
            <>
              <Text
                className="text-sm md:text-base"
                dangerouslySetInnerHTML={{
                  __html: t("home:work-step1.details", {
                    region,
                  }),
                }}
              />
              <Button
                size="lg"
                radius="md"
                variant="outline"
                color="app-pink.4"
                bg="white"
                onClick={
                  isLGU ? setLguModal.open : toggleCompatibilityModalOpen
                }
                classNames={{
                  root: "max-w-sm w-full",
                }}
              >
                <Image w={32} src={getCDNUrl("/assets/gm-device.svg")} />
                <Text className="font-bold text-black" ml={10}>
                  eSIM対応機種はこちら
                </Text>
              </Button>
            </>
          }
        />
        <WorkGuide
          count="2"
          iconURl={getCDNUrl("/assets/get-your-esim.webp")}
          header={t("home:work-step2.title")}
          content={
            <Text
              className="text-sm md:text-base"
              dangerouslySetInnerHTML={{
                __html: t("home:work-step2.details", {
                  region,
                }),
              }}
            />
          }
        />
        <WorkGuide
          count="3"
          iconURl={getCDNUrl("/assets/install-your-esim.webp")}
          header={t("home:work-step3.title")}
          content={
            <>
              <Text
                className="text-sm md:text-base"
                dangerouslySetInnerHTML={{
                  __html: t("home:work-step3.details"),
                }}
              />
              <SetupGuide />
              <Text>
                手動の設定方法は
                <Link href="/setup">こちら</Link>
              </Text>
              <CTAButton
                noIcon
                customText="旅行先を探す"
                customUrl="/destination"
              />
            </>
          }
        />
      </Stack>
      {isLGU && (
        <DynamicLGUCompatibility
          size="lg"
          title="LGU⁺ eSIM対応機種について"
          opened={lguModal}
          onClose={setLguModal.close}
        ></DynamicLGUCompatibility>
      )}
    </>
  );
};
export default HowItWorks;
