'use client';

/*
 * import modules and libraries
 */
import { useTranslation } from "react-i18next";
import {
  Text,
  List,
  Stack,
  Image,
  Box,
  Button,
  Flex,
} from "@mantine/core";
import { IconCheck, IconDevices } from "@tabler/icons-react";
/*
 * import helpers
 */
import { getCDNUrl } from "@/utils";
import { useOrderStore } from "@/store";

const WhatIsEsim = () => {
  const { t } = useTranslation();
  const toggleCompatibilityModalOpen: () => void = useOrderStore((state) => (state.toggleCompatibilityModalOpen));
  const items = [
    {
      title: t("home:benefit.item1.row1"),
      content: t("home:benefit.item1.row2"),
      // image not yet decided
      // icon: <Image src={getCDNUrl("/assets/benefits/gm-mobile.svg")} />,
    },
    {
      title: t("home:benefit.item2.row1"),
      content: t("home:benefit.item2.row2"),
      // image not yet decided
      // icon: <Image src={getCDNUrl("/assets/benefits/gm-speed.svg")} />,
    },
    {
      title: t("home:benefit.item3.row1"),
      content: t("home:benefit.item3.row2"),
      // image not yet decided
      // icon: <Image src={getCDNUrl("/assets/benefits/gm-tap.svg")} />,
    },
  ];

  return (
    <Stack gap={0} align="center">
      <Flex className="flex-nowrap flex-col md:flex-row" justify="center">
        <Image
          alt="eSIMのメソッド"
          src={getCDNUrl("/assets/benefits/global-mobile.webp")}
          className="max-w-sm w-full"
          loading="lazy"
        />
        <Box className="md:px-4">
          <List>
            {items.map((item) => (
              <List.Item
                classNames={{
                  itemIcon: "self-start",
                }}
                key={item.title}
                className="mb-6"
                icon={<IconCheck strokeWidth={3} color={"#30BF01"} />}
              >
                <Stack className="gap-0">
                  <Text className="text-primary text-sm font-bold">
                    {item.title}
                  </Text>
                  <Text component="p">{item.content}</Text>
                </Stack>
              </List.Item>
            ))}
          </List>
          <Box className="text-center md:text-left">
            <Button
              className="max-w-sm w-full"
              size="lg"
              radius="md"
              variant="outline"
              color="app-pink.4"
              onClick={toggleCompatibilityModalOpen}
              bg="white"
            >
              <IconDevices className="text-primary" size={32} stroke={1.5} />
              <Text className="font-bold" c="black" ml={10}>
                eSIM対応機種はこちら
              </Text>
            </Button>
          </Box>
        </Box>
      </Flex>
    </Stack>
  );
};

export default WhatIsEsim;
