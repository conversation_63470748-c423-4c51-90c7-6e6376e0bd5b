.kv-banner {
  width: 100%;
  left: 50%;
  top: 55%;
  transform: translate(-50%, -50%);
}

.affiliate-fiit {
  border-radius: 100%;
  border: #fff 4px solid;
}

.affiliate-line {
  border-radius: 100%;
  border: #fff 4px solid;
}

@media (min-width: 1150px) {
  .kv-banner {
    width: 112% !important;
  }
}

@media (min-width: 850px) and (max-width: 1023px) {
  .kv-banner {
    width: 112% !important;
  }
}

@media (max-width: 1023px) {
  .like-icon {
    width: 60px;
    height: 60px;
  }
}

@media (min-width: 1024px) {
  .kv-bg {
    background: rgba(0,0,0,.7) 0% 0% no-repeat padding-box;
  }
}

@media (min-width: 640px) and (max-width: 1023px) {
  .kv-bg {
    min-height: calc((100dvh - 182px) - 50px);
  }
}

@media (max-width: 639px) {
  .kv-bg {
    min-height: calc((100dvh - 175px) - 50px);
  }
}

@media (min-width: 768px) {
  .campaign-banner {
    position: absolute;
    top: -5.5rem;
    right: -5.5rem;
    background-color: #FDDB17;
    border-radius: 100%;
    padding: 20px;
    padding-top: 30px;
    font-weight: bolder;
    text-align: center;
    color: #600015;
    font-size: 28px;
    line-height: 32px;
  }
  .campaign-banner .discount {
    font-size: 80px;
    font-weight: 900;
    line-height: 64px;
  }
  .campaign-banner:after {
    content: '';
    display: block;
    position: absolute;
    bottom: -5px;
    left: 10px;
    border-width: 40px 15px 0 15px;
    border-style: solid;
    border-color: #FDDB17 transparent;	
    width: 2px;
    transform: rotate(45deg);
  }
}

@media (max-width: 767px) {
  .kv-banner {
    max-width: 425px;
  }

  .campaign-banner {
    color: white;
    font-size: 30px;
    font-weight: 900;
    text-align: center;
    margin: 10px 0;
  }
  .campaign-banner .discount {
    font-size: 50px;
    font-weight: 900;
    line-height: 64px;
  }
}


@media (max-width: 1160px) {
  .campaign-banner {
    right: -1rem;
    top: -6rem;
    font-size: 20px;
    line-height: 20px;
  }
  .campaign-banner .discount {
    font-size: 60px;
    font-weight: 900;
    line-height: 40px;
  }
}

