/*
 * import modules and libraries
 */
import { Box, Group, Stack } from "@mantine/core";
/*
 * import styles
 */
import styles from "./KVContent.module.css";

export default function Fiit({
  notAbsolute,
  center,
}: {
  notAbsolute?: boolean;
  center?: boolean;
}): JSX.Element {
    return (
      <Box className={`${styles["affiliate-line"]}
        ${notAbsolute ? "" :  "absolute -top-12 lg:-top-11 -left-8 lg:left-2"}
        bg-[#06c755] p-3 lg:p-5 shadow-md`}
      >
        <div className="font-bold text-center text-white leading-none text-xs lg:text-xl">
          韓国コスパ<br/>旅限定
        </div>
        <Group className="gap-1 font-bold items-center justify-center lg:justify-start italic">
          <div className="font-black text-[#FFFC86] text-3xl lg:text-5xl">3</div>
          <Stack className="gap-0">
            <div className="!leading-none text-[#FFFC86] text-xs lg:text-xl">%</div>
            <div className="!leading-none text-white text-xs lg:text-xl">OFF</div>
          </Stack>
        </Group>
      </Box>
    );
}