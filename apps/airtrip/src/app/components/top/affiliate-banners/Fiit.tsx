/*
 * import modules and libraries
 */
import { Box, Group, Stack } from "@mantine/core";
/*
 * import styles
 */
import styles from "./KVContent.module.css";

export default function Fiit({
  notAbsolute,
  center,
}: {
  notAbsolute?: boolean;
  center?: boolean;
}): JSX.Element {
    return (
      <Box className={`${styles["affiliate-fiit"]} 
        ${notAbsolute ? "" : "absolute -top-10 lg:-top-16 -left-8 lg:-left-2"}
        bg-action px-2 py-4 lg:px-5 lg:py-7 shadow-md`}
      >
        <div className={`font-bold ${center ? "text-center" : "text-left"} text-white leading-none text-xxs lg:text-xl`}>
          メンバーシップ<br />会員様限定
        </div>
        <Group className={`gap-1 font-bold items-center ${center ? "justify-center" : "justify-start"} italic`}>
          <div className="font-black text-[#FFFC86] text-xl lg:text-5xl">10</div>
          <Stack className="gap-0">
            <div className="!leading-none text-[#FFFC86] text-xxs lg:text-xl">%</div>
            <div className="!leading-none text-white text-xxs lg:text-xl">OFF</div>
          </Stack>
        </Group>
      </Box>
    );
}