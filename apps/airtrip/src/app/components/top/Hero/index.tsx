"use client";

import dynamic from "next/dynamic";
import Image from "next/image";
import { useSearchParams } from "next/navigation";

import { Box, Flex, Group, Stack, Text, rem } from "@mantine/core";

import {
  IconMessageCircle,
  IconMoodHeart,
  IconSettingsBolt,
  IconWifi,
  IconWorldPin,
} from "@tabler/icons-react";

import { getCDNUrl } from "@/utils";

import { FIIT_AFFILIATE } from "@/app/constants";

import styles from "./hero.module.css";

const DynamicFiit = dynamic(
  () => import("@/app/components/top/affiliate-banners/Fiit")
);
const DynamicLine = dynamic(
  () => import("@/app/components/top/affiliate-banners/Line")
);
const DynamicCospaKorea = dynamic(
  () => import("@/app/components/top/affiliate-banners/CospaKorea")
);

export default function Hero({
  countrySelector,
}: {
  countrySelector: React.ReactNode;
}) {
  const searchParams = useSearchParams();
  const agent =
    searchParams.has("via") && searchParams.get("via")
      ? searchParams.get("via")
      : null;

  return (
    <>
      <div className="invisible absolute md:-top-20" id="select-plan" />
      <Box className={`${styles["hero-bg"]} bg-primary`}>
        <Stack
          className="relative mx-auto h-full items-center justify-center md:px-4"
          maw={rem(1080)}
        >
          {FIIT_AFFILIATE.includes(agent as string) && (
            <Box className="absolute -left-20 bottom-0 hidden lg:block">
              <Box className="relative">
                {agent === "fiit01" && <DynamicFiit />}
                {agent === "fiit02" && <DynamicLine />}
                {agent === "cospakorea" && <DynamicCospaKorea />}
                <Image
                  className="relative object-contain"
                  src={
                    ["886", "209"].includes("886" as string)
                      ? "/assets/hero/affiliate.webp"
                      : "/assets/hero/affiliate-no-discount.webp"
                  }
                  height={568}
                  width={250}
                  alt="affiliate"
                />
                <Box className="absolute -right-36 bottom-1">
                  <Text className="rounded-md bg-black bg-opacity-30 p-1 text-sm font-bold leading-none text-white">
                    韓国プロデューサー　まじゅ
                  </Text>
                </Box>
              </Box>
            </Box>
          )}
          <Stack className="h-fit w-full items-center justify-center gap-0 py-4 md:rounded-md md:bg-black/70 md:py-16 md:shadow-md">
            {FIIT_AFFILIATE.includes(agent as string) && (
              <Box className="relative mb-6 mt-10 w-20 shrink-0 md:hidden">
                {agent === "fiit01" && <DynamicFiit />}
                {agent === "fiit02" && <DynamicLine />}
                {agent === "cospakorea" && <DynamicCospaKorea />}
                <Image
                  className="relative ml-auto object-contain"
                  src={
                    ["886", "209"].includes("886" as string)
                      ? "/assets/hero/affiliate.webp"
                      : "/assets/hero/affiliate-no-discount.webp"
                  }
                  height={568}
                  width={70}
                  alt="affiliate"
                />
                <Box className="absolute -bottom-4 -right-20">
                  <Text className="text-xs font-bold leading-none text-white">
                    韓国プロデューサー まじゅ
                  </Text>
                </Box>
              </Box>
            )}
            <Text className="flex items-center text-3xl font-black text-white md:text-4xl">
              <span>
                海外でeSIM使う<span className="text-2xl md:text-3xl">なら</span>
              </span>
              <IconWifi className="rotate-90 lg:h-8 lg:w-8" />
            </Text>
            <Image
              className="px-4 pb-1 pt-3"
              src={getCDNUrl("/assets/hero/esim-logo.svg")}
              width={594}
              height={70}
              alt="エアトリ eSIM"
              unoptimized
            />
            <Stack className="w-full flex-col-reverse items-center justify-center gap-4 md:flex-col">
              {countrySelector}
              <Group className="flex-nowrap items-end">
                <Flex className="flex-col gap-2 md:mt-4 md:max-w-[700px] md:flex-row md:flex-wrap md:gap-0">
                  <Text className="hidden items-center text-base font-bold text-white md:flex md:w-1/2 md:flex-wrap md:py-2 md:text-xl">
                    <IconSettingsBolt
                      className="md:text-action mb-1 mr-2"
                      size={28}
                    />
                    <span>
                     購入後、
                      <span className="text-[#FFF507]">3分で設定完了</span>
                    </span>
                  </Text>
                  <Text className="hidden items-center text-base font-bold text-white md:flex md:w-1/2 md:flex-wrap md:py-2 md:text-xl">
                    <IconMoodHeart
                      className="md:text-action mb-1 mr-2"
                      size={28}
                    />
                    <span>
                     <span className="text-[#FFF507]"> ギガ追加購入</span>可能
                    </span>
                  </Text>
                  <Text className="hidden items-center text-base font-bold text-white md:flex md:w-1/2 md:flex-wrap md:py-2 md:text-xl">
                    <IconMessageCircle
                      className="md:text-action mb-1 mr-2"
                      size={28}
                    />
                    <span>
                      安心！
                      <span className="text-[#FFF507]">365日LINEサポート</span>
                    </span>
                  </Text>
                  <Text className="hidden items-center text-base font-bold text-white md:flex md:w-1/2 md:flex-wrap md:py-2 md:text-xl">
                    <IconWorldPin
                      className="md:text-action mb-1 mr-2"
                      size={28}
                    />
                    <span>
                      <span className="text-[#FFF507]">200以上</span>
                      の国と地域で利用可能
                    </span>
                  </Text>
                </Flex>
              </Group>
            </Stack>
          </Stack>
        </Stack>
      </Box>
      <Box className="bg-black py-1 text-center">
        <Text className="text-sm text-white md:text-base">
          ※エアトリeSIMはグロモバが提供するサービスです。
        </Text>
      </Box>
    </>
  );
}
