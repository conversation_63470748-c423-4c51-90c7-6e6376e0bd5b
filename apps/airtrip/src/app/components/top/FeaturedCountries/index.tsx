"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { Button, Grid, GridCol, Stack } from "@mantine/core";

import { IconPlaneDeparture } from "@tabler/icons-react";
import sortBy from "lodash/sortBy";

import CountryCard from "@repo/ui/src/CountryCard";
import featuredPlans from "@repo/ui/src/constants/featured-plans.json";
import type { IDiscount } from "@repo/ui/src/interfaces/ICoupon";

import {
  countryAlias,
  countryNameException,
  getCDNUrl,
  normalizeAndKebabCase,
} from "@/utils";
import { Currency } from "@/utils/currency";

import { ApiService } from "@/api";

import type { IPlan } from "@/interfaces/IPlan";

import { COUNTRY_SORT } from "@/app/constants";

const TOP_COUNTRIES = ["KR", "JP", "VN", "HI", "C<PERSON>", "TW", "AS", "EU"];

function getServerSideProps() {
  let formatCountryPlans: IPlan[] = [];
  let formatRegionPlans: IPlan[] = [];
  const plans: {
    regions: IPlan[];
    countries: IPlan[];
  } = featuredPlans.data as {
    countries: any;
    regions: any;
  };

  try {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries
        .filter((item: IPlan) =>
          TOP_COUNTRIES.includes(item.countryCode as string)
        )
        .map((item: IPlan) => ({
          ...item,
          order: COUNTRY_SORT.get(item.country) || null,
          originalName: item.country,
          country: {
            name: countryAlias(item.country as unknown as string),
          },
        })),
      ["order"]
    );

    /* @ts-ignore */
    formatRegionPlans = plans.regions
      .filter((item: IPlan) =>
        TOP_COUNTRIES.includes(item.countryCode as string)
      )
      .map((item: IPlan) => ({
        ...item,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      }));
  } catch (err) {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries
        .filter((item: IPlan) =>
          TOP_COUNTRIES.includes(item.countryCode as string)
        )
        .map((item: IPlan) => ({
          ...item,
          order: COUNTRY_SORT.get(item.country) || null,
          originalName: item.country,
          country: {
            name: countryAlias(item.country as unknown as string),
          },
        })),
      ["order"]
    );

    formatRegionPlans = plans.regions.filter((item: IPlan) =>
      TOP_COUNTRIES.includes(item.countryCode as string)
    );
  }

  return {
    formatCountryPlans: [...formatCountryPlans, ...formatRegionPlans],
  };
}

export default function FeaturedCountries() {
  const searchParams = useSearchParams();
  const agent =
    searchParams.has("via") && searchParams.get("via")
      ? searchParams.get("via")
      : null;
  const countries = getServerSideProps();
  const [couponData, setCouponData] = useState<IDiscount>();

  const getCoupon = async () => {
    try {
      const coupon = await ApiService.getCoupon({ code: agent });
      setCouponData(coupon?.data?.data?.coupon);
    } catch (err) {
      console.log(err);
    }
  };
  useEffect(() => {
    getCoupon();
  }, []);

  return (
    <Stack className="gap-5 sm:gap-10">
      <Grid gutter={"1rem"} visibleFrom="sm">
        {countries.formatCountryPlans?.map?.((plan, index) => (
          <GridCol key={index} span={{ base: 12, xs: 12, sm: 4, md: 3 }}>
            <CountryCard
              img={getCDNUrl(
                `/assets/destination/${countryNameException(
                  //@ts-ignore
                  plan.originalName || plan.country
                )}.webp`
              )}
              price={plan.price + ""}
              //@ts-ignore
              url={`/region/${normalizeAndKebabCase(
                //@ts-ignore
                plan?.originalName || plan?.country?.name || plan?.country
              )}?${
                normalizeAndKebabCase(
                  //@ts-ignore
                  plan?.originalName || plan?.country?.name || plan?.country
                ) === "japan"
                  ? "scroll=plan"
                  : ""
              }${
                searchParams.has("via") ? "&via=" + searchParams.get("via") : ""
              }`}
              name={
                countryNameException(
                  plan?.originalName || plan?.country?.name
                ) as string
              }
              originalName={countryNameException(plan.originalName)}
              selectedCode={Currency.getSelectedCurrency()}
              convertedPrice={Currency.formatToSelectedNoCode({
                xe: plan.xe,
                price: +plan.price,
              })}
              // discount={couponData?.discount}
            />
          </GridCol>
        ))}
      </Grid>
      <Grid gutter={"1rem"} hiddenFrom="sm">
        {countries.formatCountryPlans?.splice(0, 4).map?.((plan, index) => (
          <GridCol key={index} span={{ base: 12, xs: 12, sm: 4, md: 3 }}>
            <CountryCard
              //@ts-ignore
              img={getCDNUrl(
                `/assets/destination/${countryNameException(
                  //@ts-ignore
                  plan.originalName || plan.country
                )}.webp`
              )}
              price={plan.price + ""}
              //@ts-ignore
              url={`/region/${normalizeAndKebabCase(
                //@ts-ignore
                plan?.originalName || plan?.country?.name || plan?.country
              )}?${
                normalizeAndKebabCase(
                  //@ts-ignore
                  plan?.originalName || plan?.country?.name || plan?.country
                ) === "japan"
                  ? "scroll=plan"
                  : ""
              }${
                searchParams.has("via") ? "&via=" + searchParams.get("via") : ""
              }`}
              name={
                countryNameException(
                  plan?.originalName || plan?.country?.name
                ) as string
              }
              originalName={countryNameException(plan.originalName)}
              selectedCode={Currency.getSelectedCurrency()}
              convertedPrice={Currency.formatToSelectedNoCode({
                xe: plan.xe,
                price: +plan.price,
              })}
              // discount={couponData?.discount}
            />
          </GridCol>
        ))}
      </Grid>
      <Button
        variant="outline"
        size="md"
        className="mx-auto w-full max-w-sm rounded-md"
        component={Link}
        href="/destination"
      >
        <IconPlaneDeparture className="text-primary mr-2" />
        <span className="text-black">すべての旅行先はこちら</span>
      </Button>
    </Stack>
  );
}
