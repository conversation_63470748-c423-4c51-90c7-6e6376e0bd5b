"use client";

import dynamic from "next/dynamic";

import Reviews from "@repo/ui/src/top/Reviews";
import { ReviewWithSummary } from "@repo/ui/types";

import { useOrderStore } from "@/store";

const DynamicDrawer = dynamic(() =>
  import("@mantine/core").then((module) => ({
    default: module.Drawer,
  }))
);

interface ReviewSectionProps {
  ukomiReviewsWithSummary: ReviewWithSummary;
}

export default function ReviewSection({
  ukomiReviewsWithSummary,
}: ReviewSectionProps) {
  const [isReviewDrawerOpen, toggleReviewDrawerOpen] = useOrderStore((s) => [
    s.isReviewDrawerOpen,
    s.toggleReviewDrawerOpen,
  ]);

  return (
    <>
      <Reviews
        toggleReviewDrawerOpen={toggleReviewDrawerOpen}
        reviewsWithSummary={ukomiReviewsWithSummary}
      />
      <DynamicDrawer
        lockScroll
        size={"lg"}
        opened={isReviewDrawerOpen}
        onClose={toggleReviewDrawerOpen}
        position="right"
        keepMounted
        withinPortal
      >
        <div
          className="review-container flex justify-center"
          data-pid="グローバルモバイル"
          data-action="dedicated-widget"
          data-review-type="product"
        >
          読み込み中...
        </div>
      </DynamicDrawer>
    </>
  );
}
