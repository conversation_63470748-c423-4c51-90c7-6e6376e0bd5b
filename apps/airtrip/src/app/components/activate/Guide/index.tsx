"use client";

/*
 * import modules and libraries
 */
import {
  em,
  <PERSON>,
  Stack,
  Text,
  Divider,
  Button,
  Alert,
} from "@mantine/core";
import { useState, useEffect } from "react";
import { Carousel, Embla } from '@mantine/carousel';
import { useMediaQuery } from "@mantine/hooks";
import Image from "next/image";
import { Trans } from "react-i18next";
import { IconChevronLeft, IconChevronRight } from "@tabler/icons-react";
/*
 * import data types
 */
import { IActivationGuide } from "@repo/ui/src/interfaces/IActivation";
/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";

const Guide = ({
  guide,
  buttonText,
  openModal,
  guideOnly,
}: {
  guide: IActivationGuide[];
  buttonText: string;
  guideOnly?: boolean;
  openModal: () => void;
}) => {
  const [embla, setEmbla] = useState<Embla | null>(null);
  // TODO: can be move to utils
  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });

  useEffect(() => {
    embla?.scrollTo(0)
  }, [guide]);

  return (
    <Card className={`rounded-lg shadow ${guideOnly ? "border" : ""}`}>
      <Carousel
        getEmblaApi={setEmbla}
        withControls={!isMobileSize}
        withIndicators
        nextControlIcon={<IconChevronRight className="text-primary" size={32} />}
        previousControlIcon={<IconChevronLeft className="text-primary" size={32} />}
        classNames={{
          indicator: "bg-primary w-2 h-2",
          indicators: "mt-4 mb-4 md:mb-0 relative bottom-[unset]",
          control: "shadow-none",
        }}
      >
        {guide?.map((item: IActivationGuide) => (
          <Carousel.Slide key={item.id}>
            <Stack className="justify-center items-center gap-8">
              <Image
                width={200}
                height={200}
                alt={item.imageUrl}
                src={getCDNUrl(item.imageUrl)}
              />
              <Text className="text-center max-w-lg">
                <Trans
                  i18nKey={item.description}
                  components={{
                    highlight: (
                      <Text
                        component="span"
                        className="text-primary font-bold"
                      />
                    ),
                  }}
                />
              </Text>
            </Stack>
          </Carousel.Slide>
        ))}
        <Carousel.Slide>
          <Stack className="justify-center items-center gap-4 md:px-20 pt-10">
            <Image
              src={getCDNUrl("/assets/checkmark.webp")}
              width={80}
              height={80}
              className="w-[80px] h-[80px]"
              alt="check"
            />
            {/* TODO: move these to translation files */}
            <Text className="font-bold">
              eSIMの設定は以上です！
            </Text>
            <Alert
              variant="light"
              classNames={{
                root: "bg-secondary border border-primary rounded-lg"
              }}
            >
              設定完了後、回線が開通するまで数分お時間がかかる場合がございます。<br />
              <br />
              eSIMは一度しかインストールできません。インストール後にeSIMを端末から削除すると再インストールできませんのでご注意ください。
            </Alert>
          </Stack>
        </Carousel.Slide>
      </Carousel>
      {!guideOnly && (
        <Stack className="hidden md:flex mt-4">
          <Divider />

          <Button
            size="md"
            color="app-pink.4"
            className="max-w-sm w-full mx-auto"
            onClick={openModal}
          >
            {buttonText}
          </Button>
        </Stack>
      )}
    </Card>
  )
};

export default Guide;