"use client";

/*
 * import modules and libraries
 */
import { useMemo } from "react";

import {
  Center,
  Divider,
  Group,
  Paper,
  Progress,
  RingProgress,
  Stack,
  Text,
} from "@mantine/core";

import { differenceInDays, isPast } from "date-fns";
import * as filesize from "filesize";
import { useTranslation } from "react-i18next";

/*
 * import constants and helpers
 */
import { formatDateAndTime, getBytes } from "@/utils";

interface IProps {
  country?: string;
  usedData: string;
  totalData: string;
  validitiyDays: number;
  startDate: Date | null;
  endDate: Date | null;
}

const colorAsPerDays = (diff: number) => {
  if (diff < 40) {
    return "#0CB21A";
  }
  if (diff >= 40 && diff < 80) {
    return "#FFCD3F";
  }
  return "red";
};

const UsageProgressChart = (props: IProps) => {
  const { t } = useTranslation();

  const totalDays = useMemo(() => {
    return props.startDate && props.endDate
      ? differenceInDays(new Date(props.endDate), new Date(props.startDate))
      : 0;
  }, []);

  const diffToday = useMemo(() => {
    return props.startDate
      ? differenceInDays(new Date(), new Date(props.startDate))
      : 0;
  }, []);

  const diffDaysInPercentage = useMemo(() => (diffToday / totalDays) * 100, []);

  const totalBytes = useMemo(() => getBytes(props.totalData || "0"), []);
  const usedBytes = useMemo(() => getBytes(props.usedData || "0"), []);

  const remainingData = useMemo(
    () => totalBytes - usedBytes,
    [totalBytes, usedBytes]
  );

  const usedPercentage = useMemo(() => {
    return (usedBytes / totalBytes) * 100 || 0;
  }, []);

  const isExpired = useMemo(
    () => !!(props.endDate && isPast(new Date(props.endDate))),
    []
  );

  const formatBytes = (bytes: number = 0) =>
    (filesize.filesize(bytes, { base: 2, standard: "jedec" }) as
      | string
      | undefined) || "0 Bytes";

  return (
    <Paper w={"100%"} className="p-4 shadow-md" radius={"md"}>
      <Stack className="gap-0">
        <Group className="justify-between">
          <Text className="font-bold capitalize">
            {t("common:siminfo-card.dataplan.title", "DATA USAGE")}
          </Text>
          <Text className="text-primary font-bold">
            {formatBytes(usedBytes)}/ {formatBytes(totalBytes)}
          </Text>
        </Group>
        {props.country === "japan" && (
          <Text className="text-sm">{t("myesim:data.usage.description")}</Text>
        )}

        <RingProgress
          thickness={20}
          size={250}
          sections={[
            {
              value: usedPercentage,
              color: colorAsPerDays(usedPercentage),
            },
          ]}
          classNames={{
            root: "mx-auto",
          }}
          label={
            <Center>
              <Stack className="align-center justify-center gap-0">
                <Text className="text-center text-sm">
                  {isExpired
                    ? t("common:siminfo-card.tag.expired", "Expired")
                    : t("common:siminfo-card.remainingdata", "Remaining Data")}
                </Text>
                {!isExpired && (
                  <Text className="text-primary text-center text-xl font-bold">
                    {!props.endDate
                      ? props.totalData
                      : formatBytes(remainingData) + ""}
                  </Text>
                )}
              </Stack>
            </Center>
          }
        />

        <Divider />
        <Stack className="mt-4 gap-0">
          <Group className="justify-between">
            <Text className="font-bold">
              {t("common:siminfo-card.usageperiod", "Usage Period")}
            </Text>
            <Text className="text-primary font-bold">
              {t("common:siminfo-card.validity.unit", {
                count: props.validitiyDays, // Get the usage plan days without unit
                defaultValue: "{{ count }} day",
                defaultValue_other: "{{ count }} days",
              })}
            </Text>
          </Group>

          <Progress
            className="mt-1"
            color={colorAsPerDays(diffDaysInPercentage)}
            value={diffDaysInPercentage}
          />

          <Group className="mt-1 justify-between">
            <Text className="text-xs text-gray-400">
              {props.startDate ? formatDateAndTime(props.startDate) : "N/A"}
            </Text>
            <Text className="text-xs text-gray-400">
              {props.endDate ? formatDateAndTime(props.endDate) : "N/A"}
            </Text>
          </Group>
        </Stack>
      </Stack>
    </Paper>
  );
};
export default UsageProgressChart;
