"use client";

/*
 * import modules and libraries
 */
import { useMemo, useState } from "react";

import { Stack } from "@mantine/core";

import InfiniteScroll from "react-infinite-scroll-component";
import { useInfiniteQuery } from "react-query";

/*
 * import components
 */
import MyEsimList from "@/app/components/my-esim/MyEsimContainer/MyEsimList";
import LoadingCard from "@/app/components/my-esim/MyEsimContainer/MyEsimList/LoadingCard";

/*
 * import api
 */
import { ApiService } from "@/api";

const QUERY_LIMIT = 10;

const MyEsimContainer = ({
  status,
  isActive,
}: {
  status: "active" | "expired";
  isActive?: boolean;
}) => {
  const [total, setTotal] = useState(0);

  const { data, fetchNextPage, isFetchingNextPage, isLoading } =
    useInfiniteQuery({
      enabled: isActive,
      keepPreviousData: true,
      queryKey: `orders-${status}`,
      queryFn: async ({ pageParam = 1 }) => {
        if (!isActive) return;
        const data = await ApiService.getOrders({
          sort_by: "id:desc",
          limit: QUERY_LIMIT,
          page: pageParam,
          status: status,
        });
        setTotal(data?.data?.data?.total);
        return data?.data?.data?.data;
      },
      getNextPageParam: (lastPage, allPages) => {
        return allPages.length + 1;
      },
      cacheTime: 100000,
    });

  const hasMoreData = useMemo(() => {
    const fetched = (data?.pages || [])?.length * QUERY_LIMIT;
    return fetched <= total;
  }, [data?.pages, total]);

  return (
    <MyEsimList
      isLoading={isLoading}
      fetchNextPage={fetchNextPage}
      items={data?.pages || []}
      status={status}
      renderWrapper={(items, children) => {
        return (
          <InfiniteScroll
            dataLength={items.length} //This is important field to render the next data
            next={fetchNextPage}
            hasMore={hasMoreData}
            loader={
              isLoading || isFetchingNextPage ? (
                <Stack mt={10}>
                  <LoadingCard />
                  <LoadingCard />
                  <LoadingCard />
                  <LoadingCard />
                </Stack>
              ) : null
            }
          >
            {children}
          </InfiniteScroll>
        );
      }}
    />
  );
};
export default MyEsimContainer;
