"use client";

/*
 * import modules and libraries
 */
import { useEffect, useState } from "react";

import { Accordion, AccordionProps, Center, Text } from "@mantine/core";

import { isPast } from "date-fns";
import { Trans } from "react-i18next";

/*
 * import component
 */
import EsimCardDetail from "@/app/components/my-esim/MyEsimContainer/MyEsimList/EsimCardDetail";
import EsimCard from "@/app/components/my-esim/MyEsimContainer/MyEsimList/EsimCardDetail/EsimCard";

/*
 * import constants and helpers
 */
import Storage from "@/utils/storage";

/*
 * import interface
 */
import { IOrder } from "@/interfaces/IOrder";

const EsimList = (props: {
  isLoading?: boolean;
  ignoreCardRenderLogic?: boolean;
  fetchNextPage: () => void;
  esimCardProps?: Partial<React.ComponentProps<typeof EsimCard>>;
  accordionProps?: Partial<AccordionProps>;
  status: "active" | "expired";
  items: Array<Array<IOrder>>;
  renderWrapper: (
    item: Array<Array<IOrder>>,
    arg: React.ReactNode
  ) => React.ReactNode;
}) => {
  const [activeItem, setActiveItem] = useState<string>("");

  useEffect(() => {
    const lastItem = Storage.getInstance().get("lastItem");
    setActiveItem(lastItem);
    Storage.getInstance().set("lastItem", "");
    setTimeout(() => {
      const element = document.getElementById(lastItem);
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }, 400);
  }, []);

  useEffect(() => {
    Storage.getInstance().set("lastItem", activeItem);
  }, [activeItem]);

  return (
    <Accordion
      value={props.accordionProps?.defaultValue ? undefined : activeItem}
      w={"100%"}
      variant="separated"
      classNames={{
        chevron: "bg-[#334CA9] w-7 h-7 justify-center text-white rounded-full",
        control: "text-white",
        item: "text-white bg-gradient-to-r from-[#082699] to-[#30395c] rounded-md",
        panel: "text-white",
      }}
      {...(props.accordionProps || {})}
      onChange={(v: string | null) => {
        setActiveItem(v || "");
      }}
    >
      {!props.isLoading && !props.items.flat().length && (
        <Center w={"100%"} h={"50vh"}>
          <Text className="text-xs">
            <Trans i18nKey={"myesim:noesim.txt"}>No eSIM order yet.</Trans>
          </Text>
        </Center>
      )}

      {props.renderWrapper(
        props.items,
        <>
          {props.items.map((page) => {
            return page
              ?.filter((item) => item !== null)
              .map?.((item) => {
                const isExpired = item.expireTime
                  ? isPast(new Date(item.expireTime))
                  : false;

                if (props.ignoreCardRenderLogic) {
                  return (
                    <EsimCardDetail
                      isExpired={isExpired}
                      key={item.id}
                      item={item}
                      esimCardProps={props.esimCardProps}
                    />
                  );
                }
                // TODO: refactor this logic
                if (item.expireTime) {
                  if (props.status === "active") {
                    return isExpired ? null : (
                      <EsimCardDetail
                        item={item}
                        key={item.id}
                        esimCardProps={props.esimCardProps}
                      />
                    );
                  } else {
                    return isExpired ? (
                      <EsimCardDetail
                        item={item}
                        key={item.id}
                        esimCardProps={props.esimCardProps}
                      />
                    ) : null;
                  }
                }
                // Orders dont have expire time, which means
                // either they are not activated or we dont have
                // Latest information
                return props.status === "active" ? (
                  <EsimCardDetail
                    key={item.id}
                    item={item}
                    esimCardProps={props.esimCardProps}
                  />
                ) : null;
              });
          })}
        </>
      )}
    </Accordion>
  );
};

export default EsimList;
