import { Button, <PERSON>lex, Image } from "@mantine/core";

const bgColors = {
  facebook: "#1C72E2",
  google: "#FFFFFF",
};
interface IProps {
  icon: "google" | "facebook";
}
const ESIM_API_PATH = process.env.NEXT_PUBLIC_API_HOST;

const google_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Google`;
const fb_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Facebook`;

const appendRedirectUri = (url: string) => {
  if (typeof window === "undefined") return url;
  let redirectUri = "";
  redirectUri =
    window.location.protocol +
    "//" +
    window.location.host +
    (process.env.NEXT_PUBLIC_BASE_PATH || "") +
    "/social/login/callback";

  if (redirectUri) {
    return url + "&redirectUri=" + redirectUri;
  }
  return url;
};

const SocialButton = (props: IProps) => {
  return (
    // <Center >
    <Button
      bg={bgColors[props.icon]}
      fullWidth
      justify="flex-start"
      size="lg"
      radius={"lg"}
      classNames={{
        label: `"flex justify-center w-full  ${
          props.icon === "facebook" ? "text-white" : "text-black"
        } `,
        root: "border-2 border-gray-100 rounded-lg",
      }}
      leftSection={
        <Image src={`/assets/${props.icon}.png`} w={20} h={20} alt="" />
      }
    >
      {/* <Text
        fw={"bold"}
        ml={"xs"}
        color={props.icon === "facebook" ? "white" : "black"}
      > */}
      {props.icon === "google" ? "Google" : "Facebook"} {/* </Text> */}
    </Button>
    // </Center>
  );
};

const SocialLogin = () => {
  return (
    <Flex justify={"center"} gap={10} direction={"column"}>
      <a href={appendRedirectUri(fb_url)} className="no-underline">
        <SocialButton icon="facebook" />
      </a>
      <a href={appendRedirectUri(google_url)} className="no-underline">
        <SocialButton icon="google" />
      </a>
    </Flex>
  );
};
export default SocialLogin;
