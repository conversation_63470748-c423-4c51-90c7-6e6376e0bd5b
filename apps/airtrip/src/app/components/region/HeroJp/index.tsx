"use client";

import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

import { Box, Button, Group, Stack, Text, rem } from "@mantine/core";

import {
  IconMoodHeart,
  IconQrcode,
  IconRouter,
  IconSettingsBolt,
  IconWifi,
} from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import { getCDNUrl } from "@/utils";

import { FIIT_AFFILIATE } from "@/app/constants";

import styles from "./hero.module.css";

const DynamicFiit = dynamic(
  () => import("@/app/components/top/affiliate-banners/Fiit")
);
const DynamicLine = dynamic(
  () => import("@/app/components/top/affiliate-banners/Line")
);

export default function Hero() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const agent =
    searchParams.has("via") && searchParams.get("via")
      ? searchParams.get("via")
      : null;

  return (
    <>
      <Box className="bg-white lg:hidden">
        <Group className="gap-2 pt-2">
          <Button
            size="md"
            component={Link}
            href="https://wifi.airtrip.jp/softbank"
            color="white"
            className="flex-1 rounded-b-none rounded-t-md border border-b-0 border-[#e5e5e5] text-black shadow-none hover:bg-white"
            classNames={{
              section: "mr-1",
            }}
            leftSection={<IconRouter size={20} stroke={2} />}
          >
            WiFi
          </Button>
          <Button
            size="md"
            component={Link}
            href="/region/japan"
            color="app-pink.4"
            className="border-primary flex-1 rounded-b-none rounded-t-md border-b-0 shadow-none"
            classNames={{
              section: "mr-1",
            }}
            leftSection={<IconQrcode size={20} stroke={2} />}
          >
            eSIM
          </Button>
        </Group>
      </Box>
      <Box className={`${styles["hero-bg"]} bg-primary`}>
        <Box className="relative mx-auto h-full" maw={rem(1080)}>
          {FIIT_AFFILIATE.includes(agent as string) && (
            <Box className="absolute bottom-0 left-0 hidden lg:block">
              <Box className="relative">
                {agent === "fiit01" && <DynamicFiit />}
                {agent === "fiit02" && <DynamicLine />}
                <Image
                  className="relative object-contain"
                  src={
                    ["886", "209"].includes("886" as string)
                      ? "/assets/hero/affiliate.webp"
                      : "/assets/hero/affiliate-no-discount.webp"
                  }
                  height={568}
                  width={250}
                  alt="affiliate"
                />
                <Box className="absolute -right-36 bottom-1">
                  <Text className="rounded-md bg-black bg-opacity-30 p-1 text-sm font-bold leading-none text-white">
                    韓国プロデューサー　まじゅ
                  </Text>
                </Box>
              </Box>
            </Box>
          )}
          <Stack className="h-full items-center justify-center gap-4 p-4 md:p-0">
            <Text className="flex items-center text-3xl font-black text-white md:text-4xl">
              <span>
                国内専用eSIM<span className="text-2xl md:text-3xl">なら</span>
              </span>
              <IconWifi className="rotate-90 lg:h-8 lg:w-8" />
            </Text>
            <Image
              src={getCDNUrl("/assets/hero/esim-logo.svg")}
              width={594}
              height={70}
              alt="エアトリ eSIM"
              unoptimized
            />
            <Group className="flex-nowrap items-end">
              {FIIT_AFFILIATE.includes(agent as string) && (
                <Box className="relative ml-8 mt-10 w-20 shrink-0 lg:hidden">
                  {agent === "fiit01" && <DynamicFiit />}
                  {agent === "fiit02" && <DynamicLine />}
                  <Image
                    className="relative ml-auto object-contain"
                    src={
                      ["886", "209"].includes("886" as string)
                        ? "/assets/hero/affiliate.webp"
                        : "/assets/hero/affiliate-no-discount.webp"
                    }
                    height={568}
                    width={70}
                    alt="affiliate"
                  />
                  <Box className="absolute -bottom-4 -right-20">
                    <Text className="text-xs font-bold leading-none text-white">
                      韓国プロデューサー まじゅ
                    </Text>
                  </Box>
                </Box>
              )}
              <Stack className="mt-4 gap-2">
                <Text className="flex items-center text-base font-bold text-white md:text-2xl">
                  <IconSettingsBolt className="mb-1 mr-2" size={28} />
                  <span>
                    申し込み後、<span className="text-[#FFF507]">即時発行</span>
                  </span>
                </Text>
                <Text className="flex items-center text-base font-bold text-white md:text-2xl">
                  <IconMoodHeart className="mb-1 mr-2" size={28} />
                  <span>
                    ３分で<span className="text-[#FFF507]">かんたん設定</span>
                  </span>
                </Text>
              </Stack>
            </Group>
          </Stack>
        </Box>
      </Box>
      <Box className="bg-black py-1 text-center">
        <Text className="text-sm text-white md:text-base">
          ※エアトリeSIMは、グロモバが提供するサービスです。
        </Text>
      </Box>
    </>
  );
}
