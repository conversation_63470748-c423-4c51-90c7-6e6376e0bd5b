'use client';

/*
 * import modules and libraries
 */
import { useMemo } from "react";



import { Anchor, Flex, Image, List, Text, ThemeIcon } from "@mantine/core";



import { useTranslation } from "react-i18next";



import { DataType } from "@repo/ui/src/constants/plans";
/*
 * import data types
 */
import { INetwork } from "@repo/ui/src/interfaces/INetwork";



import { useOrderStore } from "@/store";
/*
 * import utils
 */
import { capitalizeFirstLetter, countryAlias, getCDNUrl, regionalOrCountry } from "@/utils";





const verificationRequiredCountrys: string[] = ['hongkong', 'taiwan', 'korea']

const SpecsList = (props: {
  isModal?: boolean;
  isLGU?: boolean;
  country: string,
  network: INetwork[],
  onClick?: (option: {
    type: string,
    value: Array<string>
  }) => void
}) => {
  const selectedPlanType = useOrderStore((state) => state.selectedPlanType);
  const { t } = useTranslation()
  const regexForKorea = props.country === "korea" ? /^\-|\b3G\b\/?/g : /^\-/;
  const specNetwork =
    props.country === "japan"
      ? [props.network[0]]
      : selectedPlanType === DataType.FIXED_DAY && props.isLGU
        ? [props.network[1]]
        : selectedPlanType !== DataType.FIXED_DAY && props.isLGU
          ? [props.network[0]]
          : props.network;
  const specs = useMemo(
    () => [
      {
        name: t("region:coverage"),
        value:
          regionalOrCountry(props.country) === "Country" ? (
            t(
              `countries:${props.country}`,
              capitalizeFirstLetter(countryAlias(props.country || ""))
            )
          ) : (
            <Anchor
              td={"underline"}
              onClick={() =>
                props.onClick?.({
                  type: "specs",
                  value: [],
                })
              }
            >
              {t("region:availablecountries")}
            </Anchor>
          ),
        iconURL: "coverage.png",
        invert: true,
      },
      {
        name: t("region:network", "NETWORK"),
        value: `${specNetwork
          ?.map?.((item) =>
            `${item.name} ${item.networkGeneration}`?.replace?.(
              regexForKorea,
              ""
            )
          )
          .join(" - ")}`,
        iconURL: "specs-network.png",
      },
      {
        name: t("region:plantype", "PLAN TYPE"),
        value:
          props.isLGU && selectedPlanType !== DataType.FIXED_DAY
            ? t("region:datawithcalls", "Data and can make and receive calls")
            : t("region:dataonlynocalls", "Data only (No Calls)"),
        iconURL: "specs-type.png",
      },
      {
        name: t("region:tethering", "TETHERING"),
        value: t("region:tetheringpossible", "Tethering possible"),
        iconURL: "specs-tethering.svg",
      },
      {
        name: t("region:ekyc", "eKYC (IDENTITY VERIFICATION)"),
        value: !verificationRequiredCountrys.includes(props.country)
          ? t("region:notrequired", "Not required")
          : t("region:required", "Required"),
        iconURL: "specs-ekyc.png",
      },
      {
        name: t("region:activationpolicy.title", "ACTIVATION POLICY"),
        value: t("region:activationpolicy.desc", "ACTIVATION POLICY"),
        iconURL: "specs-activation.png",
      },
      {
        name: t("region:activationperiod.title", "ACTIVATION PERIOD"),
        value: t(
          "region:activationperiod.desc",
          "Can be activated within 180 days of purchase."
        ),
        iconURL: "specs-activation.png",
      },
      {
        name: t("region:other", "OTHER"),
        value: t("region:nonrefundable", "Non-refundable"),
        iconURL: "specs-other.png",
      },
    ],
    [selectedPlanType]
  );

  return (
    <List
      size="sm"
      classNames={{
        root: "mb-4",
        item: `${props.isModal ? "px-4 py-3" : "p-0"} border-b`,
      }}
    >
      {specs.map((spec, index) => (
        <List.Item
          key={index}
          icon={
            <ThemeIcon
              color={spec.invert ? "dark" : "transparent"}
              size={24}
              radius="xl"
            >
              <Image src={getCDNUrl(`/assets/${spec.iconURL}`)} />
            </ThemeIcon>
          }
        >
          <Flex direction={"column"}>
            <Text className="text-neutral text-xs font-semibold">
              {spec.name}
            </Text>
            <Text className="text-base">
              {spec.value}
            </Text>
          </Flex>
        </List.Item>
      ))}
    </List>
  );
};
export default SpecsList;