"use client";

/*
 * import modules and libraries
 */
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect } from "react";

import { em } from "@mantine/core";
import { useMediaQuery, useScrollIntoView } from "@mantine/hooks";

import { uniqueId } from "lodash";

import { IPlan } from "@gmesim/fe-interfaces/src";

import SharedPlanContainer from "@repo/ui/src/PlanContainer";

import { checkIsValidAirTripAgent } from "@/utils/agent-handler";

/*
 * import interfaces
 */
import type { INetwork } from "@/interfaces/INetwork";

import { checkExistAgentCode } from "@/infra/agent-code";
import { useOrderStore, useShoppingCart } from "@/store";
import { useProfile } from "@/store/UserStore";

export default function PlanContainer({
  agent,
  isNeedRegistration,
  isLGU,
  isJapan,
  country,
  ...props
}: {
  agent?: string;
  isNeedRegistration?: boolean;
  isLGU?: boolean;
  isJapan?: boolean;
  country: string;
  regionData: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  } | null;
}) {
  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });
  const { scrollIntoView, targetRef } = useScrollIntoView<HTMLDivElement>({
    offset: isMobileSize ? 70 : 250,
    duration: 250,
  });
  const AIRTRIP_AGENT = checkIsValidAirTripAgent(agent);

  const checkIsExistAgentCode = async (agentCode: string | undefined) => {
    try {
      let agent: string | undefined = agentCode || AIRTRIP_AGENT;
      const listAgentsStorage: string[] = localStorage.getItem("agents")
        ? JSON.parse(localStorage.getItem("agents") || "")
        : [];
      const result = await checkExistAgentCode({ agent_code: agent || "" });
      if (!result.is_exists) {
        localStorage.removeItem("agent");
      } else {
        if (
          agent &&
          listAgentsStorage[listAgentsStorage.length - 1] !== agent
        ) {
          listAgentsStorage.push(agent);
        }

        if (listAgentsStorage.length > 10) {
          listAgentsStorage.shift();
        }

        localStorage.setItem("agents", JSON.stringify(listAgentsStorage));
        if (agent) localStorage.setItem("agent", agent);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const searchParams = useSearchParams();
  const via = searchParams.get("via");
  const scroll = searchParams.get("scroll");

  useEffect(() => {
    if (scroll) {
      scrollIntoView({
        alignment: "start",
      });
    }
  }, [country]);

  useEffect(() => {
    checkIsExistAgentCode(agent).catch((err) => {
      console.log(err);
    });
  }, [agent]);
  const setSelectedPlan = useOrderStore((state) => state.setSelectedPlan);
  const setSelectedPlanType = useOrderStore(
    (state) => state.setSelectedPlanType
  );
  const profile = useProfile((s) => s.profile);
  const [items, addToCart, showCartMenu, toggleCartMenu, quotation] =
    useShoppingCart((s) => [
      s.items,
      s.addToCart,
      s.showCartMenu,
      s.toggleCartMenu,
      s.quotation,
    ]);

  const handleAddToCart = useCallback((selectedPlan: IPlan) => {
    addToCart({
      id: Date.now() + "" + uniqueId(),
      plan: selectedPlan,
      quantity: 1,
    });
    toggleCartMenu(true);
  }, []);

  return (
    <SharedPlanContainer
      service="AIRTRIP"
      onAddToCartClick={handleAddToCart}
      //@ts-expect-error
      checkExistAgentCode={checkExistAgentCode}
      //@ts-expect-error
      setSelectedPlan={setSelectedPlan}
      //@ts-expect-error
      setSelectedPlanType={setSelectedPlanType}
      agent={agent}
      isLGU={isLGU}
      isJapan={isJapan}
      isNeedRegistration={isNeedRegistration}
      profile={profile}
      country={country}
      {...props}
    />
  );
}
