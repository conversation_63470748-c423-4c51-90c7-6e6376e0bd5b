"use client";

/*
 * import modules and libraries
 */
import { useTranslation } from "react-i18next";
import {
  Stack,
  Text,
  Button,
  Collapse,
  Card,
  ScrollAreaAutosize,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import dynamic from "next/dynamic";
import { IconPlus, IconMinus } from "@tabler/icons-react";
/*
 * import interfaces
 */
import type { INetwork } from "@/interfaces/INetwork";
/*
 * import components
 */
import PlanList from "@/app/components/region/PlanList";
import AboutEsim from "@repo/ui/src/AboutEsim";
/*
 * import dynamic components
 */
const DynamicRegionalCountries = dynamic(
  () => import("@repo/ui/src/modals/RegionalCountries")
);
const DynamicSpecsList = dynamic(
  () => import("@/app/components/region/SpecsList")
);

const Actions = ({
  isLGU,
  isRegional,
  country,
  network,
  subcountries,
  regionData,
  isNeedRegistration,
}: {
  isLGU?: boolean;
  isRegional?: boolean;
  country: string;
  network: INetwork[];
  subcountries: string;
  // TO DO: add regionData Interface
  regionData: any | null;
  isNeedRegistration?: boolean;
}) => {
  const { t } = useTranslation();
  const [specs, setSpecs] = useDisclosure(true);
  const [regionalCountriesModal, setRegionalCountriesModalModal] =
    useDisclosure();

  return (
    <>
      <Stack>
        <PlanList
          props={regionData}
          isNeedRegistration={isNeedRegistration}
          isLGU={isLGU}
          country={country}
        />
        <Button
          fullWidth
          size="xs"
          radius="md"
          variant="outline"
          color="app-pink.4"
          bg="white"
          className=""
          classNames={{
            root: "shadow hover:bg-white relative",
            section: "absolute right-2",
          }}
          rightSection={
            specs ? (
              <IconMinus
                size={20}
                className="bg-primary rounded-full text-white"
              />
            ) : (
              <IconPlus
                size={20}
                className="bg-primary rounded-full text-white"
              />
            )
          }
          onClick={setSpecs.toggle}
        >
          <Text className="text-black font-bold text-sm">
            {t("region:about-esim")}
          </Text>
        </Button>
      </Stack>
      <Collapse in={specs} className="mb-4">
        <AboutEsim isLGU={isLGU} isNeedRegistration={isNeedRegistration} />
        <Card className="p-4 shadow-lg rounded-lg">
          <ScrollAreaAutosize type="scroll" className="max-h-fit">
            <DynamicSpecsList
              onClick={(options) => {
                if (options.type === "specs") {
                  setRegionalCountriesModalModal.open();
                }
              }}
              isLGU={isLGU}
              country={country}
              network={network}
              isModal
            />
          </ScrollAreaAutosize>
        </Card>
      </Collapse>
      {isRegional && (
        <DynamicRegionalCountries
          title={t("region:coverage")}
          opened={regionalCountriesModal}
          onClose={setRegionalCountriesModalModal.close}
          subcountries={subcountries}
        />
      )}
    </>
  );
};
export default Actions;
