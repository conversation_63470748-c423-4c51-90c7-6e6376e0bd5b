"use client";

import HomeNotes from "@repo/ui/src/HomeNotes";

const RegionNotes = ({
  isShowLGUCompatible,
  isNeedRegistration,
}: {
  isShowLGUCompatible?: boolean;
  isNeedRegistration?: boolean;
}) => {
  return (
    <HomeNotes
      notes={"home:note.caution.content.airtrip"}
      isShowLGUCompatible={isShowLGUCompatible}
      extraNotes={
        isNeedRegistration
          ? "home:note.caution.hongkongtaiwancontent"
          : undefined
      }
    />
  );
};
export default RegionNotes;
