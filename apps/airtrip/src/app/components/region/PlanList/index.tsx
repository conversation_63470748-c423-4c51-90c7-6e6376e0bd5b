"use client";

import { useR<PERSON>er, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";

import { Box, SegmentedControl, Title } from "@mantine/core";

import { Trans } from "react-i18next";

import PlanForm from "@repo/ui/src/PlanForm";
import TrafficData from "@repo/ui/src/TrafficData";
import {
  DataType,
  NetworkGenerationType,
  PER_DAY_5G,
} from "@repo/ui/src/constants/plans";
import type { IDiscount } from "@repo/ui/src/interfaces/ICoupon";
import type { IPlan, IPlans } from "@repo/ui/src/interfaces/IPlan";

import { countryAlias, reverseKebabCase } from "@/utils";
import { getCDNUrl } from "@/utils";
import { AGENT } from "@/utils/agent-handler";
import { Currency } from "@/utils/currency";

import { ApiService } from "@/api";

import type { INetwork } from "@/interfaces/INetwork";

import { useOrderStore } from "@/store";
import { useProfile } from "@/store/UserStore";

async function getQuotationAPI(planIds: any[], coupon: string) {
  try {
    const response = await ApiService.getQuotation({
      products: planIds,
      couponId: coupon,
    });

    return {
      data: response.data?.data,
    };
  } catch (err) {
    console.log(err);
    return {
      discountedPrice: null,
    };
  }
}

export default function PlanList({
  props,
  isNeedRegistration,
  isLGU,
  country,
}: {
  props: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  };
  isNeedRegistration?: boolean;
  isLGU?: boolean;
  country?: string;
}) {
  const router = useRouter();
  const profile = useProfile((s) => s.profile);
  const searchParams = useSearchParams();
  const agent =
    searchParams.has("via") && searchParams.get("via")
      ? searchParams.get("via")
      : null;
  const setSelectedPlan = useOrderStore((state) => state.setSelectedPlan);
  const setSelectedPlanType = useOrderStore(
    (state) => state.setSelectedPlanType
  );
  const [selectedData, setSelectedData] = useState<string>();
  const [discount, setDiscount] = useState<IDiscount>();
  const [activePlanList, setActivePlanList] = useState(
    props.plans?.[DataType.PER_DAY] ? DataType.PER_DAY : DataType.FIXED_DAY
  );
  const allow5GActivePlanList =
    country === "japan" && activePlanList === PER_DAY_5G;

  const separateDataPlans = (dataType: string) => {
    try {
      let organizedPlans: IPlans[] = [];
      const originalPlans = props.plans?.[dataType]?.filter((plan: any) => {
        if (
          plan?.country?.name === "japan" &&
          plan?.network?.type === "ROAMING"
        ) {
          return false;
        }
        return true;
      });

      for (let x = 0; x < originalPlans?.length || 0; x += 1) {
        const itemIndex = organizedPlans.findIndex(
          (item: IPlans) =>
            item.dataId === originalPlans[x].dataId ||
            (originalPlans[x].name === "unlimited" &&
              item.dataId === originalPlans[x].name)
        );

        if (itemIndex > -1) {
          organizedPlans[itemIndex].plans.push(originalPlans[x]);
        } else {
          organizedPlans.push({
            dataId:
              originalPlans[x].name === "unlimited"
                ? originalPlans[x].name
                : originalPlans[x].dataId,
            validityDays: originalPlans[x].validityDays,
            plans: [originalPlans[x]],
          });
        }
      }

      return organizedPlans;
    } catch (err) {
      console.log(err);
      return [];
    }
  };

  const separatePlansBy5G = (plans: IPlans[]) => {
    const [firstPlan] = plans;
    try {
      const separatedPlansBy5G = firstPlan?.plans.filter(
        (plan) =>
          plan.network?.networkGeneration === NetworkGenerationType.FIVE_G
      );
      return [
        {
          ...firstPlan,
          plans: separatedPlansBy5G,
        },
      ];
    } catch (err) {
      console.log(err);
      return [];
    }
  };

  const separatePlansBy4G = (plans: IPlans[]) => {
    const [firstPlan] = plans;
    try {
      const separatedPlansBy4G = firstPlan?.plans.filter(
        (plan) =>
          plan.network?.networkGeneration !== NetworkGenerationType.FIVE_G
      );
      return [
        {
          ...firstPlan,
          plans: separatedPlansBy4G,
        },
      ];
    } catch (err) {
      console.log(err);
      return [];
    }
  };

  const fixedDayPlansMemo = useMemo(() => {
    return separateDataPlans(DataType.FIXED_DAY);
  }, [props.plans]);

  const perDayPlansMemo = useMemo(() => {
    const separatePlans = separateDataPlans(DataType.PER_DAY);
    return separatePlansBy4G(separatePlans);
  }, [props.plans]);

  const perDayPlans5GMemo = useMemo(() => {
    const separatePlans = separateDataPlans(DataType.PER_DAY);

    return separatePlansBy5G(separatePlans);
  }, [props.plans]);

  const countryName = (
    <Trans i18nKey={`countries:${country}`}>
      {countryAlias(reverseKebabCase(country))}
    </Trans>
  );
  const planForm = useCallback(
    () => (
      <PlanForm
        allow5GActivePlanList={allow5GActivePlanList}
        plans={
          allow5GActivePlanList
            ? perDayPlans5GMemo
            : activePlanList === DataType.PER_DAY
              ? perDayPlansMemo
              : fixedDayPlansMemo
        }
        setSelectedData={setSelectedData}
        selectPlanEvent={selectPlanEvent}
        activePlanList={activePlanList}
        formatPrice={Currency.formatToSelectedNoCode}
        selectedCode={Currency.getSelectedCurrency()}
        discount={discount}
        country={countryName}
      />
    ),
    [activePlanList, fixedDayPlansMemo, perDayPlansMemo, discount]
  );

  const trafficeData = useCallback(
    () => (
      <Box className="traffic-card-container mt-4">
        <TrafficData
          dataLimit={(selectedData as string).replaceAll(" ", "")}
          getCDNUrl={getCDNUrl}
        />
      </Box>
    ),
    [selectedData]
  );

  const selectPlanEvent = async (planId: string, getQuotation?: boolean) => {
    let selectedPlan: IPlan | undefined = undefined;

    if (allow5GActivePlanList) {
      selectedPlan = props.plans?.[DataType.PER_DAY]?.find(
        (plan) => plan.planId === planId
      );
    } else {
      selectedPlan = props.plans?.[activePlanList.toUpperCase()]?.find(
        (plan) => plan.planId === planId
      );
    }

    if (getQuotation) {
      if (selectedPlan?.id && agent) {
        const res = await getQuotationAPI(
          [
            {
              optionId: `${selectedPlan?.id}`,
            },
          ],
          agent as string
        );
        setDiscount(res?.data);
      }
    } else {
      if (selectedPlan) {
        setSelectedPlan(selectedPlan);
      }

      if (
        isLGU &&
        !profile?.email &&
        searchParams.get("via") === "KOREAFREECAMPAIGN"
      ) {
        router.push(
          `/campaign/airtrip/form?via=KOREAFREECAMPAIGN&country=${selectedPlan?.country?.name}`
        );
        sessionStorage.setItem(
          "returnUrl",
          `/checkout/plan/${selectedPlan?.id}?country=${selectedPlan?.country?.name}`
        );
      } else {
        router.push(
          `/checkout/plan/${selectedPlan?.id}?country=${selectedPlan?.country?.name}`
        );
      }
    }
  };

  useEffect(() => {
    setSelectedPlanType(activePlanList);
  }, [activePlanList]);

  return (
    <Box className="relative">
      {/* {country === "japan" && perDayPlans5GMemo?.length !== 0 && (
        <SegmentedControl
          w={"100%"}
          radius="lg"
          defaultValue={activePlanList}
          value={activePlanList}
          onChange={setActivePlanList}
          color="app-pink.4"
          classNames={{
            root: `p-0 my-1 bg-white `,
            control: `rounded border border-gray-200 ${styles["mantine-SegmentedControl-control"]}`,
            indicator: "rounded-none",
            label:
              "text-primary data-[active=true]:text-white data-[disabled=true]:text-white data-[disabled=true]:bg-gray-300",
          }}
          data={[
            {
              value: PER_DAY_5G,
              label: (
                <Title order={2} className="text-base">
                  無制限プラン 5G
                </Title>
              ),
            },
            {
              disabled: true,
              value: "",
              label: (
                <Title order={2} className="text-base">
                  データプラン[近日公開!]
                </Title>
              ),
            },
          ]}
        />
      )} */}
      {fixedDayPlansMemo?.length !== 0 && perDayPlansMemo?.length !== 0 && (
        <SegmentedControl
          w={"100%"}
          radius="lg"
          defaultValue={activePlanList}
          value={activePlanList}
          onChange={setActivePlanList}
          color="app-pink.4"
          classNames={{
            root: "p-0 my-1 bg-white",
            control: "rounded border border-gray-200",
            indicator: "rounded",
            label: "text-primary data-[active=true]:text-white",
          }}
          data={[
            {
              value: DataType.PER_DAY,
              label: (
                <Title order={2} className="text-base">
                  無制限プラン
                </Title>
              ),
            },
            {
              value: DataType.FIXED_DAY,
              label: (
                <Title order={2} className="text-base">
                  データプラン
                </Title>
              ),
            },
          ]}
        />
      )}

      {planForm()}
      {selectedData &&
        activePlanList === DataType.FIXED_DAY &&
        selectedData !== "unlimited" &&
        trafficeData()}
    </Box>
  );
}
