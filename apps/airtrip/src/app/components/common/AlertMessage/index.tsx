"use client";
/*
 * import modules and libraries
 */
import { AlertMessageType, useMessageStore } from "@/store/MessageStore";
import { IconAlertCircle, IconCircleCheck } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

/*
 * import components
 */
import { Alert } from "@mantine/core";

export default function AlertMessage() {
  const [message, setMessage, messageType] = useMessageStore((s) => [
    s.message,
    s.setMessage,
    s.messageType,
  ]);
  const { t } = useTranslation();
  return (
    <>
      {message && (
        <Alert
          icon={
            messageType === AlertMessageType.ERROR ? (
              <IconAlertCircle size="1rem" />
            ) : (
              <IconCircleCheck size={"1rem"} />
            )
          }
          onClose={() => setMessage("")}
          closeButtonLabel="Close alert"
          title={
            messageType === AlertMessageType.ERROR
              ? "入力内容に誤りがあります。"
              : "Success"
          }
          color={messageType === AlertMessageType.ERROR ? "red" : "green"}
          withCloseButton
        >
          {message === "Email address already exists."
            ? t("signup:emailalreadyexists")
            : message}
        </Alert>
      )}
    </>
  );
}
