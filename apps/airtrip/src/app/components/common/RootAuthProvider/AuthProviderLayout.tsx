
"use client";

/*
 * import modules and libraries
 */
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "react-query";
/*
 * import constants and helpers
 */
import { AUTH_TOKEN_NAME } from "@/app/constants";
import { useProfile } from "@/store/UserStore";
import Storage from "@/utils/storage";
/*
 * import api
 */
import { ApiService } from "@/api";
/*
 * import interfaces
 */
import type { IProfile } from "@/interfaces/IProfile";

interface IProps {
  children?: React.ReactNode | JSX.Element | any;
  skipLoginRedirect?: boolean
}

{/* TODO: refactor this code */}
const AuthProviderLayout: React.FC<IProps> = ({ children, skipLoginRedirect }) => {
  const setPorfile = useProfile((state) => state.setPorfile);
  const profile = useProfile((state) => state.profile);
  const router = useRouter();

  const profileQuery = useQuery<IProfile>(
    "profileQuery",
    async () => {
      try {
        let firebaseToken;
        try {
          // if (router.pathname.includes("app") && process.env.NEXT_PUBLIC_IS_PUSH_NOTIFICAION === "true") {
          // firebaseToken = await getFirebaseCurrentToken();
          // }
        } catch {
          firebaseToken = null
        }

        const { data } = await ApiService.getProfile({
          token: firebaseToken || undefined
        });
        return data?.data;
      } catch (err: unknown) {
        if (!skipLoginRedirect)
          router.push("/auth/signin");
        // @ts-expect-error
        if (err.response.status) {
          Storage.getInstance().set(AUTH_TOKEN_NAME, "")
        }
      }
    },
    {
      enabled: !profile?.id,
    }
  );

  useEffect(() => {
    if (profile?.firstName) return;
    //@ts-ignore
    setPorfile(profileQuery.data);
  }, [profileQuery]);

  if (skipLoginRedirect) return children
  return children
};

export default AuthProviderLayout;
