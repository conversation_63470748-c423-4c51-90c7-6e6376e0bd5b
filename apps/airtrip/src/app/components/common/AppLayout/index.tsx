"use client";

/*
 * import modules and libraries
 */
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

import { LoadingOverlay, em } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";

import { useTranslation } from "react-i18next";

/*
 * import components
 */
import Layout from "@repo/ui/src/common/Layout";
import CompatibilityCheck from "@repo/ui/src/modals/CompatibilityCheck";

import Aside from "@/app/components/common/AppLayout/Aside";
import Footer from "@/app/components/common/AppLayout/Footer";
import Header from "@/app/components/common/AppLayout/Header";
import Logo from "@/app/components/common/AppLayout/Header/Logo";
import HeaderBanner from "@/app/components/common/AppLayout/HeaderBanner";

import Storage from "@/utils/storage";

/*
 * import interfaces
 */
import { IGetReviewsSummary } from "@/interfaces/IReviews";

/*
 * import constants and utils
 */
import { useOrderStore } from "@/store";
import { useMessageStore } from "@/store/MessageStore";

export default function AppLayout({
  children,
  contentBg,
  ctaButton,
  noShadowHeader,
  isInvertColorHeader,
  isShowCountryButton,
  ukomiSumary,
  isLoading,
  isLogoTextTitle,
}: {
  children: React.ReactNode;
  ctaButton?: React.ReactNode;
  contentBg?: string;
  noShadowHeader?: boolean;
  isInvertColorHeader?: boolean;
  isShowCountryButton?: boolean;
  ukomiSumary?: IGetReviewsSummary;
  isLoading?: boolean;
  isLogoTextTitle?: boolean;
}) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const utmAgent =
    searchParams.has("utm_source") && searchParams.get("utm_source")
      ? searchParams.get("utm_source")
      : null;
  const router = useRouter();

  const { t, i18n } = useTranslation();
  const isGlobalLoading = useMessageStore((state) => state.isLoading);
  const isShowDrawer: boolean = useOrderStore((state) => state.isShowDrawer);
  const toggleDrawer: () => void = useOrderStore((state) => state.toggleDrawer);
  const closeDrawer: () => void = useOrderStore((state) => state.closeDrawer);
  const isCompatibilityModalOpen: boolean = useOrderStore(
    (state) => state.isCompatibilityModalOpen
  );
  const isReviewDrawerOpen: boolean = useOrderStore(
    (state) => state.isReviewDrawerOpen
  );
  const toggleCompatibilityModalOpen: () => void = useOrderStore(
    (state) => state.toggleCompatibilityModalOpen
  );

  // TODO: can be move to utils
  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });

  useEffect(() => {
    closeDrawer();
  }, [pathname]);

  useEffect(() => {
    const currency = Storage.getInstance().get("currency");
    const locale = Storage.getInstance().get("locale");

    if (!currency && i18n.language === "jp") {
      Storage.getInstance().set(
        "currency",
        {
          code: "JPY",
          sign: "¥",
          name: "JPY - Japanese Yen",
        } as any,
        { storageType: "cookie" }
      );
    } else if (!currency) {
      Storage.getInstance().set(
        "currency",
        {
          code: "USD",
          sign: "$",
          name: "USD - US Dollar",
        } as any,
        { storageType: "cookie" }
      );
    }
    if (!locale) {
      Storage.getInstance().set("locale", i18n.language);
    }
  }, []);

  useEffect(() => {
    const scriptId = "ukommi-reviews";

    if (!document.getElementById(scriptId)) {
      (function u() {
        var u = document.createElement("script");
        (u.type = "text/javascript"),
          (u.async = true),
          (u.id = "ukommi-reviews"),
          (u.src =
            "//api.u-komi.com/760019e323d255f9aab5fc769cdffed1aa5765f66808288933e34c66ca381e43/widget.js");
        var k = document.getElementsByTagName("script")[0];
        //@ts-ignore
        k.parentNode.insertBefore(u, k);
      })();
    }

    return () => {
      const script = document.getElementById(scriptId);
      if (script) script.remove();
    };
  }, [isReviewDrawerOpen]);

  useEffect(() => {
    if (utmAgent) {
      sessionStorage.setItem("utm_source", utmAgent);
    } else if (!utmAgent && searchParams.has("utm_source")) {
      sessionStorage.removeItem("utm_source");
    } else {
      const utmAgent = sessionStorage.getItem("utm_source");
      if (utmAgent) {
        router.replace(`${pathname}?utm_source=${utmAgent}`);
      }
    }
  }, []);

  return (
    <>
      <Layout
        headerBanner={isMobileSize ? <HeaderBanner /> : null}
        header={
          <Header
            isInvertColorHeader={isInvertColorHeader}
            phoneNumber="050-1807‐5202"
            businessHours="営業時間：8:00～23:00"
            isShowCountryButton={isShowCountryButton}
            isLogoTextTitle={isLogoTextTitle}
          />
        }
        body={
          <>
            <LoadingOverlay
              visible={isLoading || isGlobalLoading}
              overlayProps={{ pos: "fixed" }}
              loaderProps={{
                pos: "fixed",
              }}
            />
            {children}
          </>
        }
        footer={<Footer />}
        aside={{
          body: <Aside />,
          logo: <Logo noText={true} />,
        }}
        ctaButton={ctaButton}
        headerHeight={isMobileSize ? 88 : 63}
        headerMaxWidth={1536} // GoroNavi max width
        noShadowHeader={noShadowHeader}
        contentBg={contentBg}
        isShowDrawer={isShowDrawer}
        toggleDrawer={toggleDrawer}
        closeDrawer={closeDrawer}
        isInvertColorHeader={isInvertColorHeader}
        lineUrl={/\/checkout\//i.test(pathname) ? "" : "https://lin.ee/fNSy5CJ"}
      />
      <CompatibilityCheck
        opened={isCompatibilityModalOpen}
        close={toggleCompatibilityModalOpen}
      />
    </>
  );
}
