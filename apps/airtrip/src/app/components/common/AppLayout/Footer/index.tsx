'use client';

/*
 * import modules and libraries
 */
import Link from "next/link";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import {
  Stack,
  Box,
  Grid,
  GridCol,
  Text,
  Button,
  rem,
} from "@mantine/core";
/*
 * import constants and helpers
 */
import { getCDNUrl } from '@/utils';

const LINKS = [
  {
    url: "https://www.gmobile.biz/terms-of-service/?value=esim",
    label: "home:termsandconditions",
    isInAppLink: false, 
  },
  {
    url: "/terms-for-membership?source=footer",
    label: "home:termsformembership",
    isInAppLink: true, 
  },
  {
    url: "https://www.inbound-platform.com/privacy/",
    label: "home:privacypolicy",
    isInAppLink: false, 
  },
  {
    url: "https://www.gmobile.biz/law/?value=esim",
    label: "home:legalnotation",
    isInAppLink: false, 
  },
  {
    url: "https://www.inbound-platform.com/company/",
    label: "home:aboutus",
    isInAppLink: false, 
  },
  {
    url: "/support/help",
    label: "home:help",
    isInAppLink: true, 
  },
  {
    url: "/support/contactus",
    label: "home:contactus",
    isInAppLink: true, 
  },
  {
    url: "/faq",
    label: "common:navbar.faq.label",
    isInAppLink: true, 
  },
];

export default function Footer() {
  const { t } = useTranslation();
  
  return (
    <Stack className="bg-defaultGrey" gap={rem(28)}>
      <Grid maw={rem(1080)} m="0 auto" className="px-4 pt-8">
        {LINKS.map((item) => (
          <GridCol
            key={`footer-${item.url}`}
            span={{ base: 12, xs: 12, md: 4, lg: 3 }}
          >
            <Button
              size="sm"
              component={Link}
              href={item.url}
              target={!item.isInAppLink ? "_blank" : ""}
              variant="transparent"
              classNames={{
                root: "px-0",
                label: "text-white font-normal",
              }}
            >
              {t(item.label)}
            </Button>
          </GridCol>
        ))}
      </Grid>
      <Image
        src={getCDNUrl("/assets/payment.png")}
        alt="payment"
        height={42}
        width={408}
        className="mx-auto"
      />
      <Stack align="center" gap={0}>
        <Text className="text-center text-sm text-white">
          {t("common:company-name")}
        </Text>
        <Text className="text-center text-sm text-white">
          {t("common:division-name")}
        </Text>
        <Text className="text-center text-sm text-white">
          {t("common:company-address")}
        </Text>
      </Stack>
      <Text className="text-center text-sm text-white">
        エアトリWiFiはグロモバが提供するサービスです。<br />
        また、「エアトリ®」は株式会社エアトリの登録商標（登録第5898176号）です。
      </Text>
      <Box className="text-center text-white text-sm py-0.5 pb-12">
        © 2025 Inbound Platform Corp.
      </Box>
    </Stack>
  );
}