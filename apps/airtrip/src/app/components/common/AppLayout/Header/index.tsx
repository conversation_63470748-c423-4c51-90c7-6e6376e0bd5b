"use client";

import Link from "next/link";
import { useCallback } from "react";

import { Box, Button, Group, Space } from "@mantine/core";

import {
  IconFolder,
  IconInfoCircle,
  IconPhone,
  IconUserCircle,
} from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import {
  NavbarCart,
  useDeviceQuery,
} from "@gmesim/cart-package/components/NavbarCart";

import Logo from "@/app/components/common/AppLayout/Header/Logo";

import { useOrderStore } from "@/store";
import { useProfile } from "@/store/UserStore";

import styles from "./Header.module.css";

let LINKS: {
  url: string;
  label: string;
  icon: React.ReactNode;
}[] = [
  {
    url: "/app",
    label: "common:navbar.myesim.label",
    icon: <IconFolder />,
  },
  {
    url: "/faq",
    label: "common:navbar.faq.label",
    icon: <IconInfoCircle />,
  },
];

export default function Header({
  phoneNumber,
  businessHours,
  isInvertColorHeader,
  isShowCountryButton,
  isLogoTextTitle,
}: {
  phoneNumber?: string;
  businessHours?: string;
  isInvertColorHeader?: boolean;
  isShowCountryButton?: boolean;
  isLogoTextTitle?: boolean;
}) {
  const { t } = useTranslation();
  const profile = useProfile((store) => store.profile);
  const toggleDrawer: () => void = useOrderStore((state) => state.toggleDrawer);
  const [isMobile] = useDeviceQuery();

  const LinksList = useCallback(
    () => (
      <Group className="hidden lg:flex">
        {LINKS.map((item) => (
          <Button
            size="md"
            key={`header-${item.label}`}
            component={Link}
            href={item.url}
            variant="transparent"
            leftSection={item.icon}
            classNames={{
              root: `${
                isInvertColorHeader
                  ? "text-black md:text-white hover:border-gray-300 rounded-full"
                  : "text-black hover:!text-primary"
              } px-2`,
              section: "mr-1 me-0",
            }}
          >
            {t(item.label)}
          </Button>
        ))}
        <Button
          size="md"
          variant="outline"
          leftSection={<IconUserCircle />}
          onClick={toggleDrawer}
          classNames={{
            root: `${
              isInvertColorHeader
                ? "text-black md:text-white hover:border-gray-300"
                : "text-black hover:!text-primary border-gray-300"
            } hover:bg-transparent px-2 rounded-full`,
            section: "mr-1 me-0",
          }}
        >
          {t("common:navbar.account.label")}
        </Button>
      </Group>
    ),
    [profile]
  );

  return (
    <Group h="100%" justify="space-between" className="gap-0">
      <Logo
        isInvertColorHeader={isInvertColorHeader}
        isTitle={isLogoTextTitle}
      />
      <Space w="xs" />
      {/* {isShowCountryButton && (
        <Button
          component={Link}
          passHref
          variant="outline"
          color="app-pink.4"
          href={pathname?.includes("region/japan") ? "/" : "/region/japan"}
          className="ml-auto rounded-md px-2 no-underline lg:hidden"
        >
          {pathname?.includes("region/japan") ? "海外はこちら" : "国内はこちら"}
        </Button>
      )} */}
      {phoneNumber && businessHours && (
        <Box
          className={`${styles["hide-phone-number"]} flex items-center lg:hidden`}
        >
          {phoneNumber && (
            <div className="flex flex-col items-end bg-white px-2 py-1">
              <a href={`tel:${phoneNumber}`} className="no-underline">
                <p className="text-phone text-primary relative flex text-xs font-semibold hover:underline">
                  <IconPhone size={20} />
                  {phoneNumber}
                </p>
              </a>
              <p className="text-hour text-primary">{businessHours}</p>
            </div>
          )}
        </Box>
      )}
      <Group>
        <LinksList />
        <Box mr={"md"}>
          <NavbarCart
            theme={
              isInvertColorHeader && !isMobile ? "airtripLanding" : undefined
            }
          />
        </Box>
      </Group>
    </Group>
  );
}
