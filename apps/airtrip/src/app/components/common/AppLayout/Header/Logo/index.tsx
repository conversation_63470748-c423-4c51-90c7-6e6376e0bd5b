"use client";

/*
 * import modules and libraries
 */
import Image from "next/image";
import Link from "next/link";

import { Group, Text, Title } from "@mantine/core";

/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";

export default function Logo({
  noText,
  isInvertColorHeader,
  logoText = "eSIM使うなら、エアトリeSIM",
  isTitle = true,
}: {
  noText?: boolean;
  isInvertColorHeader?: boolean;
  logoText?: string;
  isTitle?: boolean;
}) {
  return (
    <Group>
      <Link href="/">
        <picture>
          <source
            media="(max-width: 767px)"
            srcSet={getCDNUrl("/assets/hero/airtrip.webp")}
          />
          <source
            media="(min-width: 768px)"
            srcSet={getCDNUrl(
              isInvertColorHeader
                ? "/assets/hero/airtrip-white.webp"
                : "/assets/hero/airtrip.webp"
            )}
          />
          <Image
            alt="AirTrip logo"
            src={getCDNUrl("/assets/hero/airtrip.webp")}
            height={40}
            width={100}
            quality={100}
            unoptimized
          />
        </picture>
      </Link>
      {!noText &&
        (isTitle ? (
          <Title
            order={1}
            className={`${isInvertColorHeader ? "text-primary md:text-white" : "text-primary"} hidden text-xs font-normal md:inline-block md:text-base`}
          >
            {logoText}
          </Title>
        ) : (
          <Text
            className={`${isInvertColorHeader ? "text-primary md:text-white" : "text-primary"} hidden text-xs font-normal md:inline-block md:text-base`}
          >
            {logoText}
          </Text>
        ))}
    </Group>
  );
}
