'use client';

/*
 * import modules and libraries
 */
import Link from "next/link";
import {
  Stack,
  Button,
  Divider,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import {
  IconFolder,
  IconInfoCircle,
  IconUserCircle,
  IconLogout2,
  IconWorld,
  IconWifi,
} from "@tabler/icons-react";
/*
 * import constants and helpers
 */
import { AUTH_TOKEN_NAME } from "@/app/constants";
import { useProfile } from "@/store/UserStore";
import Storage from "@/utils/storage";
import { withBasePath } from "@/utils";
/*
 * import components
 */
import CTAButton from "@/app/components/common/CTAButton";

interface ILink {
  url: string;
  label: string;
  icon: React.ReactNode;
};

const LINKS: ILink[] = [
  {
    url: "/app",
    label: "common:navbar.myesim.label",
    icon: <IconFolder />
  },
  {
    url: "/faq",
    label: "common:navbar.faq.label",
    icon: <IconInfoCircle />
  }
];

const LINKS2: ILink[] = [
  {
    url: "/destination",
    label: "common:navbar.destination.label",
    icon: <IconWorld />,
  },
  {
    url: "https://wifi.airtrip.jp/",
    label: "common:navbar.pocketwifi.label",
    icon: <IconWifi />,
  },
];

export const getFlagCode = (lang: string) => {
  switch (lang) {
    case "en":
      return "us";
    case "vi":
      return "vn";
    default:
      return lang;
  }
};

export default function Aside() {
  const { t, i18n } = useTranslation();
  const profile = useProfile((store) => store.profile);

  let navLinks: ILink[];

  if (profile) {
    navLinks = [
      ...LINKS,
      {
        url: "/app/account/profile",
        label: "common:accountinformation",
        icon: <IconUserCircle />
      },
      ...LINKS2,
    ];
  } else {
    navLinks = [
      ...LINKS,
      {
        url: "/auth/signin",
        label: "common:btn.login",
        icon: <IconUserCircle />
      },
      ...LINKS2,
    ];
  }

  return (
    <>
      <Stack className="gap-2 mt-4 p-4">
        {navLinks.map((item) => (
          <MenuButton
            key={`aside-${item.label}`}
            title={t(item.label)}
            icon={item.icon}
            url={item.url}
          />
        ))}

        {profile && (
          <Button
            variant="subtle"
            fullWidth
            leftSection={<IconLogout2 />}
            size="md"
            classNames={{
              root: "text-black md:hover:text-primary px-0",
              inner: "justify-start mb-2 px-0 font-semibold"
            }}
            onClick={() => {
              Storage.getInstance().set(AUTH_TOKEN_NAME, "");
              window.location.href = withBasePath(i18n.language);
            }}
          >
            {t("common:logout", "Log out") || "Logout"}
          </Button>
        )}
        <Divider />
        <div className="mt-4">
          <CTAButton />
        </div>
      </Stack>

    </>
  )
}

const MenuButton = ({
  title,
  icon,
  url,
}: {
  title: string;
  icon: React.ReactNode;
  url: string;
}) => {
  const { i18n } = useTranslation();

  return (
    <Button
      variant="subtle"
      component={Link}
      href={url}
      fullWidth
      locale={i18n.language}
      leftSection={icon}
      size="md"
      classNames={{
        root: "text-black md:hover:text-primary px-0",
        inner: "justify-start mb-2 px-0 font-semibold"
      }}
    >
      {title}
    </Button>
  );
};