import { Box, BoxProps } from "@mantine/core";
import { ReactNode } from "react";
import styles from './stickyContainer.module.css'

const StickyContainer = (props: { children: ReactNode } & BoxProps) => {
  return (
    <Box
      className={styles["root"]}
      pos="sticky"
      bg="white"
      bottom={0}
      p="sm"
      {...props}
    >
      {props.children}
    </Box>
  );
};
export default StickyContainer;
