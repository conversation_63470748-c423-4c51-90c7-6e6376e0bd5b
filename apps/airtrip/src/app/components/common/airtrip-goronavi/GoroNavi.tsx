"use client";

/*
 * import modules and libraries
 */
import {
  <PERSON>u,
  MenuTarget,
  MenuDropdown,
} from "@mantine/core";
import { IconCaretDownFilled } from "@tabler/icons-react";
/*
 * import icons
 */
import Plus from "./icons/icon_airtrip_plus.svg";
import IconPlane from "./icons/icon_flight.svg";
import IconPlaneGlobe from "./icons/icon_global_flight.svg";
import IconPlaneHotel from "./icons/flight+hotel.svg";
import IconPlaneGlobeBuilding from "./icons/global+flight.svg";
import IconWork from "./icons/icon_work.svg";
import IconGlobalWork from "./icons/icon_global_tour.svg";
import IconHotel from "./icons/icon_dom_hotel.svg";
import IconOffice from "./icons/icon_Business.svg";
import IconShinkansen from "./icons/icon_shinkansen.svg";
import IconCar from "./icons/icon_DirectionsCar.svg";
import IconBus from "./icons/icon_directions_bus.svg";
import IconWifi from "./icons/icon_wifi.svg";
import IconActivity from "./icons/icon_activity.svg";
/*
 * import styles
 */
import styles from "./GoroNavi.module.css";

const GORONAVI_ITEMS = [
  {
    title: "国内航空券",
    href: "https://www.airtrip.jp/?source=airtrip.jp",
    icon: <IconPlane className="mx-auto" />,
    isPlus: false,
    subItems: [],
  },
  {
    title: "海外航空券",
    href: "https://www.skygate.co.jp/",
    icon: <IconPlaneGlobe className="mx-auto" />,
    isPlus: false,
    subItems: [],
  },
  {
    title: "国内航空券＋ホテル",
    href: "https://domdp.airtrip.jp/?source=globalnavi",
    icon: <IconPlaneHotel className="mx-auto" />,
    isPlus: true,
    subItems: [],
  },
  {
    title: "海外航空券＋ホテル",
    href: "https://www.skygate.co.jp/dp/",
    icon: <IconPlaneGlobeBuilding className="mx-auto" />,
    isPlus: true,
    subItems: [],
  },
  {
    title: "国内ツアー",
    href: "",
    icon: <IconWork className="mx-auto" />,
    isPlus: false,
    subItems: [
      {
        href: "https://japantour.airtrip.jp",
        text: "国内ツアー",
      },
      {
        href: "https://japantour.airtrip.jp/special/oka/",
        text: "沖縄ツアー",
      },
      {
        href: "https://japantour.airtrip.jp/special/hok/",
        text: "北海道ツアー",
      },
    ],
  },
  {
    title: "海外ツアー",
    href: "",
    icon: <IconGlobalWork className="mx-auto" />,
    isPlus: false,
    subItems: [
      {
        href: "https://www.hankyu-travel.com/airtrip/tour/",
        text: "海外ツアー",
      },
      {
        href: "https://www.skygate.co.jp/pickup/hawaii/",
        text: "ハワイ特集",
      },
    ],
  },
  {
    title: "国内ホテル",
    href: "",
    icon: <IconHotel className="mx-auto" />,
    isPlus: false,
    subItems: [
      {
        href: "https://domhotel.airtrip.jp/",
        text: "国内ホテル",
      },
      {
        href: "https://domhotel.airtrip.jp/leisure/",
        text: "テーマパーク",
      },
    ],
  },
  {
    title: "海外ホテル",
    href: "https://www.skygate.co.jp/hotel/",
    icon: <IconOffice className="mx-auto" />,
    isPlus: false,
    subItems: [],
  },
  {
    title: "新幹線",
    href: "",
    icon: <IconShinkansen className="mx-auto" />,
    isPlus: false,
    subItems: [
      {
        href: "https://train.airtrip.jp",
        text: "新幹線チケット",
      },
      {
        href: "https://www.airtrip.jp/traindp/",
        text: "新幹線＋ホテル",
      },
    ],
  },
  {
    title: "レンタカー",
    href: "https://rentacar.airtrip.jp",
    icon: <IconCar className="mx-auto" />,
    isPlus: false,
    subItems: [],
  },
  {
    title: "夜行・高速バス",
    href: "https://bus.airtrip.jp/",
    icon: <IconBus className="mx-auto" />,
    isPlus: false,
    subItems: [],
  },
  {
    title: "アクティビティ",
    href: "https://activity.airtrip.jp/",
    icon: <IconActivity className="mx-auto" />,
    isPlus: false,
    subItems: [],
  },
  {
    title: "WiFi・eSIM",
    href: "",
    icon: <IconWifi className="mx-auto" />,
    isPlus: false,
    subItems: [
      {
        href: "https://wifi.airtrip.jp/",
        text: "海外Wi-Fi",
      },
      {
        href: "https://esim.airtrip.jp/",
        text: "海外eSIM",
      },
      {
        href: "https://wifi.airtrip.jp/softbank",
        text: "国内Wi-Fi",
      },
      {
        href: "https://esim.airtrip.jp/region/japan",
        text: "国内eSIM",
      },
    ],
  },
];

export default function GoroNavi() {
  return (
    <div className={`bg-white shadow-md ${styles["goronavi-container"]}`}>
      <ul
        className={`table w-full list-none whitespace-nowrap ${styles["goronavi"]}`}
      >
        {GORONAVI_ITEMS.map((item) => (
          <li
            key={item.title}
            className="table-cell relative align-bottom text-center hover:bg-primary hover:text-white text-goronaviInactive hover:fill-white fill-goronaviInactive"
          >
            {item.subItems.length === 0 ? (
              <a href={item.href} className="no-underline text-inherit">
                <div className="px-2 pb-2 text-xs  w-full h-full">
                  {item.isPlus && <Plus className="mx-auto my-1" />}
                  <div className="mb-1">{item.icon}</div>
                  {item.title}
                </div>
              </a>
            ) : (
              <Menu
                zIndex={999}
                width={150}
                shadow="md"
                trigger="hover"
                position="bottom-start"
                offset={0}
                radius={0}
              >
                <MenuTarget>
                  <div className="px-2 pb-2 text-xs  w-full h-full">
                    {item.isPlus && <Plus className="mx-auto my-1" />}
                    <div className="mb-1">{item.icon}</div>
                    <span className="flex items-center justify-center">
                      {item.title}
                      <IconCaretDownFilled size={12} className="ml-1" />
                    </span>
                  </div>
                </MenuTarget>
                <MenuDropdown>
                  {item.subItems.map((subItem) => (
                    <a
                      key={subItem.href}
                      href={subItem.href}
                      className="no-underline text-black"
                    >
                      <div className="text-defaultGrey text-left text-base px-2 py-2 hover:bg-neutral-100">
                        {subItem.text}
                      </div>
                    </a>
                  ))}
                </MenuDropdown>
              </Menu>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}
