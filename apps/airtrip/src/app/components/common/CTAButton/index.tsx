"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";

import { Button, Image, Text } from "@mantine/core";

import { useTranslation } from "react-i18next";

import { getCDNUrl } from "@/utils";

/*
 * import constants and utils
 */
import { useOrderStore } from "@/store";

const CTAButton = ({
  compact,
  customUrl,
  notCenter,
  customText,
  noIcon = false,
}: {
  customUrl?: string;
  compact?: boolean;
  notCenter?: boolean;
  customText?: string;
  noIcon?: boolean;
}) => {
  const { t } = useTranslation();
  const closeDrawer: () => void = useOrderStore((state) => state.closeDrawer);

  return (
    <Button
      component={Link}
      href={customUrl ? customUrl : "/#select-plan"}
      passHref
      fullWidth
      size={compact ? "md" : "lg"}
      radius="md"
      variant="filled"
      color="app-action.4"
      onClick={closeDrawer}
      className={`${compact ? "max-w-sm" : ""} ${notCenter ? "" : "mx-auto"}`}
    >
      {!noIcon && (
        <Image w={37} h={35} src={getCDNUrl("/assets/gm-rates.svg")} />
      )}
      <Text className="ml-4 font-bold text-white">
        {customText ? customText : t("common:ctabutton")}
      </Text>
    </Button>
  );
};
export default CTAButton;
