"use client";

/*
 * import modules and libraries
 */
import { useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import Lottie from "lottie-react";
import { Button, Flex, Text, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";
/*
 * import components
 */
import SectionContent from "@repo/ui/src/common/SectionContent";
/*
 * import constants and helpers
 */
import { useRegisterUser } from "@/store/UserStore";
import groovyWalkAnimation from "@/app/constants/lottie/lottie.json";
/*
 * import api
 */
import { ApiService } from "@/api";

export default function VerifyProcess() {
  const { t } = useTranslation();
  const email = useRegisterUser((s) => s.email);
  const router = useRouter();
  const lottieRef = useRef();

  useEffect(() => {
    if (!email) router.push("/");
  }, []);

  const handleResendEmail = useCallback(() => {
    ApiService.resendVerificationEmail(email)
      .then(() => {
        alert("認証メールを再送しました。");
      })
      .catch(() => {
        alert("問題が発生しました。もう一度お試しください。");
      });
  }, [email]);

  return (
    <SectionContent small>
      {/* TODO: refactor this code */}
      <Flex gap={15} direction={"column"} align={"center"} justify={"center"}>
        <Flex h={300}>
          <Lottie
            lottieRef={lottieRef.current}
            animationData={groovyWalkAnimation}
          />
        </Flex>
        <Title mt={-60} order={4}>
          {t("verify:verifyemail.title")}
        </Title>
        <Text
          lh={"21px"}
          size={"14px"}
          dangerouslySetInnerHTML={{
            __html: t("verify:emailnotification.txt", {
              email,
            }),
          }}
        ></Text>
        <Button onClick={handleResendEmail} color="app-dark" w={"100%"}>
          {t("verify:btn.resendemail")}
        </Button>
      </Flex>
    </SectionContent>
  );
}
