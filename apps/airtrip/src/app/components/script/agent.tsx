import Script from "next/script";
import { memo } from "react";
import { AGENT } from "@/utils/agent-handler";

interface Props {
  agent: string;
}
const AgentScript = (props: Props) => {
  const { agent } = props;

  const scriptByAgent = () => {
    switch (agent) {
      case AGENT.AIRTRIP_GMO:
        return <Script src="https://admane.jp/ad/js/lpjs2_2.js" async />;
      case AGENT.AIRTRIP_GMOCP:
        return <Script src="https://admane.jp/ad/js/lpjs2_2.js" async />;
      default:
        return <></>;
    }
  };

  return scriptByAgent();
};

export default memo(AgentScript);
