"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";
import { useTranslation } from "react-i18next";
import {
  Button,
  Card,
  Title,
  Text,
  Image,
  Stack,
} from "@mantine/core";
/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";
import { useOrderStore } from "@/store";

export default function CompatibleDevicesLink() {
  const { t } = useTranslation();
  const toggleCompatibilityModalOpen: () => void = useOrderStore((state) => (state.toggleCompatibilityModalOpen));
  
  return (
    <Card className="bg-secondary rounded-lg">
      <Stack>
        <Title
          className="font-bold text-center text-lg"
          order={2}
        >
          {t("contact:help.compatibility.title")}
        </Title>
        <Button
          size="lg"
          radius="md"
          variant="outline"
          color="app-pink.4"
          onClick={toggleCompatibilityModalOpen}
          bg="white"
          classNames={{
            root: "max-w-sm w-full mx-auto"
          }}
        >
          <Image w={32} src={getCDNUrl("/assets/gm-device.svg")} />
          <Text className="font-bold" c="black" ml={10}>
          {t("contact:help.compatibility.btn")}
          </Text>
        </Button>
      </Stack>
    </Card>
  );
}