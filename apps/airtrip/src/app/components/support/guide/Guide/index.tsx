"use client";
import { Box, Card, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";
import SetupGuide from "@repo/ui/src/SetupGuide";

export default function Guide() {
  const { t } = useTranslation();
  return (
    <Box w="100%" p={0}>
      <Card p={0} className="bg-secondary" radius="0.5rem">
        <Title
          mt="1rem"
          order={2}
          ta="center"
          size={15}
          lh={"unset"}
        >
          {t("compatibility:home.title")}
        </Title>
          <SetupGuide compact />
      </Card>
    </Box>
  );
}