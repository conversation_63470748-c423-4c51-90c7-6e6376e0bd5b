"use client";
// import FAQLink from "@/app/components/support/FAQButton";
import { Anchor, Box, Center, Text, Title } from "@mantine/core";
import Link from "next/link";
import { useTranslation } from "react-i18next";

export default function Finished() {
  const { t } = useTranslation();

  return (
    <Box mt="10" p="20">
      <Center>
        <Title
          order={1}
          className="text-center text-2xl md:text-3xl"
          dangerouslySetInnerHTML={{
            __html: t("contact:finished.title") as string,
          }}>
        </Title>
      </Center>
      <Center mt="15">
        <Text
            className="text-xs"
            dangerouslySetInnerHTML={{
            __html: t("contact:finished.description") as string,
            }}
        />
      </Center>
      <Center mt="20" mb="40">
        <Anchor
          size="md"
          component={Link}
          href={"/"}
          c="app-pink.4"
          underline="always"
          classNames={{
            root: "px-0",
          }}
        >
          {t("contact:finished.to-top")}
        </Anchor>
      </Center>
      {/* <FAQLink /> */}
    </Box>
  );
}