"use client";

import Link from "next/link";

import { Stack, Text, Title } from "@mantine/core";

import { Trans, useTranslation } from "react-i18next";

import SectionContent from "@repo/ui/src/common/SectionContent";

import CTAButton from "@/app/components/common/CTAButton";
import CompatibleDevicesLink from "@/app/components/support/CompatibleDevicesButton";

import Guide from "./guide/Guide";

export default function Help() {
  const { t } = useTranslation();
  return (
    <SectionContent noHeader>
      <Stack>
        <Title order={1} className="text-center font-bold" size="1.8rem">
          {t("help:setup.title")}{" "}
        </Title>
        <CompatibleDevicesLink />
        <Guide />
        <Text className="self-center">
          手動の設定方法は
          <Link href="/setup">こちら</Link>
        </Text>
        <Link className="self-center text-xl" href="/faq">
          <Trans i18nKey={"help:link.label"} />
        </Link>
        <CTAButton customText={t("help:button.label")} />
      </Stack>
    </SectionContent>
  );
}
