'use client';

/*
 * import modules and libraries
 */
import {
  useCallback,
} from "react";
import Image from "next/image";
import {
  ModalProps,
  Accordion,
  Text,
  Flex,
  Box,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconCheck } from "@tabler/icons-react";
/*
 * import component
 */
import ParentModal from "@repo/ui/src/common/ParentModal";
/*
 * import constants and helpers
 */
import { isDev, getCDNUrl, } from "@/utils";
import Storage from "@/utils/storage";

const LANGUAGES_LIST = {
  jp: "日本語",
  en: "英語",
  es: "スペイン語",
  fr: "フランス語",
  ph: "フィリピン語",
  vi: "ベトナム語",
};

export const getFlagCode = (lang: string) => {
  switch (lang) {
    case "en":
      return "us";
    case "vi":
      return "vn";
    default:
      return lang;
  }
};

const LanguageSelect = (props: {
  top?: string;
  hideHeaderBorder?: boolean;
  showOverlay?: boolean;
} & ModalProps) => {
  const { i18n } = useTranslation();

  // TODO: fix rehydration errors
  const handleSelect = useCallback((v: any) => {
    Storage.getInstance().set("locale", v);
    const basePath = !!process.env.NEXT_PUBLIC_BASE_PATH;
    if ((basePath && v === "jp") || (!basePath && v === "jp")) {
      window.location.href = isDev()
        ? "https://esim-dev.airtrip.jp/"
        : "https://esim.airtrip.jp/";
      return;
    }

    window.location.href = isDev()
      ? "http://localhost:3232/" + v
      : "https://esim.airtrip.jp/" + v;
    return;
    // props.onSelect?.(v)
  }, []);

  return (
    <ParentModal
      {...props}
    >
       <Accordion
        onChange={handleSelect}
        variant="contained"
        chevron={
          <Text c={"dimmed"} fw={900}>
            &gt;
          </Text>
        }
        disableChevronRotation
      >
        {Object.keys(LANGUAGES_LIST).map((lang, index) => (
          <Accordion.Item value={lang} key={index}>
            <Accordion.Control
              chevron={i18n.language === lang ? <IconCheck /> : " "}
            >
              <Flex align="center">
                <Box className="border">
                  <Image
                    alt={lang}
                    src={getCDNUrl(
                      `/assets/flags/svg/${getFlagCode(lang)}.svg`
                    )}
                    height={18}
                    width={24}
                  />
                </Box>
                {/* @ts-ignore */}
                <Text>{LANGUAGES_LIST[lang]}</Text>
              </Flex>
            </Accordion.Control>
          </Accordion.Item>
        ))}
      </Accordion>
    </ParentModal>
  );
};

export default LanguageSelect;
