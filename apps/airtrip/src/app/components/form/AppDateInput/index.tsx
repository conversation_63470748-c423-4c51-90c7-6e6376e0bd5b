"use client";

import 'dayjs/locale/ja';
import {
  DatesProvider,
  DatePickerInput,
  DatePickerInputProps,
} from '@mantine/dates';
import styles from './appDateInput.module.css'

const AppTextInput = (props: DatePickerInputProps) => {
  return (
    <DatesProvider settings={{ locale: 'ja' }}>
      {/* @ts-ignore */}
      <DatePickerInput
        classNames={{
          input: styles.input,
          label: styles.label,
          error: styles.error,
        }}
        valueFormat="YYYY/MM/DD"
        size="lg"
        {...props}
      />
    </DatesProvider>
  );
};
export default AppTextInput;
