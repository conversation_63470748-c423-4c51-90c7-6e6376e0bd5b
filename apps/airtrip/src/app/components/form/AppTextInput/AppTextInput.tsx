"use client";

import {
  NumberInput,
  NumberInputProps,
  TextInput,
  TextInputProps,
} from "@mantine/core";
import styles from "./appTextInput.module.css";

const AppTextInput = (props: TextInputProps | NumberInputProps) => {
  if (props.type === "number")
    return (
      <NumberInput
        classNames={{
          label: styles.label,
          error: styles.error,
        }}
        size="lg"
        {...(props as NumberInputProps)}
      />
    );
  return (
    <TextInput
      classNames={{
        input: styles.input,
        label: styles.label,
        error: styles.error,
      }}
      size="lg"
      {...(props as TextInputProps)}
    />
  );
};
export default AppTextInput;
