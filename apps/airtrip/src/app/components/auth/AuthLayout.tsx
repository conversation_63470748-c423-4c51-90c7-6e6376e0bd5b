"use client";

/*
 * import modules and libraries
 */
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Alert, Box, Container, Stack } from "@mantine/core";

import { useTranslation } from "react-i18next";

/*
 * import components
 */
import AppTabs from "@/app/components/common/AppTabs/AppTabs";

/*
 * import constants and utils
 */
import { useMessageStore } from "@/store/MessageStore";

import AlertMessage from "../common/AlertMessage";

const AuthLayout = (props: { children: React.ReactNode }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const paths = pathname.split("/");
  const path = paths[paths.length - 1] as string | null;
  const [activeTab, setActiveTab] = useState<string | null>(path);
  const [setMessage, toggleLoading] = useMessageStore((s) => [
    s.setMessage,
    s.toggleLoading,
  ]);

  const headers = [
    {
      name: t("login:link.signup"),
      code: "signup",
    },
    {
      name: t("login:btn.login"),
      code: "signin",
    },
  ];

  useEffect(() => {
    toggleLoading(false);
    return () => {
      setMessage("");
    };
  }, []);

  return (
    <>
      <Box className="bg-[#f7f7f7] pt-4">
        <Container size={700}>
          <Alert variant="light" color="app-pink.4" className="rounded-lg">
            エアトリ アカウントでは当eSIMサイトにログインすることはできません。
            <br />
            <span className="font-bold">
              新たにアカウントを作成くださいませ。
            </span>
          </Alert>
        </Container>
      </Box>
      <AppTabs
        activeTab={activeTab}
        defaultValue={activeTab}
        headers={headers}
        onTabChange={(tab: string) => {
          setActiveTab(tab);
          router.push(`/auth/${tab}`);
        }}
      />
      <Container size={700} className="py-12">
        <Stack>
          <AlertMessage />
          {props.children}
        </Stack>
      </Container>
    </>
  );
};
export default AuthLayout;
