"use client";

/*
 * import modules and libraries
 */
/*
 * import components
 */
import SigninForm from "@repo/ui/src/Auth/SignInForm";

/*
 * import constants and utils
 */
import useLogin from "@/hooks/useLogin";
import { useMessageStore } from "@/store/MessageStore";

export default function SigninFormContainer() {
  const [setGlobalMessage, setGlobalLoading, loading] = useMessageStore((s) => [
    s.setMessage,
    s.toggleLoading,
    s.isLoading,
  ]);
  const { login } = useLogin();

  return (
    <>
      <SigninForm
        setGlobalLoading={setGlobalLoading}
        setGlobalMessage={setGlobalMessage}
        loading={loading}
        onLogin={login}
      />
    </>
  );
}
