"use client";

import SignupForm from "@repo/ui/src/Auth/SignupForm";

/*
 * import components
 */

/*
 * import api
 */

/*
 * import constants and utils
 */
import {
  ERR_GENERIC_REQUIRED,
  ERR_STRONG_PASSWORD,
  REGEX_STRONG_PASSWORD,
} from "@/app/constants";
import { z } from "@/i18n/ja-zod";
import { useMessageStore } from "@/store/MessageStore";
import { useRegisterUser } from "@/store/UserStore";

export const signupSchema = z.object({
  firstName: z.string().min(1, ERR_GENERIC_REQUIRED),
  lastName: z.string().min(1, ERR_GENERIC_REQUIRED),
  email: z.string().email("メールアドレスが無効です"),

  password: z.string().regex(REGEX_STRONG_PASSWORD, ERR_STRONG_PASSWORD),
  termsOfService: z
    .boolean({
      invalid_type_error: "利用規約に同意してください。",
      required_error: "利用規約に同意してください。",
    })
    .refine((value) => value === true, {
      message: "利用規約に同意してください。",
    }),
});

export default function SignupFormContainer(props: {
  enablePhone?: boolean;
  hideSocialLogin?: boolean;
  defaultTab?: string;
  onSuccess?: (payload: any) => void;
}) {
  const setRegisterEmail = useRegisterUser((s) => s.setEmail);

  const [setGlobalMessage, setGlobalLoading, loading] = useMessageStore((s) => [
    s.setMessage,
    s.toggleLoading,
    s.isLoading,
  ]);

  return (
    <>
      <SignupForm
        defaultTab={props.defaultTab}
        loading={loading}
        setGlobalLoading={setGlobalLoading}
        setGlobalMessage={setGlobalMessage}
        setRegisterEmail={setRegisterEmail}
        enablePhone={props.enablePhone}
        hideSocialLogin={props.hideSocialLogin}
        onSuccess={props.onSuccess}
      />
    </>
  );
}
