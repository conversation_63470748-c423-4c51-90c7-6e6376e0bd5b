import { NextFetchEvent, type NextRequest, NextResponse } from "next/server";

import acceptLanguage from "accept-language";
import { i18nRouter } from "next-i18n-router";

import { transformMiddlewareRequest } from "@gmesim/logger";
import { logger } from "@gmesim/logger/lib/axiom/server";

/*
 * import constants and helpers
 */
import { isDev } from "@/utils";

import { fallbackLng, locales } from "./i18n/settings";

// tes
acceptLanguage.languages(locales);

const PUBLIC_FILE = /\.(.*)$/;

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|assets|favicon.ico|sw.js|assets|site.webmanifest).*)",
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
    { source: "/" },
  ],
};

function handleAffiliate(
  request: NextRequest,
  url: URL,
  response: NextResponse
): NextResponse | null {
  const referer = request.headers.get("referer");
  if (!referer) return null;

  const existing = request.cookies.get("affiliate")?.value;

  if (!existing) {
    response.cookies.set("affiliate", referer, {
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: "/",
    });
  }

  return null;
}

export function middleware(request: NextRequest, event: NextFetchEvent) {
  const url = request.nextUrl.clone();

  if (!isDev()) {
    logger.info(...transformMiddlewareRequest(request));
    event.waitUntil(logger.flush());
  }

  request.headers.set("x-url", request.url);

  if (PUBLIC_FILE.test(request.nextUrl.pathname)) {
    return;
  }

  if (isDev()) {
    // Basic Authentication for homepage
    const authHeader = request.headers.get("authorization");
    if (!authHeader) {
      return new Response("Unauthorized", {
        status: 401,
        headers: {
          "WWW-Authenticate": 'Basic realm="Secure Area"',
        },
      });
    }

    const basicAuth = authHeader.split(" ")[1];
    const [user, pwd] = atob(basicAuth).split(":");

    // TODO: change to env after env file update
    // const validUser = process.env.AIRTRIP_FE_BASIC_AUTH_USER;
    // const validPassword = process.env.AIRTRIP_FE_BASIC_AUTH_PASSWORD;
    const validUser = "accfordev";
    const validPassword = "ipc@789";

    if (user !== validUser || pwd !== validPassword) {
      return new Response("Unauthorized", {
        status: 401,
        headers: {
          "WWW-Authenticate": 'Basic realm="Secure Area"',
        },
      });
    }
  }

  //@ts-ignore
  const response = i18nRouter(request, {
    locales,
    defaultLocale: fallbackLng,
  }) as NextResponse;

  handleAffiliate(request, url, response);
  return response;
}
