import { create } from "zustand";
import type { IPlan } from "@/interfaces/IPlan";
export * from "@gmesim/cart-package/store";

interface IOrderState {
  isShowDrawer: boolean;
  selectedPlanType?: string;
  selectedPlan?: IPlan;
  isCompatibilityModalOpen: boolean;
  isReviewDrawerOpen: boolean;
  toggleReviewDrawerOpen: () => void;
  setSelectedPlanType: (type: string) => void;
  setSelectedPlan: (plan: IPlan) => void;
  toggleDrawer: () => void;
  closeDrawer: () => void;
  toggleCompatibilityModalOpen: () => void;
}

export const useOrderStore = create<IOrderState>()((set) => ({
  isShowDrawer: false,
  isCompatibilityModalOpen: false,
  isReviewDrawerOpen: false,
  toggleReviewDrawerOpen: () => {
    set((state) => ({
      ...state,
      isReviewDrawerOpen: !state.isReviewDrawerOpen,
    }));
  },
  setSelectedPlanType: (type: string) =>
    set(() => ({ selectedPlanType: type })),
  setSelectedPlan: (plan: IPlan) => set(() => ({ selectedPlan: plan })),
  toggleDrawer: () => set((state) => ({ isShowDrawer: !state.isShowDrawer })),
  closeDrawer: () => set(() => ({ isShowDrawer: false })),
  toggleCompatibilityModalOpen: () => {
    set((state) => ({
      ...state,
      isCompatibilityModalOpen: !state.isCompatibilityModalOpen,
    }));
  },
}));
