/** @type {import('next').NextConfig} */
module.exports = {
  env: {
    NEXT_PUBLIC_USER_POOL: "AIRTRIP", // Accessible on both server and client
    NEXT_PUBLIC_SERVICE_NAME: "GLOBAL_ESIM_AIRTRIP", // Accessible on both server and client
    NEXT_PUBLIC_SOURCE: "airtrip",
  },
  transpilePackages: ["@repo/ui"],
  reactStrictMode: true,
  swcMinify: true,
  output: "standalone",
  compiler: {
    removeConsole: {
      exclude: ["log"],
    },
  },
  logging: {
    incomingRequests: {
      ignore: [/\api\/v1\/health/],
    },
  },
  images: {
    // domains: ['cdn-dev.gmobile.biz', 'cdn.gmobile.biz', 'issue.usimsa.com'],
    formats: ["image/webp"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.gmobile.biz",
        port: "",
        pathname: "/esim/**",
      },
      {
        protocol: "https",
        hostname: "cdn-dev.gmobile.biz",
        port: "",
      },
      {
        protocol: "https",
        hostname: "cdn.gmobile.biz",
        port: "",
      },
      {
        protocol: "https",
        hostname: "issue.usimsa.com",
        port: "",
      },
    ],
  },
  basePath: process.env.NEXT_PUBLIC_BASE_PATH || undefined,
  async rewrites() {
    return [
      {
        source: "/ad/view/erd.html",
        destination: "/gmo/erd.html",
      },
    ];
  },
  async redirects() {
    return [
      {
        source: "/faq/(.*)",
        destination: "/faq",
        permanent: true,
      },
      {
        source: "/help",
        destination: "/support/help",
        permanent: true,
      },
      {
        source: "/app/orders/:orderId/complete",
        destination: "/checkout/:orderId/complete",
        permanent: true,
      },
    ];
  },

  webpack(config) {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.(".svg")
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ["@svgr/webpack"],
      }
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

// Injected content via Sentry wizard below

const { withSentryConfig } = require("@sentry/nextjs");

module.exports = withSentryConfig(
  module.exports,
  {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    // Suppresses source map uploading logs during build
    silent: true,
    org: "inbound-platform",
    project: "airtrip-esim",
  },
  {
    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    authToken: process.env.SENTRY_AUTH_TOKEN,
    widenClientFileUpload: true,

    // Transpiles SDK to be compatible with IE11 (increases bundle size)
    transpileClientSDK: true,

    // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    // tunnelRoute: "/monitoring",

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors.
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,
  }
);
