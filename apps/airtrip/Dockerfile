FROM node:20-alpine AS base

# This Dockerfile is copy-pasted into our main docs at /docs/handbook/deploying-with-docker.
# Make sure you update both files!

FROM base AS builder
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
RUN apk update
# Set working directory
WORKDIR /app
RUN yarn global add turbo
COPY . .
RUN turbo prune airtrip --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
RUN apk add --no-cache libc6-compat
RUN apk update
WORKDIR /app

# First install the dependencies (as they change less often)
COPY .gitignore .gitignore
COPY --from=builder /app/apps/airtrip/.env /app/apps/airtrip/.env
COPY --from=builder /app/out/json/ .
# COPY --from=builder /app/out/yarn.lock ./g.lock
RUN yarn install

# Build the project
COPY --from=builder /app/out/full/ .
COPY turbo.json turbo.json

# Uncomment and use build args to enable remote caching
# ARG TURBO_TEAM
# ENV TURBO_TEAM=$TURBO_TEAM

# ARG TURBO_TOKEN
# ENV TURBO_TOKEN=$TURBO_TOKEN

RUN yarn turbo run build --filter=airtrip...

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

COPY --from=installer /app/apps/airtrip/next.config.js .
COPY --from=installer /app/apps/airtrip/package.json .

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/apps/airtrip/.env ./apps/airtrip/.env
COPY --from=installer --chown=nextjs:nodejs /app/apps/airtrip/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/airtrip/.next/static ./apps/airtrip/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/airtrip/public ./apps/airtrip/public
ENV SENTRY_AUTH_TOKEN=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
CMD node apps/airtrip/server.js