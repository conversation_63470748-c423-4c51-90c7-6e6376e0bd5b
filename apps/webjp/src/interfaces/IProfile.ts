import { ICurrency } from "./ICurrency";

export interface IProfile {
  email: string;
  firstName: string;
  lastName: string;
  id: number;
  createdAt: string;
  profileImage: string;
  defaultPaymentMethodId: string;
  identityProvider: "IDP_MANUAL";
  isVerified: boolean;
  locale: string;
  currency?: ICurrency;
  corporate?: {
    id: number;
    name: string;
    code: string;
    enabled: boolean;
  };
}
