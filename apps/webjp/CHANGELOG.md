# Changelog

## [2.45.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.45.1...webjp-v2.45.2) (2025-07-11)


### Bug Fixes

* enable 5G plans for lgu in APIs ([6d7a6e4](https://github.com/InboundPlatform/global-esim/commit/6d7a6e439b7ae3d799f80d95a91aaef5404570e5))

## [2.45.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.45.0...webjp-v2.45.1) (2025-07-11)


### Bug Fixes

* trigger deploy ([7241d5f](https://github.com/InboundPlatform/global-esim/commit/7241d5fa1f5fc19fdbbd54ffadd53a529ee7840b))

## [2.45.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.44.0...webjp-v2.45.0) (2025-07-10)


### Features

* add paypay banner ([ad0bd0c](https://github.com/InboundPlatform/global-esim/commit/ad0bd0c3b8c96bbd18774c6e7e87169f1f48bbe8))

## [2.44.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.43.0...webjp-v2.44.0) (2025-07-04)


### Features

* **ges-267:** add review cards on GM/AT landing page ([bec5463](https://github.com/InboundPlatform/global-esim/commit/bec546336421b29f5762974118822f4decf516e1))
* **gm-airtrip-seo:** add removed props from the compatible-devices page ([0259c82](https://github.com/InboundPlatform/global-esim/commit/0259c825f3e0d91617889ec860542988806b74d0))

## [2.43.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.42.0...webjp-v2.43.0) (2025-07-03)


### Features

* **ges-256:** update corporate account constraints ([b095343](https://github.com/InboundPlatform/global-esim/commit/b0953439aa89c2fd522ec33a163ddbcf9846c6a7))

## [2.42.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.41.3...webjp-v2.42.0) (2025-06-30)


### Features

* update free esim campaign banners ([d83ecd4](https://github.com/InboundPlatform/global-esim/commit/d83ecd46ca39b950542e38206a85b196cc2987ca))

## [2.41.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.41.2...webjp-v2.41.3) (2025-06-27)


### Bug Fixes

* ui flickering ([8a92921](https://github.com/InboundPlatform/global-esim/commit/8a9292165365c7ebfebda7c45cf0a0922d98ed7d))
* ui flickering ([8527742](https://github.com/InboundPlatform/global-esim/commit/852774262092a0391a5b181676da73b1aaa5e1c6))

## [2.41.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.41.1...webjp-v2.41.2) (2025-06-27)


### Bug Fixes

* do nothing postOrder throws and enable session replay ([b9dc034](https://github.com/InboundPlatform/global-esim/commit/b9dc034dbc49bee77c3e916fa4062b6ef45fcd35))
* trigger deploy ([ca237c7](https://github.com/InboundPlatform/global-esim/commit/ca237c7040e98c6c560906efa2e7bb80313cd339))

## [2.41.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.41.0...webjp-v2.41.1) (2025-06-23)


### Bug Fixes

* build issues ([8408823](https://github.com/InboundPlatform/global-esim/commit/8408823ffb3e72ce587fe0a054e85239595c2641))

## [2.41.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.40.0...webjp-v2.41.0) (2025-06-23)


### Features

* **seo-optimization:** webjp - add the faq, how it works and region about-esim for webjp as well same as airtrip ([d3c39a6](https://github.com/InboundPlatform/global-esim/commit/d3c39a69db228ae15786566e4726d2313e1eb01e))
* **seo-optimization:** webjp & airtrip sync translations with google sheets ([554b0be](https://github.com/InboundPlatform/global-esim/commit/554b0be984297b56367b3a3609d62d41eefea072))

## [2.40.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.39.0...webjp-v2.40.0) (2025-06-19)


### Features

* lgu topup ([c1c40d9](https://github.com/InboundPlatform/global-esim/commit/c1c40d9731c7ee971d28134d567af1e495806d4a))


### Bug Fixes

* revert home.json translation ([909f2a1](https://github.com/InboundPlatform/global-esim/commit/909f2a175a4328e92f856638406dd0242a52de46))

## [2.39.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.38.5...webjp-v2.39.0) (2025-06-18)


### Features

* **paypay:** add translations for paypay airtrip and gm esim jp page ([6413f66](https://github.com/InboundPlatform/global-esim/commit/6413f66faebdefda53e908a4611f8ee5c49da71b))


### Bug Fixes

* update sentry dsn ([14725e2](https://github.com/InboundPlatform/global-esim/commit/14725e2f80dd92cd5c7c3dde48747a9ef07fde81))

## [2.38.5](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.38.4...webjp-v2.38.5) (2025-06-12)


### Bug Fixes

* trigger release ([8212bfe](https://github.com/InboundPlatform/global-esim/commit/8212bfeaedd06a1cfb76ade18fb0359b4b43669b))

## [2.38.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.38.3...webjp-v2.38.4) (2025-06-11)


### Bug Fixes

* add logger for request ([eefc635](https://github.com/InboundPlatform/global-esim/commit/eefc6356eeae47a976c6de6dd7cb35af2d92733f))
* improved logging for frontend and middle ware cookies ([a7c423e](https://github.com/InboundPlatform/global-esim/commit/a7c423e9be7ad1ca886415897199d181ef8afe24))

## [2.38.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.38.2...webjp-v2.38.3) (2025-06-09)


### Bug Fixes

* trigger release ([c294caf](https://github.com/InboundPlatform/global-esim/commit/c294caf3cf656b963c4a8d74c7386a93d9d6856a))

## [2.38.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.38.1...webjp-v2.38.2) (2025-06-09)


### Bug Fixes

* disable phone for verification ([f191f26](https://github.com/InboundPlatform/global-esim/commit/f191f26a010b90b6211b1f277a71b65f5215939a))
* update auth token and project name for sentry ([b8e4372](https://github.com/InboundPlatform/global-esim/commit/b8e4372f875a6be1ebeaa0a5c54260e11c03d24a))

## [2.38.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.38.0...webjp-v2.38.1) (2025-06-05)


### Bug Fixes

* remove error page for sentry verification ([9428e68](https://github.com/InboundPlatform/global-esim/commit/9428e6899c9462c5fff504cab9053e95e1d7e30d))

## [2.38.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.37.0...webjp-v2.38.0) (2025-06-05)


### Features

* **store-affiliate:** add middleware logic to the webjp order data ([e76b69a](https://github.com/InboundPlatform/global-esim/commit/e76b69ab057d604a565214f18587dd9f7e396f00))
* **store-affiliate:** add middleware to add via to cookie for tracking the affiliate ([178adcb](https://github.com/InboundPlatform/global-esim/commit/178adcb2daff3179dc467c8725370cdbc2a0067e))
* **store-affiliate:** correct the usage of new storage get method across the app ([139922a](https://github.com/InboundPlatform/global-esim/commit/139922a023b8f80b48206f4283e151acd9c7cf92))
* **store-affiliate:** get the affiliate from the referer ([f77f20a](https://github.com/InboundPlatform/global-esim/commit/f77f20a70438f41016ca794a087eb21efc9d48ca))
* **store-affiliate:** set default storage to fetch the data from local storage ([07664d9](https://github.com/InboundPlatform/global-esim/commit/07664d95af71c6cad5680b2567f5df88073b2f3c))
* **store-affiliate:** stop redirection and return modified response on saving cookie ([d72181e](https://github.com/InboundPlatform/global-esim/commit/d72181e29b42c7b1e757588310724f502e31bc73))


### Bug Fixes

* coupon issue ([4cfe556](https://github.com/InboundPlatform/global-esim/commit/4cfe5567ac833672cc0978912f4cdd794df0412a))
* enable sentry and handle error handling ([6a9b4a3](https://github.com/InboundPlatform/global-esim/commit/6a9b4a3e34abd90b339004559845f9cbdca29d92))
* ireland image update ([79a5188](https://github.com/InboundPlatform/global-esim/commit/79a5188e4fd9aa1be79d8c30488e3f6e41fffb58))
* remove duplicate product review ([e47934f](https://github.com/InboundPlatform/global-esim/commit/e47934f3890645216802991dd85bcf68d1986485))
* sentry env for webjp and web ([9a27005](https://github.com/InboundPlatform/global-esim/commit/9a27005587c9090488bd8adcb30a0808830d7f3d))

## [2.37.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.7...webjp-v2.37.0) (2025-05-08)


### Features

* add json ld reviews for airtrip and gm jp ([bab8c4d](https://github.com/InboundPlatform/global-esim/commit/bab8c4d4e37b79c668446f1b803aa7d6aeb441d2))
* service specific coupon ([8fca803](https://github.com/InboundPlatform/global-esim/commit/8fca8031632062e49493b1a20c0d14dcb860df97))


### Bug Fixes

* remove lgu text for non-lgu korea plan ([110d667](https://github.com/InboundPlatform/global-esim/commit/110d667b77bf62087056dcc6e1263733d3a993cc))

## [2.36.7](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.6...webjp-v2.36.7) (2025-04-24)


### Bug Fixes

* check duplicate phone number and  block ([3efebad](https://github.com/InboundPlatform/global-esim/commit/3efebad4bfb4b0560f88984385e521f43cd902bb))
* corporate feature ([3efebad](https://github.com/InboundPlatform/global-esim/commit/3efebad4bfb4b0560f88984385e521f43cd902bb))
* invoice information on database ([711c52f](https://github.com/InboundPlatform/global-esim/commit/711c52ff1e854a34c9d08bec8124a10e388eb75c))

## [2.36.6](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.5...webjp-v2.36.6) (2025-04-22)


### Bug Fixes

* trigger deployments ([#1637](https://github.com/InboundPlatform/global-esim/issues/1637)) ([30842ef](https://github.com/InboundPlatform/global-esim/commit/30842ef4a7c1c47775c8ef23723cd91afdc902ad))

## [2.36.5](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.4...webjp-v2.36.5) (2025-04-18)


### Bug Fixes

* trigger release ([92b9c43](https://github.com/InboundPlatform/global-esim/commit/92b9c43b10d66d35e6000514eb96b524fa2a634b))

## [2.36.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.3...webjp-v2.36.4) (2025-04-18)


### Bug Fixes

* trigger release ([a02ed57](https://github.com/InboundPlatform/global-esim/commit/a02ed5700a55eb52947fbc1948bcbd090289ccfd))

## [2.36.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.2...webjp-v2.36.3) (2025-04-18)


### Bug Fixes

* **webjp:** clean up filenames for imags ([756ce0c](https://github.com/InboundPlatform/global-esim/commit/756ce0c9fdb197ce8937e2cd86c8e076d352bacb))
* **webjp:** translate japanese filenames to english ([6c700b5](https://github.com/InboundPlatform/global-esim/commit/6c700b57023c3868923be4bf04d5b62dc56b1ecb))

## [2.36.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.1...webjp-v2.36.2) (2025-04-17)


### Bug Fixes

* correct free esim email template ([#1614](https://github.com/InboundPlatform/global-esim/issues/1614)) ([5ccba7f](https://github.com/InboundPlatform/global-esim/commit/5ccba7f3a520bcf838059d2e98db08a908403d56))
* **webjp:** add new images for google shopping ([d6b8f3f](https://github.com/InboundPlatform/global-esim/commit/d6b8f3f0a9edda250831822ad1c833dc401308d1))

## [2.36.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.36.0...webjp-v2.36.1) (2025-04-14)


### Bug Fixes

* gmo campaign form ([158872a](https://github.com/InboundPlatform/global-esim/commit/158872a7446aecbdb536842e5d5291b83e220671))
* refactor top page to ssg gmesim ([9f5a98c](https://github.com/InboundPlatform/global-esim/commit/9f5a98c457092c0fc11afc83e1c5e218cba3efa0))

## [2.36.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.8...webjp-v2.36.0) (2025-04-10)


### Features

* add to specification table ([#1571](https://github.com/InboundPlatform/global-esim/issues/1571)) ([552deb9](https://github.com/InboundPlatform/global-esim/commit/552deb90f25aa34562c6ee9b83b7bfe9ea65e388))


### Bug Fixes

* allow word break on column breadCrumb ([#1577](https://github.com/InboundPlatform/global-esim/issues/1577)) ([83b4c2c](https://github.com/InboundPlatform/global-esim/commit/83b4c2ce042c7439f67abec178cba26976b2975e))
* catch error on all routes ([b8c262c](https://github.com/InboundPlatform/global-esim/commit/b8c262c8a9558bb7977241b9dc5423bc5c43da5b))
* set proper id at top of complete page ([#1572](https://github.com/InboundPlatform/global-esim/issues/1572)) ([9c88195](https://github.com/InboundPlatform/global-esim/commit/9c881955adfd2bf7b3274615a2b49afd1e26bf33))
* update global navi links ([#1576](https://github.com/InboundPlatform/global-esim/issues/1576)) ([c1e924a](https://github.com/InboundPlatform/global-esim/commit/c1e924ad3882eac10a1d73185fc11279cd56be75))
* update translations ([#1588](https://github.com/InboundPlatform/global-esim/issues/1588)) ([93c41f5](https://github.com/InboundPlatform/global-esim/commit/93c41f5a5fdbb3836f86439449a211f81b6aa39c))
* **webjp&airtrip:** import not found component dynamically with no ssr ([32477a0](https://github.com/InboundPlatform/global-esim/commit/32477a0cb1f486444ecf7fd683f9d4626ba52709))

## [2.35.8](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.7...webjp-v2.35.8) (2025-04-08)


### Bug Fixes

* trigger release ([d7d5e91](https://github.com/InboundPlatform/global-esim/commit/d7d5e91e6daa26ae91d29b6e939f76bc12c87a29))

## [2.35.7](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.6...webjp-v2.35.7) (2025-04-02)


### Bug Fixes

* trigger deploy ([8c604b6](https://github.com/InboundPlatform/global-esim/commit/8c604b6153933a23d66917e265afc8f76d701fd0))

## [2.35.6](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.5...webjp-v2.35.6) (2025-04-01)


### Bug Fixes

* update free esim campaign ([#1557](https://github.com/InboundPlatform/global-esim/issues/1557)) ([#1559](https://github.com/InboundPlatform/global-esim/issues/1559)) ([7e2c0d9](https://github.com/InboundPlatform/global-esim/commit/7e2c0d9cad20daf12bd0331fb226c0f1ec13a300))

## [2.35.5](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.4...webjp-v2.35.5) (2025-03-28)


### Bug Fixes

* faq correct links ([#1546](https://github.com/InboundPlatform/global-esim/issues/1546)) ([6f0191e](https://github.com/InboundPlatform/global-esim/commit/6f0191e0a37c08d30791b3a1ac47bafb94786227))

## [2.35.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.3...webjp-v2.35.4) (2025-03-27)


### Bug Fixes

* trigger release ([47b8a2d](https://github.com/InboundPlatform/global-esim/commit/47b8a2df46e5e181c6cb2254a02d4dae2517180e))

## [2.35.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.2...webjp-v2.35.3) (2025-03-26)


### Bug Fixes

* region translations and button color ([#1530](https://github.com/InboundPlatform/global-esim/issues/1530)) ([0a39ec2](https://github.com/InboundPlatform/global-esim/commit/0a39ec2d3bddbf2decd3ba52683e29f0dd3251d7))
* remove airtrip 20 off ([#1520](https://github.com/InboundPlatform/global-esim/issues/1520)) ([5f09041](https://github.com/InboundPlatform/global-esim/commit/5f090415e09848b8ce38fb8549940245e39a8e9b))

## [2.35.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.1...webjp-v2.35.2) (2025-03-25)


### Bug Fixes

* translations for basket ([7fe9cbe](https://github.com/InboundPlatform/global-esim/commit/7fe9cbe19f97b3e1f96b2364b726fb286fd31958))

## [2.35.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.35.0...webjp-v2.35.1) (2025-03-25)


### Bug Fixes

* language update for airtrip, missing locale for qatar, turkey and republic of south africa ([165dd55](https://github.com/InboundPlatform/global-esim/commit/165dd550380927e55d013780dd468a7aa4ea48d2))
* language, mobile app locale addition for airtrip mobile app ([bb53ec1](https://github.com/InboundPlatform/global-esim/commit/bb53ec1c0068bd414ac839495d3ef140183e6143))

## [2.35.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.34.1...webjp-v2.35.0) (2025-03-19)


### Features

* add country param gmesim ([abf3fc0](https://github.com/InboundPlatform/global-esim/commit/abf3fc0c9866536f21a885c39aea82c2874ec545))


### Bug Fixes

* convert png images to webp gmesim ([60750ac](https://github.com/InboundPlatform/global-esim/commit/60750ac288e6cf946651046694cfc954bc368a80))
* translation fixes for error sheet ([97c7a35](https://github.com/InboundPlatform/global-esim/commit/97c7a357199eced4de2122b017780be1e4a42e87))

## [2.34.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.34.0...webjp-v2.34.1) (2025-03-14)


### Bug Fixes

* build issues ([14f0b00](https://github.com/InboundPlatform/global-esim/commit/14f0b005ad1137a4580e926cdc54121ebf8d05c3))

## [2.34.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.33.1...webjp-v2.34.0) (2025-03-14)


### Features

* add new esim provider LINK Korea for vietnam, SKT, thailand ([07bb8c4](https://github.com/InboundPlatform/global-esim/commit/07bb8c4ab7d4956e3ea73bafaff739fe03e50725))


### Bug Fixes

* change text and layout ([b9bb9f8](https://github.com/InboundPlatform/global-esim/commit/b9bb9f82e5b0fec977fda8dda5dc06b64fadfaad))
* hyperlinks and translation consumption ([4810d09](https://github.com/InboundPlatform/global-esim/commit/4810d09f05e314a9d55182039266a55038fd911c))
* update countries keys on the sheets ([3c52b84](https://github.com/InboundPlatform/global-esim/commit/3c52b84dc03a502a9010040176f4561e29afd86a))

## [2.33.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.33.0...webjp-v2.33.1) (2025-03-10)


### Bug Fixes

* title tags for gm esim region page ([2fa1330](https://github.com/InboundPlatform/global-esim/commit/2fa1330d614278822a3f194b2c14338e56cb3d2e))

## [2.33.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.32.4...webjp-v2.33.0) (2025-03-06)


### Features

* add gmo script for gmesim ([#1454](https://github.com/InboundPlatform/global-esim/issues/1454)) ([e3896a0](https://github.com/InboundPlatform/global-esim/commit/e3896a0353378aee40d37dfff56fed4ae3f0c625))
* korea and tokukusho coupon compatible ([afaa9dc](https://github.com/InboundPlatform/global-esim/commit/afaa9dc45f14bb0a117916727e4c9591c63ec015))


### Bug Fixes

* add line break for mobile view ([d180dfa](https://github.com/InboundPlatform/global-esim/commit/d180dfa5a57889efe1e2b18a170aa8168344c52a))
* show qr on the esim details accordion ([624719d](https://github.com/InboundPlatform/global-esim/commit/624719d48bd81c6f0ec1561ae2900f8638636c40))

## [2.32.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.32.3...webjp-v2.32.4) (2025-02-28)


### Bug Fixes

* build issues ([78a7d1b](https://github.com/InboundPlatform/global-esim/commit/78a7d1bb5e69ff957f17b1dfd569c7cca3faa6e8))

## [2.32.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.32.2...webjp-v2.32.3) (2025-02-28)


### Bug Fixes

* ges-149 utm parameter bug fix ([a8d6d5d](https://github.com/InboundPlatform/global-esim/commit/a8d6d5dd3bf8c08948cc3d7e84421c0ae26b30a9))
* prevent url to gm wifi when selecting jp ([9130bd8](https://github.com/InboundPlatform/global-esim/commit/9130bd82092b6de2c6f877beb52769d979792ebd))

## [2.32.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.32.1...webjp-v2.32.2) (2025-02-27)


### Bug Fixes

* language select url and environment ([52e26ac](https://github.com/InboundPlatform/global-esim/commit/52e26acc16abc19dd91b147d3ab4e16bddeb685e))
* language select url error ([2d070df](https://github.com/InboundPlatform/global-esim/commit/2d070dff07a5d91a13311af3ac4662eda0bcccd2))
* remove cta button from side menu ([9ef0ebd](https://github.com/InboundPlatform/global-esim/commit/9ef0ebd6e18bba4478cf97606c1de9b448fd9993))

## [2.32.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.32.0...webjp-v2.32.1) (2025-02-26)


### Bug Fixes

* basket hotfixes ([ed10a81](https://github.com/InboundPlatform/global-esim/commit/ed10a81767de78b1df0a62bc2dee8bef1c57cc3b))

## [2.32.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.31.4...webjp-v2.32.0) (2025-02-20)


### Features

* campaign sampo ([89a317c](https://github.com/InboundPlatform/global-esim/commit/89a317c7ce6ab84584349bd5646351d0fc1fc35c))


### Bug Fixes

* correct campaign prices not showing ([a389e7b](https://github.com/InboundPlatform/global-esim/commit/a389e7b751170e8caa50e1bb455881b55a1d1439))
* pass params to checkout page as well ([90a6c49](https://github.com/InboundPlatform/global-esim/commit/90a6c491a441daa9830ac03dbb8635e5ae2c7d6f))

## [2.31.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.31.3...webjp-v2.31.4) (2025-02-18)


### Bug Fixes

* network spec for korea plan ([#1399](https://github.com/InboundPlatform/global-esim/issues/1399)) ([5df89be](https://github.com/InboundPlatform/global-esim/commit/5df89bef3e2530580262948ba0cf250f067fac5c))
* update spec list for lgu ([#1397](https://github.com/InboundPlatform/global-esim/issues/1397)) ([421cc05](https://github.com/InboundPlatform/global-esim/commit/421cc05c797ee082226a37d6c3d7c6d9996d9fcb))

## [2.31.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.31.2...webjp-v2.31.3) (2025-02-14)


### Bug Fixes

* add utm_source agent ([7997d54](https://github.com/InboundPlatform/global-esim/commit/7997d54d814892f9fca625cb3b51b697c0d1d748))

## [2.31.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.31.1...webjp-v2.31.2) (2025-02-07)


### Bug Fixes

* missing user source for social login users ([4581a7d](https://github.com/InboundPlatform/global-esim/commit/4581a7d04609db3a7520383cbb8e458b014085c3))
* style override from ukomi ([6a50f66](https://github.com/InboundPlatform/global-esim/commit/6a50f66af92a7640548d24248665b5012ea64e62))

## [2.31.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.31.0...webjp-v2.31.1) (2025-02-06)


### Bug Fixes

* trigger deploy ([e7d6263](https://github.com/InboundPlatform/global-esim/commit/e7d6263062b8568c815694f6b02a5297d702249e))

## [2.31.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.6...webjp-v2.31.0) (2025-02-06)


### Features

* ukomi review api ([#1369](https://github.com/InboundPlatform/global-esim/issues/1369)) ([883dfd3](https://github.com/InboundPlatform/global-esim/commit/883dfd34955bf34ca5093da6c4085d2d10906931))

## [2.30.6](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.5...webjp-v2.30.6) (2025-02-05)


### Bug Fixes

* webjp payment button fix, content fix ([255e9d6](https://github.com/InboundPlatform/global-esim/commit/255e9d6a85fcd4992a2a05fb76c44c8972172255))

## [2.30.5](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.4...webjp-v2.30.5) (2025-02-03)


### Bug Fixes

* un-hide campaign banners ([52a97f3](https://github.com/InboundPlatform/global-esim/commit/52a97f3edfbf1ca388b217615e580f22b2c1336a))

## [2.30.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.3...webjp-v2.30.4) (2025-01-31)


### Bug Fixes

* trigger release ([0594570](https://github.com/InboundPlatform/global-esim/commit/0594570ce33d7c09c9959a57fe06f4a8137c3770))
* webjp build issue ([ef7b197](https://github.com/InboundPlatform/global-esim/commit/ef7b1971f3fdb3c35184909451b8a9b56dc15d9f))

## [2.30.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.2...webjp-v2.30.3) (2025-01-31)


### Bug Fixes

* campaign plan esim not being disabled because of plan being disabled ([b48eac1](https://github.com/InboundPlatform/global-esim/commit/b48eac10e8bc99550166019dbd059e869cfe746d))
* remove default coupon code GM20 ([713dda4](https://github.com/InboundPlatform/global-esim/commit/713dda4284fbf1813383cb48e72b2408334a3871))
* tab translation and unique key error ([#1343](https://github.com/InboundPlatform/global-esim/issues/1343)) ([#1344](https://github.com/InboundPlatform/global-esim/issues/1344)) ([ee7951d](https://github.com/InboundPlatform/global-esim/commit/ee7951d94a09a47896b606ee984b2b689084bec9))
* webjp build issue ([ef7b197](https://github.com/InboundPlatform/global-esim/commit/ef7b1971f3fdb3c35184909451b8a9b56dc15d9f))

## [2.30.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.2...webjp-v2.30.3) (2025-01-31)


### Bug Fixes

* campaign plan esim not being disabled because of plan being disabled ([b48eac1](https://github.com/InboundPlatform/global-esim/commit/b48eac10e8bc99550166019dbd059e869cfe746d))
* remove default coupon code GM20 ([713dda4](https://github.com/InboundPlatform/global-esim/commit/713dda4284fbf1813383cb48e72b2408334a3871))
* tab translation and unique key error ([#1343](https://github.com/InboundPlatform/global-esim/issues/1343)) ([#1344](https://github.com/InboundPlatform/global-esim/issues/1344)) ([ee7951d](https://github.com/InboundPlatform/global-esim/commit/ee7951d94a09a47896b606ee984b2b689084bec9))

## [2.30.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.1...webjp-v2.30.2) (2025-01-30)


### Bug Fixes

* hide region banner ([2f4a525](https://github.com/InboundPlatform/global-esim/commit/2f4a525ce486d57d00d8c86b054f343c2c9ee416))

## [2.30.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.30.0...webjp-v2.30.1) (2025-01-30)


### Bug Fixes

* hide top banner campaign ([9655865](https://github.com/InboundPlatform/global-esim/commit/9655865635ff39cf9cc35b8fd869f39aadceac84))

## [2.30.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.29.0...webjp-v2.30.0) (2025-01-30)


### Features

* enhanced authentication flow ([ca7a64c](https://github.com/InboundPlatform/global-esim/commit/ca7a64c2c712241ab2c12ffffb0b3e496e2b2c2a))
* refactor gm esim campaign page ([584e390](https://github.com/InboundPlatform/global-esim/commit/584e390be10b46b212037b392982395ad317e0e4))


### Bug Fixes

* misc fixes ([6f2bbae](https://github.com/InboundPlatform/global-esim/commit/6f2bbaecac3b93fb70e9574b3100c0134cf4deb8))
* show social message error ([027a8c8](https://github.com/InboundPlatform/global-esim/commit/027a8c86958225e0a2ef01d15b36b3a52ee7c8a3))
* social login email conflict message ([dda5342](https://github.com/InboundPlatform/global-esim/commit/dda534242c37e333a391837199f228d9e724b229))

## [2.29.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.27...webjp-v2.29.0) (2025-01-28)


### Features

* add e commerce ([#1313](https://github.com/InboundPlatform/global-esim/issues/1313)) ([d50beeb](https://github.com/InboundPlatform/global-esim/commit/d50beebbe92c0f63bbcc01b770e8d5f70b9b88a2))

## [2.28.27](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.26...webjp-v2.28.27) (2025-01-27)


### Bug Fixes

* env issues for dockerfile ([de79151](https://github.com/InboundPlatform/global-esim/commit/de79151d23de9626c8ac909911345c854b0d9215))
* trigger deploys ([d511ce7](https://github.com/InboundPlatform/global-esim/commit/d511ce732e2efef9a994ec120cd787015afb75c5))
* trigger fresh deploys ([4e9ea54](https://github.com/InboundPlatform/global-esim/commit/4e9ea54cef6dd7e94cf706dcbdb56419e1fee90e))

## [2.28.26](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.25...webjp-v2.28.26) (2025-01-24)


### Bug Fixes

* dev deploy ([57390a1](https://github.com/InboundPlatform/global-esim/commit/57390a1008e0b70f25d43b3dc851fb37db490752))
* disable kddi plan selection ([e75e4d1](https://github.com/InboundPlatform/global-esim/commit/e75e4d15ae3998f3292be98e668b2653fe623b1e))

## [2.28.25](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.24...webjp-v2.28.25) (2025-01-23)


### Bug Fixes

* prevent duplicate plans ([87020dc](https://github.com/InboundPlatform/global-esim/commit/87020dc5e493d921dcd24f1248a6521be0ce9c6d))
* remove console log ([91b3e42](https://github.com/InboundPlatform/global-esim/commit/91b3e4230d5fcf9b6daac16b064b7c76ed5d8425))

## [2.28.24](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.23...webjp-v2.28.24) (2025-01-23)


### Bug Fixes

* correct origin address for japanese global esim ([fbc303d](https://github.com/InboundPlatform/global-esim/commit/fbc303d29b1e8f60bbfd184f496c7ce0be95690f))

## [2.28.23](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.22...webjp-v2.28.23) (2025-01-23)


### Bug Fixes

* broken cover image for some countries ([a99c630](https://github.com/InboundPlatform/global-esim/commit/a99c63033eea0a0e47f1596026001785270c0d54))

## [2.28.22](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.21...webjp-v2.28.22) (2025-01-16)


### Bug Fixes

* trigger deploy ([e0095de](https://github.com/InboundPlatform/global-esim/commit/e0095dea1df2c745d09a53482881b30ddf4ff766))

## [2.28.21](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.20...webjp-v2.28.21) (2025-01-15)


### Bug Fixes

* missing translations on all destinations page ([#1261](https://github.com/InboundPlatform/global-esim/issues/1261)) ([38201b8](https://github.com/InboundPlatform/global-esim/commit/38201b856b9b0a86575c7b548ba426464471bea2))
* translations and top page ([#1259](https://github.com/InboundPlatform/global-esim/issues/1259)) ([c356dc9](https://github.com/InboundPlatform/global-esim/commit/c356dc9e67956d1d484c31d982af63c35b98bda2))

## [2.28.20](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.19...webjp-v2.28.20) (2025-01-14)


### Bug Fixes

* trigger webjp deployment ([331e1f9](https://github.com/InboundPlatform/global-esim/commit/331e1f9075e0ba4ca78edf02f92cd905d95e42ef))

## [2.28.19](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.18...webjp-v2.28.19) (2025-01-10)


### Bug Fixes

* trigger deployment ([ebe33c7](https://github.com/InboundPlatform/global-esim/commit/ebe33c70ccf611771c02ac88f69eb93b5b615625))

## [2.28.18](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.17...webjp-v2.28.18) (2025-01-09)


### Bug Fixes

* build issues ([48fccd2](https://github.com/InboundPlatform/global-esim/commit/48fccd2e95908074a7f99a101d79999461f589b7))
* trigger deploy ([27b9f02](https://github.com/InboundPlatform/global-esim/commit/27b9f025a51fe53f88e5037ac66df613704f9566))

## [2.28.17](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.16...webjp-v2.28.17) (2025-01-08)


### Bug Fixes

* build issue ([abb3418](https://github.com/InboundPlatform/global-esim/commit/abb341811adb53e176caebeaed26021cb4e93f5b))
* build issue, server components issue ([098cc9c](https://github.com/InboundPlatform/global-esim/commit/098cc9cebc2d625add742c96a9dfeb0928adc722))
* home page translatgion ([892fcd0](https://github.com/InboundPlatform/global-esim/commit/892fcd001ec8ff08bb372df6dc1b44df9dad1885))
* remove nikkei top campaign banner ([3604af8](https://github.com/InboundPlatform/global-esim/commit/3604af8330dbeb874d47ff30adb651a3db341b96))
* remove nikkei top campaign banner ([8773369](https://github.com/InboundPlatform/global-esim/commit/87733695a1591e38d11be329fd77ea8e292606d0))

## [2.28.16](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.15...webjp-v2.28.16) (2025-01-07)


### Bug Fixes

* remove nikkei top campaign banner ([285c1de](https://github.com/InboundPlatform/global-esim/commit/285c1de43248f71cb59cee5fe9e405220d8ef55d))

## [2.28.15](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.14...webjp-v2.28.15) (2025-01-06)


### Bug Fixes

* build issue ([0b5a183](https://github.com/InboundPlatform/global-esim/commit/0b5a1836d90c18668e43f92d89ae01c11b34d18e))

## [2.28.14](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.13...webjp-v2.28.14) (2024-12-27)


### Bug Fixes

* default plan information, new link ([2e1e42b](https://github.com/InboundPlatform/global-esim/commit/2e1e42b2d8c14f320c96d807b0f9e6adca53e5f3))
* new compatibility information ([07af2fc](https://github.com/InboundPlatform/global-esim/commit/07af2fcdd671d3e39a9245b3180654b2dc40407c))
* order form payment button fix, footer year fix ([34b2d57](https://github.com/InboundPlatform/global-esim/commit/34b2d57ad50d2bebad8c26c18ec3a761d8eeda57))
* us virgin islands misspell ([22910ec](https://github.com/InboundPlatform/global-esim/commit/22910ec7aefdf1f7e01fd64490489041c9284f70))
* virgin island image name ([412d4e7](https://github.com/InboundPlatform/global-esim/commit/412d4e744735ed047e7cc41a283a96b0806d81cb))

## [2.28.13](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.12...webjp-v2.28.13) (2024-12-25)


### Bug Fixes

* content fixes ([7da27ca](https://github.com/InboundPlatform/global-esim/commit/7da27ca97bd0e96ebebb375e656a5ffb79a097a3))

## [2.28.12](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.11...webjp-v2.28.12) (2024-12-24)


### Bug Fixes

* trigger release for column page update ([6cc85fc](https://github.com/InboundPlatform/global-esim/commit/6cc85fcd1b31bff3ab1a79d52d2b7d8c0dc8003b))

## [2.28.11](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.10...webjp-v2.28.11) (2024-12-19)


### Bug Fixes

* show total gb for fixed plans on checkout ([#1177](https://github.com/InboundPlatform/global-esim/issues/1177)) ([c1b7785](https://github.com/InboundPlatform/global-esim/commit/c1b7785d082a6ee97affd0f9c13100c755991cc6))

## [2.28.10](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.9...webjp-v2.28.10) (2024-12-16)


### Bug Fixes

* package ([374766f](https://github.com/InboundPlatform/global-esim/commit/374766ff67c5d0a81b7684aa800740fa9f87f67a))

## [2.28.9](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.8...webjp-v2.28.9) (2024-12-13)


### Bug Fixes

* add name registration guide ([#1138](https://github.com/InboundPlatform/global-esim/issues/1138)) ([dd006be](https://github.com/InboundPlatform/global-esim/commit/dd006be069b3d49b9b8064e25e64f13af59affa3))
* fixes for translations ([98f9c00](https://github.com/InboundPlatform/global-esim/commit/98f9c004497431488fcaca5743348257b8da8c2e))
* fixes for translations component ([1b3716b](https://github.com/InboundPlatform/global-esim/commit/1b3716be43522ee0d39e3117eba451ffe7972f4d))
* fixes for ui ([4076490](https://github.com/InboundPlatform/global-esim/commit/407649080278a0c1581fbcca4e624abd31455904))
* removed line button from the order page ([4e22dd7](https://github.com/InboundPlatform/global-esim/commit/4e22dd7607be9259f778e74ffd8baf2624b9afe1))

## [2.28.8](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.7...webjp-v2.28.8) (2024-12-12)


### Bug Fixes

* build issues webjp ([41dd56a](https://github.com/InboundPlatform/global-esim/commit/41dd56a12d5d10a60a0be646c5bf54d9231187ca))

## [2.28.7](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.6...webjp-v2.28.7) (2024-12-12)


### Bug Fixes

* removed currency change sp button ([aa0b207](https://github.com/InboundPlatform/global-esim/commit/aa0b20715eab5f78b3da4c92577fd54f11bd44fa))

## [2.28.6](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.5...webjp-v2.28.6) (2024-12-12)


### Bug Fixes

* add recaptcha ([ab1af8f](https://github.com/InboundPlatform/global-esim/commit/ab1af8f8c4a1bc97079a348d5e9bd827be942ccf))

## [2.28.5](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.4...webjp-v2.28.5) (2024-12-11)


### Bug Fixes

* build issues ([4debcfb](https://github.com/InboundPlatform/global-esim/commit/4debcfb49ac0007730be35d6351b26f15e6f9de4))

## [2.28.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.3...webjp-v2.28.4) (2024-12-06)


### Bug Fixes

* build issues ([3116055](https://github.com/InboundPlatform/global-esim/commit/3116055f08c7359d8de52b8b9eb4bf92131f7e88))

## [2.28.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.2...webjp-v2.28.3) (2024-12-05)


### Bug Fixes

* new design ([68f0ef9](https://github.com/InboundPlatform/global-esim/commit/68f0ef982157be2ff97f76f68d53cb90e92f8bb3))

## [2.28.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.1...webjp-v2.28.2) (2024-12-05)


### Bug Fixes

* build issue ([ff4315b](https://github.com/InboundPlatform/global-esim/commit/ff4315b51f88c19ccd596c04f52afaec1c125e65))

## [2.28.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.28.0...webjp-v2.28.1) (2024-12-03)


### Bug Fixes

* noindex ([9d94c1a](https://github.com/InboundPlatform/global-esim/commit/9d94c1a94b481d7d57185f29850ab810cc595779))

## [2.28.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.27.2...webjp-v2.28.0) (2024-12-02)


### Features

* add package page ([9f7f31b](https://github.com/InboundPlatform/global-esim/commit/9f7f31b1f8ce967ab2a963e869faba7056aacdd1))

## [2.27.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.27.1...webjp-v2.27.2) (2024-12-02)


### Bug Fixes

* build issues ([a3dfd73](https://github.com/InboundPlatform/global-esim/commit/a3dfd73e1072543b889c85dd23d291f5ee42a1f2))

## [2.27.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.27.0...webjp-v2.27.1) (2024-11-28)


### Bug Fixes

* build issues ([936d351](https://github.com/InboundPlatform/global-esim/commit/936d351c82e1388c015ab7032b2675d7bbf6dafd))

## [2.27.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.26.4...webjp-v2.27.0) (2024-11-15)


### Features

* add instant eSIM ([#1012](https://github.com/InboundPlatform/global-esim/issues/1012)) ([d776496](https://github.com/InboundPlatform/global-esim/commit/d776496384265260d042128a982a3c941368c585))


### Bug Fixes

* fv for gm esim mobile ([#1019](https://github.com/InboundPlatform/global-esim/issues/1019)) ([80af06c](https://github.com/InboundPlatform/global-esim/commit/80af06c7131dd0412052ce313390ef9bd3fa572c))
* manual fixes for pdf ([2be75d3](https://github.com/InboundPlatform/global-esim/commit/2be75d301852b0d9008a44987de3cee112a60b03))
* reduce empty space on hero sp ([#1004](https://github.com/InboundPlatform/global-esim/issues/1004)) ([cf95047](https://github.com/InboundPlatform/global-esim/commit/cf9504765a7b95f601061fff9789a54d8c69fade))
* reduce pixel height for hero on sp ([#1018](https://github.com/InboundPlatform/global-esim/issues/1018)) ([4b824fd](https://github.com/InboundPlatform/global-esim/commit/4b824fd255326c13067606dd1694592e314c621d))
* remove fixed size for hero on mobile ([#1020](https://github.com/InboundPlatform/global-esim/issues/1020)) ([9d7bd53](https://github.com/InboundPlatform/global-esim/commit/9d7bd53f44c5394a2b6c79b843a6914466ad3b61))
* update plan images ([#1022](https://github.com/InboundPlatform/global-esim/issues/1022)) ([1387554](https://github.com/InboundPlatform/global-esim/commit/1387554e43d6af6a394defb7609823db3aa3fec5))
* update spec list for korean plans ([#1005](https://github.com/InboundPlatform/global-esim/issues/1005)) ([3bab37b](https://github.com/InboundPlatform/global-esim/commit/3bab37b067d6f4982c80ce6856490d48a636f2e8))

## [2.26.4](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.26.3...webjp-v2.26.4) (2024-11-06)


### Bug Fixes

* compatibilty update for iphone 16 ([2475e85](https://github.com/InboundPlatform/global-esim/commit/2475e854175e1fb84ce0979e6ac53515d8661144))
* nikkei always for korean, taiwan and hawaii ([8942b3d](https://github.com/InboundPlatform/global-esim/commit/8942b3d6fbdb4e42620e4d6071145daf07fd95a8))

## [2.26.3](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.26.2...webjp-v2.26.3) (2024-11-05)


### Bug Fixes

* esim counter korea image change ([b5c95ac](https://github.com/InboundPlatform/global-esim/commit/b5c95ac47971b747e484122a2de87962b9df2184))

## [2.26.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.26.1...webjp-v2.26.2) (2024-11-01)


### Bug Fixes

* update price ([595a50a](https://github.com/InboundPlatform/global-esim/commit/595a50ae525e7ca95c0e197bde36745f8cc20a04))

## [2.26.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.26.0...webjp-v2.26.1) (2024-11-01)


### Bug Fixes

* jp for phone already exists error ([154616d](https://github.com/InboundPlatform/global-esim/commit/154616d6e0b517ea8e9c18dde0568223ffc062f1))

## [2.26.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.25.0...webjp-v2.26.0) (2024-10-31)


### Features

* update images ([450d631](https://github.com/InboundPlatform/global-esim/commit/450d63136f4c9aa8074c29c3b8c94dfcd8818c56))

## [2.25.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.24.0...webjp-v2.25.0) (2024-10-31)


### Features

* re-add top banner ([1a7ca0e](https://github.com/InboundPlatform/global-esim/commit/1a7ca0e3c1d1a536d41fabc9e4cf336cf5e7e589))
* trendy banner ([dd398ae](https://github.com/InboundPlatform/global-esim/commit/dd398ae63170ca43dd4bde68c49e8cb324069174))


### Bug Fixes

* image quality ([2d1d49d](https://github.com/InboundPlatform/global-esim/commit/2d1d49d69b842e1a02a1bb4773d47a91daafa068))

## [2.24.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.23.0...webjp-v2.24.0) (2024-10-31)


### Features

* text updates ([e7f1b26](https://github.com/InboundPlatform/global-esim/commit/e7f1b26397864162565dcda415020b71f6bdf310))

## [2.23.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.22.0...webjp-v2.23.0) (2024-10-30)


### Features

* text update ([7c04939](https://github.com/InboundPlatform/global-esim/commit/7c04939e952ff8319cd29d9a53332e049c327c1a))


### Bug Fixes

* campaign form scroll to top ([803961d](https://github.com/InboundPlatform/global-esim/commit/803961d8ad70ca057694b99e164759cac9be5d3c))

## [2.22.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.21.1...webjp-v2.22.0) (2024-10-30)


### Features

* SOMPO2411 coupon ([df2ada1](https://github.com/InboundPlatform/global-esim/commit/df2ada11e3939f08d28ee681eb30f6bd47dd26a4))

## [2.21.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.21.0...webjp-v2.21.1) (2024-10-29)


### Bug Fixes

* please release ([f6d354b](https://github.com/InboundPlatform/global-esim/commit/f6d354bb0155389f6e42d2ce6110c23807e11c00))

## [2.21.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.20.0...webjp-v2.21.0) (2024-10-29)


### Features

* 51025 campaign banner ([d2ca621](https://github.com/InboundPlatform/global-esim/commit/d2ca6214f5552999ede61e44c6dbe3edf8b48ccb))
* 51025 campaign banner updates ([6c1528a](https://github.com/InboundPlatform/global-esim/commit/6c1528aba7b6451b0fc1f8368d888218aea836d2))
* add email to app return values ([90220df](https://github.com/InboundPlatform/global-esim/commit/90220dfa6528f56a04b1e34cb5c70155721e92b0))
* campaign text updates ([7ac4189](https://github.com/InboundPlatform/global-esim/commit/7ac4189ea2239782799bd805e1b84612b5be55d4))
* country search updtes ([1b16f0b](https://github.com/InboundPlatform/global-esim/commit/1b16f0b1aeba4f121da4ee96b2a293cd2a92dad3))
* remove top banner, email template ([007a758](https://github.com/InboundPlatform/global-esim/commit/007a7584d250c23bc97216840195c91c3055a893))


### Bug Fixes

* campaign banner updates ([97ad1f0](https://github.com/InboundPlatform/global-esim/commit/97ad1f0d75c5ecf497a94828ac5a556cb83bf43f))
* modal position, country search fixes ([d8cba65](https://github.com/InboundPlatform/global-esim/commit/d8cba65aefcc307415a4a4457831d6aa02b8a8c6))
* phone number resend fix ([e45e10c](https://github.com/InboundPlatform/global-esim/commit/e45e10c7b7407061f44b54c7f30b91dc10e62258))

## [2.20.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.19.0...webjp-v2.20.0) (2024-10-24)


### Features

* instant esim pdf ([676f8d5](https://github.com/InboundPlatform/global-esim/commit/676f8d5ca9b5a8c0724b1a563499e13facdb39cf))
* language select updates ([34ff2ea](https://github.com/InboundPlatform/global-esim/commit/34ff2ea2a627f61ebde5007eeac9a4dca240c841))


### Bug Fixes

* temporary remove pdf download ([a7c50d0](https://github.com/InboundPlatform/global-esim/commit/a7c50d0606c95870654735e2200ca2ed8b519dec))

## [2.19.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.18.0...webjp-v2.19.0) (2024-10-10)


### Features

* redeploy ([f683203](https://github.com/InboundPlatform/global-esim/commit/f683203aa0a09720b7cd0707adf017333a2fc357))

## [2.18.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.17.2...webjp-v2.18.0) (2024-10-09)


### Features

* rebuild region page cache ([00094fa](https://github.com/InboundPlatform/global-esim/commit/00094fa15e9bef6c1120fd36338c9be8fd9090ca))


### Bug Fixes

* show error message on order page airtrip ([9ce0937](https://github.com/InboundPlatform/global-esim/commit/9ce0937f430e7c28d4748beab37615bc857cb3a4))

## [2.17.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.17.1...webjp-v2.17.2) (2024-10-04)


### Bug Fixes

* column page fixes ([510f430](https://github.com/InboundPlatform/global-esim/commit/510f4300022ea9eff9049abc0f6dc55da174a8ac))
* country page redirect ([229d252](https://github.com/InboundPlatform/global-esim/commit/229d2529cc7204c0049a7544f14226f2026555f9))

## [2.17.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.17.0...webjp-v2.17.1) (2024-10-04)


### Bug Fixes

* column page fixes ([ca5923a](https://github.com/InboundPlatform/global-esim/commit/ca5923a83262eee5d61cc70931609b570f00dc3c))

## [2.17.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.16.2...webjp-v2.17.0) (2024-09-27)


### Features

* change country page to static ([d625a5b](https://github.com/InboundPlatform/global-esim/commit/d625a5b4d47039d303414184e8cd0c06f290bd36))


### Bug Fixes

* show payment error message ([5650b76](https://github.com/InboundPlatform/global-esim/commit/5650b76d1476de143545fc6b881269dbb47ffc80))

## [2.16.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.16.1...webjp-v2.16.2) (2024-09-13)


### Bug Fixes

* column page 500 error ([7250463](https://github.com/InboundPlatform/global-esim/commit/725046348425a1b2839a0ffa50f41a6bd537c476))
* column page 500 error ([089917b](https://github.com/InboundPlatform/global-esim/commit/089917b726d2758eaf53b6450ba4b08e30afc59f))

## [2.16.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.16.0...webjp-v2.16.1) (2024-09-12)


### Bug Fixes

* login user purchase bug fix ([c39d5c7](https://github.com/InboundPlatform/global-esim/commit/c39d5c77536575a6d77abfe9e27a77671666558d))

## [2.16.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.15.2...webjp-v2.16.0) (2024-09-12)


### Features

* get iccid on complete page for guestc ([5e35c70](https://github.com/InboundPlatform/global-esim/commit/5e35c702c3bf0c9c3e50865ec35781ee85866e6a))
* remove iccid on thank you page ([456c6e3](https://github.com/InboundPlatform/global-esim/commit/456c6e327c0f6471c7a715d8df8cffe1363eee20))
* reset guest user password ([1d66447](https://github.com/InboundPlatform/global-esim/commit/1d66447ac94056f6bee167c1bf094d42d8ac9e72))
* text updates and resend email link ([c10ed9d](https://github.com/InboundPlatform/global-esim/commit/c10ed9df8b1289bd4489e2b4440dc55a3d005ad2))


### Bug Fixes

* dynamic rendering on column pages ([0b9b317](https://github.com/InboundPlatform/global-esim/commit/0b9b31700561a254d92b109e3de12da943ae34f8))
* login error translation fix ([ca9b890](https://github.com/InboundPlatform/global-esim/commit/ca9b89003d39e7d13048853f9787306d59135759))
* move complete page to no auth ([a888eb0](https://github.com/InboundPlatform/global-esim/commit/a888eb08dec18a6ba4b992f4d37569dcb3210df5))
* reset email logic fixes ([6199079](https://github.com/InboundPlatform/global-esim/commit/619907971b14fe50fa1c125afe26b71f5354dee9))

## [2.15.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.15.1...webjp-v2.15.2) (2024-09-11)


### Bug Fixes

* column remove no index ([c618e2f](https://github.com/InboundPlatform/global-esim/commit/c618e2f06f2020fc97eaf3133a1dd82d9b569ddc))

## [2.15.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.15.0...webjp-v2.15.1) (2024-09-05)


### Bug Fixes

* add 404 redirects to non-existent countries ([7767e3c](https://github.com/InboundPlatform/global-esim/commit/7767e3c7a361607993b2b156f7dc876a7eac0f94))

## [2.15.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.14.0...webjp-v2.15.0) (2024-08-29)


### Features

* 50860 column page esim ([bca7bcf](https://github.com/InboundPlatform/global-esim/commit/bca7bcf799bdb5990a147beefe7b3875ac997bc7))
* 50860 column page fixes ([f0f30ea](https://github.com/InboundPlatform/global-esim/commit/f0f30ea2be418b3495a7b4d6cd443012e567a489))
* 50860 column page popular columns ([4f2e6ac](https://github.com/InboundPlatform/global-esim/commit/4f2e6ac3c2808f00f83a791ff50fe8a7aeafefed))
* 51182 gm global navigation ([ad34d61](https://github.com/InboundPlatform/global-esim/commit/ad34d61f48a3a7356b00fca2aee108ff136cff13))

## [2.14.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.13.1...webjp-v2.14.0) (2024-08-23)


### Features

* add recaptcha to signup ([3d896e2](https://github.com/InboundPlatform/global-esim/commit/3d896e2f87d2692c36324ddc0e59f639e20957c3))


### Bug Fixes

* add recaptcha to callback ([fbd0ebb](https://github.com/InboundPlatform/global-esim/commit/fbd0ebb9c25d75a7ff6d2d6ba2d4b6ff141d3930))
* signup error message fix ([a54a96d](https://github.com/InboundPlatform/global-esim/commit/a54a96d498e27c366804e4b86934a24bbceb1639))
* translation fixes ([872db33](https://github.com/InboundPlatform/global-esim/commit/872db3302a86ce19d03c0dd6e0485f38da502954))

## [2.13.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.13.0...webjp-v2.13.1) (2024-08-16)


### Bug Fixes

* build issues webjp ([43f7986](https://github.com/InboundPlatform/global-esim/commit/43f7986c1847a072aa1695a6ad6dd0d0ef5fb684))

## [2.13.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.12.0...webjp-v2.13.0) (2024-08-16)


### Features

* add source parameter to gm esim signup ([637bde5](https://github.com/InboundPlatform/global-esim/commit/637bde54d99f9dfe06c95c907edcaede8fcfacd9))

## [2.12.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.11.0...webjp-v2.12.0) (2024-08-15)


### Features

* 50839 feedback updates 8/14 ([001c2b1](https://github.com/InboundPlatform/global-esim/commit/001c2b11954a40fde3209db6bb827e06789e3b3f))
* 51080 setup page ([e78ae66](https://github.com/InboundPlatform/global-esim/commit/e78ae6636c5b5a083ece984011c975b5adc55889))
* 51116 data usage note ([8d296f9](https://github.com/InboundPlatform/global-esim/commit/8d296f9f1ff21fd3ab7a315e7416b83a7d7316d2))


### Bug Fixes

* 51080 reset carrousel on tab change ([e667add](https://github.com/InboundPlatform/global-esim/commit/e667add9474154d7c797e64b4026cffd9fd53be1))

## [2.11.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.10.2...webjp-v2.11.0) (2024-08-08)


### Features

* 08/08 feedbacks ([d8423d4](https://github.com/InboundPlatform/global-esim/commit/d8423d489274ea7b97d097485ba6236cea70b5cf))

## [2.10.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.10.1...webjp-v2.10.2) (2024-08-07)


### Bug Fixes

* webjp social login issues ([39f1aec](https://github.com/InboundPlatform/global-esim/commit/39f1aec10c83e1ec1edd97d64f96ebecd4d3fa56))

## [2.10.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.10.0...webjp-v2.10.1) (2024-08-07)


### Bug Fixes

* deploy fix webjp ([c9bc136](https://github.com/InboundPlatform/global-esim/commit/c9bc1367216ac3b59341988c22dec220d8842833))

## [2.10.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.9.0...webjp-v2.10.0) (2024-08-07)


### Features

* 50839 feedback fixes ([a733ee4](https://github.com/InboundPlatform/global-esim/commit/a733ee45a757239b6726bd51946a34c95bfc4fce))
* 50839 feedback fixes ([8e44a17](https://github.com/InboundPlatform/global-esim/commit/8e44a17922ab78b58bd7e34ccf702e2465bf747c))
* 50839 feedback updates ([e518ec1](https://github.com/InboundPlatform/global-esim/commit/e518ec13f11d5423e3cc240c2fe667e31f7c7ee6))
* 51010 destination page ([36343dc](https://github.com/InboundPlatform/global-esim/commit/36343dce393a7bb984135053f624f85d06066fa1))
* add iccid to gm esim complete page ([56ab054](https://github.com/InboundPlatform/global-esim/commit/56ab0542200ca093fea87a7ff9e86480dd83cb5e))


### Bug Fixes

* 51010 uk search fix ([426641a](https://github.com/InboundPlatform/global-esim/commit/426641aa61f26bf5b2910e232ed38567332c55e5))
* image fixes, cherry pick fixes ([aa6567a](https://github.com/InboundPlatform/global-esim/commit/aa6567a74951b7379ccbca520b126890dbec3810))

## [2.9.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.8.1...webjp-v2.9.0) (2024-08-01)


### Features

* 50839 feedback fixes ([72ba8eb](https://github.com/InboundPlatform/global-esim/commit/72ba8eb1f1a5d68961c1182836c0a88a4d9a8a4c))
* 50964 add compatible function ([2be2fef](https://github.com/InboundPlatform/global-esim/commit/2be2fefd36f318de4780bb8e9dea3e11aad34621))
* gm esim search module ([d7fa8ae](https://github.com/InboundPlatform/global-esim/commit/d7fa8ae36d1c9bc8119c0f9ab327fffd5560a096))
* search by upper case ([0fa3fe5](https://github.com/InboundPlatform/global-esim/commit/0fa3fe52f0d2df531b612c5607e9914c90d80aae))


### Bug Fixes

* 50948 text updates ([5588f76](https://github.com/InboundPlatform/global-esim/commit/5588f7634d7131c97ea0c34120e7cdd604ca8274))
* 50964 translation fixes, image fixes ([eb6cc31](https://github.com/InboundPlatform/global-esim/commit/eb6cc313c6d613f62d5818864b27f5ad89711a91))
* image fixes, cherry pick fixes ([ba4626c](https://github.com/InboundPlatform/global-esim/commit/ba4626cff4137683e8d903d602f380e3b7ec6c44))
* search module missing image fix ([fa031c4](https://github.com/InboundPlatform/global-esim/commit/fa031c4c041da815c13dc32a97efa69ad52981d7))

## [2.8.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.8.0...webjp-v2.8.1) (2024-07-29)


### Bug Fixes

* remove spaces in country names ([8c73ab1](https://github.com/InboundPlatform/global-esim/commit/8c73ab197496cd3f9c076e09ebd806b07c61ed2e))
* top page sort fix ([637bfe6](https://github.com/InboundPlatform/global-esim/commit/637bfe6edc86b6f72acb3172dba82aaffce84d06))

## [2.8.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.7.2...webjp-v2.8.0) (2024-07-26)


### Features

* added recaptcha for airtrip fe ([48855de](https://github.com/InboundPlatform/global-esim/commit/48855deb4b273b92156f1ad75d428bf780b561d1))
* added recaptcha for fe ([520d2c0](https://github.com/InboundPlatform/global-esim/commit/520d2c00bcd603d5f20a39eb540821df991e5ff2))


### Bug Fixes

* add loading indicator for signin signup buttons ([9a58ad6](https://github.com/InboundPlatform/global-esim/commit/9a58ad613f10b7b0645674f5f54bedf2224ec44c))
* login error translations ([7720fb3](https://github.com/InboundPlatform/global-esim/commit/7720fb34769856bb82befa8f1b5123e9568fb957))

## [2.7.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.7.1...webjp-v2.7.2) (2024-07-26)


### Bug Fixes

* image fix ([b39d3b8](https://github.com/InboundPlatform/global-esim/commit/b39d3b89e6dc45c9d4793572f077d9f3b603892c))

## [2.7.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.7.0...webjp-v2.7.1) (2024-07-25)


### Bug Fixes

* missing images and api parameter fix ([f358950](https://github.com/InboundPlatform/global-esim/commit/f358950aa677c240267de70f3aea3992ebceaa8e))

## [2.7.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.6.2...webjp-v2.7.0) (2024-07-25)


### Features

* 50973 add new countries ([b830ee7](https://github.com/InboundPlatform/global-esim/commit/b830ee79cb9d04eb167b2cfd4346ab5cbdc674e8))
* 50973 updated countries price, country images ([b6b6d7d](https://github.com/InboundPlatform/global-esim/commit/b6b6d7dea667e4f074d6633c7a951bbfd324c004))
* countries translation updates, mobile ui updates ([ff483dd](https://github.com/InboundPlatform/global-esim/commit/ff483dd16abdc6cf2232ca9e32d47a349481641e))

## [2.6.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.6.1...webjp-v2.6.2) (2024-07-23)


### Bug Fixes

* region page currency symbol fix ([0e00ead](https://github.com/InboundPlatform/global-esim/commit/0e00ead32b28eb42a55d28a106d26531ffb4b3cb))

## [2.6.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.6.0...webjp-v2.6.1) (2024-07-22)


### Bug Fixes

* lgu feedback fixes ([#605](https://github.com/InboundPlatform/global-esim/issues/605)) ([624aebf](https://github.com/InboundPlatform/global-esim/commit/624aebf7dc8a0ebe69c6efd2158d7d12b336a4c0))
* regional data fix webjp ([2c99fd1](https://github.com/InboundPlatform/global-esim/commit/2c99fd122ef583ff94f0fecf3b76d6e6e458d33e))
* regional data fix webjp ([2c99fd1](https://github.com/InboundPlatform/global-esim/commit/2c99fd122ef583ff94f0fecf3b76d6e6e458d33e))
* regional data fix webjp ([f4f38a3](https://github.com/InboundPlatform/global-esim/commit/f4f38a3d782101d6403b2ca488e273586837afa4))
* regional data fix webjp ([5d97a6c](https://github.com/InboundPlatform/global-esim/commit/5d97a6c7678b245cc8cf5c186cc8a47cbeffae8b))

## [2.6.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.5.0...webjp-v2.6.0) (2024-07-18)


### Features

* 50839 lgu updates ([545b72d](https://github.com/InboundPlatform/global-esim/commit/545b72d93fd050c5638c38f2963bf78e1855f4d0))
* 50839 show LGU and ui updates ([46e1caa](https://github.com/InboundPlatform/global-esim/commit/46e1caad56b0716d254e5a95241ff2319d1b0a85))
* added featured plans to public and cdn ([1a16f73](https://github.com/InboundPlatform/global-esim/commit/1a16f733531d63345b673c30dfaa60b3f581e801))
* airtrip ui/ux updates, passport form updates ([56bb006](https://github.com/InboundPlatform/global-esim/commit/56bb0067b4137eb079ec3dd2a5d346d7ce944e66))


### Bug Fixes

* add auth signin/signup back to webjp ([b0590d6](https://github.com/InboundPlatform/global-esim/commit/b0590d67279f58bd7bff9096a69a96f097dfc3be))
* add auth signin/signup back to webjp ([f2f574f](https://github.com/InboundPlatform/global-esim/commit/f2f574f2f7fc8a70a4c1753790e239506a7e8d2a))
* build error fixes ([a149743](https://github.com/InboundPlatform/global-esim/commit/a14974312c57bc5561b4487492445c9a6b01dbc0))
* cdn image fix ([f9a4fbe](https://github.com/InboundPlatform/global-esim/commit/f9a4fbe41cfca9946c3b1bf13468143d7069613d))
* disable sso for now ([89926a9](https://github.com/InboundPlatform/global-esim/commit/89926a92e912dc3b2472970992291302a6a6d8eb))
* get plans api parameter fix ([16388f1](https://github.com/InboundPlatform/global-esim/commit/16388f161116480a23672b420ff1e4e3d491066a))
* gm esim jp signin page translation fix ([bab5a91](https://github.com/InboundPlatform/global-esim/commit/bab5a91037ba6f6667be04eb3b8339d4869e3f74))
* guide link fix ([2c21f28](https://github.com/InboundPlatform/global-esim/commit/2c21f2830a931a02e6b22d3accfb9f42cbae434b))
* lgu banner link fix ([9324de5](https://github.com/InboundPlatform/global-esim/commit/9324de5e29e48df7e2dfc0f94bc30e7e6a328754))
* lgu fixes ([8f89d23](https://github.com/InboundPlatform/global-esim/commit/8f89d23bf918a4d801debea213fa224e88c74db7))
* link fixes ([291569d](https://github.com/InboundPlatform/global-esim/commit/291569d132f0f09b8a0628e537fb73f532713f88))
* qr code src fix ([5f56cd3](https://github.com/InboundPlatform/global-esim/commit/5f56cd378e69b9addbd425dd5013515da01f6131))
* resend verification email link ([dcefa1a](https://github.com/InboundPlatform/global-esim/commit/dcefa1a5c01a1c2a5e173669658d65834f815a99))
* text and redirection fixes ([ed52b02](https://github.com/InboundPlatform/global-esim/commit/ed52b028e89ac4ab5e488e8051659b6bb380562b))
* text and translation fixes ([edf22fa](https://github.com/InboundPlatform/global-esim/commit/edf22fa2a38a3e6b8c8871cf68c8d6086134096f))
* wip ([095d7c3](https://github.com/InboundPlatform/global-esim/commit/095d7c35e51ce02c5fee2bad706dd86edb28943c))

## [2.5.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.4.1...webjp-v2.5.0) (2024-07-11)


### Features

* 50839 50932 ui/ux updates ([2ac3050](https://github.com/InboundPlatform/global-esim/commit/2ac30506725de33bff032df27e2cdf26434d6e1b))
* 50881 gm passport form ui/ux updates ([d8bf771](https://github.com/InboundPlatform/global-esim/commit/d8bf7719668e491a761bdbeec9fe37597e8a05b5))
* 50931 added traffic data ui ([ee1a61e](https://github.com/InboundPlatform/global-esim/commit/ee1a61e49f7990f73ced42819653daa78b506180))
* added new countries tawain hongkong ([42ef3e9](https://github.com/InboundPlatform/global-esim/commit/42ef3e9a91214cacc522c4fc996012a1bbf09285))
* airtrip hongkong taiwan, moved plan api call to client ([0cf53b8](https://github.com/InboundPlatform/global-esim/commit/0cf53b85c9425375a1497b497be33d708da77686))
* bg updates, scroll to traffic data ([058dfdb](https://github.com/InboundPlatform/global-esim/commit/058dfdb56aa033badb1590ad741a74179436f9c7))
* gm taiwan hongkong ([1410eb0](https://github.com/InboundPlatform/global-esim/commit/1410eb08e6da831f95893ad422c59d288f93dbae))
* order form appeal, text fixes ([3a0e9c1](https://github.com/InboundPlatform/global-esim/commit/3a0e9c157d28bef5eb14aa7532aa677efe29c8d2))
* top page country sort and price updates ([1b8edc0](https://github.com/InboundPlatform/global-esim/commit/1b8edc02ee4f3b6f4fa30bfd8fc103b95296ed18))
* update loading ui ([bbca723](https://github.com/InboundPlatform/global-esim/commit/bbca723dcf6ef4b46df25f9d9eaa8d23061d595d))


### Bug Fixes

* complete page message fix ([4272689](https://github.com/InboundPlatform/global-esim/commit/4272689225fb4d4780e1aa9323d6c44e773430f1))
* feedback fixes ([f0fb6c7](https://github.com/InboundPlatform/global-esim/commit/f0fb6c7aa52eee78fb5b6cd209a0600f1ab28ed3))
* feedback fixes 07/11 ([c816c1b](https://github.com/InboundPlatform/global-esim/commit/c816c1bc907b40e338b1748b3a150ad941f70aa5))
* gm complete redirect fix ([92a2b40](https://github.com/InboundPlatform/global-esim/commit/92a2b40094ef2aebbe04f08283a56e4d0bb47ca4))
* gm specs scroll area ([e274990](https://github.com/InboundPlatform/global-esim/commit/e274990188c56eae065ce872764b353c67d0eefe))
* gm specs scroll area ([cf73f91](https://github.com/InboundPlatform/global-esim/commit/cf73f91e941ae44512efdcda7701c1e290df287a))
* need registration session fix ([16d550e](https://github.com/InboundPlatform/global-esim/commit/16d550e2bf19ba3db761bfa0e7a49965aab6beb8))
* text fixes ([f863083](https://github.com/InboundPlatform/global-esim/commit/f863083f8d53182cc66f01737b373a71aeb82479))
* translation fix ([2aec554](https://github.com/InboundPlatform/global-esim/commit/2aec5543f08fd6abc962f475978316e79620d29e))

## [2.4.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.4.0...webjp-v2.4.1) (2024-07-08)


### Bug Fixes

* redeploy static page ([122737b](https://github.com/InboundPlatform/global-esim/commit/122737b7a7234196572ef4f614d7313ddb0e4cff))

## [2.4.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.3.1...webjp-v2.4.0) (2024-07-05)


### Features

* add line button, re-add guam ([d38edfd](https://github.com/InboundPlatform/global-esim/commit/d38edfd16322d3c2c9b18c622d60d045ee95fe01))

## [2.3.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.3.0...webjp-v2.3.1) (2024-07-04)


### Bug Fixes

* remove guam temporarily ([07a48f4](https://github.com/InboundPlatform/global-esim/commit/07a48f48be4a8cc91bcb54b00e6807064ec8cae7))

## [2.3.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.2.0...webjp-v2.3.0) (2024-07-04)


### Features

* 50449 android guide images ([46b4473](https://github.com/InboundPlatform/global-esim/commit/46b44735294ab261477af689d54c40087ce9a853))
* 50449 gm esim activation page updates ([5590470](https://github.com/InboundPlatform/global-esim/commit/5590470b638a9208152e8c09f33f2fa6e407df56))
* 50567 compatible page redesign ([0909b82](https://github.com/InboundPlatform/global-esim/commit/0909b820d578e219bf9b70eb252689f1956ad7ae))
* 50567 removed modal for future redesign ([5db9f8c](https://github.com/InboundPlatform/global-esim/commit/5db9f8c9139d97fe6568ce975fd494c0dbe42927))
* 50759 faq page updates for airtrip and gm ([3f66850](https://github.com/InboundPlatform/global-esim/commit/3f668504a34fec94066f8f2d70dd4c1db6b9d627))
* 50845 order form redesign ([1bb6c6c](https://github.com/InboundPlatform/global-esim/commit/1bb6c6c4587a53459a9e88a41560e94cbcc0a798))
* added new countries to gm esim ([20e88e6](https://github.com/InboundPlatform/global-esim/commit/20e88e601d6e323f9052df7947f7393dc6e44ff8))
* handle unlimited plans ([266ba44](https://github.com/InboundPlatform/global-esim/commit/266ba44209f824dbac697f126b44bbaa7d2fba54))
* help page redirection ([d6ff8d1](https://github.com/InboundPlatform/global-esim/commit/d6ff8d15ebbdaf68e4d01a67eb9e23a4fc147ecb))
* new faq page ([f41af7f](https://github.com/InboundPlatform/global-esim/commit/f41af7f13c5b03dbf47e9d1b9d938b04fb1d11f1))


### Bug Fixes

* 50839 gm scroll fix, ui updates ([ec9deb9](https://github.com/InboundPlatform/global-esim/commit/ec9deb98a13b139aedcfbd84c6abdfc276ae12ec))
* 50839 setup guide fixes ([b2bf6b9](https://github.com/InboundPlatform/global-esim/commit/b2bf6b954378266fff2bbde24ab81a73cb675f4e))
* 50839 text updates, validation updates ([edb2262](https://github.com/InboundPlatform/global-esim/commit/edb226231370178fb7e970af67161a63c1f136eb))
* button size fixes, text size fixes, link fixes ([34d64d3](https://github.com/InboundPlatform/global-esim/commit/34d64d3a4662744bd2549fd2d2420374f0916f9b))
* icons, style fixes ([b2a68e2](https://github.com/InboundPlatform/global-esim/commit/b2a68e29ee3f433a09a34cbf53d07088db4ded61))
* login error message update ([626e5d2](https://github.com/InboundPlatform/global-esim/commit/626e5d22dcb0e3ac9de61afbac1eac0a0389e549))
* missing gm esim activation texts ([829a4ef](https://github.com/InboundPlatform/global-esim/commit/829a4ef49b740ac18c9cc8dd96d8f4bbde55942f))
* missing hawaii translation ([826e6e0](https://github.com/InboundPlatform/global-esim/commit/826e6e091c1175b06cbf6bff160fb25eeaf1c8da))
* redirect update ([0fbdfd9](https://github.com/InboundPlatform/global-esim/commit/0fbdfd95c32b95958967b89161bc10338a29b9eb))
* style update ([aa7e5bf](https://github.com/InboundPlatform/global-esim/commit/aa7e5bf24b22849a96eee73246b83d919d62ec49))
* url update ([d567e6f](https://github.com/InboundPlatform/global-esim/commit/d567e6f1b361ac82fa0a4889dc01060c77f0dcdc))

## [2.2.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.1.0...webjp-v2.2.0) (2024-07-01)


### Features

* added line button, removed lgu contents ([4fd7f11](https://github.com/InboundPlatform/global-esim/commit/4fd7f11c642e89f1699e7d3245bd7f368970b736))
* added reset password, resend email pages ([157c1fd](https://github.com/InboundPlatform/global-esim/commit/157c1fd167bf48d862ab144b2280ca1664847971))
* init airtrip kv ([37ce23b](https://github.com/InboundPlatform/global-esim/commit/37ce23bbba2d33c467bb376184715e8749c9b049))
* sp kv updates ([f045019](https://github.com/InboundPlatform/global-esim/commit/f0450197569b798198bc6a59abfbd721ade2da2c))
* update airtrip register api ([2322082](https://github.com/InboundPlatform/global-esim/commit/2322082357fdae2ff24851f1873f130f2cc14761))
* update korea banners to lgu ([b9e1cee](https://github.com/InboundPlatform/global-esim/commit/b9e1cee9fecae313717194b52fb3b9db22b7a8bc))


### Bug Fixes

* 50839 added no user error message ([7f8623e](https://github.com/InboundPlatform/global-esim/commit/7f8623ede3ace3ca8343c3afea848137e9efa594))
* added terms and conditions page ([ee7d68c](https://github.com/InboundPlatform/global-esim/commit/ee7d68cd48fdf794012affb915f9f10b55b131f4))
* airtrip link fixes, korea counter details, lgu compatiblity ([6ac7ec0](https://github.com/InboundPlatform/global-esim/commit/6ac7ec0d280eb365b1b81195edda1cf235d3c5ef))
* changed name char limit to 1, update error messages to jp ([2aa461c](https://github.com/InboundPlatform/global-esim/commit/2aa461cb704eb468dc8f993a8ba3003043337e02))
* feedback fixes ([2ff26a6](https://github.com/InboundPlatform/global-esim/commit/2ff26a672afd17be780ff0c5b389889c729b824f))
* spacing fixes ([00c33b7](https://github.com/InboundPlatform/global-esim/commit/00c33b7d99c00e26b3c403a1307cc9e3e132ea9e))
* spacing fixes ([0ac9d8d](https://github.com/InboundPlatform/global-esim/commit/0ac9d8d07fa38196eb59c46807e1368e1788c013))

## [2.1.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.0.2...webjp-v2.1.0) (2024-06-19)


### Features

* contact page and help page updates ([7c7f858](https://github.com/InboundPlatform/global-esim/commit/7c7f8588219528bc5a7ea2edafacca08e66880b0))


### Bug Fixes

* feedback fixes 06/19 ([79d7dc6](https://github.com/InboundPlatform/global-esim/commit/79d7dc6fbd9fa17cb14514bb37a54b68606c08a9))
* footer links ([df58ab3](https://github.com/InboundPlatform/global-esim/commit/df58ab3aa1012e4353690b5375317be70f63747b))
* lang and currency modal fixes ([3de2717](https://github.com/InboundPlatform/global-esim/commit/3de2717b81292bbfeff45d49a208b0e289f7c3f9))
* **page:** missing locale for ToM page ([#494](https://github.com/InboundPlatform/global-esim/issues/494)) ([167ed1f](https://github.com/InboundPlatform/global-esim/commit/167ed1f80d935b1651e032e766948ad1c8d9d244))
* redirect profile page to top page# ([ab922c0](https://github.com/InboundPlatform/global-esim/commit/ab922c0aa36d91caf192b7b03ebdd399cdc6c66f))

## [2.0.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.0.1...webjp-v2.0.2) (2024-06-14)


### Bug Fixes

* manual url fix ([dce3a22](https://github.com/InboundPlatform/global-esim/commit/dce3a22365e06af2f9a9f3fa0c6b6a71578cead8))

## [2.0.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.0.0...webjp-v2.0.1) (2024-06-14)


### Bug Fixes

* **webjp:** hotfixes for webjp release ([a827704](https://github.com/InboundPlatform/global-esim/commit/a8277049fefbbd484c76fbd5a912a8b36663b3a6))

## [2.0.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v1.5.0...webjp-v2.0.0) (2024-06-14)


### ⚠ BREAKING CHANGES

* **web-jp:** new release

### Features

* **web-jp:** add japanese pc version ([95995bd](https://github.com/InboundPlatform/global-esim/commit/95995bdb9f45f263e3c81355c1446db372515bb3))


### Bug Fixes

* added dependency packages ([aa7b3c2](https://github.com/InboundPlatform/global-esim/commit/aa7b3c2facf37b5b6fe9ad01133c379478277d79))
* added eslint shared config ([b99cb7f](https://github.com/InboundPlatform/global-esim/commit/b99cb7fbe5124c07ee0a578799138d61b417d3b6))
* added eslint shared config ([46f47a4](https://github.com/InboundPlatform/global-esim/commit/46f47a4759d814005475d256ebd7525f5228b082))
* added eslint shared config ([24a63de](https://github.com/InboundPlatform/global-esim/commit/24a63de5a8ed02d9076948bc209185b33e2e4e2b))
* react dom version fix ([0505ad7](https://github.com/InboundPlatform/global-esim/commit/0505ad765baa7c839fe15bc752b335b7ebf1f404))
* translation error on packages ([c96da3e](https://github.com/InboundPlatform/global-esim/commit/c96da3e7c2ec03c79e040a481376afb595877011))
* update webjp node version ([3c6d029](https://github.com/InboundPlatform/global-esim/commit/3c6d029994149dc3a03a865fdcc1cf8cc10b7001))
* web jp only build fixes ([7827116](https://github.com/InboundPlatform/global-esim/commit/78271167678421c5ad349b7939c2e9fc832bc74f))
