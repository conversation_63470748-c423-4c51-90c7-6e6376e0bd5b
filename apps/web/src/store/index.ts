import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import Storage from "@/utils/storage";

import type { IPlan } from "@/interfaces/IPlan";
import { IQuotation } from "@/interfaces/IQuotation";

interface IOrderState {
  isShowDrawer: boolean;
  selectedPlanType?: string;
  selectedPlan?: IPlan;
  isCompatibilityModalOpen: boolean;
  setSelectedPlanType: (type: string) => void;
  setSelectedPlan: (plan: IPlan) => void;
  toggleDrawer: () => void;
  closeDrawer: () => void;
  toggleCompatibilityModalOpen: () => void;
}

export const useOrderStore = create<IOrderState>()((set) => ({
  isShowDrawer: false,
  isCompatibilityModalOpen: false,
  setSelectedPlanType: (type: string) =>
    set(() => ({ selectedPlanType: type })),
  setSelectedPlan: (plan: IPlan) => set(() => ({ selectedPlan: plan })),
  toggleDrawer: () => set((state) => ({ isShowDrawer: !state.isShowDrawer })),
  closeDrawer: () => set(() => ({ isShowDrawer: false })),
  toggleCompatibilityModalOpen: () => {
    set((state) => ({
      ...state,
      isCompatibilityModalOpen: !state.isCompatibilityModalOpen,
    }));
  },
}));

export const useSelectedPlan = create<{
  plan?: IPlan;
  setPlan: (plan: Partial<IPlan>) => void;
}>((set) => ({
  plan: undefined,
  setPlan: (plan: Partial<IPlan>) =>
    //@ts-ignore
    set((state) => ({
      ...state,
      plan,
    })),
}));

export interface ICart {
  plan: IPlan;
  insurance?: string;
  id: string;
  quantity: number;
}
export const useShoppingCart = create<{
  quotation?: IQuotation;
  setQuotation: (quotation: IQuotation) => void;
  showCartMenu: boolean;
  toggleCartMenu: (state: boolean) => void;
  items: ICart[];
  removeFromCart: (id: string) => void;
  addToCart: (item: ICart) => void;
  removeBulk: (ids: string[]) => void;
  addBulk: (items: ICart[]) => void;
  clearItems: () => void;
  coupon?: string;
  setCoupon: (coupon: string) => void;
}>(
  // @ts-expect-error
  persist(
    (set) => ({
      coupon: undefined,
      setCoupon: (coupon: string) =>
        set((state) => ({
          ...state,
          coupon,
        })),
      clearItems: () =>
        set((state) => ({
          ...state,
          items: [],
          quotation: undefined,
          showCartMenu: false,
        })),
      quotation: undefined,
      setQuotation: (quotation: IQuotation) =>
        set((state) => ({
          ...state,
          quotation,
        })),
      showCartMenu: false,
      toggleCartMenu: (status: boolean) =>
        set((state) => ({
          ...state,
          showCartMenu: status,
        })),
      items: [],
      addBulk: (items: ICart[]) =>
        set((state) => {
          const nextCart = [...state.items, ...items];
          Storage.getInstance().set("cart", nextCart);
          return {
            ...state,
            items: nextCart,
          };
        }),
      removeBulk: (ids: string[]) =>
        set((state) => {
          const nextCart = state.items.filter((item) => !ids.includes(item.id));
          Storage.getInstance().set("cart", nextCart);
          return {
            ...state,
            items: nextCart,
          };
        }),
      removeFromCart: (id: string) =>
        set((state) => {
          const nextCart = state.items.filter((item) => item.id !== id);
          Storage.getInstance().set("cart", nextCart);
          return {
            ...state,
            items: nextCart,
          };
        }),

      addToCart: (item: ICart) =>
        set((state) => {
          const nextCart = [...state.items, item];
          Storage.getInstance().set("cart", nextCart);
          return {
            ...state,
            items: nextCart,
          };
        }),
    }),
    {
      name: "cart-storage", // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => sessionStorage), // (optional) by default, 'localStorage' is used
      partialize: (state) =>
        Object.fromEntries(
          Object.entries(state).filter(([key]) => !["quotation"].includes(key))
        ),
    }
  )
);
export const useCheckoutEmail = create<{
  email: string | undefined;
  setEmail: (email: string) => void;
  clearEmail: () => void;
}>()(
  persist(
    (set, get) => ({
      email: undefined,
      setEmail: (email) => set({ email: (get().email = email) }),
      clearEmail: () => {
        useCheckoutEmail.persist.clearStorage();
        set({ email: undefined });
      },
    }),
    {
      name: "checkout-email",
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) => ({ email: state.email }),
    }
  )
);
