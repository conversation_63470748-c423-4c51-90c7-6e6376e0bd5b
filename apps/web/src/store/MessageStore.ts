import { create } from 'zustand';

export enum AlertMessageType {
  SUCCESS,
  ERROR,
}

export const useMessageStore = create<{
  isLoading: boolean;
  message: React.ReactNode | string | null;
  messageType: AlertMessageType;
  setMessage: (
    message: React.ReactNode | string | null,
    messageType?: AlertMessageType
  ) => void;
  toggleLoading: (defaultValue?: boolean) => void;
}>((set) => ({
  isLoading: false,
  message: null,
  messageType: AlertMessageType.ERROR,
  setMessage: (message, messageType) =>
    set((state) => ({
      ...state,
      message,
      messageType: messageType ?? AlertMessageType.ERROR,
    })),
  toggleLoading: (defaultValue?: boolean) =>
    set((state) => ({
      ...state,
      isLoading: defaultValue ?? !state.isLoading,
    })),
}));