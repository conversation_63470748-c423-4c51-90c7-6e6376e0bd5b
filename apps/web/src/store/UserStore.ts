import { create } from "zustand";

import type { IProfile } from "@/interfaces/IProfile";

export const useRegisterUser = create<{
  email: string;
  setEmail: (email: string) => void;
}>((set) => ({
  email: "",
  setEmail: (email: string) => set((s) => ({ email })),
}));

export const useProfile = create<{
  profile?: IProfile;
  setPorfile: (profile: Partial<IProfile>) => void;
}>((set) => ({
  profile: undefined,
  //@ts-ignore
  setPorfile: (profile) => set((state) => ({ ...state, profile: profile })),
}));
