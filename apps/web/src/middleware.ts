import { type NextRequest, NextResponse } from "next/server";

import acceptLanguage from "accept-language";
import { i18nRouter } from "next-i18n-router";

import { fallbackLng, locales } from "./i18n/settings";

acceptLanguage.languages(locales);

const PUBLIC_FILE = /\.(.*)$/;

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|assets|favicon.ico|sw.js|assets|site.webmanifest).*)",
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
    { source: "/" },
  ],
};

function handleAffiliate(request: NextRequest, url: URL): NextResponse | null {
  const referer = request.headers.get("referer");
  if (!referer) return null;

  const existing = request.cookies.get("affiliate")?.value;

  const response = NextResponse.next();
  if (!existing) {
    response.cookies.set("affiliate", referer, {
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: "/",
    });

    return response;
  }

  return null;
}

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();

  // Affiliate handling
  const affiliateResponse = handleAffiliate(request, url);
  if (affiliateResponse) return affiliateResponse;

  request.headers.set("x-url", request.url);

  if (PUBLIC_FILE.test(request.nextUrl.pathname)) {
    return;
  }

  //@ts-ignore
  return i18nRouter(request, {
    locales,
    defaultLocale: fallbackLng,
    basePath: process.env.NEXT_PUBLIC_BASE_PATH || undefined,
  });
}
