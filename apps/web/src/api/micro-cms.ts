import { createClient } from "microcms-js-sdk";
import {
  IBlog,
  IGetColumnsParams,
  ILpDescription,
} from "@repo/ui/src/interfaces/IBlog";

export const cmsClient = createClient({
  serviceDomain: "gmobile",
  apiKey: "bsctEHmp2D6BfPgl4kJmQEx4xnxYgAB8CNN2",
});


export async function getColumns(params: IGetColumnsParams): Promise<IBlog[]> {
  try {
    const data = await cmsClient.get({
      endpoint: "columns",
      queries: params,
    });

    return data?.contents || [];
  } catch (err) {
    console.error(err);

    return [];
  }
}

export async function getColumnItem(
  id: string,
  filter?: string,
  draftKey?: string
): Promise<IBlog | null> {
  try {
    const column = await cmsClient.get({
      endpoint: "columns",
      queries: { filters: filter, draftKey: draftKey },
      contentId: id,
    });

    return column || null;
  } catch (err) {
    console.error(err);

    return null;
  }
}

export async function getLpDescription(
  params: IGetColumnsParams,
): Promise<ILpDescription | null> {
  try {
    const data = await cmsClient.get({
      endpoint: "country",
      queries: params, // { filters: `country[contains]${area}`, ...fields },
    });

    if (!data || data?.totalCount === 0) {
      return null;
    } else if (params.limit === 1) {
      return data?.contents[0];
    } else {
      return data?.contents;
    }
  } catch (err) {
    console.error(err);

    return null;
  }
}
