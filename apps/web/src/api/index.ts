import axios from "axios";

import Storage from "@/utils/storage";

import { IPostOrderPayload } from "@/interfaces/IPostOrderPayload";
import { IProfile } from "@/interfaces/IProfile";
import { IQuotationPayload } from "@/interfaces/IQuotationPayload";

import { AUTH_TOKEN_NAME } from "@/app/constants";

type RequestOriginServiceNameQuery = {
  requestOriginServiceName?: string;
};

const baseURL = process.env.NEXT_PUBLIC_API_HOST;

export const httpClient = axios.create({
  baseURL,
});
const resendVerificationEmail = (email: string) =>
  httpClient.get("/auth/resend-verification", {
    params: {
      email,
    },
  });
export const privateHttpClient = axios.create({
  baseURL,
  validateStatus: (status) => status >= 200 && status < 300, // default,
});

httpClient.interceptors.request.use((config) => {
  const serviceName = process.env.NEXT_PUBLIC_SERVICE_NAME;
  const userPool = process.env.NEXT_PUBLIC_USER_POOL;
  if (userPool) config.headers["x-user-pool"] = userPool;

  if (serviceName) config.headers["x-service-name"] = serviceName;
  return config;
});

privateHttpClient.interceptors.request.use((config) => {
  const serviceName = process.env.NEXT_PUBLIC_SERVICE_NAME;
  const userPool = process.env.NEXT_PUBLIC_USER_POOL;
  const token = Storage.getInstance().get(AUTH_TOKEN_NAME);

  if (userPool) config.headers["x-user-pool"] = userPool;
  if (serviceName) config.headers["x-service-name"] = serviceName;

  if (config.headers && token?.accessToken)
    config.headers["Authorization"] = `Bearer ${token?.accessToken || ""}`;

  return config;
});

const login = (payload: object) => httpClient.post("/auth/login", payload);
const register = (payload: object) =>
  httpClient.post("/auth/register", payload);

const getProfile = (query: object) =>
  privateHttpClient.get("/auth/profile", {
    params: query,
  });

const getPlans = (query: object) =>
  httpClient.get("/plans", {
    params: query,
  });
const getPlanById = (planId: string) => httpClient.get("/plans/" + planId, {});

const getClientSecret = (payload: {}) => {
  return privateHttpClient.post("/payment/setup", payload);
};
const purchasePlan = (plan: object) =>
  privateHttpClient.post("/esim/subscribe", plan);
const purchasePlanAsGuest = (plan: object) =>
  privateHttpClient.post("/esim/subscribe/guest", plan);

const updateProfile = (profile: Pick<IProfile, "firstName" | "lastName">) =>
  privateHttpClient.post("/auth/profile", profile);

const getOrders = (options: object) =>
  privateHttpClient.get("/esim/orders", {
    params: options,
  });
const getOrder = (orderId: string, options?: object) =>
  privateHttpClient.get("/esim/orders/" + orderId, {
    params: options,
  });

const forgotPassword = (email: string) =>
  httpClient.post("/auth/forgot-password", {
    email,
  });

const resetPassword = (payload: {
  confirmationCode: string;
  newPassword: string;
  email: string;
}) => httpClient.post("/auth/confirm-password", payload);

const saveUserFirebaseToken = (token: string) => {
  return privateHttpClient.post("/notifications/register-token", {
    token,
  });
};

const instantEsim = (payload: { secret: string; [key: string]: string }) =>
  httpClient.post("/esim/instant-esim", payload);

const instantEsimNew = (payload: { secret: string; [key: string]: string }) =>
  httpClient.post("/esim/instant-esim-new", payload);

const corporateSubscribe = (
  corporateCode: string,
  corporateId: string,
  payload: object
) => {
  return privateHttpClient.post(
    `/esim/corporate/${corporateCode}/${corporateId}/subscribe`,
    payload
  );
};
const getQuotation = (payload: object, query?: RequestOriginServiceNameQuery) =>
  httpClient.post("/esim/order/quotation-new", payload, { params: query });
const getOrderQuotation = (
  payload: IQuotationPayload,
  query?: RequestOriginServiceNameQuery
) => {
  return httpClient.post("/esim/order/quotation-new", payload, {
    params: query,
  });
};

const postOrder = (payload: IPostOrderPayload) => {
  return privateHttpClient.post("/esim/order", payload);
};
const myCoupons = () => {
  return privateHttpClient.get("/coupons/my-coupons");
};
const postOrderAsGuest = (payload: IPostOrderPayload) => {
  return httpClient.post("/esim/order/guest", payload);
};

const contactUs = (payload: any) => {
  return httpClient.post("/emails/contact-us", payload);
};

const getPdfDownLoadUrl = (params: any) => {
  const urlParams = new URLSearchParams(params);
  return (
    baseURL +
    "/esim/orders/" +
    params.orderId +
    "/download?" +
    urlParams.toString()
  );
};

const initiatePayPayPayment = (payload: any) => {
  return privateHttpClient.post("/paypay-payment/create", payload);
};

const initiatePayPayPaymentAsGuest = (payload: any) => {
  return httpClient.post("/paypay-payment/create/guest", payload);
};

export const ApiService = {
  contactUs,
  getPdfDownLoadUrl,
  getOrderQuotation,
  postOrder,
  postOrderAsGuest,
  getQuotation,
  saveUserFirebaseToken,
  resetPassword,
  corporateSubscribe,
  getOrder,
  forgotPassword,
  getOrders,
  purchasePlan,
  purchasePlanAsGuest,
  getClientSecret,
  login,
  getPlanById,
  instantEsimNew,
  getProfile,
  register,
  getPlans,
  updateProfile,
  resendVerificationEmail,
  instantEsim,
  initiatePayPayPayment,
  initiatePayPayPaymentAsGuest,
};
