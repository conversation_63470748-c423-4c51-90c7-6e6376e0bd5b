import type { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
  return [
    {
      url: "https://esim.gmobile.biz",
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 1,
      alternates: {
        languages: {
          "x-default": `https://esim.gmobile.biz`,
          vi: `https://esim.gmobile.biz/vi`,
          fr: `https://esim.gmobile.biz/fr`,
          es: `https://esim.gmobile.biz/es`,
          //@ts-ignore
          ph: `https://esim.gmobile.biz/ph`,
        },
      },
    },
    {
      url: "https://esim.gmobile.biz/faq",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
      alternates: {
        languages: {
          "x-default": `https://esim.gmobile.biz/faq`,
          vi: `https://esim.gmobile.biz/vi/faq`,
          fr: `https://esim.gmobile.biz/fr/faq`,
          es: `https://esim.gmobile.biz/es/faq`,
          //@ts-ignore
          ph: `https://esim.gmobile.biz/ph/faq`,
        },
      },
    },
    {
      url: "https://esim.gmobile.biz/support/help",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
      alternates: {
        languages: {
          "x-default": `https://esim.gmobile.biz/support/help`,
          vi: `https://esim.gmobile.biz/vi/support/help`,
          fr: `https://esim.gmobile.biz/fr/support/help`,
          es: `https://esim.gmobile.biz/es/support/help`,
          //@ts-ignore
          ph: `https://esim.gmobile.biz/ph/support/help`,
        },
      },
    },
    {
      url: "https://esim.gmobile.biz/support/contactus",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
      alternates: {
        languages: {
          "x-default": `https://esim.gmobile.biz/support/contactus`,
          vi: `https://esim.gmobile.biz/vi/support/contactus`,
          fr: `https://esim.gmobile.biz/fr/support/contactus`,
          es: `https://esim.gmobile.biz/es/support/contactus`,
          //@ts-ignore
          ph: `https://esim.gmobile.biz/ph/support/contactus`,
        },
      },
    },
    {
      url: "https://esim.gmobile.biz/destination",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
      alternates: {
        languages: {
          "x-default": `https://esim.gmobile.biz/destination`,
          vi: `https://esim.gmobile.biz/vi/destination`,
          fr: `https://esim.gmobile.biz/fr/destination`,
          es: `https://esim.gmobile.biz/es/destination`,
          //@ts-ignore
          ph: `https://esim.gmobile.biz/ph/destination`,
        },
      },
    },
  ];
}
