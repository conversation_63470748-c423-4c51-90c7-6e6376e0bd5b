"use client";

import dynamic from "next/dynamic";
import { useEffect } from "react";

import * as Sentry from "@sentry/nextjs";

import AppLayout from "./components/common/AppLayout";

const NotFoundComponent = dynamic(
  () => import("@repo/ui/src/NotFoundComponent"),
  { ssr: false }
);

export default function GlobalError({ error }: { error: any }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <AppLayout contentBg="#fff">
      <NotFoundComponent />
    </AppLayout>
  );
}
