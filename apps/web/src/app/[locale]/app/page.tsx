"use client";

/*
 * import modules and libraries
 */
import { useCallback, useState, } from "react";
import { useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Box } from "@mantine/core";
/*
 * import components
 */
import AppTabs from "@/app/components/common/AppTabs/AppTabs";
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import MyEsimContainer from "@/app/components/my-esim/MyEsimContainer";

export default function Page() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const tabQuery = searchParams?.get("search");

  const [activeTab, setActiveTab] = useState<string | null>(tabQuery as string || "active");

  const headers = useCallback(
    (activeTab: string) => [
      {
        name: t("myesim:tab.purchased"),
        code: "active",
        children: (
          <SectionContent
            noHeader
            small
          >
            <Box className="pt-4">
              <MyEsimContainer
                isActive={activeTab === "active"}
                status="active"
              />
            </Box>
          </SectionContent>
        ),
      },
      {
        name: t("myesim:tab.expired"),
        code: "expired",
        children: (
          <SectionContent
            noHeader
            small
          >
            <Box className="pt-4">
              <MyEsimContainer
                isActive={activeTab === "expired"}
                status="expired"
              />
            </Box>
          </SectionContent>
        ),
      },
    ],
    []
  );

  return (
    <AppLayout contentBg="#f3f3f3">
      <AppTabs
        activeTab={activeTab}
        defaultValue={activeTab}
        headers={headers(activeTab || "")}
        onTabChange={setActiveTab}
      />
    </AppLayout>
  );
}
