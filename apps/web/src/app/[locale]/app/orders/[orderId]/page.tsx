"use client";

/*
 * import modules and libraries
 */
import { useMemo } from "react";
import { useQuery } from "react-query";
import { useTranslation } from "react-i18next";
import { Box, Stack, } from "@mantine/core";
import { AxiosError } from "axios";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import MyEsimList from "@/app/components/my-esim/MyEsimContainer/MyEsimList";
import EsimProgressChart from "@/app/components/my-esim/EsimProgressChart";
import SectionContent from "@repo/ui/src/common/SectionContent";
/*
 * import api
 */
import { ApiService } from "@/api";
/*
 * import interfaces
 */
import { IOrder } from "@/interfaces/IOrder";
import { IUsage } from "@/interfaces/IUsage";

export default function Page({
  params,
}: {
  params: {
    orderId: string;
    os: string;
  };
}) {
  const { t } = useTranslation();

  const { data, error, isLoading } = useQuery(
    `orders-${params.orderId}`,
    async () => {
      const response = await ApiService.getOrder(params.orderId + "", {
        type: "usage",
      });
      if (response instanceof AxiosError) throw new Error("Order not found.");
      return response;
    },
    {
      enabled: !!params.orderId,
    }
  );

  const orderInfo = useMemo<{ order: IOrder; usage: IUsage }>(
    () => data?.data?.data,
    [data?.data?.data]
  );

  return (
    <AppLayout>
      <Box className="bg-[#f3f3f3] pb-8">
        <SectionContent 
          title={t("region:checkusage", "Check Usage")}
          small
        >
          <Stack>
            {orderInfo && (
              <MyEsimList
                ignoreCardRenderLogic
                esimCardProps={{
                  hideButton: true,
                }}
                accordionProps={{
                  defaultValue: orderInfo?.order?.orderId,
                }}
                fetchNextPage={() => {}} // TODO: fetch next page function
                items={data ? [[orderInfo.order]] : []}
                status={"active"}
                renderWrapper={(_, children) => <>{children}</>}
              />
            )}
            {orderInfo && (
              <EsimProgressChart
                country={orderInfo?.order?.plan?.country?.name}
                endDate={orderInfo?.order?.expireTime}
                startDate={orderInfo?.order?.activateDate}
                totalData={orderInfo?.order?.plan?.dataId}
                usedData={orderInfo?.usage?.topup?.usage}
                validitiyDays={orderInfo?.order?.plan?.validityDays}
              />
            )}
          </Stack>
        </SectionContent>
      </Box>
    </AppLayout>
  );
}
