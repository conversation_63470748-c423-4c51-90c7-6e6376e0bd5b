import { IActivationGuide } from "@repo/ui/src/interfaces/IActivation";

const iosGuideManualNumberOfSteps = 11;
const iosQRNumberOfSteps = 13;

export const getIosGuide = (method: string) => {
  let iosGuide: IActivationGuide[] = [];
  const numberOfSteps: number = method === "qr" ? iosQRNumberOfSteps : iosGuideManualNumberOfSteps;
  
  for (let x = 1; x <= numberOfSteps; x += 1) {
    iosGuide.push({
      id: x,
      description: `activation:ios.${method}${x}`,
      imageUrl: `/assets/activation/${method}-ios/${method}-${x}.webp`,
    });
  }

  return iosGuide;
}