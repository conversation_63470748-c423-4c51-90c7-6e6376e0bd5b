import { IActivationGuide } from "@repo/ui/src/interfaces/IActivation";

const androidGuideManualNumberOfSteps = 8;
const androidQRNumberOfSteps = 10;

export const getAndroidGuide = (method: string) => {
  let androidGuide: IActivationGuide[] = [];
  const numberOfSteps: number = method === "qr" ? androidQRNumberOfSteps : androidGuideManualNumberOfSteps;
  
  for (let x = 1; x <= numberOfSteps; x += 1) {
    androidGuide.push({
      id: x,
      description: `activation:android.${method}${x}`,
      imageUrl: `/assets/activation/${method}-android/${method}-${x}.webp`,
    });
  }

  return androidGuide;
}