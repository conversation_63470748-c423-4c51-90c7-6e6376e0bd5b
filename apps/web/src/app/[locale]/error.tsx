"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import {
  Button,
  Center,
  Group,
  Paper,
  Text,
  ThemeIcon,
  Title,
  rem,
} from "@mantine/core";

import * as Sentry from "@sentry/nextjs";
import { IconExclamationCircle } from "@tabler/icons-react";

import AppLayout from "../components/common/AppLayout";

export default function Error({
  error,
}: {
  error: Error & { digest?: string };
}) {
  const router = useRouter();
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push("/");
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  return (
    <AppLayout contentBg="#fff">
      <div className="-mt-16 flex min-h-[65vh] flex-col items-center justify-center px-4">
        <Paper
          shadow="xl"
          radius="lg"
          p="xl"
          withBorder
          className="text-center"
        >
          <Center mb="md">
            <ThemeIcon size={rem(80)} radius="xl" color="primary">
              <IconExclamationCircle size={rem(50)} />
            </ThemeIcon>
          </Center>

          <Title order={2} mb="md" className="text-primary">
            Oops! Something went wrong
          </Title>

          <Text c="dimmed" mb="lg">
            We're sorry for the inconvenience. The error has been reported and
            we're working on it.
          </Text>

          <Text size="sm" c="dimmed">
            Redirecting to homepage in{" "}
            <Text span fw={500}>
              {countdown}
            </Text>{" "}
            seconds.
          </Text>

          <Group justify="center" mt="xl">
            <Button radius="md" size="md" onClick={() => router.push("/")}>
              ホームページに戻る
            </Button>
          </Group>
        </Paper>
      </div>
    </AppLayout>
  );
}
