"use server";
import type { Metada<PERSON> } from "next";
import AppLayout from "@/app/components/common/AppLayout";
import { Suspense } from "react";
import Finished from "@/app/components/support/Finished";

export async function metadata() {
  const metadata: Metadata = {
    title: "Thank you for your inquiry - GM eSIM",
    description:
      "Trusted eSIM service in over 100 countries! Instant line activation from application, easy setup. The perfect simple and user-friendly web app for travel enthusiasts.",
  };

  return metadata;
}

export default async function Page() {
  return (
    <AppLayout>
      <Suspense>
        <Finished />
      </Suspense>
    </AppLayout>
  );
}
