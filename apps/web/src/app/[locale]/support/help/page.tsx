"use server";
import type { Metada<PERSON> } from "next";
import AppLayout from "@/app/components/common/AppLayout";
import { Suspense } from "react";
import ContactLayout from "@/app/components/support/ContactLayout";
import Help from "@/app/components/support/Help";

export async function metadata() {
  const metadata: Metadata = {
    title: "Help - GM eSIM",
    description:
      "Resolve your questions about eSIM settings and compatible devices! Video explanations and guides for beginners. Trusted eSIM service used in over 100 countries.",
  };

  return metadata;
}

export default async function Page() {
  return (
    <AppLayout>
      <Suspense>
        <ContactLayout>
          <Help />
        </ContactLayout>
      </Suspense>
    </AppLayout>
  );
}
