"use server";
import type { Metada<PERSON> } from "next";
import AppLayout from "@/app/components/common/AppLayout";
import { Suspense } from "react";
import ContactLayout from "@/app/components/support/ContactLayout";
import ContactusForm from "@/app/components/support/ContactusForm";

export async function metadata() {
  const metadata: Metadata = {
    title: "Contact Us - GM eSIM",
    description:
      "Resolve your questions about eSIM settings and compatible devices! Video explanations and guides for first-time users. Reliable eSIM service used in over 100 countries.",
  };

  return metadata;
}

export default async function Page() {
  return (
    <AppLayout>
      <Suspense>
        <ContactLayout>
          <ContactusForm />
        </ContactLayout>
      </Suspense>
    </AppLayout>
  );
}
