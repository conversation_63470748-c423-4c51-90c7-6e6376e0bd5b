"use client";

/*
 * import modules and libraries
 */
import {
  MantineProvider,
  Loader,
  Center,
} from '@mantine/core';
import RingLoader from "@/app/components/common/RingLoader";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";

// TODO: design loading UI before page load
export default function Loading() {
  return (
    <AppLayout contentBg="#f3f3f3">
      <MantineProvider
        theme={{
          components: {
            Loader: Loader.extend({
              defaultProps: {
                loaders: { ...Loader.defaultLoaders, ring: RingLoader },
                type: 'ring',
              },
            }),
          },
        }}
      >
        <Center className="py-12 mt-12">
          <Loader color="app-pink.4" size={100}/>
        </Center>
      </MantineProvider>
    </AppLayout>
  );
}