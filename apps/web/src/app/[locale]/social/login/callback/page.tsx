"use client";
import { useEffect } from "react";
// import jwt_decode from "jwt-decode";
import { LoadingOverlay } from "@mantine/core";
import { useAuth } from "@ri/fe-auth";
import { withBasePath } from "@/utils";
import Head from "next/head";
import { useTranslation } from "react-i18next";
import Storage from "@/utils/storage";
import { AUTH_TOKEN_NAME } from "@/app/constants";
import { useParams, useRouter, useSearchParams } from "next/navigation";

const getCognitoRedirectUri = () => {
  return (
    window.location.protocol +
    "//" +
    window.location.host +
    process.env.NEXT_PUBLIC_BASE_PATH +
    "/social/login/callback"
  );
};

const SocialLoginCallback = () => {
  const auth = useAuth();
  const router = useRouter();
  const params = useSearchParams();
  const code = params?.get("code");
  const { t } = useTranslation("login");

  useEffect(() => {
    if (!code) {
      window.location.href = withBasePath("/");
      return;
    }
    const time = setTimeout(() => {
      let query = `code=${code}`;
      //@ts-ignore
      const referral = window?.Rewardful?.referral;
      if (referral) {
        query += `&referral=${referral}`;
      }

      if (process.env.NEXT_PUBLIC_BASE_PATH) {
        query += `&redirectUri=${getCognitoRedirectUri()}`;
      }
      query += "&userPool=GLOBAL";
      query += "&source=global-esim";

      fetch(`${process.env.NEXT_PUBLIC_API_HOST}/auth/social/login?${query}`)
        .then((response) => response.json())
        .then(async ({ data, ...rest }: any) => {
          try {
            let path;
            if (data?.access_token) {
              Storage.getInstance().set(AUTH_TOKEN_NAME, {
                accessToken: data.access_token,
              });
              // const decoded = jwt_decode(data.access_token) as {
              //   email: string;
              //   user_id: string;
              //   name: string;
              //   companyId: string;
              // };
              // auth?.startSession({
              //   id: decoded.user_id,
              //   displayName: decoded.name,
              //   email: decoded.email,
              //   data: {
              //     companyId: data?.data?.companyId,
              //   },
              // });
              path = Storage.getInstance().get("returnUrl") || "/app";
              Storage.getInstance().set("returnUrl", "");
              const locale = Storage.getInstance().get("locale") || "jp";
              router.replace("/" + "jp" + "/" + path || "/");
            } else {
              //@ts-ignore
              if (rest.statusCode === 409) {
                alert(
                  `You previously signed up to Global Mobile eSIM manually.Please use the normal email address/password function to log in.`
                );
                path = "/auth/signin";
              }
              router.replace(path || "/");
            }
          } catch (error) {
            alert(
              t("login:tryAgain-error", "Unexpected error, please try again!")
            );
            router.replace("/auth/signin");
          }
        })
        .catch(() => {
          alert(
            t("login:tryAgain-error", "Unexpected error, please try again!")
          );
          router.replace("/auth/signin");
        });
    }, 1500);
    return () => clearTimeout(time);
  }, []);

  return (
    <>
      <Head>
        <meta name="robots" content="noindex" />
      </Head>
      <div>
        <LoadingOverlay visible />
      </div>
    </>
  );
};
SocialLoginCallback.getInitialProps = async () => {
  return {};
};
export default SocialLoginCallback;
