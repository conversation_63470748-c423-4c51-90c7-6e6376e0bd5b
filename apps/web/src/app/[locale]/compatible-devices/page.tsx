"use server";

/*
 * import modules and libraries
 */
import {
  Box,
} from "@mantine/core";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import CompatibleDevices from "@repo/ui/src/CompatibleDevices";
/*
 * import utils
 */
import { getCDNUrl } from "@/utils";

export default async function Page() {
  return (
    <AppLayout contentBg="#f3f3f3">
      <SectionContent 
        small 
        title={
          "compatibility:headers.title"
        }
      >
        <Box className="bg-white p-4 rounded-xl">
          <CompatibleDevices
            imageUrl={getCDNUrl("/assets/compatibility-note.webp")}
          />
        </Box>
      </SectionContent>
    </AppLayout>
  );
}
