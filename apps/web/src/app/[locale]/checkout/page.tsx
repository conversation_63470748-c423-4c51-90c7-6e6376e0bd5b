"use client";

/*
 * import modules and libraries
 */
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useCallback, useEffect, useState } from "react";

import { Box } from "@mantine/core";
import { useDebouncedValue } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";

import { IconMoodSad } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

/*
 * import components
 */
import Checkout from "@/app/components/checkout/Checkout";
import AppLayout from "@/app/components/common/AppLayout";

import { ApiService } from "@/api";

import { useShoppingCart } from "@/store";

export default function Page() {
  const router = useRouter();
  const { t } = useTranslation();
  const [items, setCoupon, setQuotation, coupon] = useShoppingCart((s) => [
    s.items,
    s.setCoupon,
    s.setQuotation,
    s.coupon,
  ]);

  const searchParams = useSearchParams();
  const [via, setVia] = useState<string | null>(
    searchParams?.has("via") && searchParams?.get("via")
      ? searchParams.get("via")
      : null
  );

  // Create a new query for the quotation with coupon support
  const { data: quotationWithCoupon, isLoading: isQuotationLoading } = useQuery(
    ["checkout-quotation", items, via],
    async () => {
      if (!items.length) return null;

      // If coupon exists, refetch quotation with coupon
      try {
        const products =
          items?.map((order) => ({
            optionId: order.plan?.id?.toString() || "",
            insurance: order.insurance ? "insured" : undefined,
          })) || [];

        const response = await ApiService.getQuotation(
          {
            recaptha: "",
            couponId: via,
            products: products,
          },
          {
            requestOriginServiceName: via as string,
          }
        );
        const newQuotation = response.data?.data;
        setQuotation(newQuotation); // Update store
        return newQuotation;

        return response.data?.data;
      } catch (error) {
        console.error("Error applying coupon:", error);
        // Return original quotation if coupon application fails
        return null;
      }
    },
    {
      enabled: !!items.length,
      keepPreviousData: true,
    }
  );

  const [quotationDebounced] = useDebouncedValue(quotationWithCoupon, 1000);

  useEffect(() => {
    const { errors } = quotationDebounced || {};
    if (!errors?.length) return;

    errors.forEach((element: any) => {
      if (!element) return;
      const couponEl = document.getElementById("coupon-code");
      if (couponEl) {
        couponEl.classList.add("border-red-500", "text-red-500");
      }
      notifications.show({
        id: `notification-${element.code}`,
        icon: <IconMoodSad />,
        message: t(
          `error:coupons.${element.code}`,
          t(`error:coupons.selfonlycoupon`)
        ) as string,
      });
    });
  }, [quotationDebounced, t]);

  const handleCouponChange = useCallback(
    (coupon?: string | null) => {
      setVia(coupon || null);
      setCoupon(coupon || "");
    },
    [setCoupon]
  );

  useEffect(() => {
    if (!items.length) router.push("/");
  }, [items]);

  return (
    <AppLayout contentBg="#f3f3f3">
      <Box className="pb-8">
        <Suspense>
          <Checkout
            source="multiple"
            quotation={quotationWithCoupon}
            onCouponChange={handleCouponChange}
          />
        </Suspense>
      </Box>
    </AppLayout>
  );
}
