"use client";

import { useParams, useSearchParams } from "next/navigation";
import { Suspense, useCallback, useEffect, useState } from "react";

import { Box } from "@mantine/core";
import { useDebouncedValue } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";

import { IconMoodSad } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

import Checkout from "@/app/components/checkout/Checkout";
import AppLayout from "@/app/components/common/AppLayout";

import { ApiService } from "@/api";

export default function Page() {
  const { t } = useTranslation();

  const searchParams = useSearchParams();
  const { planId } = useParams() || {};

  const [via, setVia] = useState(
    searchParams?.has("via") && searchParams?.get("via")
      ? searchParams.get("via")
      : null
  );

  const { data: quotation } = useQuery(
    ["quotation", planId, via],
    async () => {
      const planResponse = await ApiService.getQuotation(
        {
          recaptha: "",
          couponId: via,
          products: [{ optionId: planId + "" }],
        },
        {
          requestOriginServiceName: via as string,
        }
      );
      return planResponse.data?.data;
    },
    {
      enabled: !!planId,
      keepPreviousData: true,
    }
  );

  const [quotationDebounced] = useDebouncedValue(quotation, 1000);

  useEffect(() => {
    const { errors } = quotationDebounced || {};
    if (!errors?.length) return;

    errors.forEach((element: any) => {
      if (!element) return;
      const couponEl = document.getElementById("coupon-code");
      if (couponEl) {
        couponEl.classList.add("border-red-500", "text-red-500");
      }
      notifications.show({
        id: `notification-${element.code}`,
        icon: <IconMoodSad />,
        message: t(
          `error:coupons.${element.code}`,
          t(`error:coupons.selfonlycoupon`)
        ) as string,
      });
    });
  }, [quotationDebounced, t]);

  const handleCouponChange = useCallback((coupon?: string | null) => {
    setVia(coupon || null);
  }, []);

  return (
    <AppLayout contentBg="#f3f3f3">
      <Box className="pb-8">
        <Suspense>
          <Checkout
            source="single"
            quotation={quotation}
            plan={quotation?.orders?.[0]?.plan}
            onCouponChange={handleCouponChange}
          />
        </Suspense>
      </Box>
    </AppLayout>
  );
}
