/*
 * import modules and libraries
 */
import type { Metadata } from "next";

/*
 * import components
 */
import TranslationsProvider from "@/app/components/TranslationProvider";

import initTranslations from "@/i18n";
/*
 * import constants and helpers
 */
import { locales } from "@/i18n/settings";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "countries",
  "payment",
  "region",
  "signup",
  "compatibility",
  "faq",
  "login",
  "signup",
  "error",
];

export const metadata: Metadata = {
  title: "Checkout - GM eSIM",
  robots: {
    index: false,
  },
};

const Layout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default Layout;
