import TranslationsProvider from "@/app/components/TranslationProvider";
import { locales } from "@/i18n/settings";
import { Metadata } from "next";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "countries",
  "region",
  "help",
  "profile",
];

export const metadata: Metadata = {
  title: "Frequently Asked Questions - GM eSIM",
  description:
    "Resolve your questions about eSIM setup and compatible devices! Video explanations and guides for beginners. Trusted eSIM service used in over 100 countries.",
};

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
}
