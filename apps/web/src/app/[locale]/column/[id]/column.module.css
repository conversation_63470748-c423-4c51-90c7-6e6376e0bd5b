.content blockquote, .content body, .content dd, .content dl, .content dt, .content h3, .content h4, .content h5, .content h6, .content ol, .content p, .content ul, .content table {
  margin-bottom: 1rem;
  margin-top: 1rem;
}

.image-border {
  border-radius: 10px 10px 50px 10px;
}

.content img {
  margin: 0 auto;
}

.content h2 {
  padding-top: 12px;
  font-weight: bold;
  font-size: 24px;
}

.content h3 {
  font-weight: bold;
  font-size: 20px;
}

.content h4 {
  font-weight: bold;
  font-size: 18px;
}

.content ul {
  list-style: none;
  padding-right: 1.5rem;
}
.content ul li {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.content ul li::before {
  flex-shrink: 0;
  content: "";
  display: inline-block;
  height: 4px;
  width: 9px;
  background: #C72B4D;
  border-radius: 4px;
  margin: 0 10px;
}

.content ol {
  list-style: decimal;
  padding-left: 2rem;
  padding-right: 2rem;
}
.content ol li::marker {
  content: counter(list-item)"\a0\a0";
}
.content ol li:nth-child(-n+9)::marker {
  content: "0"counter(list-item)"\a0\a0";
}

.content li {
  margin-top: 1rem;
  margin-bottom: 1rem;
  font-size: 18px;
  font-size: 14px;
}

.content iframe {
  padding: 12px;
  border: 1px solid #D8D8D8;
  border-radius: 10px;
}

.content table {
  border-radius: 14px;
  width: 95%;
  margin-left: auto;
  margin-right: auto;
}

.content table, .content table tbody tr, .content table tbody tr td {
  border: 1px solid #D8D8D8;
  font-size: 14px;
}

.content tr td {
  text-align: center;
  padding: 0 4px;
}

.content table tbody tr:first-child th {
  color: white;
  background-color: #C72B4D;
  font-size: 14px;
}


/* .column-footer-bg {
  position: relative;
  background-image: url("/components/column/footer-bg.webp");
  background-size: cover;
  background-position: center;
} */

/* .column-footer {
  height: 100%;
  width: 100%;
  background: #C72B4D;
  opacity: 80%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
} */