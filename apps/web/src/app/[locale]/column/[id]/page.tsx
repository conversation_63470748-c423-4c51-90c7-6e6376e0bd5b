"use server";

/*
 * import modules and libraries
 */
import Image from "next/image";
import dynamic from "next/dynamic";
import {
  useMemo,
  useCallback,
} from "react";
import {
  Group,
  Stack,
  Text,
  Box,
  Flex,
} from "@mantine/core";
import {
  IconClockHour4,
  IconRefresh,
} from "@tabler/icons-react";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import ColumnBreadCrumbs from "@/app/components/column/ColumnBreadCrumbs";
import ColumnFooter from "@repo/ui/src/column/ColumnFooter";
// import Tags from "@repo/ui/src/column/Tags"
/*
 * import utils
 */
import { getCDNUrl } from "@/utils";
/*
 * import API
 */
import { getColumnItem, getColumns } from "@/api/micro-cms";
/*
 * import interface
 */
import { IBlog } from "@repo/ui/src/interfaces/IBlog";
/*
 * import styles
 */
import styles from "./column.module.css";
/*
 * import dynamic components
 */
const DynamicCompactBlogCard = dynamic(
  () => import("@repo/ui/src/column/blog-card-compact/"),
);

export default async function Page(context: {
  params: {
    id: string;
  }
}) {
  // TODO: refresh cache daily or set to dynamic rendering
  const getColumn = useCallback(async () => {
    try {
      const column: IBlog | null = await getColumnItem(context.params.id, `country[contains]esim`);

      let categoryFilter = "";
      for (let x = 0; x < (column?.category?.length || 0); x += 1) {
        categoryFilter += `category[contains]${column?.category[x]}${x + 1 !== column?.category?.length ? "[or]" : ""}`
      }

      const relatedColumns = await getColumns({
        limit: 3,
        fields: "id,title,bannerUrl,country,category",
        filters: `(${categoryFilter})[and]id[not_equals]${context.params.id}`,
      });
      const recommendedColumns = await getColumns({
        limit: 5,
        fields: "id,title,bannerUrl,country,category",
        filters: `isRecommended[equals]true[and]country[contains]esim[and]id[not_equals]${context.params.id}`,
      });

      return {
        relatedColumns,
        recommendedColumns,
        column,
      };
    } catch (err) {
      console.log(err);
      return null;
    }
  }, [context.params.id])
  const data = await useMemo(async () => await getColumn(), [context.params.id]);

  const formatYear = (date: string) => {
    const dataObject = new Date(date);
    const year = dataObject.getFullYear();
    const month = dataObject.getMonth();
    const day = dataObject.getDate();

    return `${year}年${month + 1}月${day}日`
  }
  
  return (
    <AppLayout contentBg="#fff">

      <ColumnBreadCrumbs columnTitle={data?.column?.title || ""}/>

      <Flex className="max-w-[1048px] mx-auto relative gap-8 mb-8 px-0 md:px-4 lg:px-0 flex-col md:flex-row">
        <div className="md:mb-10 relative flex-1 px-4 md:px-0">
          <Stack>
            <Box>
              <h1 className="font-bold text-3xl border-b border-primary pb-4">{data?.column?.title}</h1>
                <Group className="items-center gap-0 my-6">
                  <IconClockHour4 className="text-primary mr-1" />
                  <Text className="text-sm text-neutral-400">
                    {formatYear(data?.column?.publishedAt as string)}
                  </Text>
                  {data?.column?.revisedAt && (
                    <>
                      <IconRefresh className="ml-10 text-primary mr-1" />
                      <Text className="text-sm text-neutral-400">
                        {formatYear(data?.column?.revisedAt)}
                      </Text>
                    </>
                  )}
                </Group>
                {!(data?.column?.content as string).includes("img") && data?.column?.bannerUrl && (
                  <Image
                    className={`rounded-lg object-contain shadow-md`}
                    src={data?.column.bannerUrl.url}
                    height={data?.column.bannerUrl.height}
                    width={data?.column.bannerUrl.width}
                    alt=""
                  />
                )}

              <div
                className={`
                  ${styles["content"]}
                  [&>img]:shadow-md
                  [&>img]:rounded-lg
                  [&>figure]:my-4
                  [&>figure>img]:shadow-md
                  [&>figure>img]:rounded-lg
                  [&>h2]:pb-4
                  [&>h2]:border-b
                  [&>h2]:border-primary
                  [&>h3]:text-primary
                  [&>li]:marker:text-primary
                  [&>blockquote]:bg-secondary
                  [&>blockquote]:px-4
                  [&>blockquote]:py-0.5
                  [&>blockquote]:rounded-lg
                  [&>blockquote>p]:text-sm
                  [&>li]:marker:font-bold
                  [&>p]:text-sm
                  [&>a]:text-primary
                  [&>p>a]:text-primary
                `}
                dangerouslySetInnerHTML={{ __html: data?.column?.content as string }}
            />
            </Box>
          </Stack>
        </div>
        <Box className="md:hidden">
          <ColumnFooter getCDNUrl={getCDNUrl} />
        </Box>
        <Stack className="px-4 md:px-0 md:w-1/3 shrink-0">
          <Box className="bg-[#f5f5f5] p-2 rounded-lg">
            <h2 className="font-bold text-xxs text-primary">ライター</h2>
            <p className="font-bold text-sm pb-2">グロモバ編集部</p>
            <p className="text-xs">海外旅行に役立つ情報を発信しています。観光スポット、グルメ、ホテルの現地情報に加え、WiFiやeSIMなど海外でのインターネット利用に関する情報もお届けします。</p>
          </Box>
          <Image
            width={767}
            height={437}
            src={getCDNUrl("/assets/column/gmobile-banner.webp")}
            alt="すぐ発行＆使用できるから待ち時間ゼロ！現地の安定高速ネットで、思いっきり自由に旅を満喫しよう！"
            className="shadow-md rounded-lg"
          />
          <Stack className="flex-col-reverse md:flex-col">
            {/* <Tags /> */}
            {data?.relatedColumns && data?.relatedColumns.length !== 0 && (
              <Box className="md:hidden">
                <h1 className="font-bold text-2xl border-b border-primary pb-4">関連記事</h1>
                
                <Stack className="mt-6">
                  {data?.relatedColumns.map((item: IBlog) => (
                    <DynamicCompactBlogCard
                      key={item.id}
                      column={item}
                      fullWidth
                      row
                    />
                  ))}
                </Stack>
              </Box>
            )}
            {data?.recommendedColumns && data?.recommendedColumns.length !== 0 && (
              <Box>
                <h2 className="font-bold text-2xl border-b border-primary pb-4">おすすめの記事</h2>
                <Stack className="mt-6">
                  {data?.recommendedColumns.map((item: IBlog) => (
                    <DynamicCompactBlogCard
                      key={item.id}
                      column={item}
                      fullWidth
                      row
                    />
                  ))}
                </Stack>
              </Box>
            )}
          </Stack>
        </Stack>
      </Flex>
      <Box className="hidden md:block">
        <ColumnFooter getCDNUrl={getCDNUrl} />
      </Box>
    </AppLayout>
  );
}
