"use server";

/*
 * import modules and libraries
 */
import Image from "next/image";
import dynamic from "next/dynamic";
import {
  useMemo,
  useCallback,
} from "react";
import {
  Stack,
  Box,
  Flex,
  Divider,
} from "@mantine/core";
import { headers } from 'next/headers';
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import TopBanner from "@repo/ui/src/common/TopBanner";
import KVContent from "@repo/ui/src/column/KVContent";
import ColumnFooter from "@repo/ui/src/column/ColumnFooter";
// import Tags from "@repo/ui/src/column/Tags"
// import Categories from "@repo/ui/src/column/Categories"
/*
 * import utils
 */
import { getCDNUrl } from "@/utils";
/*
 * import API
 */
import { getColumns, getLpDescription } from "@/api/micro-cms";
/*
 * import interface
 */
import { IBlog, ILpDescription } from "@repo/ui/src/interfaces/IBlog";
/*
 * import dynamic components
 */
const DynamicBlogCard = dynamic(
  () => import("@repo/ui/src/column/blog-card/BlogCard"),
);
const DynamicCompactBlogCard = dynamic(
  () => import("@repo/ui/src/column/blog-card-compact/"),
);


export default async function Page() {
  // NOTE: used for dynamic rendering https://nextjs.org/docs/app/api-reference/functions/headers
  const headersList = headers();
  const getCategoryColumn = useCallback(async (country: string, category: string) => {
    try {
      const res: IBlog[] | null = await getColumns({
        limit: 10,
        fields: "id,title,bannerUrl,country,category,shortDescription",
        filters: `category[contains]${category}[and]country[contains]${country}`,
      });

      return res;
    } catch (err) {
      console.log(err);
      return null;
    }
  }, []);

  const getColumnPageDetails = useCallback(async () => {
    try {
      const lpDescription: ILpDescription | null = await getLpDescription({
        limit: 1,
        fields: "id,title,subtitle,content,country,bannerUrl,h1,metaTitle,metaDescription",
        filters: `country[contains]esim`,
      });
      const recommendedColumns: IBlog[] | null = await getColumns({
        limit: 5,
        fields: "id,title,bannerUrl,country,category",
        filters: `isRecommended[equals]true[and]country[contains]esim`,
      });
      const popularColumns = await getColumns({
        limit: 5,
        fields: "id,title,bannerUrl,country,category",
        filters: `isPopular[equals]true[and]country[contains]esim`,
      });

      const topColumns = await getCategoryColumn("top", "top");
      const sightSeeColumns = await getCategoryColumn("esim", "sightsee");
      const gourmetColumns = await getCategoryColumn("esim", "gourmet");
      const shoppingColumns = await getCategoryColumn("esim", "shopping");
      const hotelColumns = await getCategoryColumn("esim", "hotel");
      const wifiColumns = await getCategoryColumn("esim", "wifi");
      const esimColumns = await getCategoryColumn("esim", "esim");
      

      return {
        popularColumns,
        lpDescription,
        topColumns,
        sightSeeColumns,
        gourmetColumns,
        shoppingColumns,
        hotelColumns,
        wifiColumns,
        esimColumns,
        recommendedColumns,
      };
    } catch (err) {
      console.log(err);
      return null;
    }
  }, []);

  const data = await useMemo(async () => await getColumnPageDetails(), []);

  return (
    <AppLayout contentBg="#fff">
      <div className="mb-16 md:mb-20">
        <div>
          <h1 className="hidden">{data?.lpDescription?.h1}</h1>
          <TopBanner
            small
            bgUrl={{
              spUrl: data?.lpDescription?.bannerUrl?.url,
              pcUrl: data?.lpDescription?.bannerUrl?.url,
            }}
          >
            {data?.lpDescription && <KVContent lpDescription={data?.lpDescription} />}
          </TopBanner>
        </div>

        <Flex className="max-w-[1048px] mx-auto relative gap-8 my-8 pb-10 px-0 md:px-4 lg:px-0 flex-col md:flex-row">
          <Stack className="relative flex-1 md:px-0">
            <Stack className="gap-10 md:gap-20">
              {data?.popularColumns && data?.popularColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title="人気記事"
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="primary.0" />
                    <div
                      className={`flex snap-x ${
                        data?.popularColumns.length < 3
                          ? "md:justify-start"
                          : "md:grid md:grid-cols-2"
                      } overflow-x-scroll md:overflow-x-visible pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.popularColumns.map((item: IBlog) => (
                        <DynamicBlogCard
                          key={item.id}
                          column={item}
                          compact
                          bgPrimary
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}

              {data?.sightSeeColumns && data?.sightSeeColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title={`観光`}
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="app-pink.4" />
                    <div
                      className={`grid grid-cols-2 md:grid-cols-3 pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.sightSeeColumns.map((item: IBlog) => (
                        <DynamicCompactBlogCard
                          key={item.id}
                          column={item}
                          fullWidth
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}
              {data?.gourmetColumns && data?.gourmetColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title={`グルメ`}
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="app-pink.4" />
                    <div
                      className={`grid grid-cols-2 md:grid-cols-3 pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.gourmetColumns.map((item: IBlog) => (
                        <DynamicCompactBlogCard
                          key={item.id}
                          column={item}
                          fullWidth
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}
              {data?.hotelColumns && data?.hotelColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title={`ホテル`}
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="app-pink.4" />
                    <div
                      className={`grid grid-cols-2 md:grid-cols-3 pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.hotelColumns.map((item: IBlog) => (
                        <DynamicCompactBlogCard
                          key={item.id}
                          column={item}
                          fullWidth
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}
              {data?.esimColumns && data?.esimColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title={`eSIM`}
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="app-pink.4" />
                    <div
                      className={`grid grid-cols-2 md:grid-cols-3 pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.esimColumns.map((item: IBlog) => (
                        <DynamicCompactBlogCard
                          key={item.id}
                          column={item}
                          fullWidth
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}
              {data?.wifiColumns && data?.wifiColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title={`インターネット`}
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="app-pink.4" />
                    <div
                      className={`grid grid-cols-2 md:grid-cols-3 pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.wifiColumns.map((item: IBlog) => (
                        <DynamicCompactBlogCard
                          key={item.id}
                          column={item}
                          fullWidth
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}
              {data?.shoppingColumns && data?.shoppingColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title={`ショッピング`}
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="app-pink.4" />
                    <div
                      className={`grid grid-cols-2 md:grid-cols-3 pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.shoppingColumns.map((item: IBlog) => (
                        <DynamicCompactBlogCard
                          key={item.id}
                          column={item}
                          fullWidth
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}
              {data?.topColumns && data?.topColumns.length !== 0 && (
                <section>
                  <SectionContent
                    title={`お役立ち情報`}
                    titleAlign="text-left"
                    allBlack
                    zeroHeader
                    noFooter
                  >
                    <Divider className="my-4" color="app-pink.4" />
                    <div
                      className={`grid grid-cols-2 md:grid-cols-3 pb-4 md:pb-0 px-1 md:px-0 pt-1 md:pt-0 gap-4`}
                    >
                      {data?.topColumns.map((item: IBlog) => (
                        <DynamicCompactBlogCard
                          key={item.id}
                          column={item}
                          fullWidth
                        />
                      ))}
                    </div>
                  </SectionContent>
                </section>
              )}
            </Stack>
          </Stack>
          <Stack className="px-4 md:px-0 md:w-1/3 shrink-0">
            <Image
              width={767}
              height={437}
              src={getCDNUrl("/assets/column/gmobile-banner.webp")}
              alt="すぐ発行＆使用できるから待ち時間ゼロ！現地の安定高速ネットで、思いっきり自由に旅を満喫しよう！"
              className="shadow-md rounded-lg"
            />
            <Stack className="flex-col-reverse md:flex-col">
              {/* <Tags />
              <Categories /> */}
              {data?.recommendedColumns && data?.recommendedColumns.length !== 0 && (
                <Box>
                  <h2 className="font-bold text-2xl border-b border-primary pb-4">おすすめの記事</h2>
                  <Stack className="mt-6">
                    {data?.recommendedColumns.map((item: IBlog) => (
                      <DynamicCompactBlogCard
                        key={item.id}
                        column={item}
                        fullWidth
                        row
                      />
                    ))}
                  </Stack>
                </Box>
              )}
            </Stack>
          </Stack>
        </Flex>
      </div>
      <ColumnFooter getCDNUrl={getCDNUrl} />
    </AppLayout>
  );
}
