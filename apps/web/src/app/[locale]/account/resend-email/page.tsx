"use client";
/*
 * import modules and libraries
 */
import { showNotification } from '@mantine/notifications';
import { useForm } from "@mantine/form";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Stack,
  Text,
} from "@mantine/core";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";
/*
 * import constants and helpers
 */
import { useMessageStore } from "@/store/MessageStore";
/*
 * import api
 */
import { ApiService } from "@/api";

export default function ResendEmail() {
  const { t } = useTranslation();
  const [loading, toggleLoading, setMessage] = useMessageStore((s) => [
    s.isLoading,
    s.toggleLoading,
    s.setMessage,
  ]);
  const form = useForm({
    initialValues: {
      email: "",
    },

    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Invalid email"),
    },
  });

  const handleForgotPassword = (values: typeof form.values) => {
    toggleLoading(true);
    ApiService.resendVerificationEmail(values.email)
      .then(() => {
        form.setValues({
          email: "",
        });
        showNotification({
          message: t(
            "password:forgotpassword.resendverificationemail.success",
            "You'll receive a new confirmation email"
          ),
          color: "green",
        });
      })
      .catch(() => {
        showNotification({
          color: "red",
          message: t(
            "password:forgotpassword.resendverificationemail.error",
            "Unable to sent confirmation email. Please try again."
          ),
        });
      })
      .finally(() => {
        toggleLoading(false);
      });
  };

  return (
    <AppLayout contentBg="#fff">
      <SectionContent
        small 
        title="Resend confirmation email"
      >
        <form onSubmit={form.onSubmit(handleForgotPassword)}>
          <Stack>
            <Text
              className="text-sm text-center"
            >
              {t(
                "password:forgotpassword.resendverificationemail.notice",
                "Enter the email address associated with your account and we'll send you a verification email."
              )}
            </Text>
            <AppTextInput
              withAsterisk
              placeholder={
                t("password:forgotpassword.email.placeholder") as string
              }
              label={t("password:forgotpassword.email.placeholder") as string}
              {...form.getInputProps("email")}
            />
            <Button
              loading={loading}
              w={"100%"}
              type="submit"
              color="app-dark"
            >
              {t("password:forgotpassword.resendverificationemail.title", "Resend confirmation email")}
            </Button>
          </Stack>
        </form>
      </SectionContent>
    </AppLayout>
  );
}
