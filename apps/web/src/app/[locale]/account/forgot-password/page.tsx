"use client";

/*
 * import modules and libraries
 */
import { showNotification } from "@mantine/notifications";
import { useForm } from "@mantine/form";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import { <PERSON><PERSON>, Stack, Text } from "@mantine/core";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";
/*
 * import constants and helpers
 */
import { useMessageStore } from "@/store/MessageStore";
/*
 * import api
 */
import { ApiService } from "@/api";

export default function ForgotPassword() {
  const { t } = useTranslation();
  const [loading, toggleLoading, setMessage] = useMessageStore((s) => [
    s.isLoading,
    s.toggleLoading,
    s.setMessage,
  ]);
  const form = useForm({
    initialValues: {
      email: "",
    },

    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Invalid email"),
    },
  });

  const handleForgotPassword = (values: typeof form.values) => {
    toggleLoading(true);
    ApiService.forgotPassword(values.email)
      .then(() => {
        form.setValues({
          email: "",
        });
        showNotification({
          message: "A password reset link has been sent.",
          color: "green",
        });
      })
      .catch((err: any) => {
        let errorMessage =
          "Unable to send password reset email. Please try again.";
        if (err.response?.data?.message === "User not found.") {
          errorMessage = "User not found. Please register first.";
        }
        if (err.response?.data?.errorCode === 4008) {
          errorMessage =
            "Account was created using social login. Please sign in using social login.";
        }
        showNotification({
          color: "red",
          message: errorMessage,
        });
      })
      .finally(() => {
        toggleLoading(false);
      });
  };

  return (
    <AppLayout contentBg="#fff">
      <SectionContent
        small
        title={t("password:forgotpassword.title") as string}
      >
        <form onSubmit={form.onSubmit(handleForgotPassword)}>
          <Stack>
            <Text
              className="text-sm text-center"
              dangerouslySetInnerHTML={{
                __html: t("password:forgotpassword.notice"),
              }}
            />
            <AppTextInput
              withAsterisk
              placeholder={
                t("password:forgotpassword.email.placeholder") as string
              }
              label={t("password:forgotpassword.email.placeholder") as string}
              {...form.getInputProps("email")}
            />
            <Link
              className="text-sm no-underline text-right"
              href="/account/resend-email"
            >
              Resend verification email
            </Link>
            <Button loading={loading} w={"100%"} type="submit" color="app-dark">
              {t("password:forgotpassword.btn.sendresetlink")}
            </Button>
          </Stack>
        </form>
      </SectionContent>
    </AppLayout>
  );
}
