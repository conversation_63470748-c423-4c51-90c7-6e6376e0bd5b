"use client";

/*
 * import modules and libraries
 */
import { useSearchParams } from "next/navigation";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { Text, Box } from "@mantine/core";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import AppTabs from "@/app/components/common/AppTabs/AppTabs";
import ActivateContainer from "@/app/components/activate/ActivateContainer";
/*
 * import constants
 */
import { getIosGuide } from "@/app/[locale]/app/orders/[orderId]/[os]/activate/ios-guide";
import { getAndroidGuide } from "@/app/[locale]/app/orders/[orderId]/[os]/activate/android-guide";

export default function Page() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();

  const tabQuery = searchParams?.get("os");
  const defaultTab = ["ios", "android"].includes(tabQuery as string)
    ? tabQuery
    : "ios";
  const [activeTab, setActiveTab] = useState<string | null | undefined>(
    defaultTab
  );

  const headers = useCallback(
    () => [
      {
        name: "iOS",
        code: "ios",
        children: (
          <Box className="bg-white pt-10">
            <ActivateContainer guide={getIosGuide} guideOnly error={null} />
          </Box>
        ),
      },
      {
        name: "Android",
        code: "android",
        children: (
          <Box className="bg-white pt-10">
            <ActivateContainer guide={getAndroidGuide} guideOnly error={null} />
          </Box>
        ),
      },
    ],
    []
  );

  return (
    <AppLayout contentBg="#f7f7f7">
      <SectionContent small noFooter title="activation:setuppage.title">
        <Text
          className="md:text-nowrap"
          component="p"
          dangerouslySetInnerHTML={{
            __html: t("activation:setuppage.description"),
          }}
        />
      </SectionContent>

      <AppTabs
        activeTab={activeTab}
        defaultValue={activeTab}
        headers={headers()}
        onTabChange={setActiveTab}
      />
    </AppLayout>
  );
}
