export const dynamic = "force-static";

import { Box } from "@mantine/core";

import sortBy from "lodash/sortBy";

import SectionContent from "@repo/ui/src/common/SectionContent";
import featuredPlans from "@repo/ui/src/constants/featured-plans.json";

import AppLayout from "@/app/components/common/AppLayout";
import FeaturedCountries from "@/app/components/top/FeaturedCountries";
import Features from "@/app/components/top/Features";
import Hero from "@/app/components/top/Hero";
import HomeFAQ from "@/app/components/top/HomeFAQ";
import HowItWorks from "@/app/components/top/HowItWorks";
import WhatIsEsim from "@/app/components/top/WhatIsEsim";

import { countryAlias } from "@/utils";

import type { IPlan } from "@/interfaces/IPlan";

import { COUNTRY_SORT } from "@/app/constants";


const TOP_COUNTRIES = ["KR", "JP", "VN", "HI", "CN", "TW", "AS", "EU"];

async function getPlans() {
  let formatCountryPlans: IPlan[] = [];
  let formatRegionPlans: IPlan[] = [];
  const plans: {
    regions: IPlan[];
    countries: IPlan[];
  } = featuredPlans.data as {
    countries: any;
    regions: any;
  };

  try {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries
        .filter((item: IPlan) =>
          TOP_COUNTRIES.includes(item.countryCode as string)
        )
        .map((item: IPlan) => ({
          ...item,
          order: COUNTRY_SORT.get(item.country) || null,
          originalName: item.country,
          country: {
            name: countryAlias(item.country as unknown as string),
          },
        })),
      ["order"]
    );

    /* @ts-ignore */
    formatRegionPlans = plans.regions
      .filter((item: IPlan) =>
        TOP_COUNTRIES.includes(item.countryCode as string)
      )
      .map((item: IPlan) => ({
        ...item,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      }));
  } catch (err) {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries
        .filter((item: IPlan) =>
          TOP_COUNTRIES.includes(item.countryCode as string)
        )
        .map((item: IPlan) => ({
          ...item,
          order: COUNTRY_SORT.get(item.country) || null,
          originalName: item.country,
          country: {
            name: countryAlias(item.country as unknown as string),
          },
        })),
      ["order"]
    );

    formatRegionPlans = plans.regions.filter((item: IPlan) =>
      TOP_COUNTRIES.includes(item.countryCode as string)
    );
  }

  return (formatCountryPlans = [...formatCountryPlans, ...formatRegionPlans]);
}

export default async function Page() {
  const formatCountryPlans = await getPlans();

  return (
    <AppLayout contentBg="#fff">
      <Hero />
      <SectionContent title={"home:tab.header"}>
        <FeaturedCountries data={formatCountryPlans} />
      </SectionContent>
      <SectionContent
        title={"home:benefit.title"}
        caption={"home:benefit.description"}
      >
        <WhatIsEsim />
      </SectionContent>
      <SectionContent title={"home:features.title"}>
        <Features />
      </SectionContent>
      <SectionContent title={"home:howitworks.title"}>
        <HowItWorks />
      </SectionContent>
      <Box className="bg-secondary">
        <SectionContent title={"faq:home.title"}>
          <HomeFAQ />
        </SectionContent>
      </Box>
    </AppLayout>
  );
}
