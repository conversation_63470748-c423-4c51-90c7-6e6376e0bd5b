import type { Metadata } from "next";
import TranslationsProvider from "@/app/components/TranslationProvider";
import { locales } from "@/i18n/settings";
import initTranslations from "@/i18n";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "signup",
  "signin",
  "login",
  "verify",
];

export const metadata: Metadata = {
  title: "GM eSIM - eSIM Service for Overseas Travel",
  description:
    "Trusted eSIM service in over 100 countries! Instant activation from application, easy setup. The perfect simple and user-friendly web app for travel enthusiasts.",
  robots: {
    index: false,
  },
};

const Layout = async ({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) => {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
};

export default Layout;
