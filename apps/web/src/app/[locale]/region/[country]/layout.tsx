/*
 * import constants and helpers
 */
import { locales } from "@/i18n/settings";
import initTranslations from "@/i18n";
/*
 * import components
 */
import TranslationsProvider from "@/app/components/TranslationProvider";
/*
 * import constants
 */
import { countryMetaTags } from "@repo/ui/src/constants/regions";
/*
 * import styles
 */
import "./PlanForm.css";
import { capitalize } from "lodash";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "countries",
  "region",
  "profile",
  "chooseplan",
];

export async function generateMetadata(context: {
  params: {
    country: string;
  };
}) {
  return {
    title: `eSIM for ${capitalize(context.params.country)} - GM eSIM`,
    description: `Choose an eSIM for ${capitalize(
      context.params.country
    )} here! Freely select the number of usage days and data capacity to easily find the best plan for you. Ideal for travel and business, with reliable connection quality.`,
  };
}

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={locale}
      resources={resources}
    >
      {children}
    </TranslationsProvider>
  );
}
