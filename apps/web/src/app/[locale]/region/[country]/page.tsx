"use server";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import { Box, Container, Title } from "@mantine/core";

import SectionContent from "@repo/ui/src/common/SectionContent";
import { countryMetaTags } from "@repo/ui/src/constants/regions";
import type { IPlan } from "@repo/ui/src/interfaces/IPlan";

import AppLayout from "@/app/components/common/AppLayout";
import PlanContainer from "@/app/components/region/PlanContainer";
import RegionNotes from "@/app/components/region/RegionNotes";
import HomeFAQ from "@/app/components/top/HomeFAQ";
import HowItWorks from "@/app/components/top/HowItWorks";

import { countryNameException, reverseKebabCase } from "@/utils";

import { ApiService } from "@/api";

import type { INetwork } from "@/interfaces/INetwork";

import { PURCHASE_STEPS } from "@/app/constants";

const KoreaCounter = dynamic(
  () => import("@/app/components/region/KoreaCounter")
);
const DynamicFlowStepper = dynamic(
  () => import("@repo/ui/src/PurchaseFlowStepper")
);

const currentStep = 0;
async function getServerSideProps(context: {
  params: {
    country: string;
  };
}) {
  try {
    const countryException = countryNameException(context.params?.country);
    const country = reverseKebabCase(countryException);
    const response = await ApiService.getPlans({ country });
    const network: INetwork[] = response?.data?.data.network || {};
    const profile = response?.data?.data.country;

    return {
      network,
      countryProfile: profile || null,
      plans: response.data.data.plans,
    };
  } catch (err) {
    console.log(err);
    return null;
  }
}

export default async function Region(context: {
  params: {
    country: string;
  };
}) {
  if (!countryMetaTags[context.params.country as string]) {
    return notFound();
  }

  const props: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  } | null = await getServerSideProps(context);

  const country = context.params.country;
  const isLGU = country === "korea";
  const isNeedRegistration = ["taiwan", "hongkong"].includes(country);

  return (
    <AppLayout contentBg="#f3f3f3">
        <>
          <DynamicFlowStepper
            steps={PURCHASE_STEPS}
            currentStep={currentStep}
          />
          <div className="mb-[10px]"></div>
        </>
      <Box className="relative">
        <Title order={1} className="hidden">
          {`${country}用eSIM - エアトリeSIM`}
        </Title>
        <Container
          size={700}
          classNames={{
            root: `px-0 sticky top-[70px] z-20`,
          }}
        ></Container>
        <PlanContainer
          isLGU={isLGU}
          isNeedRegistration={isNeedRegistration}
          country={country}
          regionData={props}
        />
      </Box>
      <Box className="bg-white">
        <SectionContent title={"home:note.caution.title"}>
          <RegionNotes
            isShowLGUCompatible={isLGU}
            isNeedRegistration={isNeedRegistration}
          />
        </SectionContent>
        {isLGU && (
          <SectionContent title={"chooseplan:lgu.airport.support.title"}>
            <KoreaCounter />
          </SectionContent>
        )}
        <SectionContent title={"home:howitworks.title"}>
          <HowItWorks isLGU={isLGU} />
        </SectionContent>
      </Box>
      <Box className="bg-secondary">
        <SectionContent title={"faq:home.title"}>
          <HomeFAQ />
        </SectionContent>
      </Box>
    </AppLayout>
  );
}
