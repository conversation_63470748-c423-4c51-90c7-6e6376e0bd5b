"use server";

/*
 * import modules and libraries
 */
import { Suspense } from "react";
import { Text } from "@mantine/core"
import sortBy from "lodash/sortBy";
/*
 * import components
 */
import AppLayout from "@/app/components/common/AppLayout";
import SectionContent from "@repo/ui/src/common/SectionContent";
import Countries from "@/app/components/destination/Countries";
/*
 * import constants and utils
 */
import featuredPlans from "@repo/ui/src/constants/featured-plans.json";
import { REGIONS } from "@repo/ui/src/constants/regions";
import { COUNTRY_SORT } from "@/app/constants";
import { countryAlias } from "@/utils";
/*
 * import interfaces
 */
import type { IPlan } from "@/interfaces/IPlan";

async function getServerSideProps() {
  let formatCountryPlans: IPlan[] = [];
  const plans: {
    regions: IPlan[];
    countries: IPlan[];
  } = featuredPlans.data as {
    countries: any;
    regions: any;
  };

  try {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries.map((item: IPlan) => ({
        ...item,
        order: COUNTRY_SORT.get(item.country) || null,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      })),
      ["order"]
    );

    /* @ts-ignore */
    plans.regions = plans.regions.map((item: IPlan) => ({
      ...item,
      originalName: item.country,
      country: {
        name: countryAlias(item.country as unknown as string),
      },
    }));
  } catch (err) {
    /* @ts-ignore */
    formatCountryPlans = sortBy(
      plans.countries.map((item: IPlan) => ({
        ...item,
        order: COUNTRY_SORT.get(item.country) || null,
        originalName: item.country,
        country: {
          name: countryAlias(item.country as unknown as string),
        },
      })),
      ["order"]
    );
  }

  return {
    plans,
    formatCountryPlans,
  };
}

export default async function Destination() {
  const props = await getServerSideProps();

  const getRegionCountries = (region: string) => {
    if (!props.formatCountryPlans) {
      return [];
    }

    return props.formatCountryPlans.filter((item: IPlan) => item.region === region);
  }

  return (
    <AppLayout contentBg="#fff">
      <SectionContent mainTitle={"destination:header"} usePrimaryColor={true}>
        <Suspense>
          <Countries
            data={[
              ...REGIONS.map((item) => (
                {
                  name: `region:${item.name}`,
                  code: item.code,
                  plans: getRegionCountries(item.code),
                }
              )),
              {
                name: "common:regional-esim",
                code: "regional",
                plans: props.plans.regions,
              },
            ]}
            allData={[
              ...props.formatCountryPlans,
              ...props.plans.regions,
            ]}
          />
        </Suspense>
      </SectionContent>
    </AppLayout>
  );
}
