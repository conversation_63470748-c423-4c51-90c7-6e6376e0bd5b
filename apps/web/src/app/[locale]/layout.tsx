/*
 * import modules and libraries
 */
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import <PERSON>ript from "next/script";
import { Suspense } from "react";

import "@mantine/carousel/styles.css";
import { ColorSchemeScript, MantineProvider } from "@mantine/core";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css";
import { Notifications } from "@mantine/notifications";
import "@mantine/notifications/styles.css";

import { dir } from "i18next";

/*
 * import components
 */
import TranslationsProvider from "@/app/components/TranslationProvider";
import RootAuthProvider from "@/app/components/common/RootAuthProvider";

/*
 * import styles
 */
import "@/app/globals.css";
/*
 * import constants and helpers
 */
import { theme } from "@/app/theme";
import initTranslations from "@/i18n";
import { locales } from "@/i18n/settings";

export async function generateStaticParams() {
  return locales.map((locale: string) => ({ locale }));
}

const inter = Inter({ subsets: ["latin"], display: "swap" });
// Test namespaces
const i18nNamespaces = [
  "common",
  "currencies",
  "home",
  "faq",
  "compatibility",
  "countries",
  "region",
  "payment",
  "paypay-error",
];

export const metadata: Metadata = {
  title: "Global Mobile eSIM - Unlimited Data plans for Travelers",
  description:
    "Stay connected worldwide with Global Mobile eSIM. Choose from affordable unlimited or fixed data plans, enjoy instant activation, and reliable internet for your travels. No physical SIM needed – activate in minutes!",
};

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  const { resources } = await initTranslations(locale, i18nNamespaces);
  return (
    <html lang={locale} dir={dir(locale)}>
      <head>
        <ColorSchemeScript />
        <meta
          name="google-site-verification"
          content="9nlt4Wh0oUSTtlR-31UyO9KB6xIiH9qiGDmAcJC1bDE"
        />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body className={inter.className} id="app">
        <Script
          id="jquery"
          src="https://code.jquery.com/jquery-3.7.0.min.js"
          integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g="
          crossOrigin="anonymous"
        ></Script>
        <Script
          id="script-1"
          dangerouslySetInnerHTML={{
            __html: `
            (function(w,r)\{w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful')
            `,
          }}
        ></Script>

        {process.env.NODE_ENV !== "development" && (
          <Script
            type="text/javascript"
            id="script-3"
            dangerouslySetInnerHTML={{
              __html: `
              (function(w,d,s,l,i){w[l] = w[l] || [];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-TBGT3KJ');
              `,
            }}
          ></Script>
        )}
        {process.env.NODE_ENV !== "development" && (
          <div
            dangerouslySetInnerHTML={{
              __html: `
            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TBGT3KJ"
            height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
            <!-- End Google Tag Manager (noscript) -->
            `,
            }}
          ></div>
        )}
        <Script
          id="script-2"
          async
          src="https://r.wdfl.co/rw.js"
          data-rewardful="1ef043"
        ></Script>

        <TranslationsProvider
          namespaces={i18nNamespaces}
          locale={locale}
          resources={resources}
        >
          <MantineProvider theme={theme}>
            <Notifications position="top-right" />
            <Suspense>
              <RootAuthProvider>{children}</RootAuthProvider>
            </Suspense>
          </MantineProvider>
        </TranslationsProvider>
      </body>
    </html>
  );
}
