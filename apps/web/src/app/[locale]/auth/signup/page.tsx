"use server";

/*
 * import modules and libraries
 */
import { Suspense } from "react";
import type { Metada<PERSON> } from "next";
/*
 * import components
 */
import SignupForm from "@/app/components/auth/SignupForm";
import AppLayout from "@/app/components/common/AppLayout";
import AuthLayout from "@/app/components/auth/AuthLayout";

export async function metadata() {
  const metadata: Metadata = {
    title: "Sign Up - GM eSIM",
    description:
      "Easy registration! Trusted eSIM service available in over 100 countries. Recommended for everyone from first-time eSIM users to frequent travelers. Enjoy smooth network activation and simple operation, anytime, anywhere.",
  };

  return metadata;
}

export default async function Page() {
  return (
    <AppLayout contentBg="#fff">
      <Suspense>
        <AuthLayout>
          <SignupForm />
        </AuthLayout>
      </Suspense>
    </AppLayout>
  );
}
