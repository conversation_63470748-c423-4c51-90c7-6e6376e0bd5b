"use server";
/*
 * import modules and libraries
 */
import type { Metadata } from "next";

import { Suspense } from "react";
/*
 * import components
 */
import SignInForm from "@/app/components/auth/SignInForm";
import AppLayout from "@/app/components/common/AppLayout";
import AuthLayout from "@/app/components/auth/AuthLayout";

export async function metadata() {
  const metadata: Metadata = {
    title: "Login - GM eSIM",
    description:
      "Trusted eSIM service in over 100 countries! Instant activation from application, easy setup. The perfect simple and user-friendly web app for travel enthusiasts.",
  };

  return metadata;
}

export default async function SignIn() {
  return (
    <AppLayout contentBg="#fff">
      <Suspense>
        <AuthLayout>
          <SignInForm />
        </AuthLayout>
      </Suspense>
    </AppLayout>
  );
}
