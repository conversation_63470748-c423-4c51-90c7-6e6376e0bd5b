"use client";
import { httpClient } from "@/api";
import useLogin from "@/hooks/useLogin";
import Storage from "@/utils/storage";
import { LoadingOverlay } from "@mantine/core";
import { useMounted } from "@mantine/hooks";
import { useSearchParams } from "next/navigation";
import { useLayoutEffect } from "react";

const getToken = (payload: object) => httpClient.post("/auth/token", payload);

const SSOHandler = () => {
  const params = useSearchParams();
  const { onToken } = useLogin();
  const firstRender = useMounted();

  useLayoutEffect(() => {
    if (!firstRender) return;
    const code = params?.get("code");
    if (!code) return;

    getToken({ code })
      .then(({ data }: any) => {
        onToken(data.data);
        const returnUrl = params?.get("returnUrl");
        window.location.href =
          (returnUrl && decodeURIComponent(returnUrl)) ||
          Storage.getInstance().get("return_url") ||
          "/app";
      })
      .catch(() => {
        alert("Invalid or Expired credentials.");
        window.location.href = "/";
      });
  }, [firstRender]);

  return (
    <div>
      <LoadingOverlay visible />
    </div>
  );
};

export default SSOHandler;
