export const ESIM_API_PATH = process.env.NEXT_PUBLIC_API_HOST;
export const ERR_GENERIC_REQUIRED = "This field is required.";
export const REGEX_STRONG_PASSWORD = /^(?=.*[A-Z]).{8,}$/;
export const ERR_MIN_PASSWORD_LENGTH =
  "Password must be at least 8 characters long.";
export const ERR_STRONG_PASSWORD =
  "Passwords must be at least eight characters long and contain at least one uppercase character.";
export const AUTH_TOKEN_NAME = "gesim";
export const ERR_PASSWORD_NOT_MATCHED =
  "The username and password combination did not match.";
export const google_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Google`;
export const fb_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Facebook`;

const countries = [
  "Japan",
  "Korea",
  "Taiwan",
  "China",
  "Macau",
  "HongKong",
  "USA",
  "Hawaii",
  "Canada",
  "Philippines",
  "Thailand",
  "Singapore",
  "Vietnam",
  "Malaysia",
  "Indonesia",
  "Cambodia",
  "Laos",
  "Italy",
  "France",
  "Germany",
  "UK",
  "Australia",
  "New Zealand",
  "Guam",
];
export const COUNTRY_SORT = new Map();
// Sorting countries
for (let x = 0; x < countries.length; x += 1) {
  const country_name = countries[x];
  COUNTRY_SORT.set(country_name.toLocaleLowerCase(), x + 1);
}

export const PURCHASE_STEPS = ["Pick", "Confirm", "Complete"];
