'use client';

/*
 * import modules and libraries
 */
import Link from "next/link";
import { useTranslation, Trans } from "react-i18next";
import { Box } from "@mantine/core";
/*
 * import components
 */
import AppBreadCrumb from "@/app/components/common/AppBreadCrumb";
import SectionContent from "@repo/ui/src/common/SectionContent";
/*
 * import constants and utils
 */
import { countryAlias, reverseKebabCase } from "@/utils";

const ColumnBreadCrumbs = ({
  columnTitle
}: {
  columnTitle: string;
}) => {
  const { t } = useTranslation();

  return (
    <SectionContent zeroHeader noFooter>
      <Box className="pt-8 md:pt-12 pb-4">
        <AppBreadCrumb
          component="ul"
          classNames={{
            root: "flex-wrap",
            breadcrumb: "text-ellipsis overflow-hidden py-1"
          }}
        >
          <li><Link href={"/"} className="text-black no-underline hover:underline">{t("common:navbar.home.label")}</Link></li>
          <li><Link href={"/column"} className="text-black no-underline hover:underline">コラム・ブログ</Link></li>
          <li><Link href={"#"} className="text-[#a3a3a3] no-underline">{columnTitle}</Link></li>
        </AppBreadCrumb>
      </Box>
    </SectionContent>
  );
};

export default ColumnBreadCrumbs;
