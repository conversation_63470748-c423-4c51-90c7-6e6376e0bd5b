"use client";

/*
 * import modules and libraries
 */
import {
  Button,
  CopyButton,
  Paper,
  Stack,
  Text,
  TextInput,
} from "@mantine/core";
import { useTranslation } from "react-i18next";

const ManualCopyBox = (props: { title: string; copyValue: string }) => {
  const { t } = useTranslation()
  return (
    <Paper 
      className="w-fill p-2 bg-[#F9F9F9]"
    >
      <Stack>
        <Text className="text-sm font-semibold">
          {props.title}
        </Text>
        <TextInput
          value={props.copyValue}
          defaultValue={props.copyValue}
          size="xs"
          classNames={{
            section: "w-[unset]"
          }}
          rightSection={
            <CopyButton value={props.copyValue}>
              {({ copied, copy }) => (
                <Button
                  bg={copied ? "app-dark" : "app-pink.4"}
                  size="xs"
                  onClick={copy}
                  color="app-dark"
                >
                  {copied ? "Copied" : t("common:copyable-item.btn.copy.txt")}
                </Button>
              )}
            </CopyButton>
          }
        />
      </Stack>
    </Paper>
  );
};

const ManualActivation = ({
  activateCode,
  smdp,
  downloadLink,
  os,
}: {
  os: "ios" | "android";
  activateCode: string,
  smdp: string,
  downloadLink: string,
}) => {
  const { t } = useTranslation();

  return (
    <Stack className="mb-4 gap-4">
      {os === "android" ? (
        <ManualCopyBox
          title={os === "android" ? t(`activation:downloadlink`) : t(`activation:smdp.address`)}
          copyValue={downloadLink + ""}
        />
      ) : (
        <>
          <ManualCopyBox
            title={t("activation:smdp.address")}
            copyValue={smdp}
          />
          <ManualCopyBox
            title={t("activation:activation-code.txt")}
            copyValue={activateCode + ""}
          />
        </>
      )}
    </Stack>
  );
};
export default ManualActivation;
