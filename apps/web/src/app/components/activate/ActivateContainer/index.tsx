"use client";

/*
 * import modules and libraries
 */
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import {
  Stack,
  Divider,
  Button,
  SegmentedControl,
  Box,
  Title,
  Drawer,
  Image,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { IconRouter, IconQrcode } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
/*
 * import components
 */
import SectionContent from "@repo/ui/src/common/SectionContent";
import ManualActivation from "@/app/components/activate/ManualActivation";
import Guide from "@/app/components/activate/Guide";
/*
 * import constants and helpers
 */
import { useMessageStore } from "@/store/MessageStore";
/*
 * import data types
 */
import { IActivationGuide } from "@repo/ui/src/interfaces/IActivation";
/*
 * import dynamic components
 */
const DynamicActivationModal = dynamic(
  () => import("@repo/ui/src/modals/ActivationModal")
);

const DataType = {
  MANUAL: "MANUAL",
  QR: "QR",
};

const ActivateContainer = (props: {
  error: any;
  isLoading?: boolean;
  guideOnly?: boolean;
  os?: string;
  guide: (method: string) => IActivationGuide[];
  orderInfo?: {
    order: {
      qrCodeImgUrl: string;
      downloadLink: string;
      activateCode: string;
      smdp: string;
    };
  };
}) => {
  const router = useRouter();
  const [isGlobalLoading, setGlobalLoading] = useMessageStore((s) => [
    s.isLoading,
    s.toggleLoading,
  ]);
  const [opened, { open, close }] = useDisclosure(false);
  const [activeMethod, setActiveMethod] = useState(DataType.MANUAL);
  const { t } = useTranslation();

  useEffect(() => {
    if (props.error) {
      router.push("/app");
      setGlobalLoading(false);
      return;
    }
    setGlobalLoading(props.isLoading);
  }, [props.error, props.isLoading]);

  return (
    <>
      <SectionContent
        title={props.guideOnly ? "" : "activation:header"}
        noHeader={props.guideOnly}
        small
      >
        <Stack>
          <SegmentedControl
            w={"100%"}
            radius="lg"
            size={props.guideOnly ? "lg" : "sm"}
            defaultValue={activeMethod}
            value={activeMethod}
            onChange={setActiveMethod}
            color="app-pink.4"
            classNames={{
              root: "p-0 mt-1 mb-4 bg-white",
              control: "rounded-none border border-gray-200",
              indicator: "rounded-none",
              label: "text-primary data-[active=true]:text-white",
            }}
            data={[
              {
                value: DataType.MANUAL,
                label: (
                  <Title
                    order={2}
                    className="text-base flex justify-center items-center gap-2"
                  >
                    <IconRouter /> {t("setupguide:setup.setupmanually")}
                  </Title>
                ),
              },
              {
                value: DataType.QR,
                label: (
                  <Title
                    order={2}
                    className="text-base flex justify-center items-center gap-2"
                  >
                    <IconQrcode /> {t("setupguide:setup.setupqr")}
                  </Title>
                ),
              },
            ]}
          />
          <Guide
            guide={props.guide(activeMethod.toLowerCase())}
            buttonText={
              activeMethod === DataType.QR
                ? t("setupguide:setup.displayqr")
                : t("setupguide:setup.displayinstall")
            }
            openModal={open}
            guideOnly={props.guideOnly}
          />
        </Stack>
      </SectionContent>
      {!props.guideOnly && (
        <>
          <Box className="sticky bottom-0 bg-white p-4 md:hidden border-t-2 border-t-gray-100 text-center">
            <Button
              size="md"
              color="app-pink.4"
              className="max-w-sm w-full mx-auto"
              onClick={open}
            >
              {activeMethod === DataType.QR
                ? t("setupguide:setup.qrcode")
                : t("setupguide:setup.setupmanually")}
            </Button>
          </Box>
          <DynamicActivationModal
            title={
              activeMethod === DataType.QR
                ? t("setupguide:setup.setupqr")
                : t("setupguide:setup.setupmanually")
            }
            opened={opened}
            onClose={close}
            visibleFrom="md"
          >
            <Divider className="mb-4" />
            {activeMethod === DataType.QR && (
              <Box className="bg-gray-100 py-4">
                <Image
                  alt="qr"
                  src={props.orderInfo?.order?.qrCodeImgUrl}
                  h={160}
                  w={160}
                  classNames={{
                    root: "mx-auto !h-40 !w-40",
                  }}
                />
              </Box>
            )}
            {activeMethod === DataType.MANUAL && props.orderInfo && (
              <ManualActivation
                os={(props.os as "android") || "android"}
                activateCode={props.orderInfo.order.activateCode}
                downloadLink={props.orderInfo.order.downloadLink}
                smdp={props.orderInfo.order.smdp}
              />
            )}
          </DynamicActivationModal>
          <Drawer
            radius="md"
            size="360px"
            opened={opened}
            onClose={close}
            title={
              activeMethod === DataType.QR
                ? t("setupguide:setup.qrcode")
                : t("setupguide:setup.install")
            }
            hiddenFrom="md"
            position="bottom"
            classNames={{
              header: "justify-center",
              title: "text-base font-bold",
              close: "absolute right-3",
              body: "p-0",
            }}
          >
            <Box className="p-4">
              <Divider className="mb-4" />
              {activeMethod === DataType.QR && (
                <Box className="bg-gray-100 py-4">
                  <Image
                    alt="qr"
                    src={props.orderInfo?.order?.qrCodeImgUrl}
                    h={160}
                    w={160}
                    classNames={{
                      root: "mx-auto !h-40 !w-40",
                    }}
                  />
                </Box>
              )}
              {activeMethod === DataType.MANUAL && props.orderInfo && (
                <ManualActivation
                  os={(props.os as "android") || "android"}
                  activateCode={props.orderInfo.order.activateCode}
                  downloadLink={props.orderInfo.order.downloadLink}
                  smdp={props.orderInfo.order.smdp}
                />
              )}
            </Box>
          </Drawer>
        </>
      )}
    </>
  );
};

export default ActivateContainer;
