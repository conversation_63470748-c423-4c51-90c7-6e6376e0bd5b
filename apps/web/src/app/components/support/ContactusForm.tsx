"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";

import {
  <PERSON>chor,
  Button,
  Center,
  Checkbox,
  Flex,
  SegmentedControl,
  Stack,
  Text,
} from "@mantine/core";
import { useForm } from "@mantine/form";

import { GoogleReCaptcha } from "react-google-recaptcha-v3";
import { Trans, useTranslation } from "react-i18next";

import SectionContent from "@repo/ui/src/common/SectionContent";

import { ApiService } from "@/api";

import { useMessageStore } from "@/store/MessageStore";

import AppNativeSelect from "../form/AppNativeSelect/AppNativeSelect";
import AppTextInput from "../form/AppTextInput/AppTextInput";
import AppTextarea from "../form/AppTextarea/AppTextarea";

export default function ContactusForm() {
  const router = useRouter();
  const [orderStatus, setOrderStatus] = React.useState("applied");
  const [resetKey, setResetKey] = useState("");
  const [recapatcha, setRecaptcha] = useState("");
  const [savedOrderNumber, setSavedOrderNumber] = React.useState("");
  const { t, i18n } = useTranslation();
  const locale = i18n?.language;
  const [setGlobalMessage, setGlobalLoading] = useMessageStore((s) => [
    s.setMessage,
    s.toggleLoading,
  ]);
  const [isFormFilled, setIsFormFilled] = useState<boolean>(false);

  useEffect(() => {
    if (orderStatus === "notApplied") {
      setSavedOrderNumber(form.getValues().orderNumber);
      form.setValues({
        orderStatus: orderStatus,
        orderNumber: "",
      });
    } else {
      form.setValues({
        orderStatus: orderStatus,
        orderNumber: savedOrderNumber,
      });
    }
  }, [orderStatus]);

  const form = useForm({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      orderNumber: "",
      problemCategory: "",
      problem: "",
      informationSource: "",
      orderStatus: "",
    },
    validate: {
      firstName: (value) =>
        value.length < 2 ? t("contact:contactus.error-message.default") : null,
      lastName: (value) =>
        value.length < 2 ? t("contact:contactus.error-message.default") : null,
      email: (value) =>
        /^\S+@\S+$/.test(value)
          ? null
          : t("contact:contactus.error-message.mail"),
      orderNumber: (value, values) =>
        values.orderStatus === "applied" && !value
          ? t("contact:contactus.error-message.default")
          : null,
      problemCategory: (value) =>
        value ? null : t("contact:contactus.error-message.unselected"),
      problem: (value) =>
        value ? null : t("contact:contactus.error-message.default"),
      informationSource: (value) =>
        value ? null : t("contact:contactus.error-message.unselected"),
    },
  });

  useEffect(() => {
    const {
      firstName,
      lastName,
      email,
      orderNumber,
      problemCategory,
      problem,
      informationSource,
      orderStatus,
    } = form.values;
    if (orderStatus === "applied") {
      setIsFormFilled(
        !!(
          firstName &&
          lastName &&
          email &&
          orderNumber &&
          problemCategory &&
          problem &&
          informationSource
        )
      );
    } else {
      setIsFormFilled(
        !!(
          firstName &&
          lastName &&
          email &&
          problemCategory &&
          problem &&
          informationSource
        )
      );
    }
  }, [form.values]);

  const handleSubmit = React.useCallback(
    async (values: any) => {
      try {
        setGlobalLoading();
        await ApiService.contactUs({
          orderNumber: values?.orderNumber,
          firstName: values.firstName,
          lastName: values.lastName,
          emailAddress: values.email,
          problemCategory: values.problemCategory,
          problem: values.problem,
          informationSource: values.informationSource,
          language: "en",
          recaptha: recapatcha,
          origin: "GLOBAL_ESIM_ENGLISH",
        });
        router.push("/support/finished");
      } catch (err: any) {
        setGlobalMessage(err.response.data.message);
      } finally {
        setGlobalLoading();
        setResetKey(Date.now() + "");
      }
    },
    [recapatcha]
  );

  const onVerify = useCallback((token: string) => {
    setRecaptcha(token);
  }, []);

  const orderStatusData = [
    {
      value: "applied",
      label: <Text>{t("contact:formfield.order-status-applied")}</Text>,
    },
    {
      value: "notApplied",
      label: <Text>{t("contact:formfield.order-status-not-applied")}</Text>,
    },
  ];

  const problemCategory = [
    {
      label: t("contact:formfield.default-value"),
      value: "",
    },
    {
      label: t("contact:formfield.problem-category-value1"),
      value: t("contact:formfield.problem-category-value1"),
    },
    {
      label: t("contact:formfield.problem-category-value2"),
      value: t("contact:formfield.problem-category-value2"),
    },
    {
      label: t("contact:formfield.problem-category-value3"),
      value: t("contact:formfield.problem-category-value3"),
    },
    {
      label: t("contact:formfield.problem-category-value4"),
      value: t("contact:formfield.problem-category-value4"),
    },
    {
      label: t("contact:formfield.problem-category-value5"),
      value: t("contact:formfield.problem-category-value5"),
    },
  ];

  const informationSource = [
    {
      label: t("contact:formfield.default-value"),
      value: "",
    },
    {
      label: t("contact:formfield.information-source-value1"),
      value: t("contact:formfield.information-source-value1"),
    },
    {
      label: t("contact:formfield.information-source-value2"),
      value: t("contact:formfield.information-source-value2"),
    },
    {
      label: t("contact:formfield.information-source-value3"),
      value: t("contact:formfield.information-source-value3"),
    },
    {
      label: t("contact:formfield.information-source-value4"),
      value: t("contact:formfield.information-source-value4"),
    },
    {
      label: t("contact:formfield.information-source-value5"),
      value: t("contact:formfield.information-source-value5"),
    },
  ];

  return (
    <SectionContent
      mainTitle={t("contact:contactus.title") as string}
      caption={t("contact:contactus.description") as string}
      noHeader={true}
    >
      <GoogleReCaptcha onVerify={onVerify} refreshReCaptcha={resetKey} />
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack mt="20">
          <Flex direction={"row"} justify={"space-between"}>
            <AppTextInput
              label={t("signup:label.name")}
              className={"w-[48%]"}
              withAsterisk
              placeholder={t("signup:formfield.lastname.placeholder") as string}
              {...form.getInputProps("lastName")}
            />
            <AppTextInput
              label={" "}
              className={"w-[48%]"}
              withAsterisk
              placeholder={
                t("signup:formfield.firstname.placeholder") as string
              }
              {...form.getInputProps("firstName")}
            />
          </Flex>
          <AppTextInput
            label={t("login:email.title")}
            withAsterisk
            placeholder={t("signup:formfield.email.placeholder") as string}
            {...form.getInputProps("email")}
          />
          <Text mb="20" span>
            <Trans i18nKey="signup:email.warning">
              ※Please enter an address where you can receive emails from
              <Link
                className="text-[#C72B4D] no-underline"
                href={"mailto:<EMAIL>"}
              >
                <EMAIL>
              </Link>
            </Trans>
          </Text>
          <SegmentedControl
            w={"100%"}
            radius="lg"
            mb="0.5rem"
            defaultValue={orderStatus}
            value={orderStatus}
            onChange={setOrderStatus}
            size="lg"
            classNames={{
              root: "p-0 mt-1 mb-4 max-w-lg self-center bg-white",
              control: "rounded-none border border-gray-200",
              indicator: "rounded-none",
              label: "text-primary data-[active=true]:text-white",
            }}
            data={orderStatusData}
            color="app-pink.4"
            fw={700}
          />
          {orderStatus === "applied" ? (
            <AppTextInput
              label={t("contact:formfield.order-number")}
              withAsterisk
              placeholder={"GEsim" as string}
              {...form.getInputProps("orderNumber")}
            />
          ) : null}
          <AppNativeSelect
            label={t("contact:formfield.problem-category")}
            data={problemCategory}
            withAsterisk
            {...form.getInputProps("problemCategory")}
          />
          <AppTextarea
            size="lg"
            placeholder={t("contact:formfield.problem-placeholder")}
            withAsterisk
            {...form.getInputProps("problem")}
          />
          <AppNativeSelect
            label={t("contact:formfield.information-source")}
            data={informationSource}
            withAsterisk
            {...form.getInputProps("informationSource")}
          />
          <Text>{t("contact:formfield.customer-information-policy")}</Text>
          <Center>
            <Anchor
              size="md"
              component={Link}
              href={"https://www.inbound-platform.com/en/privacy-contact/"}
              c="app-pink.4"
              underline="never"
              classNames={{
                root: "px-0",
              }}
            >
              {t("contact:formfield.customer-information-policy-check")}
            </Anchor>
          </Center>
          <Button
            size="lg"
            w={"100%"}
            type="submit"
            color="app-pink"
            disabled={!isFormFilled}
          >
            {t("contact:sendmessage")}
          </Button>
          <Text>
            <Trans i18nKey="contact:formfield.inputproblems">
              If the form input fields are not displayed, please contact us at
              <Link
                className="text-[#C72B4D] no-underline"
                href={"mailto:<EMAIL>"}
              >
                <EMAIL>
              </Link>
            </Trans>
          </Text>
        </Stack>
      </form>
    </SectionContent>
  );
}
