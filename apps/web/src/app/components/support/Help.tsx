"use client";

import { Stack, Text, Title } from "@mantine/core";

import { Trans, useTranslation } from "react-i18next";

import SectionContent from "@repo/ui/src/common/SectionContent";

import CTAButton from "@/app/components/common/CTAButton";
import CompatibleDevicesLink from "@/app/components/support/CompatibleDevicesButton";

export default function Help() {
  const { t } = useTranslation();

  return (
    <SectionContent noHeader>
      <Stack>
        <Title order={1} className="text-center font-bold" size="1.8rem">
          {t("help:setup.title")}{" "}
        </Title>
        {/* <FAQLink /> */}
        <CompatibleDevicesLink />
        {/* TO DO: Add links to the "how to set" video - EN */}
        {/* <Guide /> */}
        <Text className="self-center">
          <Trans i18nKey="help:setuplink.label" components={{ a: <a /> }} />
        </Text>
        <CTAButton />
      </Stack>
    </SectionContent>
  );
}
