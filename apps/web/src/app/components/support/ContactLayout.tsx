"use client";

/*
 * import modules and libraries
 */
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@mantine/core";
import { usePathname, useRouter } from "next/navigation";
/*
 * import components
 */
import AppTabs from "@/app/components/common/AppTabs/AppTabs";

const ContactLayout = (props: { children: React.ReactNode }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const paths = pathname?.split("/") || [];
  const path = paths[paths.length - 1] as string | null;
  const [activeTab, setActiveTab] = useState<string | null>(path);

  const headers = [
    {
      name: t("contact:btn.help"),
      code: "help",
    },
    {
      name: t("contact:btn.contactus"),
      code: "contactus",
    },
  ];

  return (
    <>
      <AppTabs
        activeTab={activeTab}
        defaultValue={activeTab}
        headers={headers}
        onTabChange={(tab: string) => {
          setActiveTab(tab);
          router.push(`/support/${tab}`);
        }}
      />
      <Container size={1080} className="py-12">
        {props.children}
      </Container>
    </>
  );
};
export default ContactLayout;
