import { Divider, Flex, List, Text, Title } from "@mantine/core";

const SubHelp = ({
    items = [],
  }: {
    items: {
      title: string;
      content: JSX.Element | string;
    }[];
  }) => {
    return (
      <List
        w={"100%"}
        listStyleType="none"
        spacing={16}
      >
        {items.map((item, index) => (
          <List.Item key={index}>
            <Flex direction={"column"} mb={10} gap={5}>
              <Title fw={800} order={4} size={14}>
                {item.title}
              </Title>
              <Text className="text-left" size={"sm"}>
                {item.content}
              </Text>
            </Flex>
            {index < items.length - 1 && <Divider w={"100%"} mt={10} />}
          </List.Item>
        ))}
      </List>
    );
  };
  export default SubHelp;