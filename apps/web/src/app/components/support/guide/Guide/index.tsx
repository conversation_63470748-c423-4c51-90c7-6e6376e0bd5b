"use client";

/*
 * import modules and libraries
 */
import { Card, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";
/*
 * import components
 */
import SetupGuide from "@repo/ui/src/SetupGuide";

export default function Guide() {
  const { t } = useTranslation();
  return (
    <Card className="bg-secondary rounded-lg">
      <Title
        className="font-bold text-center text-lg"
        order={2}
      >
        {t("compatibility:home.title")}
      </Title>
      <SetupGuide
        compact
        hideVideo
      />
    </Card>
  );
}