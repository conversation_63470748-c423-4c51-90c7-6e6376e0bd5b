"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";
import {
  Button,
  Card,
  Title,
  Text,
  Image,
  Stack,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";

export default function FAQLink() {
  const { t } = useTranslation();
  return (
    <Card className="bg-secondary rounded-lg">
      <Stack>
        <Title
          className="font-bold text-center text-lg"
          order={2}
        >
          {t("contact:help.faq.title")}
        </Title>
        <Text
          className="max-w-lg text-sm md:text-base mx-auto"
          dangerouslySetInnerHTML={{
            __html: t("contact:help.faq.description"),
          }}
        />
        <Button
          size="lg"
          radius="md"
          variant="outline"
          color="app-pink.4"
          component={Link}
          href="/faq"
          bg="white"
          classNames={{
            root: "max-w-sm w-full mx-auto"
          }}
        >
          <Image w={32} src={getCDNUrl("/assets/gm-guide.svg")} />
          <Text className="font-bold" c="black" ml={10}>
            よくある質問をもっと見る
          </Text>
        </Button>
      </Stack>
    </Card>
  );
}