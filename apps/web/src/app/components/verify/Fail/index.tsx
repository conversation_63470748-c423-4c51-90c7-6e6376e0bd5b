"use client";

/*
 * import modules and libraries
 */
import { Button, Flex, Text, Title, Image } from "@mantine/core";
import { useRouter } from "next/navigation";
/*
 * import components
 */
import SectionContent from "@repo/ui/src/common/SectionContent";
/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";

export default function VerifyFail() {
  const router = useRouter();

  return (
    <SectionContent small>
      {/* TODO: refactor this code */}
      <Flex gap={25} direction={"column"} align={"center"} justify={"center"}>
        <Image
          src={getCDNUrl("/assets/fail.png")}
          width={80}
          height={80}
          className="w-[80px] h-[80px]"
        />
        <Title order={4}>検証に失敗しました。</Title>
        <Text className="text-center">
          こちらの検証リンクが無効であるか、期限切れです。<br />
          認証用メールの再送をお願いいたします。
        </Text>
        <Button
          color="app-dark"
          w={"100%"}
          onClick={() => router.push("/account/resend-email")}
        >
          認証用メールを再送する
        </Button>
      </Flex>
    </SectionContent>
  );
}
