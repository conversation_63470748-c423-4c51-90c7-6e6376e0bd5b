"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";
import { Button, Flex, Text, Title, Image, Alert } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconInfoCircle } from '@tabler/icons-react';
import { useSearchParams } from "next/navigation";
/*
 * import components
 */
import SectionContent from "@repo/ui/src/common/SectionContent";
/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";

export default function VerifySuccess() {
  const { t } = useTranslation();
  const queryParams = useSearchParams();

  return (
    <SectionContent small>
      {/* TODO: refactor this code */}
      <Flex
        w={"100%"}
        gap={20}
        direction={"column"}
        align={"center"}
        justify={"center"}
      >
        <Image
          src={getCDNUrl("/assets/checkmark.webp")}
          width={80}
          height={80}
          fit="contain"
          className="w-[80px] h-[80px]"
        />
        <Title order={4}>{t("verify:verify-success.header.title")}</Title>
        <Text>{t("verify:verify-success.title")}</Text>
        {
          queryParams?.get("guest") === "true" && (
            <Alert
              variant="light"
              color="app-pink.4"
              title="通知"
              icon={<IconInfoCircle />}
              classNames={{
                message: "text-sm"
              }}
            >
              本ご注文で、お客様のアカウントを自動作成いたしました。
              リンクをメールに送信致しました。
              以降、マイアカウントにログインする際は、パスワードが必要でございますので、初期パスワードを設定してください。
            </Alert>
          )
        }
        <Link
          style={{
            width: "100%",
            textDecoration: "none",
          }}
          href={"/auth/signin"}
        >
          <Button fullWidth color="app-dark" w={"100%"}>
            {t("verify:verify-success.btn.continue")}
          </Button>
        </Link>
      </Flex>
    </SectionContent>
  );
}
