"use client";

import { useRouter } from "next/navigation";

import {
  Box,
  Button,
  CopyButton,
  Divider,
  Group,
  Image,
  Stack,
  Text,
  ThemeIcon,
  Tooltip,
} from "@mantine/core";

import { IconCopy } from "@tabler/icons-react";
import { Trans, useTranslation } from "react-i18next";

import ActivateButtons from "@/app/components/my-esim/MyEsimContainer/MyEsimList/EsimCardDetail/EsimCard/ActivateButtons";

import { countryAlias, getCDNUrl } from "@/utils";

import { IOrder } from "@/interfaces/IOrder";

const CardDetail = ({
  iconUrl,
  label,
  value,
}: {
  iconUrl: string;
  label: string;
  value: string;
}) => {
  return (
    <>
      <Divider color="#3E53A3" />
      <Group w="100%" justify={"flex-start"} align={"center"}>
        <ThemeIcon color={"transparent"} size={18} radius="xl">
          <Image src={getCDNUrl(iconUrl)} />
        </ThemeIcon>
        <Text className="font-semibold text-white">{label}</Text>
        <Text className="ml-auto font-semibold capitalize text-white">
          <Trans i18nKey={`countries:${value}`}>
            {/* @ts-ignore */}
            {countryAlias(value)}
          </Trans>
        </Text>
      </Group>
    </>
  );
};

const MyEsimCard = ({
  order,
  ...props
}: {
  hideButton?: boolean;
  status?: string;
  isExpired?: boolean;
  order: IOrder;
}) => {
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <Stack className="gap-3 text-white">
      <CopyButton value={order.iccid || "N/A"}>
        {({ copied, copy }) => (
          <Group className="items-center gap-2">
            <Text className="text-white">ICCID: {order.iccid || "N/A"}</Text>
            <Tooltip label="コピーされました。" opened={copied} position="top">
              <IconCopy size={18} onClick={copy} className="cursor-pointer" />
            </Tooltip>
          </Group>
        )}
      </CopyButton>

      {order.qrCodeImgUrl && !props.isExpired && (
        <>
          <Divider color="#3E53A3" />
          <Box className="py-4">
            <Image
              alt="qr"
              src={order.qrCodeImgUrl}
              h={160}
              w={160}
              classNames={{
                root: "mx-auto !h-40 !w-40",
              }}
            />
          </Box>
        </>
      )}

      <CardDetail
        iconUrl="/assets/coverage.png"
        label={t("region:coverage")}
        /* @ts-ignore */
        value={order?.plan?.country?.name || order?.plan?.country}
      />

      <CardDetail
        iconUrl="/assets/data.png"
        label={t("region:data")}
        value={
          order.plan.name.toLowerCase() === "unlimited"
            ? "無制限"
            : order.plan.dataId
        }
      />

      <CardDetail
        iconUrl="/assets/validity.png"
        label={t("region:validity")}
        value={t("common:siminfo-card.validity.unit", {
          count: order.plan.validityDays, // Get the usage plan days without unit
          defaultValue: "{{ count }} day",
          defaultValue_other: "{{ count }} days",
        })}
      />

      <Divider color="#3E53A3" />
      {props.hideButton && props.isExpired && (
        <Button
          classNames={{
            label: "text-black",
          }}
          bg={"white"}
          /* @ts-ignore */
          onClick={() => router.push("/checkout/plan/" + order.plan.id)}
        >
          {t("region:buyagain")}
        </Button>
      )}
      {props.status === "expired" && (
        <>
          <Button
            classNames={{
              root: "border-white",
              label: "white",
            }}
            variant="outline"
            color="white"
            onClick={() => router.push("/app/orders/" + order.orderId)}
          >
            {t("region:details")}
          </Button>
          <Button
            classNames={{
              label: "text-black",
            }}
            bg={"white"}
            /* @ts-ignore */
            onClick={() => router.push("/checkout/plan/" + order.plan.id)}
          >
            {t("region:buyagain")}
          </Button>
        </>
      )}
      {!props.hideButton && (
        <>
          {order.isActivated && props.status !== "expired" && (
            <>
              <Button
                classNames={{
                  label: "text-black",
                }}
                bg={"white"}
                onClick={() => router.push("/app/orders/" + order.orderId)}
              >
                {t("region:checkusage")}
              </Button>
            </>
          )}
          {!order.isActivated && (
            <ActivateButtons orderdId={order.orderId as string} />
          )}
        </>
      )}
    </Stack>
  );
};
export default MyEsimCard;
