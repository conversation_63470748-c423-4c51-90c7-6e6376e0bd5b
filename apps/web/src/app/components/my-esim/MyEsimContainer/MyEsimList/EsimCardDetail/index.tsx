"use client";

/*
 * import modules and libraries
 */
import { useEffect } from "react";
import {
  Accordion,
  Badge,
  Flex,
  Group,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import Image from "next/image";
import { isPast } from "date-fns";
import { Trans, useTranslation } from "react-i18next";
/*
 * import components
 */
import EsimCard from "@/app/components/my-esim/MyEsimContainer/MyEsimList/EsimCardDetail/EsimCard";
/*
 * import helpers and constants
 */
import {
  countryAlias,
  formatDate,
  getCDNUrl,
  regionalOrCountry,
} from "@/utils";
/*
 * import interfaces
 */
import { IOrder } from "@/interfaces/IOrder";

const EsimCardDetail = ({
  item,
  ...props
}: {
  esimCardProps?: Partial<React.ComponentProps<typeof EsimCard>>;
  isExpired?: boolean
  item: IOrder & { status?: string };
}) => {
  const { t } = useTranslation()
  useEffect(() => {
    if (!item || !item.orderId || item.activateDate) return;
    if (item.status === "expired") return
    if (item.expireTime) return
  }, [item?.orderId]);

  return (
    <Accordion.Item
      value={item.orderId + ""}
      key={item.orderId}
      className="shadow-md"
    >
      <Group className="p-2 bg-[#ffffff30] justify-around">
        <Group className="w-full">
          <Text className="text-sm">
            {t("common:siminfo-card.orderdate")} {formatDate(item.createdAt)}
          </Text>
          {!isPast(new Date(item.expireTime || "")) ? (
            item.isActivated ? (
              <Badge className="bg-[#0CB21A] rounded-md" variant="filled">
                <Trans i18nKey={"common:siminfo-card.tag.activated"}>
                  ACTIVE
                </Trans>
              </Badge>
            ) : (
              <Badge className="bg-[#FF913F] rounded-md"  variant="filled">
                <Trans i18nKey={"common:siminfo-card.tag.notactivated"}>
                  INACTIVE
                </Trans>
              </Badge>
            )
          ) : null}
        </Group>
      </Group>
      <Accordion.Control>
        <Group id={item.orderId + ""}>
          <Image
            alt="esim"
            height={48}
            width={90}
            src={getCDNUrl("/assets/card-esim.webp")}
          />
          <Stack className="gap-0">
            <Text className="text-sm text-white">
              {regionalOrCountry(item?.plan?.country?.name) === "Country" ?
                t("common:country-esim") :
                t("common:regional-esim")
              }
            </Text>
            <Title tt="capitalize" order={6}>
              <Trans i18nKey={`countries:${item.plan?.country?.name}`}>
                {/* @ts-ignore */}
                {countryAlias(item?.plan?.country?.name || item?.plan?.country || "")}
              </Trans>
            </Title>
          </Stack>
        </Group>
      </Accordion.Control>
      <Accordion.Panel>
        <EsimCard
          isExpired={props.isExpired}
          status={item.status}
          order={item} {...props.esimCardProps}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};

export default EsimCardDetail;
