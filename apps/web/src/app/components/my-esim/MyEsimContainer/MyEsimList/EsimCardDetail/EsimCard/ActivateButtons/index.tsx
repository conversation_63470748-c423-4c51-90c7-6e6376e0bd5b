"use client";

/*
 * import modules and libraries
 */
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  Button,
  ButtonProps,
} from "@mantine/core";
/*
 * import helpers and constants
 */
import Storage from "@/utils/storage";
import { getCDNUrl } from "@/utils";

const Btn = (props: ButtonProps) => (
  <Button
    fullWidth
    classNames={{
      label: "text-black",
      root: "hover:bg-white",
    }}
    bg={"white"}
    {...props}
  >
    {props.children}
  </Button>
);
const ActivateButtons = (props: { orderdId: string }) => {
  const router = useRouter();

  const onBtnClick = (deviceType: string) => {
    Storage.getInstance().set("lastItem", props.orderdId);
    router.push(`/app/orders/${props.orderdId}/${deviceType}/activate`)
  }
  return (
    <>
      <Btn
        leftSection={
          <Image
            src={getCDNUrl("/assets/icon-ios.png")}
            width={16}
            height={16}
            alt="ios"
          />
        }
        //@ts-expect-error
        onClick={() => {
          onBtnClick("ios")
        }}
      >
        iOS
      </Btn>
      <Btn
        leftSection={
          <Image
            src={getCDNUrl("/assets/icon-android.png")}
            width={16}
            height={16}
            alt="android"
          />
        }
        //@ts-expect-error
        onClick={() => {
          onBtnClick("android")
        }}
      >
        Android
      </Btn>
    </>
  );
};
export default ActivateButtons;
