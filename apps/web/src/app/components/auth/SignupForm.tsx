"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { Flex, Checkbox, Button, Text, Stack, Divider } from "@mantine/core";
import { Trans, useTranslation } from "react-i18next";
import { useF<PERSON>, zodResolver } from "@mantine/form";
import { IconEye, IconEyeOff } from "@tabler/icons-react";
import { GoogleReCaptcha } from "react-google-recaptcha-v3";
/*
 * import components
 */
import InternalLink from "@/app/components/common/InternalLink";
import SocialLogin from "@/app/components/SocialLogin";
import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";
/*
 * import constants and utils
 */
import { z } from "@/i18n/en-zod";
import {
  ERR_GENERIC_REQUIRED,
  ERR_STRONG_PASSWORD,
  REGEX_STRONG_PASSWORD,
} from "@/app/constants";
import { useRegisterUser } from "@/store/UserStore";
import { useMessageStore } from "@/store/MessageStore";
/*
 * import api
 */
import { ApiService } from "@/api";

export const signupSchema = z.object({
  firstName: z.string().min(1, ERR_GENERIC_REQUIRED),
  lastName: z.string().min(1, ERR_GENERIC_REQUIRED),
  email: z.string().email(),

  password: z.string().regex(REGEX_STRONG_PASSWORD, ERR_STRONG_PASSWORD),
  termsOfService: z
    .boolean({
      invalid_type_error: "Please agree to the terms of service",
      required_error: "Please agree to the terms of service",
    })
    .refine((value) => value === true, {
      message: "Please agree to the terms of service",
    }),
});

export default function SignupForm() {
  const [recaptcha, setRecaptcha] = useState<string | null>("");
  const [captchaResetKey, setCaptchaResetKey] = useState(Date.now());
  const setRegisterEmail = useRegisterUser((s) => s.setEmail);
  const [referral, setReferral] = useState("");
  const [isShowPassword, setIsShowPassword] = useState(false);
  const router = useRouter();
  const { t, i18n } = useTranslation();
  const locale = i18n?.language;
  const [setGlobalMessage, setGlobalLoading, loading] = useMessageStore((s) => [
    s.setMessage,
    s.toggleLoading,
    s.isLoading,
  ]);

  const form = useForm({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      termsOfService: "",
    },
    validate: zodResolver(signupSchema),
  });

  const handleSubmit = useCallback(
    async (values: { email: string }) => {
      if (!recaptcha) {
        alert(`Please complete reCAPTCHA before proceeding`);
        return;
      }
      try {
        setGlobalLoading();
        const registerPayload = {
          ...values,
          recaptcha,
          locale: locale,
          source: "global-esim",
        };
        if (referral) {
          //@ts-ignore
          registerPayload.referral = referral;
        }
        await ApiService.register(registerPayload);
        router.push("/verify/process");
        setRegisterEmail(values.email);
      } catch (err: any) {
        setCaptchaResetKey(Date.now());
        let errorMessage =
          err?.response?.data?.message ||
          "エラーが発生しました。もう一度お試しください。";
        if (errorMessage.includes("malformed")) {
          errorMessage = "エラーが発生しました。もう一度お試しください。";
        } else if (errorMessage.includes("no longer valid")) {
          errorMessage =
            "セッションが期限切れです。ブラウザをリフレッシュしてもう一度お試しください。";
        }
        setGlobalMessage(errorMessage);
      } finally {
        setGlobalLoading();
      }
    },
    [referral, recaptcha]
  );

  const onVerify = useCallback((token: string) => {
    setRecaptcha(token);
  }, []);

  useEffect(() => {
    //@ts-ignore
    window?.rewardful?.("ready", () => {
      //@ts-ignore
      if (window.Rewardful.referral) {
        //@ts-ignore
        setReferral(window.Rewardful.referral);
      }
    });
  }, []);

  return (
    <>
      <GoogleReCaptcha onVerify={onVerify} refreshReCaptcha={captchaResetKey} />
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack>
          <Flex direction={"row"} justify={"space-between"}>
            <AppTextInput
              label={t("signup:label.name")}
              className={"w-[48%]"}
              withAsterisk
              placeholder={t("signup:formfield.lastname.placeholder") as string}
              {...form.getInputProps("lastName")}
            />
            <AppTextInput
              label={" "}
              className={"w-[48%]"}
              withAsterisk
              placeholder={
                t("signup:formfield.firstname.placeholder") as string
              }
              {...form.getInputProps("firstName")}
            />
          </Flex>
          <AppTextInput
            label={t("login:email.title")}
            withAsterisk
            placeholder={t("signup:formfield.email.placeholder") as string}
            {...form.getInputProps("email")}
          />
          <Text span>
            <Trans i18nKey="signup:email.warning">
              ※Please enter an address where you can receive emails from
              <Link
                className="no-underline text-[#C72B4D]"
                href={"mailto:<EMAIL>"}
                
              >
                <EMAIL>
              </Link>
            </Trans>
          </Text>
          <AppTextInput
            label={t("login:password.title")}
            withAsterisk
            placeholder={t("signup:formfield.password.placeholder") as string}
            type={isShowPassword ? "text" : "password"}
            rightSection={
              isShowPassword ? (
                <IconEyeOff onClick={() => setIsShowPassword(false)} />
              ) : (
                <IconEye onClick={() => setIsShowPassword(true)} />
              )
            }
            {...form.getInputProps("password")}
          />
          <Checkbox
            error={1}
            mt="md"
            label={
              <Trans i18nKey="signup:agreement.txt">
                I agree to Global Mobile&apos;s
                <InternalLink
                  underline
                  target="_blank"
                  href={"/terms-and-conditions"}
                >
                  Terms and Conditions
                </InternalLink>
                and
                <InternalLink
                  underline
                  target="_blank"
                  href={
                    locale === "jp"
                      ? "https://www.inbound-platform.com/privacy/"
                      : "https://www.inbound-platform.com/en/privacy/"
                  }
                >
                  Privacy Policy
                </InternalLink>
                and
                <InternalLink
                  underline
                  target="_blank"
                  href="/terms-for-membership"
                >
                  Terms for membership.
                </InternalLink>
              </Trans>
            }
            {...form.getInputProps("termsOfService", { type: "checkbox" })}
          />

          <Button
            className="rounded-[15px] text-2xl"
            loading={loading}
            size="md"
            w={"100%"}
            type="submit"
            color="app-dark"
          >
            {t("signup:btn.signup")}
          </Button>
          <Divider label="OR" />
          <SocialLogin />
        </Stack>
      </form>
    </>
  );
}
