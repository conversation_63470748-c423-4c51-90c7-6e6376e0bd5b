"use client";

/*
 * import modules and libraries
 */
import { useCallback, useState } from "react";
import { <PERSON><PERSON>, Button, Flex, Text, Stack, Divider } from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import { useTranslation } from "react-i18next";
import { IconEye, IconEyeOff } from "@tabler/icons-react";
/*
 * import components
 */
import AppTextInput from "../form/AppTextInput/AppTextInput";
import InternalLink from "../common/InternalLink";
/*
 * import constants and utils
 */
import { z } from "@/i18n/en-zod";
import { useMessageStore } from "@/store/MessageStore";
import useLogin from "@/hooks/useLogin";
import { ERR_STRONG_PASSWORD } from "@/app/constants";
import SocialLogin from "../SocialLogin";

const schema = z.object({
  password: z.string().min(8, ERR_STRONG_PASSWORD),
  email: z.string().email("The email address is invalid"),
});

export default function SigninForm() {
  const [setGlobalMessage, setGlobalLoading, loading] = useMessageStore((s) => [
    s.setMessage,
    s.toggleLoading,
    s.isLoading,
  ]);
  const { login } = useLogin();
  const { t } = useTranslation();
  const [isShowPassword, setIsShowPassword] = useState(false);

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },
    validate: zodResolver(schema),
  });

  const handleSubmit = useCallback(async (values: typeof form.values) => {
    try {
      setGlobalLoading(true);
      login(values);
    } catch (err: any) {
      setGlobalLoading(false);
      setGlobalMessage(err.response.data.message);
    }
  }, []);

  return (
    <>
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack>
          <AppTextInput
            label={t("login:email.title")}
            withAsterisk
            placeholder={t("login:email.title") as string}
            {...form.getInputProps("email")}
          />
          <AppTextInput
            className="w-full"
            label={t("login:password.title")}
            withAsterisk
            placeholder={t("login:password.title") as string}
            type={isShowPassword ? "text" : "password"}
            rightSection={
              isShowPassword ? (
                <IconEyeOff onClick={() => setIsShowPassword(false)} />
              ) : (
                <IconEye onClick={() => setIsShowPassword(true)} />
              )
            }
            {...form.getInputProps("password")}
          />
          <Flex justify={"space-between"} align={"end"}>
            <Checkbox
              size={"sm"}
              label={t("login:rememberme.txt")}
              {...form.getInputProps("rememberMe", { type: "checkbox" })}
            />

            <InternalLink underline href={"/account/forgot-password"}>
              <Text size={"sm"} c="app-dark">
                {t("login:forgotyourpassword")}
              </Text>
            </InternalLink>
          </Flex>

          <Button
            className="text-2xl mt-4"
            loading={loading}
            size="md"
            radius="md"
            type="submit"
            color="app-dark"
          >
            {t("common:btn.login")}
          </Button>
          <Divider label="OR" />
          <SocialLogin />
        </Stack>
      </form>
    </>
  );
}
