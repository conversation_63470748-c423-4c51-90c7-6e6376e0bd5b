"use client";

/*
 * import modules and libraries
 */
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@mantine/core";
import { usePathname, useRouter } from "next/navigation";
/*
 * import components
 */
import AppTabs from "@/app/components/common/AppTabs/AppTabs";
import AlertMessage from "../common/AlertMessage";
/*
 * import constants and utils
 */
import { useMessageStore } from "@/store/MessageStore";

const AuthLayout = (props: { children: React.ReactNode }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const paths = pathname?.split("/") || [];
  const path = paths[paths.length - 1] as string | null;
  const [activeTab, setActiveTab] = useState<string | null>(path);
  const [setMessage, toggleLoading] = useMessageStore((s) => [
    s.setMessage,
    s.toggleLoading,
  ]);

  const headers = [
    {
      name: t("login:link.signup"),
      code: "signup",
    },
    {
      name: t("login:btn.login"),
      code: "signin",
    },
  ];

  useEffect(() => {
    toggleLoading(false);
    return () => {
      setMessage("");
    };
  }, []);

  return (
    <>
      <AppTabs
        activeTab={activeTab}
        defaultValue={activeTab}
        headers={headers}
        onTabChange={(tab: string) => {
          setActiveTab(tab);
          router.push(`/auth/${tab}`);
        }}
      />
      <Container size={700} className="py-12">
        <AlertMessage />
        {props.children}
      </Container>
    </>
  );
};
export default AuthLayout;
