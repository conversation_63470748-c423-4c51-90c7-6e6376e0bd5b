import { Box, Center, Flex, Image, Text } from "@mantine/core";
import classes from "./socialIcons.module.css";

const bgColors = {
  facebook: "#1C72E2",
  google: "#FFFFFF",
};
interface IProps {
  icon: "google" | "facebook";
}
const ESIM_API_PATH = process.env.NEXT_PUBLIC_API_HOST;

const google_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Google`;
const fb_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Facebook`;

const appendRedirectUri = (url: string) => {
  if (typeof window === "undefined") return url;
  let redirectUri = "";
  redirectUri =
    window.location.protocol +
    "//" +
    window.location.host +
    (process.env.NEXT_PUBLIC_BASE_PATH || "") +
    "/social/login/callback";

  if (redirectUri) {
    return url + "&redirectUri=" + redirectUri;
  }
  return url;
};

const SocialButton = (props: IProps) => {
  return (
    <Center className={classes.button} bg={bgColors[props.icon]}>
      <Image src={`/assets/${props.icon}.png`} w={20} h={20} alt="" />
      <Text
        fw={"bold"}
        ml={"xs"}
        color={props.icon === "facebook" ? "white" : "black"}
      >
        {props.icon === "google" ? "Google" : "Facebook"}{" "}
      </Text>
    </Center>
  );
};

const SocialLogin = () => {
  return (
    <Flex justify={"center"} gap={10}>
      <a href={appendRedirectUri(fb_url)} className="no-underline">
        <SocialButton icon="facebook" />
      </a>
      <a href={appendRedirectUri(google_url)} className="no-underline">
        <SocialButton icon="google" />
      </a>
    </Flex>
  );
};
export default SocialLogin;
