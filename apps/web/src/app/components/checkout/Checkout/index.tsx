"use client";

import dynamic from "next/dynamic";
import Image from "next/image";
import { useParams, useSearchParams } from "next/navigation";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { Box, Button, Center, Skeleton, Stack, Title } from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import { useInterval } from "@mantine/hooks";

import { Elements, PaymentRequestButtonElement } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import isEmpty from "lodash/isEmpty";
import { GoogleReCaptcha } from "react-google-recaptcha-v3";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

import CouponContainer from "@repo/ui/src/CouponContainer";
import SectionContent from "@repo/ui/src/common/SectionContent";

import PaymentCheckoutBox from "@/app/components/checkout/PaymentCheckoutBox";
import StickyContainer from "@/app/components/common/StickyContainer";

import { getCDNUrl } from "@/utils";
import { Currency } from "@/utils/currency";
import Storage from "@/utils/storage";

import { ApiService } from "@/api";

import { ICouponDiscounted } from "@/interfaces/ICouponDiscounted";
import { IPlan } from "@/interfaces/IPlan";
import { IQuotation } from "@/interfaces/IQuotation";

import { ERR_GENERIC_REQUIRED, PURCHASE_STEPS } from "@/app/constants";
import { z } from "@/i18n/en-zod";
import { useMessageStore } from "@/store/MessageStore";
import { useProfile } from "@/store/UserStore";

import OrderDetailsNew from "../../OrderDetailsNew";

const DynamicPurchaseFlowStepper = dynamic(
  () => import("@repo/ui/src/PurchaseFlowStepper")
);
const DynamicUserInfo = dynamic(
  () => import("@/app/components/checkout/UserInfo")
);

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string
);

const schema = z.object({
  email: z.string().email(),
  firstName: z
    .string()
    .min(1, ERR_GENERIC_REQUIRED)
    .regex(/^[a-zA-Z. ]*$/, "Please write in English letters"),
  lastName: z
    .string()
    .min(1, ERR_GENERIC_REQUIRED)
    .regex(/^[a-zA-Z. ]*$/, "Please write in English letters"),
});

interface IProps {
  discount?: ICouponDiscounted | null;
  renderCheckoutButton?: () => JSX.Element;
  isMobile?: boolean;
  quotation?: IQuotation;
  source: "single" | "multiple";
  plan?: IPlan;
  onCouponChange?: (coupon?: string | null) => void;
  renderBasketCheckout?: (props: IProps) => JSX.Element;
}

const currentStep = 1;

const Checkout = React.memo(({ ...props }: IProps) => {
  const { planId } = useParams() || {};
  const { profile } = useProfile();
  const formRef = useRef<HTMLFormElement>(null);
  const [discount, setDiscount] = useState<ICouponDiscounted | null>(null);
  const [recaptcha, setRecaptcha] = useState<string | null>("");
  const [captchaResetKey, setCaptchaResetKey] = useState(Date.now());
  const [paymentMethod, setPaymentMethod] = useState("credit");
  const [paymentRequest, setPaymentRequest] = useState();
  const [isErrorOnAgreement, setIsErrorOnAgreement] = useState(false);
  const [isCheckoutDisabled, setIsCheckoutDisabled] = useState({
    number: false,
    expiry: false,
    cvc: false,
  });
  const toggleLoading = useMessageStore((state) => state.toggleLoading);
  const isLoading = useMessageStore((state) => state.isLoading);
  const searchParams = useSearchParams();
  const affiliate =
    Storage.getInstance("cookie").getFromCookie("affiliate") || undefined;

  const isLGU = useMemo(
    () =>
      props.plan?.country?.name.toLowerCase() === "korea" ||
      props.quotation?.orders?.some(
        (order) => order?.plan?.country.name?.toLowerCase?.() === "korea"
      ),
    [props.plan, props.quotation]
  );
  const isNeedRegistration = useMemo(
    () =>
      ["taiwan", "hongkong"].includes(props.plan?.country?.name as string) ||
      props.quotation?.orders?.some((order) =>
        ["taiwan", "hongkong"].includes(order?.plan?.country.name)
      ),
    [props.plan, props.quotation]
  );

  const form = useForm({
    initialValues: {
      email: "",
      firstName: "",
      lastName: "",
    },
    validate: zodResolver(schema),
    validateInputOnBlur: true,
  });

  const [isCompatibilityChecked, setIsCompatibilityChecked] =
    useState<boolean>(false);
  const [isTermsChecked, setIsTermsChecked] = useState<boolean>(false);
  const [isUserInfoValid, setIsUserInfoValid] = useState<boolean>(
    profile ? true : false
  );
  const [isCardDataValid, setIsCardDataValid] = useState<boolean>(
    Object.values(isCheckoutDisabled).every((value) => value === true)
      ? true
      : false
  );
  const [isNeedRegistrationChecked, setIsNeedRegistrationChecked] =
    useState<boolean>(false);
  const { t, i18n } = useTranslation();
  const handleInvoiceCheckout = useCallback(async () => {
    if (!profile?.corporate) return;
    const response = await ApiService.corporateSubscribe(
      profile?.corporate?.code,
      profile?.corporate?.id + "",
      {
        locale: i18n.language || "en",
        products: {
          planId,
          qty: 1,
        },
      }
    );
    window.location.href =
      window.location.protocol +
      "//" +
      window.location.host +
      `/checkout/${response.data.data.orderId}/complete`;
  }, [profile]);

  const handlePayPayPayment = useCallback(async () => {
    if (!isCompatibilityChecked || !isTermsChecked || !isUserInfoValid) return;
    toggleLoading(true);

    let result: any;
    const orderPayload = {
      recaptha: recaptcha || "",
      firstName: form?.values?.firstName,
      lastName: form?.values?.lastName,
      email: form?.values?.email,
      locale: i18n.language || "jp",
      currency:
        i18n.language === "jp" ? "JPY" : Currency.getSelectedCurrency()?.code,
      couponId: props.quotation?.coupon?.code,
      products: props.quotation?.orders
        ? props.quotation.orders.map((order: any) => ({
            optionId: order.plan.id.toString(),
            insurance: order.insured ? "insured" : undefined,
          }))
        : [],
      source: "global-esim",
      affiliate,
      paymentMethod: "paypay",
    };

    const postOrderApiResponse = profile
      ? await ApiService.postOrder(orderPayload)
      : await ApiService.postOrderAsGuest(orderPayload);

    const orderResult = postOrderApiResponse.data?.data;

    const validLocales = ["vi", "kr", "es", "fr", "ph"];
    const firstPathSegment = window.location.pathname.split("/")[1];

    const localePath =
      firstPathSegment && validLocales.includes(firstPathSegment)
        ? `/${firstPathSegment}`
        : "";

    const paypayPayload = {
      orderId: orderResult.orderId,
      amount: orderResult.payment.amount,
      customerId: profile?.id || orderResult.userId,
      customerEmail: orderPayload.email || profile?.email,
      customerName:
        constructFullName(orderPayload.firstName, orderPayload.lastName) ||
        constructFullName(profile?.firstName, profile?.lastName),
      planIds:
        orderPayload.products.length > 0
          ? orderPayload.products.map((product) => product.optionId)
          : [],
      source: "global-esim",
      requestOriginUrl: `${window.location.protocol}//${window.location.host}${localePath}`,
    };

    try {
      const paymentResult = profile
        ? await ApiService.initiatePayPayPayment(paypayPayload)
        : await ApiService.initiatePayPayPaymentAsGuest(paypayPayload);

      result = paymentResult.data?.data;

      if (result?.action && result?.fields) {
        // Create and submit the form to redirect to the SBPS payment page
        const form = document.createElement("form");
        form.method = result.method || "POST";
        form.action = result.action; // The SBPS payment URL (e.g., https://stbfep.sps-system.com/xxxx/xxxxxxxx)
        form.acceptCharset = "shift_jis";

        Object.keys(result.fields).forEach((key) => {
          const input = document.createElement("input");
          input.type = "hidden";
          input.name = key;
          input.value = result.fields[key];
          form.appendChild(input);
        });

        document.body.appendChild(form);

        form.submit();
      }
    } catch (err: any) {
      alert(err);
      console.log(err);
      toggleLoading(false);
      return;
    }
  }, [
    isCompatibilityChecked,
    isTermsChecked,
    isUserInfoValid,
    recaptcha,
    form?.values,
    i18n.language,
    props.quotation,
    profile,
    toggleLoading,
  ]);

  const constructFullName = (first?: string, last?: string): string => {
    return [first, last].filter(Boolean).join(" ");
  };

  const StickyPaymentButton = () => (
    <>
      {paymentMethod === "invoice" && (
        <Box className="sticky bottom-0 w-full bg-white py-2">
          <Button
            loading={isLoading}
            size="lg"
            onClick={handleInvoiceCheckout}
            disabled={
              !isCompatibilityChecked || !isTermsChecked || !isUserInfoValid
            }
            fullWidth
          >
            {t("payment:pay-nows", "Buy")}
          </Button>
        </Box>
      )}
      {paymentMethod === "credit" && (
        <Box className="sticky bottom-0 w-full bg-white py-2">
          <Button
            loading={isLoading}
            size="lg"
            onClick={handleCheckout}
            disabled={
              !recaptcha?.length ||
              !isCompatibilityChecked ||
              !isTermsChecked ||
              !isUserInfoValid ||
              !isCardDataValid ||
              (isNeedRegistration && !isNeedRegistrationChecked)
            }
            fullWidth
            color="app-action.4"
            classNames={{
              label: "text-black",
            }}
          >
            {t("payment:pay-now")}
          </Button>
        </Box>
      )}
      {paymentMethod === "paypay" && (
        <Box className="sticky bottom-0 w-full bg-white py-2">
          <Button
            loading={isLoading}
            size="lg"
            onClick={handlePayPayPayment}
            disabled={
              !isCompatibilityChecked || !isTermsChecked || !isUserInfoValid
            }
            fullWidth
            color="app-action.4"
            classNames={{ label: "text-black" }}
          >
            {t("payment:pay-now")}
          </Button>
        </Box>
      )}
      {paymentRequest &&
        !["credit", "invoice", "paypay"].includes(paymentMethod) && (
          <StickyContainer>
            <PaymentRequestButtonElement
              onClick={(e) => {
                if (
                  !isCompatibilityChecked ||
                  (isNeedRegistration && !isNeedRegistrationChecked)
                ) {
                  setIsErrorOnAgreement(true);
                  e.preventDefault();
                  return;
                }
                sessionStorage.setItem(
                  "isNeedRegistrationChecked",
                  isNeedRegistrationChecked.toString()
                );
                setIsErrorOnAgreement(false);
              }}
              options={{
                paymentRequest,
                style: {
                  paymentRequestButton: {
                    height: "50px", // same as "Pay Now" button
                  },
                },
              }}
            />
          </StickyContainer>
        )}
    </>
  );
  const interval = useInterval(() => {
    setCaptchaResetKey(Date.now());
    return interval.stop;
  }, 30000);

  const handleCheckout = useCallback(() => {
    if (!recaptcha) {
      alert(`Please complete reCAPTCHA before proceeding`);
      return;
    }

    formRef.current?.dispatchEvent(
      new Event("submit", {
        bubbles: true,
        cancelable: true,
      })
    );
  }, [formRef, recaptcha]);

  const onVerify = useCallback((token: string) => {
    setRecaptcha(token);
  }, []);

  const { data: setUpIntentQuery, isLoading: isSetupIntentLoading } = useQuery(
    "setupIntent",
    () => {
      return ApiService.getClientSecret({
        planId,
        source: "global-esim",
        userPool: "GLOBAL",
      });
    }
  );

  useEffect(() => {
    toggleLoading(isSetupIntentLoading);
  }, [isSetupIntentLoading]);

  useEffect(() => {
    if (
      profile ||
      (isEmpty(form.errors) &&
        form.values.email &&
        form.values.firstName &&
        form.values.lastName)
    ) {
      setIsUserInfoValid(true);
    } else {
      setIsUserInfoValid(false);
    }
  }, [form, profile]);

  useEffect(() => {
    setIsCardDataValid(
      Object.values(isCheckoutDisabled).every((value) => value === true)
        ? true
        : false
    );
  }, [isCheckoutDisabled]);

  const finalPriceJPY = useMemo(() => {
    return props.quotation?.xe["JPY"] as number;
  }, [props.quotation]);

  const couponFromLocalStorage = useMemo(() => {
    return Storage.getInstance().get("coupon");
  }, []);

  const handleCouponApplied = useCallback((coupon: string | null) => {
    props?.onCouponChange?.(coupon);
  }, []);

  return (
    <>
      <Center>
        <GoogleReCaptcha
          onVerify={onVerify}
          refreshReCaptcha={captchaResetKey}
        />
      </Center>

      {setUpIntentQuery?.data?.data && (
        <Elements
          stripe={stripePromise}
          options={{
            clientSecret: setUpIntentQuery?.data?.data.client_secret,
            appearance: {
              theme: "flat",
              rules: {
                ".Input": {
                  padding: "14px",
                  border: "1px solid #DDDDDD",
                  borderRadius: "4px",
                  backgroundColor: "white",
                },
                ".Input::placeholder": {
                  color: "#AAAAAA",
                  fontSize: "14px",
                  lineHeight: "21px",
                },
                ".Label": {
                  color: "#555555",
                  fontSize: "12px",
                  lineHeight: "18px",
                },
              },
            },
          }}
        >
          <DynamicPurchaseFlowStepper
            currentStep={currentStep}
            steps={PURCHASE_STEPS}
          />
          <div className="mb-[10px]"></div>
          <SectionContent small noHeader isWhiteBg noFooter>
            <Stack className="gap-6 pt-6">
              {props.quotation ? (
                <OrderDetailsNew
                  source={props.source}
                  discount={discount}
                  isMobile={false}
                  quotation={props.quotation}
                  renderCheckoutButton={() => <></>}
                />
              ) : (
                <Stack className="pt-6">
                  <Skeleton height={25} className="shadow-xs rounded-md" />
                  <Skeleton height={200} className="shadow-xs rounded-md" />
                </Stack>
              )}
              {!profile && (
                <React.Suspense>
                  <DynamicUserInfo form={form} />
                </React.Suspense>
              )}
              {props.quotation ? (
                <CouponContainer
                  defaultCouponCode={
                    (searchParams?.get("coupon") ||
                      couponFromLocalStorage) as string
                  }
                  defaultCoupon={props.quotation?.coupon}
                  onValidCoupon={handleCouponApplied}
                  planId={searchParams?.get("planId") + ""}
                />
              ) : (
                <Stack className="pt-6">
                  <Skeleton height={25} className="shadow-xs rounded-md" />
                  <Skeleton height={200} className="shadow-xs rounded-md" />
                </Stack>
              )}
              <Stack className="gap-4" mt={"-30px"}>
                {(props.plan || props.quotation) && (
                  <>
                    <Title order={4}>{t("payment:method.title")}</Title>
                    <Box>
                      <Image
                        height={23}
                        width={220}
                        src={getCDNUrl("/assets/payment.png")}
                        alt="payment image."
                      />
                    </Box>
                  </>
                )}
                <Box>
                  {(props.plan || props.quotation) && (
                    <>
                      <PaymentCheckoutBox
                        //@ts-expect-error
                        quotation={props.quotation}
                        isAgreementError={isErrorOnAgreement}
                        clientSecret={
                          setUpIntentQuery?.data?.data.client_secret
                        }
                        setPaymentMethod={setPaymentMethod}
                        setPaymentRequest={setPaymentRequest}
                        price={finalPriceJPY + ""}
                        planId={planId as string}
                        ref={formRef}
                        // TODO: should move this to state management or refactor checkout components
                        isCompatibilityChecked={isCompatibilityChecked}
                        setIsCompatibilityChecked={setIsCompatibilityChecked}
                        isTermsChecked={isTermsChecked}
                        setIsTermsChecked={setIsTermsChecked}
                        isCheckoutDisabled={isCheckoutDisabled}
                        setIsCheckoutDisabled={setIsCheckoutDisabled}
                        isNeedRegistrationChecked={isNeedRegistrationChecked}
                        setIsNeedRegistrationChecked={
                          setIsNeedRegistrationChecked
                        }
                        //
                        isLGU={isLGU}
                        form={form}
                        isNeedRegistration={isNeedRegistration}
                        recaptcha={recaptcha ? recaptcha : undefined}
                        setCaptchaResetKey={setCaptchaResetKey}
                        toggleLoading={toggleLoading}
                      ></PaymentCheckoutBox>
                    </>
                  )}
                </Box>

                {(props.plan || props.quotation) && <StickyPaymentButton />}
              </Stack>
            </Stack>
          </SectionContent>
        </Elements>
      )}
    </>
  );
});
export default Checkout;
