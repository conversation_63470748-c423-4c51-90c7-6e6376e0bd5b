"use client";

/*
 * import modules and libraries
 */
import { Dispatch, ForwardedRef, forwardRef, SetStateAction } from "react";
/*
 * import components
 */
import CheckoutForm from "@/app/components/checkout/CheckoutForm";
import { IQuotation } from "@/interfaces/IQuotation";
import { IPlan } from "@/interfaces/IPlan";

const PaymentCheckoutBox = forwardRef(
  (
    {
      planId,
      plan,
      price,
      isLGU,
      isNeedRegistration,
      isCompatibilityChecked,
      setIsCompatibilityChecked,
      isTermsChecked,
      setIsTermsChecked,
      isNeedRegistrationChecked,
      setIsNeedRegistrationChecked,
      isCheckoutDisabled,
      setIsCheckoutDisabled,
      recaptcha,
      setCaptchaResetKey,
      form,
      quotation,
      ...props
    }: {
      quotation: IQuotation;
      isAgreementError: boolean;
      clientSecret: string;
      price: string;
      planId: string;
      plan: IPlan | undefined;
      setPaymentMethod: (paymentMethod: string) => void;
      setPaymentRequest: (pr: any) => void;
      isCompatibilityChecked: boolean;
      setIsCompatibilityChecked: Dispatch<SetStateAction<boolean>>;
      isTermsChecked: boolean;
      setIsTermsChecked: Dispatch<SetStateAction<boolean>>;
      isNeedRegistrationChecked: boolean;
      setIsNeedRegistrationChecked: Dispatch<SetStateAction<boolean>>;
      isLGU?: boolean;
      form?: any;
      isNeedRegistration?: boolean;
      isCheckoutDisabled: { [key: string]: boolean };
      setIsCheckoutDisabled: Dispatch<
        SetStateAction<{ number: boolean; expiry: boolean; cvc: boolean }>
      >;
      recaptcha?: string;
      setCaptchaResetKey: (key: number) => void;
    },
    ref: ForwardedRef<HTMLFormElement>
  ) => {
    return (
      <CheckoutForm
        quotation={quotation}
        isAgreementError={props.isAgreementError}
        setPaymentMethod={props.setPaymentMethod}
        setPaymentRequest={props.setPaymentRequest}
        price={+price}
        planId={planId}
        plan={plan}
        clientSecret={props.clientSecret}
        ref={ref}
        // TODO: should move this to state management or refactor checkout components
        isCompatibilityChecked={isCompatibilityChecked}
        setIsCompatibilityChecked={setIsCompatibilityChecked}
        isTermsChecked={isTermsChecked}
        setIsTermsChecked={setIsTermsChecked}
        isNeedRegistrationChecked={isNeedRegistrationChecked}
        setIsNeedRegistrationChecked={setIsNeedRegistrationChecked}
        isCheckoutDisabled={isCheckoutDisabled}
        setIsCheckoutDisabled={setIsCheckoutDisabled}
        isLGU={isLGU}
        form={form}
        isNeedRegistration={isNeedRegistration}
        recaptcha={recaptcha ? recaptcha : undefined}
        setCaptchaResetKey={setCaptchaResetKey}
      />
    );
  }
);

PaymentCheckoutBox.displayName = "PaymentCheckoutBox";

export default PaymentCheckoutBox;
