"use client";

import { useCallback } from "react";

import { Box, Group, Stack, Text, Title } from "@mantine/core";

import { useTranslation } from "react-i18next";

import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";

const UserInfo = ({ form }: { form: any }) => {
  const { t } = useTranslation();

  const handleSubmit = useCallback(
    async (values: typeof form.values) => {
      try {
      } catch (err: any) {}
    },
    [form]
  );

  return (
    <form onSubmit={form.onSubmit(handleSubmit)}>
      <Stack className="gap-4">
        <Box>
          <Title order={4}>{t("payment:user.information.title")}</Title>
        </Box>
        <Stack className="gap-4 border border-solid border-[#dee2e6] bg-[#f8f9fa] p-4">
          <AppTextInput
            withAsterisk
            label={t("login:email.title")}
            placeholder={t("signup:formfield.email.placeholder") as string}
            className="flex-1"
            {...form.getInputProps("email")}
          />
          <Box className="mb-[10px]">
            <Text className="mb-[10px] text-sm">
              {t("signup:label.name")}{" "}
              <Text component="span" c="red.6" className="text-sm">
                *
              </Text>
            </Text>
            <Group className="items-start">
              <AppTextInput
                withAsterisk
                placeholder={`YAMADA（${t("signup:formfield.lastname.placeholder")}）`}
                className="flex-1"
                {...form.getInputProps("lastName")}
              />
              <AppTextInput
                withAsterisk
                placeholder={`HANAKO（${t("signup:formfield.firstname.placeholder")}）`}
                className="flex-1"
                {...form.getInputProps("firstName")}
              />
            </Group>
          </Box>
        </Stack>
      </Stack>
    </form>
  );
};

export default UserInfo;
