"use client";

import Image from "next/image";
import { useRouter, useSearch<PERSON>arams } from "next/navigation";
import {
  ComponentPropsWithoutRef,
  Dispatch,
  ForwardedRef,
  SetStateAction,
  forwardRef,
  useCallback,
  useEffect,
  useState,
} from "react";

import {
  Accordion,
  Alert,
  Avatar,
  Checkbox,
  Group,
  Radio,
  Stack,
  Text,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";

import { useElements, useStripe } from "@stripe/react-stripe-js";
import {
  IconCreditCard,
  IconInfoCircle,
  IconInvoice,
} from "@tabler/icons-react";
import { startCase } from "lodash";
import { Trans, useTranslation } from "react-i18next";

import { getCDNUrl, simpleStringEncode } from "@/utils";
import Storage from "@/utils/storage";
import { Currency } from "@/utils/currency";

import { ApiService } from "@/api";

import { IPlan } from "@/interfaces/IPlan";
import { IPostOrderPayload } from "@/interfaces/IPostOrderPayload";
import { IQuotation } from "@/interfaces/IQuotation";

import { useCheckoutEmail, useOrderStore } from "@/store";
import { useMessageStore } from "@/store/MessageStore";
import { useProfile } from "@/store/UserStore";

import InternalLink from "../../common/InternalLink";
import CreditCardCheckoutForm from "../CreditCardCheckoutForm";
import styles from "./checkoutForm.module.css";

interface ItemProps extends ComponentPropsWithoutRef<"div"> {
  image: string;
  label: string;
  description: string;
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(
  ({ image, label, description, ...others }: ItemProps, ref) => (
    <div ref={ref} {...others}>
      <Group wrap="nowrap">
        <Avatar src={image} size={"xs"} />
        <div>
          <Text size="sm">{label}</Text>
        </div>
      </Group>
    </div>
  )
);

SelectItem.displayName = "SelectItem";

const CheckoutForm = forwardRef(
  (
    {
      isAgreementError,
      planId,
      plan,
      clientSecret,
      price,
      isLGU,
      form,
      isNeedRegistration,
      isCompatibilityChecked,
      setIsCompatibilityChecked,
      isTermsChecked,
      setIsTermsChecked,
      isNeedRegistrationChecked,
      setIsNeedRegistrationChecked,
      isCheckoutDisabled,
      setIsCheckoutDisabled,
      recaptcha,
      setCaptchaResetKey,
      quotation,
      ...props
    }: {
      quotation: IQuotation;
      planId: string;
      plan: IPlan | undefined;
      clientSecret: string;
      price: number;
      isAgreementError: boolean;
      setPaymentRequest: (pr: any) => void;
      setPaymentMethod: (paymentMethod: string) => void;
      isCompatibilityChecked: boolean;
      setIsCompatibilityChecked: Dispatch<SetStateAction<boolean>>;
      isTermsChecked: boolean;
      setIsTermsChecked: Dispatch<SetStateAction<boolean>>;
      isNeedRegistrationChecked: boolean;
      setIsNeedRegistrationChecked: Dispatch<SetStateAction<boolean>>;
      isCheckoutDisabled: { [key: string]: boolean };
      setIsCheckoutDisabled: Dispatch<
        SetStateAction<{ number: boolean; expiry: boolean; cvc: boolean }>
      >;
      isLGU?: boolean;
      form?: any;
      isNeedRegistration?: boolean;
      recaptcha?: string;
      setCaptchaResetKey: (key: number) => void;
    },
    ref: ForwardedRef<HTMLFormElement>
  ) => {
    const router = useRouter();
    const { t, i18n } = useTranslation();
    const setEmail = useCheckoutEmail((state) => state.setEmail);
    const stripe = useStripe();
    const elements = useElements();
    let buttonList = ["credit", "paypay"];

    const toggleCompatibilityModalOpen: () => void = useOrderStore(
      (state) => state.toggleCompatibilityModalOpen
    );
    const { profile } = useProfile();
    const toggleLoading = useMessageStore((state) => state.toggleLoading);
    const [activePaymentMethod, setActivePaymentMethod] = useState("credit");
    const [paymentRequest, setPaymentRequest] = useState<any>(null);
    const [canApplePay, setCanApplePay] = useState(false);
    const [canGooglePay, setCanGooglePay] = useState(false);
    const [cardErrMessage, setCardErrMessage] = useState<string | null>(null);
    const [checkoutAgreeError, setCheckoutAgreeError] = useState<string | null>(
      null
    );

    const affiliate =
      Storage.getInstance("cookie").getFromCookie("affiliate") || undefined;

    const handleCreditCardPaymentMethod = async (
      ev: any,
      onSuccess?: () => void,
      onError?: () => void,
      isWallet = false
    ) => {
      setCardErrMessage("");
      if (!isCompatibilityChecked) return;
      if (!isTermsChecked) return;
      if (!stripe || !elements) return;
      const [firstName, middleName, ...lastName] =
        ev.payerName?.split?.(" ") || [];
      const email =
        (form?.values?.email ? form?.values?.email : undefined) ||
        ev.payerEmail;

      setEmail(email);
      toggleLoading();

      let result;
      const payload = {
        recaptha: recaptcha,
        firstName: form?.values?.firstName || firstName,
        lastName: form?.values?.lastName || middleName,
        email: email,
        locale: i18n.language || "en",
        currency: Currency.getSelectedCurrency()?.code,
        couponId: quotation?.coupon?.code,
        // couponReferenceId: props.coupon?.referenceId,
        products: quotation?.orders.map((item) => ({
          optionId: item.plan.id + "",
          insurance: item.insured ? "insured" : undefined,
        })),
        affiliate,
        source: "global-esim",
      } as IPostOrderPayload;

      try {
        const response = profile
          ? await ApiService.postOrder(payload)
          : await ApiService.postOrderAsGuest({
              ...payload,
            });
        result = response.data?.data;
      } catch (err: any) {
        setCardErrMessage(
          startCase(err.response?.data?.message[0]) ||
            "An error occurred. Please try again."
        );
        setCaptchaResetKey(Date.now());
        toggleLoading(false);
      }

      const { error, paymentIntent } = await stripe.confirmCardPayment(
        result.payment.client_secret,
        {
          payment_method: isWallet
            ? ev.paymentMethod.id
            : { card: elements?.getElement("cardNumber") },
        },
        isWallet ? { handleActions: false } : {}
      );

      if (error) {
        // This point will only be reached if there is an immediate error when
        // confirming the payment. Show error to your customer (for example, payment
        // details incomplete)
        setCaptchaResetKey(Date.now());
        onError?.();
        setCardErrMessage(
          error?.message || "An error occurred. Please try again."
        );
        toggleLoading(false);
      } else {
        if (paymentIntent.status === "succeeded") {
          onSuccess?.();
          sessionStorage.setItem(
            "isNeedRegistrationChecked",
            isNeedRegistrationChecked.toString()
          );
          router.replace(
            window.location.protocol +
              "//" +
              window.location.host +
              `/checkout/${result.orderId}/complete?transaction=${simpleStringEncode(
                Date.now() + ""
              )}&event=complete&uid=${simpleStringEncode(
                result.instantEsimSecret
              )}&planId=${planId || "none"}&country=${plan?.country?.name}`
          );
        } else {
          toggleLoading(false);
        }
      }
    };

    const AdditionalPaymentOption = (props: {
      payment: "apple" | "google";
    }) => (
      <Accordion.Item value={props.payment}>
        <Accordion.Control>
          <Group align="center">
            <Radio
              readOnly
              color={"app-pink.4"}
              checked={isActivePaymentMethod(props.payment)}
            />
            <Group gap={5} align="center">
              <Image
                src={getCDNUrl(
                  `/assets/payments/${props.payment}_pay_logo.png`
                )}
                width={35}
                height={35}
                alt={`${props.payment} Pay Logo`}
              />
              <Text tt="capitalize" fw={600}>
                {props.payment} Pay
              </Text>
            </Group>
          </Group>
        </Accordion.Control>
      </Accordion.Item>
    );

    const PayPayPaymentOption = () => (
      <Accordion.Item value="paypay">
        <Accordion.Control>
          <Group align="center">
            <Radio
              readOnly
              color="app-pink.4"
              checked={isActivePaymentMethod("paypay")}
            />
            <Group gap={5} align="center">
              <Image
                src={getCDNUrl("/assets/paypay_logo.png")}
                width={35}
                height={35}
                alt="PayPay Logo"
              />
              <Text fw={600}>PayPay</Text>
            </Group>
          </Group>
        </Accordion.Control>
      </Accordion.Item>
    );

    const AgreementCheckboxes = () => {
      return (
        <Stack className="mt-4 gap-4">
          <Checkbox
            error={checkoutAgreeError || isAgreementError}
            onChange={(event) =>
              setIsCompatibilityChecked(event.currentTarget.checked)
            }
            checked={isCompatibilityChecked}
            className="items-center justify-center"
            label={
              <Text c={"#555555"}>
                {t(
                  "region:checkoutagreement",
                  "Before completing this order, please confirm your device is eSIM compatible and network-unlocked."
                )}
                <button
                  className="text-[#0000ee] underline decoration-black"
                  onClick={toggleCompatibilityModalOpen}
                >
                  {t("region:checkcompatibility", "Check Compatibility")}
                </button>
              </Text>
            }
          />
          {isNeedRegistration && (
            <Checkbox
              onChange={(event) =>
                setIsNeedRegistrationChecked(event.currentTarget.checked)
              }
              checked={isNeedRegistrationChecked}
              className="items-center justify-center"
              label={
                <Text
                  c={"#555555"}
                  dangerouslySetInnerHTML={{
                    __html: t("payment:hongkongtaiwannote"),
                  }}
                ></Text>
              }
            />
          )}
          <Checkbox
            error={isAgreementError}
            checked={isTermsChecked}
            onChange={(event) => setIsTermsChecked(event.currentTarget.checked)}
            label={
              <Text c={"#555555"}>
                <Trans i18nKey="signup:agreement.txt">
                  I agree to エアトリ&apos;s
                  <InternalLink
                    underline
                    target="_blank"
                    defaultColor
                    href={
                      i18n?.language === "jp"
                        ? "https://www.gmobile.biz/terms-of-service/?value=esim"
                        : "/terms-and-conditions"
                    }
                  >
                    Terms and Conditions
                  </InternalLink>{" "}
                  <InternalLink
                    underline
                    target="_blank"
                    defaultColor
                    href={
                      i18n?.language === "jp"
                        ? "https://www.inbound-platform.com/privacy/"
                        : "https://www.inbound-platform.com/en/privacy/"
                    }
                  >
                    Privacy Policy{" "}
                  </InternalLink>
                  <InternalLink
                    underline
                    target="_blank"
                    href="/terms-for-membership"
                  >
                    Terms for membership.
                  </InternalLink>
                </Trans>
              </Text>
            }
          />
        </Stack>
      );
    };

    const isActivePaymentMethod = (
      paymentMethodType: "credit" | "apple" | "google" | "invoice" | "paypay"
    ) => {
      return activePaymentMethod === paymentMethodType;
    };

    useEffect(() => {
      setCheckoutAgreeError(null);
    }, [isCompatibilityChecked]);

    useEffect(() => {
      if (cardErrMessage) {
        showNotification({
          message: cardErrMessage,
          color: "red",
        });
      }
    }, [cardErrMessage]);

    const handlePaymentMethodClick = useCallback(
      (value: string | null) => {
        setActivePaymentMethod(value || "credit");
        props.setPaymentMethod(value || "credit");
      },
      [props, isCompatibilityChecked]
    );

    useEffect(() => {
      if (!paymentRequest) return;
      //@ts-ignore
      const handler = async (ev) => {
        handleCreditCardPaymentMethod(
          ev,
          () => {
            ev.complete("success");
          },
          () => {
            ev.complete("fail");
          },
          true
        );
      };

      paymentRequest.on("paymentmethod", handler);

      props.setPaymentRequest(paymentRequest);

      return () => {
        paymentRequest.off("paymentmethod", handler);
      };
    }, [paymentRequest, handleCreditCardPaymentMethod, recaptcha]);

    useEffect(() => {
      if (stripe) {
        if (!Number(price || 0)) {
          router.back();
          return;
        }
        const pr = stripe.paymentRequest({
          country: "JP",
          currency: "jpy",
          total: {
            label: "",
            amount: Number(price),
          },
          requestPayerName: true,
          requestPayerEmail: true,
        });

        const checkCanMakePayment = async () => {
          const canMakePaymentObject = await pr.canMakePayment();
          if (canMakePaymentObject) {
            const { applePay, googlePay } = canMakePaymentObject;
            if (applePay) {
              setCanApplePay(true);
              if (!buttonList.includes("apple")) {
                buttonList.push("apple");
              }
            }

            if (googlePay) {
              setCanGooglePay(true);
              if (!buttonList.includes("google")) {
                buttonList.push("google");
              }
            }
            //@ts-ignore
            setPaymentRequest(pr);
          }
        };
        checkCanMakePayment();
      }
    }, [stripe]);

    return (
      <>
        <Accordion
          classNames={{ chevron: styles["chevron"] }}
          value={activePaymentMethod}
          onChange={handlePaymentMethodClick}
          variant="contained"
          defaultValue="credit"
        >
          {profile?.corporate?.id && (
            <Accordion.Item value="invoice">
              <Accordion.Control>
                <Group align="center">
                  <Radio
                    readOnly
                    color={"app-pink.4"}
                    checked={isActivePaymentMethod("invoice")}
                  />
                  <Group gap={5} align="center">
                    <IconInvoice size={35} strokeWidth={1} color={"black"} />
                    <Text fw={600}>Invoice</Text>
                  </Group>
                </Group>
              </Accordion.Control>
            </Accordion.Item>
          )}
          <Accordion.Item value="credit">
            <Accordion.Control>
              <Group align="center">
                <Radio
                  readOnly
                  color={"app-pink.4"}
                  checked={isActivePaymentMethod("credit")}
                />
                <Group gap={5} align="center">
                  <IconCreditCard size={35} strokeWidth={1} color={"black"} />
                  <Text fw={600}>Credit Card</Text>
                </Group>
              </Group>
            </Accordion.Control>
            <Accordion.Panel>
              {isActivePaymentMethod("credit") && (
                <CreditCardCheckoutForm
                  formRef={ref}
                  clientSecret={clientSecret}
                  planId={planId}
                  handlePaymentMethod={handleCreditCardPaymentMethod}
                  isCheckoutDisabled={isCheckoutDisabled}
                  setIsCheckoutDisabled={setIsCheckoutDisabled}
                />
              )}
            </Accordion.Panel>
          </Accordion.Item>
          {canApplePay && <AdditionalPaymentOption payment="apple" />}
          {canGooglePay && <AdditionalPaymentOption payment="google" />}
          <PayPayPaymentOption />
        </Accordion>
        {cardErrMessage && (
          <Alert
            className="my-4"
            color="red"
            title="Error"
            icon={<IconInfoCircle />}
          >
            {cardErrMessage}
          </Alert>
        )}
        <AgreementCheckboxes />
      </>
    );
  }
);

CheckoutForm.displayName = "CheckoutForm";
export default CheckoutForm;
