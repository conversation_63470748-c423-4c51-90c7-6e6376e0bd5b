import { Anchor, Group, Text } from "@mantine/core";
import { useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { useShoppingCart } from "@/store";
import { useCartController } from "@/hooks/useCartController";
import { useMediaQuery } from "@mantine/hooks";
import CartMenuItem from "@repo/ui/src/CartMenuItem";

export const useDeviceQuery = () => {
  const isMobile = useMediaQuery("(max-width: 768px)", false, {
    getInitialValueInEffect: false,
  });
  return [isMobile];
};

export const NavbarCart = () => {
  const { t } = useTranslation();
  const [isMobile] = useDeviceQuery();
  const pathname = usePathname();
  const router = useRouter();

  const [showCartMenu, toggleCartMenu, quotation] = useShoppingCart((s) => [
    s.showCartMenu,
    s.toggleCartMenu,
    s.quotation,
  ]);

  const {
    loading,
    handleCheckout,
    handleItemRemove,
    handleQuantityChange,
    items,
    clearItems,
    formattedDiscount,
    total,
  } = useCartController({ quotation });

  useEffect(() => {
    const isCheckoutPage = pathname?.includes("checkout");
    if (isCheckoutPage) toggleCartMenu(false);
  }, [router]);

  useEffect(() => {
    if (!items.length) {
      toggleCartMenu(false);
    }
  }, [items]);

  return (
    <>
      <CartMenuItem
        discountLabel={`${t("common:cart.menu.discount")} ${
          quotation?.coupon?.type === "PERCENTAGE"
            ? `(${quotation?.coupon?.discount}% OFF)`
            : ``
        } `}
        discount={quotation?.discount ? "-" + formattedDiscount : undefined}
        minItemPerGroup={1}
        maxItemPerGroup={3}
        disableProductHover
        loading={loading}
        menuProps={{
          width: isMobile ? 360 : 450,
          //@ts-ignore
          closeOnClickOutside: true,
          //@ts-ignore
          closeOnEscape: true,
          //@ts-ignore
          opened: showCartMenu,
          //@ts-ignore
          onClick: () => {
            if (!items.length) return;
            toggleCartMenu(!showCartMenu);
          },
          //@ts-ignore
          onClose: () => {
            toggleCartMenu(false);
          },
        }}
        onCheckout={handleCheckout}
        onRemoveItem={handleItemRemove}
        onQuantityChange={handleQuantityChange}
        onContinueShooping={() => {
          if (!pathname?.includes("region")) {
            router.push(`/`);
          }
          toggleCartMenu(false);
        }}
        totalItems={quotation?.orders?.length || 0}
        total={total}
        renderOrderSubInformation={(item: any) => {
          return (
            <Text c={"dimmed"} size="xs" fw={600}>
              <small>X</small> {item.perPrice}
            </Text>
          );
        }}
        items={items.map((item) => {
          return {
            id: item.id,
            label: item.label,
            price: item.price,
            quantity: item.quantity,
            perPrice: item.perPrice,
            productImage: item.productImage,
          };
        })}
        headerLabel={t("common:cart.menu.youritems")}
        totalLabel={t("common:cart.menu.total")}
        continueShoppingLabel={
          <Text fw={"bold"} className="underline">
            {t("common:cart.menu.continueshopping")}
          </Text>
        }
        checkoutLabel={<>{t("common:cart.menu.checkout")}</>}
        renderAfterTotal={() => {
          return (
            <Group justify="end">
              <Anchor
                onClick={() => {
                  if (confirm(t("common:cart.error.emptycartwarning")))
                    clearItems();
                }}
              >
                <Text size="xs" c={"dimmed"} mt={"-10"} className="underline">
                  {t("common:cart.menu.emptycart")}
                </Text>
              </Anchor>
            </Group>
          );
        }}
      />
    </>
  );
};
