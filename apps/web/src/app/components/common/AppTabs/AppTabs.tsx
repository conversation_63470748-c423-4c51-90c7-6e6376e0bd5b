"use client";

/*
 * import modules and libraries
 */
import { TabsTabProps, Tabs, Title } from "@mantine/core";

interface IProps {
  headers: { name: string; code?: string; children?: React.ReactNode }[];
  defaultValue: string | null | undefined;
  onTabChange(value: string | null): void;
  activeTab: string | null | undefined;
}

const AppTabs = (
  props: Partial<Omit<TabsTabProps, "defaultValue">> & IProps
) => {
  return (
    <Tabs
      w={"100%"}
      variant="pills"
      defaultValue={props.defaultValue}
      onChange={props.onTabChange}
      color="app-pink.4"
      classNames={{
        root: "bg-[#f7f7f7] md:pt-8",
        tab: "w-1/2 md:w-[350px] border-0 data-[active=true]:bg-white bg-[#f7f7f7] rounded-b-none",
        list: "gap-0",
      }}
    >
      <Tabs.List justify="center" color="app-pink.4">
        {props.headers.map((header) => (
          <Tabs.Tab
            value={header.code || header.name}
            key={header.code || header.name}
            h={"70px"}
          >
            <Title
              className={`font-bold text-lg md:text-2xl ${
                props.activeTab === (header.code || header.name)
                  ? "text-primary"
                  : ""
              }`}
              order={3}
            >
              {header.name}
            </Title>
          </Tabs.Tab>
        ))}
      </Tabs.List>
      {props.headers.map((header) =>
        header.children ? (
          <Tabs.Panel
            key={header.code || header.name}
            value={header.code || header.name}
          >
            {header.children}
          </Tabs.Panel>
        ) : null
      )}
    </Tabs>
  );
};
export default AppTabs;
