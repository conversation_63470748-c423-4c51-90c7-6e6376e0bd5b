'use client';

/*
 * import modules and libraries
 */
import {
  useState,
  useMemo,
} from "react";
import Image from "next/image";
import {
  Stack,
  Button,
  Combobox,
  useCombobox,
  ComboboxTarget,
  ComboboxDropdown,
  ComboboxOptions,
  ComboboxOption,
  ComboboxEmpty,
  TextInput,
  ScrollAreaAutosize,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconSearch } from "@tabler/icons-react";
/*
 * import constants and utils
 */
import { COUNTRIES } from "@repo/ui/src/constants/regions";

export default function CountrySearchInput({
  onSearch
}: {
  onSearch: (searchValue: string) => void;
}) {
  const { t } = useTranslation();
  const [searchCountry, setSearchCountry] = useState("");
  const [search, setSearch] = useState("");
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const shouldFilterOptions = !COUNTRIES.some(
    (item) => item.value === searchCountry.toLowerCase() && item.jp === search.toLowerCase()
  );
  const filteredOptions = shouldFilterOptions
    ? COUNTRIES.filter((item) =>
        item.jp.includes(search.trim().replaceAll(" ", "").toLowerCase()) ||
        item.value.includes(search.trim().replaceAll(" ", "").toLowerCase()) ||
        item.hiragana.includes(search.trim().replaceAll(" ", "").toLowerCase()) ||
        item.katakana.includes(search.trim().replaceAll(" ", "").toLowerCase())
      )
    : COUNTRIES;

  const options = useMemo(() =>
    filteredOptions.map((item) => (
      <ComboboxOption
        value={item.value}
        key={item.value}
        className="text-center md:text-left border-t py-4 md:border-t-0 last:border-b-0 md:py-2 last:mb-10 last:md:mb-0 flex items-center gap-2"
      >
        <Image
          src={`/assets/flags/svg/${item.code.toLowerCase()}.svg`}
          width={24}
          height={24}
          alt={item.code}
          className="border border-gray-100"
        />
        {t(`countries:${item.value}`)}
      </ComboboxOption>
    )),
    [filteredOptions]
  );
  
  const onSubmit = () => {
    if (search) {
      onSearch(search);
    } else {
      onSearch("");
      {/* TODO: add parameter after refactor with top page search */}
      // alert("旅行先を選択してください。")
    }
  }

  return (
    <>
      <Stack className="w-full items-center justify-center gap-2">
        <Combobox
          store={combobox}
          withinPortal={true}
          position="bottom"
          onOptionSubmit={(val) => {
            setSearchCountry(val);
            setSearch(
              t(`countries:${
              COUNTRIES.find(
                (item) => item.value === searchCountry
              )?.value || ""}`)
            );
            combobox.closeDropdown();
          }}
        >
          <ComboboxTarget>
            <TextInput
              size="lg"
              label={t("home:search.module.title")}
              placeholder={t("home:empty.field.search.module")}
              className="flex-1"
              leftSection={
                <IconSearch className="text-primary w-12" />
              }
              rightSection={
                // TODO: add parameter after refactor with top page search
                // <ComboboxChevron onClick={() => combobox.openDropdown()} />
                <Button
                  size="md"
                  color="app-action.4"
                  className="max-w-sm w-full mx-auto h-[50px] rounded-r-md text-black"
                  onClick={onSubmit}
                >
                  {t("common:ctabutton")}
                </Button>
              }
              value={search}
              onChange={(event: any) => {
                combobox.openDropdown();
                combobox.updateSelectedOptionIndex();
                setSearch(event.currentTarget.value);
              }}
              classNames={{
                root: "w-full max-w-[600px] mt-4 ",
                label: "!text-white",
                section: "w-[unset] top-0 bottom-0",
                input: "rounded-md"
              }}
              styles={{
                label: {
                  paddingBottom: "6px",
                  fontSize: "0.875rem",
                  lineHeight: "18px",
                  letterSpacing: "0px",
                  color: "#555555",
                },
                input: {
                  boxSizing: "border-box",
                  border: "1px solid #DDDDDD",
                  backgroundColor: "#FFFFFF",
                },
              }}
              onClick={() => combobox.openDropdown()}
              onFocus={() => combobox.openDropdown()}
              onBlur={() => {
                combobox.closeDropdown();
                {/* TODO: add parameter after refactor with top page search */}
                // if (searchCountry) {
                //   setSearch(
                //     COUNTRIES.find(
                //       (item) => item.value === searchCountry
                //     )?.jp || ""
                //   );
                // } else {
                //   setSearch("");
                // }
              }}
            />
          </ComboboxTarget>

          <ComboboxDropdown className="shadow-lg z-10">
            {COUNTRIES.length !== 0 && (
              <ComboboxOptions>
                <ScrollAreaAutosize mah={200} type="scroll">
                  {options.length > 0 ? (
                    options
                  ) : (
                    <ComboboxEmpty>
                      国のデータが見つかりません。
                    </ComboboxEmpty>
                  )}
                </ScrollAreaAutosize>
              </ComboboxOptions>
            )}
          </ComboboxDropdown>
        </Combobox>
        {/* TODO: add parameter after refactor with top page search */}
        {/* <Button
          size="md"
          color="app-action.4"
          className="max-w-sm w-full mx-auto"
          onClick={onSubmit}
        >
          {t("common:ctabutton")}
        </Button> */}
      </Stack>
    </>
  );
}