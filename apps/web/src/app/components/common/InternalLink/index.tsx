import Link, { LinkProps } from "next/link";

const InternalLink = ({
  target,
  defaultColor,
  underline = false,
  ...props
}: LinkProps & {
  style?: any;
  underline?: boolean;
  target?: string;
  children: React.ReactNode;
  defaultColor?: boolean;
}) => {
  return (
    <Link
      className={`${defaultColor ? "text-blue" : "text-[#333333]"} ${underline ? "underline" : "none"} decoration-[#333333]`}
      target={target}
      {...props}
    >
      {props.children}
    </Link>
  );
};
export default InternalLink;
