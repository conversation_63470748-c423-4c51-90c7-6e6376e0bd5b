"use client";

/*
 * import modules and libraries
 */
import { useCallback } from "react";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { Group, Button, Box } from "@mantine/core";
import {
  IconFolder,
  IconInfoCircle,
  IconUserCircle,
} from "@tabler/icons-react";
/*
 * import components
 */
import Logo from "@/app/components/common/AppLayout/Header/Logo";
/*
 * import constants and helpers
 */
import { useProfile } from "@/store/UserStore";
import { useOrderStore } from "@/store";
import { NavbarCart } from "@/app/components/NavbarCart";

let LINKS: {
  url: string;
  label: string;
  icon: React.ReactNode;
}[] = [
  {
    url: "/app",
    label: "common:navbar.myesim.label",
    icon: <IconFolder />,
  },
  {
    url: "/faq",
    label: "common:navbar.faq.label",
    icon: <IconInfoCircle />,
  },
];

export default function Header() {
  const { t } = useTranslation();
  const profile = useProfile((store) => store.profile);
  const toggleDrawer: () => void = useOrderStore((state) => state.toggleDrawer);

  const LinksList = useCallback(
    () => (
      <Group className="hidden lg:flex">
        {LINKS.map((item) => (
          <Button
            size="md"
            key={`header-${item.label}`}
            component={Link}
            href={item.url}
            variant="transparent"
            leftSection={item.icon}
            classNames={{
              root: "text-black hover:text-primary px-2",
              section: "mr-1 me-0",
            }}
          >
            {t(item.label)}
          </Button>
        ))}
        <Button
          size="md"
          variant="outline"
          leftSection={<IconUserCircle />}
          onClick={toggleDrawer}
          classNames={{
            root: "text-black hover:text-primary hover:bg-transparent px-2 rounded-full border-gray-300",
            section: "mr-1 me-0",
          }}
        >
          {t("common:navbar.account.label")}
        </Button>
      </Group>
    ),
    [profile]
  );

  return (
    <Group h="100%" justify="space-between" className="gap-0">
      <Group className="gap-0">
        <Logo isShowJpx />
      </Group>
      <Group>
        <LinksList />
        <Box mr={"md"}>
          <NavbarCart />
        </Box>
      </Group>
    </Group>
  );
}
