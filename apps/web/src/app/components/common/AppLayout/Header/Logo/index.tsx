"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";
import Image from "next/image";
import { Group, Text } from "@mantine/core";
/*
 * import constants and helpers
 */
import { getCDNUrl } from "@/utils";

export default function Logo({ isShowJpx }: { isShowJpx?: boolean }) {
  return (
    <Group gap={0}>
      <Link href="/">
        <Image
          alt="Global Mobile logo"
          src={getCDNUrl("/assets/logo/logo.svg")}
          height={40}
          width={100}
        />
      </Link>
      {isShowJpx && (
        <>
          <Image
            src={getCDNUrl("/assets/jpx-logo.png")}
            width={35}
            height={35}
            alt="jpx-logo"
            className="ml-4 hidden xxs:block"
          />
          <Text
            classNames={{
              root: "text-[8px] md:!text-xxs hidden xxs:block ml-2",
            }}
          >
            TSE Growth
            <br />
            Code:5587
          </Text>
        </>
      )}
    </Group>
  );
}
