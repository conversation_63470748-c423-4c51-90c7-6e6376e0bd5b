"use client";

/*
 * import modules and libraries
 */
import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { useTranslation } from "react-i18next";
import { useDisclosure } from "@mantine/hooks";
import dynamic from "next/dynamic";
/*
 * import components
 */
import Layout from "@repo/ui/src/common/Layout";
import Header from "@/app/components/common/AppLayout/Header";
import Footer from "@/app/components/common/AppLayout/Footer";
import Aside from "@/app/components/common/AppLayout/Aside";
import Logo from "@/app/components/common/AppLayout/Header/Logo";
import CompatibilityCheck from "@repo/ui/src/modals/CompatibilityCheck";
/*
 * import constants and utils
 */
import { useOrderStore } from "@/store";
import Storage from "@/utils/storage";
/*
 * import dynamic components
 */
const DynamicCurrencySelect = dynamic(
  () => import("@/app/components/modal/CurrencySelect")
);
const DynamicLanguageSelect = dynamic(
  () => import("@/app/components/modal/LanguageSelect")
);

export default function AppLayout({
  children,
  contentBg,
}: {
  children: React.ReactNode;
  contentBg?: string;
}) {
  const pathname = usePathname();
  const { t, i18n } = useTranslation();
  const isShowDrawer: boolean = useOrderStore((state) => state.isShowDrawer);
  const toggleDrawer: () => void = useOrderStore((state) => state.toggleDrawer);
  const closeDrawer: () => void = useOrderStore((state) => state.closeDrawer);
  const isCompatibilityModalOpen: boolean = useOrderStore(
    (state) => state.isCompatibilityModalOpen
  );
  const toggleCompatibilityModalOpen: () => void = useOrderStore(
    (state) => state.toggleCompatibilityModalOpen
  );
  const [languageModal, setLanguageModal] = useDisclosure();
  const [currencyModal, setCurrencyModal] = useDisclosure();

  useEffect(() => {
    closeDrawer();
  }, [pathname]);

  useEffect(() => {
    const currency = Storage.getInstance().get("currency");
    const locale = Storage.getInstance().get("locale");

    if (!currency && i18n.language === "jp") {
      Storage.getInstance().set(
        "currency",
        {
          code: "JPY",
          sign: "¥",
          name: "JPY - Japanese Yen",
        } as any,
        { storageType: "cookie" }
      );
    } else if (!currency) {
      Storage.getInstance().set(
        "currency",
        {
          code: "USD",
          sign: "$",
          name: "USD - US Dollar",
        } as any,
        { storageType: "cookie" }
      );
    }
    if (!locale) {
      Storage.getInstance().set("locale", i18n.language);
    }
  }, []);

  return (
    <>
      <Layout
        header={<Header />}
        body={children}
        footer={<Footer />}
        aside={{
          body: (
            <Aside
              setLanguageModalOpen={setLanguageModal.open}
              setCurrencyModalOpen={setCurrencyModal.open}
            />
          ),
          logo: <Logo />,
        }}
        headerHeight={70}
        headerMaxWidth={1440}
        contentBg={contentBg}
        isShowDrawer={isShowDrawer}
        toggleDrawer={toggleDrawer}
        closeDrawer={closeDrawer}
      />
      <DynamicLanguageSelect
        title={t("common:language.title")}
        opened={languageModal}
        onClose={setLanguageModal.close}
      />
      <DynamicCurrencySelect
        title={t("currencies:title")}
        opened={currencyModal}
        onClose={setCurrencyModal.close}
      />
      <CompatibilityCheck
        opened={isCompatibilityModalOpen}
        close={toggleCompatibilityModalOpen}
      />
    </>
  );
}
