"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";
import { useEffect, useState } from "react";

import { <PERSON><PERSON>, Divider, Stack } from "@mantine/core";

/*
 * import components
 */
import {
  IconFolder,
  IconHeadset,
  IconInfoCircle,
  IconLanguage,
  IconSettingsQuestion,
  IconUserCircle,
  IconWorld,
} from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

/*
 * import constants and helpers
 */
import { AUTH_TOKEN_NAME } from "@/app/constants";
/*
 * import data types
 */
import { ICurrency } from "@/interfaces/ICurrency";
import { useOrderStore } from "@/store";
import { useProfile } from "@/store/UserStore";
import { withBasePath } from "@/utils";
import { getCurrencyIcon } from "@/utils/display_helper";
import Storage from "@/utils/storage";

interface ILink {
  url: string;
  label: string;
  icon: React.ReactNode;
}

const MenuButton = ({
  title,
  icon,
  url,
}: {
  title: string;
  icon: React.ReactNode;
  url: string;
}) => {
  const { i18n } = useTranslation();

  return (
    <Button
      variant="subtle"
      component={Link}
      href={url}
      fullWidth
      locale={i18n.language}
      leftSection={icon}
      size="md"
      classNames={menuButtonStyles}
    >
      {title}
    </Button>
  );
};

const menuLinks: ILink[] = [
  {
    url: "/faq",
    label: "common:navbar.faq.label",
    icon: <IconInfoCircle />,
  },
  {
    url: "/support/help",
    label: "common:navbar.help.label",
    icon: <IconSettingsQuestion />,
  },
  {
    url: "/destination",
    label: "common:navbar.destination.label",
    icon: <IconWorld />,
  },
  {
    url: "/support/contactus",
    label: "common:navbar.contactus.label",
    icon: <IconHeadset />,
  },
];

const menuButtonStyles = {
  root: "text-black md:hover:text-primary px-0",
  inner: "justify-start mb-2 px-0 font-semibold",
};

export const getFlagCode = (lang: string) => {
  switch (lang) {
    case "en":
      return "us";
    case "vi":
      return "vn";
    default:
      return lang;
  }
};

export default function Aside({
  setLanguageModalOpen,
  setCurrencyModalOpen,
}: {
  setLanguageModalOpen: () => void;
  setCurrencyModalOpen: () => void;
}) {
  const { t, i18n } = useTranslation();
  const profile = useProfile((store) => store.profile);
  const [currency, setCurrency] = useState<ICurrency | undefined>(
    Storage.getInstance().get("currency")
  );
  const closeDrawer: () => void = useOrderStore((state) => state.closeDrawer);

  useEffect(() => {
    setCurrency(Storage.getInstance().get("currency"));
  }, [profile]);

  return (
    <>
      <Stack className="mt-4 gap-2 p-4">
        {menuLinks.map((item) => (
          <MenuButton
            key={`aside-${item.label}`}
            title={t(item.label)}
            icon={item.icon}
            url={item.url}
          />
        ))}
        <Divider />
        {profile ? (
          <>
            <MenuButton
              url="/app"
              title={t("common:navbar.myesim.label")}
              icon={<IconFolder />}
            />
            <Button
              fullWidth
              classNames={menuButtonStyles}
              variant="subtle"
              size="md"
              leftSection={<IconUserCircle />}
              onClick={() => {
                Storage.getInstance().set(AUTH_TOKEN_NAME, "");
                window.location.href = withBasePath(i18n.language);
              }}
            >
              {t("common:logout")}
            </Button>
          </>
        ) : (
          <Button
            fullWidth
            classNames={menuButtonStyles}
            variant="subtle"
            size="md"
            locale={i18n.language}
            component={Link}
            title={t("common:btn.login")}
            leftSection={<IconUserCircle />}
            href="/auth/signin"
          >
            {t("common:btn.login")}
          </Button>
        )}
        <Divider />
        <Button
          variant="subtle"
          fullWidth
          leftSection={<IconLanguage />}
          size="md"
          classNames={menuButtonStyles}
          onClick={() => {
            closeDrawer();
            setLanguageModalOpen();
          }}
        >
          {t("common:language.title")}
        </Button>
        <Button
          variant="subtle"
          fullWidth
          leftSection={currency ? <>{getCurrencyIcon(currency?.code)}</> : null}
          size="md"
          classNames={{
            root: "text-black md:hover:text-primary px-0",
            inner: "justify-start mb-2 px-0 font-semibold",
          }}
          onClick={() => {
            closeDrawer();
            setCurrencyModalOpen();
          }}
        >
          {t("currencies:title")}
        </Button>
      </Stack>
    </>
  );
}
