import { Breadcrumbs, BreadcrumbsProps } from "@mantine/core";
import styles from "./appBreadCrumb.module.css";

const AppBreadCrumb = (props: BreadcrumbsProps & {
  component?: string;
}) => {
  return (
    <Breadcrumbs
      component={props.component}
      separator=">"
      classNames={{
        root: styles["root"],
        separator: "text-sm pb-0.5"
      }}
      {...props}
    >
      {props.children}
    </Breadcrumbs>
  );
};
export default AppBreadCrumb;
