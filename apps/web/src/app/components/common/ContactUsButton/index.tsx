'use client';

/*
 * import modules and libraries
 */
import {
  Text,
  Image,
  Button,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { getCDNUrl } from "@/utils";
import Link from "next/link";

/*
 * import constants and utils
 */

const ContactUsButton = () => {
  const { t } = useTranslation();
  const support = <Image w={32} src={getCDNUrl("/assets/support-icon.svg")} />;

  return (
    <Button
      size="lg"
      radius="md"
      variant="outline"
      color="app-pink.4"
      component={Link}
      href="/support/contactus"
      bg="white"
      >
      {support}
      <Text className="font-bold" c="black" ml={10}>
          {t("faq:contactus")}
      </Text>
    </Button>
  )
}
export default ContactUsButton;