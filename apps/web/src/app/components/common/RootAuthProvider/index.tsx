"use client";

/*
 * import modules and libraries
 */
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect } from "react";

import { LoadingOverlay } from "@mantine/core";

import { AuthProvider } from "@ri/fe-auth";
import { isPast } from "date-fns";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";
import { QueryClient, QueryClientProvider } from "react-query";

/*
 * import components
 */
import AuthProviderLayout from "@/app/components/common/RootAuthProvider/AuthProviderLayout";

import { getDecodedCode } from "@/utils";
import Storage from "@/utils/storage";

/*
 * import interfaces
 */
import { IOauthResponse } from "@/interfaces/IOauthResponse";

/*
 * import constants and helpers
 */
import { AUTH_TOKEN_NAME } from "@/app/constants";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    },
  },
});

export default function RootAuthProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const paramsString = searchParams?.toString();

  const onAuthenticationPath = useCallback(() => {
    if (typeof window === "undefined") return;
    const url = Storage.getInstance().get("returnUrl") || "/app";
    return url;
  }, []);

  useEffect(() => {
    if (!pathname?.includes("/checkout/plan")) {
      sessionStorage.removeItem("esim-lgu-form");
    }
  }, [pathname]);

  return (
    <>
      <GoogleReCaptchaProvider
        reCaptchaKey={process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHA_SITE_KEY + ""}
        language="[optional_language]"
        scriptProps={{
          async: false, // optional, default to false,
          defer: false, // optional, default to false
          appendTo: "head", // optional, default to "head", can be "head" or "body",
          nonce: undefined, // optional, default undefined
        }}
      >
        <QueryClientProvider client={queryClient}>
          <AuthProvider<{ companyId: string }>
            restrictedPathsForAuth={[
              "/auth/signin",
              "/auth/signup",
              "/account/reset-password",
              "/account/forgot-password",
            ]}
            protectedPaths={["/app"]}
            pathname={pathname}
            onAuthenticationRedirectionPath={onAuthenticationPath()}
            renderOpenRoutes={(children: React.ReactNode) =>
              pathname?.includes("/profile") ? (
                // <AuthLayout skipLoginRedirect>{children}</AuthLayout>
                <AuthProviderLayout>{children}</AuthProviderLayout>
              ) : pathname?.includes("/app") ? (
                <LoadingOverlay visible />
              ) : (
                children
              )
            }
            renderPrivateRoutes={(children: React.ReactNode) => (
              <AuthProviderLayout>{children}</AuthProviderLayout>
            )}
            onUnauthorizedAccess={() => {
              Storage.getInstance().set(
                "returnUrl",
                `${pathname}?${paramsString}`
              );
              router.replace(
                `/auth/signin?redirectUri=${process.env.NEXT_PUBLIC_SSO_REDIRECT_URI}&returnUrl=` +
                  window.location.href
              );
            }}
            onAuthenticated={async () => {
              const nextRoute = await onAuthenticationPath();
              Storage.getInstance().set("returnUrl", "");
              router.replace(nextRoute);
            }}
            authResolver={async () => {
              const token = Storage.getInstance().get(
                AUTH_TOKEN_NAME
              ) as IOauthResponse;
              if (!token || !token.accessToken)
                throw new Error("Token not found");

              const decodedCode = getDecodedCode();
              if (isPast((decodedCode.exp as number) * 1000)) {
                throw new Error("Token Expired");
              }

              return {
                id: token.userId, // todo : ask BE to send userId in decoded code like of name
                displayName: String(decodedCode.name),
                email: String(decodedCode.email),
                data: {},
              };
            }}
          >
            {children}
          </AuthProvider>
        </QueryClientProvider>
      </GoogleReCaptchaProvider>
    </>
  );
}
