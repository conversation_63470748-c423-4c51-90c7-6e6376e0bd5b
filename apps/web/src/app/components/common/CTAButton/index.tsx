"use client";

import Link from "next/link";

import { Button, Text } from "@mantine/core";

import { useTranslation } from "react-i18next";

import { useOrderStore } from "@/store";

const CTAButton = ({
  customText,
  customUrl,
}: {
  customText?: string;
  customUrl?: string;
}) => {
  const { t } = useTranslation();
  const closeDrawer: () => void = useOrderStore((state) => state.closeDrawer);

  return (
    <Button
      component={Link}
      href={customUrl ? customUrl : "/#select-plan"}
      passHref
      fullWidth
      size="lg"
      radius="md"
      variant="filled"
      color="app-action.4"
      onClick={closeDrawer}
    >
      <Text className="ml-4 font-bold text-black">
        {customText ? customText : t("common:ctabutton")}
      </Text>
    </Button>
  );
};
export default CTAButton;
