"use client";
import { getCDNUrl } from "@/utils";
import { ActionIcon, Flex, Title, Image } from "@mantine/core";
import { useSearchParams, useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

export default function BackButton(props: {
  referer?: string | null;
  title: string;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { t } = useTranslation();

  const handleOnClick = () => {
    const referer = props.referer;
    if (searchParams?.has("email")) {
      router.push("/app");
      return;
    }
    if (referer?.startsWith(window.location.origin)) {
      router.back();
    } else {
      router.push("/");
    }
  };

  return (
    <Flex
      className="z-[999]"
      w={"100%"}
      bg={"white"}
      p={5}
      direction={"row"}
      align={"center"}
      pos={"sticky"}
      top={0}
    >
      <ActionIcon onClick={handleOnClick} variant="transparent">
        <Image
          src={getCDNUrl("/assets/left-arrow.png")}
          alt="left arrow icon"
        />
      </ActionIcon>
      <Flex w={"100%"} justify={"center"}>
        <Title order={1} size={16}>
          {props.title}
        </Title>
      </Flex>
    </Flex>
  );
}
