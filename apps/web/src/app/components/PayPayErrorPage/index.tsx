"use client";

import { useRouter } from "next/navigation";

import { <PERSON><PERSON>, <PERSON><PERSON>, Container, Flex, Title } from "@mantine/core";

import { useTranslation } from "react-i18next";

export default function PayPayErrorPage() {
  const router = useRouter();
  const { t } = useTranslation();

  const handleRetry = () => {
    router.push("/");
  };

  return (
    <Container size="sm" style={{ textAlign: "center", marginTop: "50px" }}>
      <Flex direction="column" align="center" gap="md">
        <Title order={1} className="text-primary">
          {t("paypay-error:title")}
        </Title>
        <Alert
          title={t("paypay-error:alertTitle")}
          color="gray"
          icon={<i className="fas fa-exclamation-circle text-primary"></i>}
          classNames={{
            root: "p-5 w-full",
            title: "text-center mb-2 ml-5 text-primary",
            message: "mx-[5px] leading-[1.5] text-center text-primary",
          }}
        >
          {t("paypay-error:alertMessage")}
        </Alert>
        <Button 
          variant="light" 
          onClick={handleRetry}
          className="text-primary border-primary hover:bg-primary hover:bg-opacity-10"
          styles={{
            root: { 
              backgroundColor: 'transparent',
              '&:hover': {
                backgroundColor: 'rgb(from theme(colors.primary) r g b / 0.1)',
              }
            }
          }}
        >
          {t("paypay-error:button")}
        </Button>
      </Flex>
    </Container>
  );
}
