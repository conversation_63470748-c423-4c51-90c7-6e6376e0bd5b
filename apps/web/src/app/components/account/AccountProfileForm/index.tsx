"use client";

/*
 * import modules and libraries
 */
import React, { useEffect } from "react";
import { useForm, zodResolver } from "@mantine/form";
import { <PERSON><PERSON>, <PERSON>ack, } from "@mantine/core";
import { useTranslation } from "react-i18next";
/*
 * import components
 */
import AlertMessage from "@/app/components/common/AlertMessage";
import AppTextInput from "@/app/components/form/AppTextInput/AppTextInput";
import SectionContent from "@repo/ui/src/common/SectionContent";
/*
 * import constants and utils
 */
import { z } from "@/i18n/en-zod";
import { ERR_GENERIC_REQUIRED } from "@/app/constants";
import { useProfile } from "@/store/UserStore";
import { AlertMessageType, useMessageStore } from "@/store/MessageStore";
/*
 * import api
 */
import { ApiService } from "@/api";

const schema = z.object({
  firstName: z.string().min(1, ERR_GENERIC_REQUIRED),
  lastName: z.string().min(1, ERR_GENERIC_REQUIRED),
});

export default function AccountProfileForm() {
  const { t } = useTranslation();
  const [toggleLoading, setMessage] = useMessageStore((s) => [
    s.toggleLoading,
    s.setMessage,
  ]);
  const [profile, setProfile] = useProfile((p) => [p.profile, p.setPorfile]);
  const form = useForm({
    initialValues: {
      firstName: profile?.firstName || "",
      lastName: profile?.lastName || "",
      email: profile?.email || "",
    },
    validate: zodResolver(schema),
  });

  useEffect(() => {
    form.setValues({
      firstName: profile?.firstName || "",
      lastName: profile?.lastName || "",
      email: profile?.email || "",
    });
  }, [profile]);

  const handleProfileUpdate = React.useCallback(
    async (values: typeof form.values) => {
      try {
        toggleLoading();
        const response = await ApiService.updateProfile({
          firstName: values.firstName as string,
          lastName: values.lastName as string,
        });
        setProfile(response.data.data);
        setMessage(t("profile:updated-message"), AlertMessageType.SUCCESS);
      } catch (err: any) {
        setMessage(
          err.response?.data?.data?.message || "Unable to update profile"
        );
      } finally {
        toggleLoading(false);
      }
    },
    [setProfile]
  );

  return (
    <SectionContent small title={t("common:navbar.account.label")}>
      <Stack>
        <AlertMessage />
        <form onSubmit={form.onSubmit(handleProfileUpdate)}>
          <Stack justify={"space-between"}>
            <AppTextInput
              withAsterisk
              placeholder={t("signup:formfield.lastname.placeholder") as string}
              label={t("signup:formfield.lastname.placeholder")}
              {...form.getInputProps("lastName")}
            />
            <AppTextInput
              withAsterisk
              placeholder={
                t("signup:formfield.firstname.placeholder") as string
              }
              label={t("signup:formfield.firstname.placeholder")}
              {...form.getInputProps("firstName")}
            />
            <AppTextInput
              disabled
              withAsterisk
              placeholder={t("signup:formfield.email.placeholder") as string}
              label={t("signup:formfield.email.placeholder")}
              {...form.getInputProps("email")}
            />
            <Button mt={10} size="md" w={"100%"} type="submit" color="app-dark">
              {t("profile:update")}
            </Button>
          </Stack>
        </form>
      </Stack>
    </SectionContent>
  );
}
