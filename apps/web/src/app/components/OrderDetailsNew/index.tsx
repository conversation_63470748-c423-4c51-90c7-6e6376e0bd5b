import {
  An<PERSON>,
  Group,
  MantineProvider,
  Stack,
  Text,
  Title,
  TypographyStylesProvider,
} from "@mantine/core";
import { IQuotation } from "../../../interfaces/IQuotation";
import { useCartController } from "../../../hooks/useCartController";
import { useEffect, useMemo } from "react";
import Image from "next/image";
import { ICouponDiscounted } from "@/interfaces/ICouponDiscounted";
import { useTranslation } from "react-i18next";
import CartListBox from "@repo/ui/src/CartListBox";
import { useRouter } from "next/navigation";

export interface OrderDetailsNewProps {
  discount: ICouponDiscounted | null;
  renderCheckoutButton?: () => JSX.Element;
  isMobile?: boolean;
  quotation: IQuotation;
  source: "single" | "multiple";
}
const OrderDetailsNew = ({ quotation, ...props }: OrderDetailsNewProps) => {
  const {
    loading,
    handleCheckout,
    handleItemRemove,
    handleQuantityChange,
    items,
    total,
    handleContinueShopping,
    formattedDiscount,
    formattedTotalWithoutDiscount,
    clearItems,
  } = useCartController({ discount: props.discount, quotation });

  const isSingleMode = useMemo(() => {
    return props.source === "single";
  }, [props.source]);
  const { t } = useTranslation();
  const router = useRouter();
  useEffect(() => {
    if (!items.length) router.push("/");
  }, [items]);

  return (
    <Stack className="pt-0 md:mt-0" w={"100%"}>
      <MantineProvider
        theme={{
          components: {
            Text: {
              defaultProps: {
                c: "white",
              },
            },
            Title: {
              defaultProps: {
                c: "white",
              },
            },
            Anchor: {
              defaultProps: {
                c: "white",
              },
            },
          },
        }}
      >
        <CartListBox
          mode="EMBEDED"
          minItemPerGroup={1}
          dimmedTextColor="white"
          maxItemPerGroup={3}
          singleMode={isSingleMode}
          containerProps={
            props.isMobile
              ? {
                  p: "0",
                  shadow: "lg",
                  bg: "linear-gradient(324.8deg, #082699 0%, #2B3D82 100%)",
                  radius: "lg",
                }
              : {
                  bg: "linear-gradient(324.8deg, #082699 0%, #2B3D82 100%)",
                  radius: "lg",
                  shadow: "sm",
                }
          }
          renderTitle={() => {
            return props.isMobile ? (
              <></>
            ) : (
              <Group align="center" justify="center">
                <Group gap={1} justify="center">
                  <img src="/favicon.ico" width={32} height={32} />
                  <Title order={4} fw={"bolder"}>
                    {t("common:cart.menu.ordersummary")}
                  </Title>
                </Group>
              </Group>
            );
          }}
          renderCheckoutButton={
            props.isMobile ? () => <></> : props.renderCheckoutButton
          }
          loading={loading}
          onCheckout={handleCheckout}
          onRemoveItem={handleItemRemove}
          onQuantityChange={handleQuantityChange}
          total={total}
          onContinueShooping={handleContinueShopping}
          renderOrderSubInformation={(item: any) => {
            return (
              <Text c={"white"} size="sm" fw={600}>
                {isSingleMode ? (
                  <>
                    <small>X</small> 1
                  </>
                ) : (
                  <>
                    <small>X</small> {item.perPrice}
                  </>
                )}
              </Text>
            );
          }}
          items={items.map((item) => {
            return {
              id: item.id,
              label: (
                <Group wrap="nowrap" gap={0}>
                  {item.label}
                </Group>
              ),
              price: item.price,
              quantity: item.quantity,
              perPrice: item.perPrice,
              productImage: item.productImage,
              link: item.link,
              hoverImage: item.hoverImage,
            };
          })}
          discountLabel={`${t("common:cart.menu.discount")} ${
            quotation?.coupon?.type === "PERCENTAGE"
              ? `(${quotation?.coupon?.discount}% OFF)`
              : ``
          } `}
          discount={quotation?.discount ? "-" + formattedDiscount : undefined}
          pseudoTotal={
            quotation?.discount ? formattedTotalWithoutDiscount : undefined
          }
          hideContinueShopping
          collapsable={props.isMobile}
          headerLabel={t("common:cart.menu.youritems")}
          totalLabel={t("common:cart.menu.total")}
          continueShoppingLabel={<>{t("common:cart.menu.continueshopping")}</>}
          checkoutLabel={<>{t("common:cart.menu.checkout")}</>}
          renderSummaryLabel={(state: boolean) => {
            return (
              <Text size="xs" c={"dimmed"}>
                {" "}
                {t("common:cart.menu.details")} &gt;{" "}
              </Text>
            );
          }}
          renderAfterTotal={() => {
            return isSingleMode ? (
              <> </>
            ) : (
              <Group justify="end">
                <Anchor
                  onClick={() => {
                    if (confirm(t("common:cart.error.emptycartwarning")))
                      clearItems();
                  }}
                >
                  <Text
                    size="xs"
                    c={"#dadada"}
                    mt={"-10"}
                    fw={"bold"}
                    className="underline"
                  >
                    {t("common:cart.menu.emptycart")}
                  </Text>
                </Anchor>
              </Group>
            );
          }}
        />
      </MantineProvider>
    </Stack>
  );
};
export default OrderDetailsNew;
