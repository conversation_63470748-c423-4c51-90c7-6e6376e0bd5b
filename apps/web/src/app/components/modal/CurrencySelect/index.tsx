"use client";

/*
 * import modules and libraries
 */
import { useState, useCallback } from "react";
import { ModalProps, Accordion, Text, Flex } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconCheck } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
/*
 * import component
 */
import ParentModal from "@repo/ui/src/common/ParentModal";
/*
 * import constants and helpers
 */
import { useProfile } from "@/store/UserStore";
import Storage from "@/utils/storage";
import { getCurrencyIcon } from "@/utils/display_helper";
/*
 * import data types
 */
import { ICurrency } from "@/interfaces/ICurrency";

const CurrencySelect = (
  props: {
    top?: string;
    hideHeaderBorder?: boolean;
    showOverlay?: boolean;
  } & ModalProps
) => {
  const { t } = useTranslation();
  const router = useRouter();
  const setProfile = useProfile((s) => s.setPorfile);
  const [savedCurrency, setCurrency] = useState(
    Storage.getInstance().get("currency")
  );

  const currencies = [
    {
      code: "JPY",
      name: t("currencies:jpy"),
      sign: "¥",
    },
    {
      code: "USD",
      name: t("currencies:usd"),
      sign: "$",
    },
    {
      code: "EUR",
      name: t("currencies:eur"),
      sign: "€",
    },

    {
      code: "AUD",
      name: t("currencies:aud"),
      sign: "$",
    },
    {
      code: "CAD",
      name: t("currencies:cad"),
      sign: "$",
    },
    {
      code: "TWD",
      name: t("currencies:twd"),
      sign: "$",
    },
    {
      code: "HKD",
      name: t("currencies:hkd"),
      sign: "$",
    },
    {
      code: "CNY",
      name: t("currencies:cny"),
      sign: "¥",
    },
    {
      code: "GBP",
      name: t("currencies:gbp"),
      sign: "£",
    },
  ];

  // TODO: fix rehydration errors
  const handleSelect = useCallback((v: any) => {
    if (!v) return;
    const currency = currencies.find((c) => c.code === v) as ICurrency;
    Storage.getInstance().set("currency", currency as any, { storageType:"cookie" });
    props.onClose();
    setProfile({
      currency,
    });
    setCurrency(currency);
    router.refresh();
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }, []);

  return (
    <ParentModal {...props}>
      <Accordion
        onChange={handleSelect}
        variant="contained"
        chevron={
          <Text c={"dimmed"} fw={900}>
            &gt;
          </Text>
        }
        disableChevronRotation
      >
        {currencies.map((currency, index) => (
          <Accordion.Item value={currency.code} key={index}>
            <Accordion.Control
              chevron={
                savedCurrency?.code === currency.code ? <IconCheck /> : " "
              }
            >
              <Flex align="center">
                {getCurrencyIcon(currency.code)}
                <Text ml={4}>{currency.name}</Text>
              </Flex>
            </Accordion.Control>
          </Accordion.Item>
        ))}
      </Accordion>
    </ParentModal>
  );
};

export default CurrencySelect;
