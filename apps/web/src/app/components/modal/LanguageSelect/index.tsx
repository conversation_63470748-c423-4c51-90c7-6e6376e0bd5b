"use client";

/*
 * import modules and libraries
 */
import { useCallback } from "react";
import { ModalProps, Accordion, Text, Flex } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconCheck } from "@tabler/icons-react";
/*
 * import component
 */
import ParentModal from "@repo/ui/src/common/ParentModal";
/*
 * import constants and helpers
 */
import { isDev } from "@/utils";
import Storage from "@/utils/storage";

const LANGUAGES_LIST: { [key: string]: string } = {
  jp: "Japanese",
  en: "English",
  es: "Español",
  fr: "Français",
  ph: "Tagalog",
  vi: "Tiếng Việt",
  kr: "Korean"
};

export const getFlagCode = (lang: string) => {
  switch (lang) {
    case "en":
      return "us";
    case "vi":
      return "vn";
    default:
      return lang;
  }
};

const LanguageSelect = (
  props: {
    top?: string;
    hideHeaderBorder?: boolean;
    showOverlay?: boolean;
  } & ModalProps
) => {
  const { i18n } = useTranslation();
  const baseUrl =
    typeof window !== "undefined"
      ? window?.location?.origin
      : isDev()
      ? "http://localhost:3014"
      : "https://www.esim.gmobile.biz";

  // TODO: fix rehydration errors
  const handleSelect = useCallback((v: any) => {
    Storage.getInstance().set("locale", v);
    const basePath = !!process.env.NEXT_PUBLIC_BASE_PATH;
    if ((basePath && v === "jp") || (!basePath && v === "jp")) {
     window.location.href = isDev()
       ? "http://localhost:3013/esim"
       : (process.env.NEXT_PUBLIC_ESIM_JP ||
           "https://www-dev.gmobile.biz/esim");
      return;
    }
    window.location.href = `${baseUrl}/` + v;
    return;
  }, []);

  return (
    <ParentModal {...props}>
      <Accordion
        onChange={handleSelect}
        variant="contained"
        chevron={
          <Text c={"dimmed"} fw={900}>
            &gt;
          </Text>
        }
        disableChevronRotation
      >
        {Object.keys(LANGUAGES_LIST).map((lang, index) => (
          <Accordion.Item value={lang} key={index}>
            <Accordion.Control
              chevron={i18n.language === lang ? <IconCheck /> : " "}
            >
              <Flex align="center">
                <Text>{LANGUAGES_LIST[lang]}</Text>
              </Flex>
            </Accordion.Control>
          </Accordion.Item>
        ))}
      </Accordion>
    </ParentModal>
  );
};

export default LanguageSelect;
