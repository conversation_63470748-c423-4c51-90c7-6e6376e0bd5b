'use server';

/*
 * import modules and libraries
 */
import {
  Grid,
  GridCol,
  Stack,
} from "@mantine/core";
/*
 * import components
 */
import CountryCard from "@repo/ui/src/CountryCard";
import SeeMoreButton from "@/app/components/top/FeaturedCountries/SeeMoreButton";
/*
 * import constants and utils
 */
import { getCDNUrl, normalizeAndKebabCase, countryNameException } from "@/utils";
import { Currency } from "@/utils/currency";
/*
 * import interfaces
 */
import type { IPlan } from "@/interfaces/IPlan";

export default async function FeaturedCountries({
  data,
}: {
  data: IPlan[];
}) {
  return (
    <Stack className="gap-5 sm:gap-10">
      <Grid
        gutter={"1rem"}
        visibleFrom="sm"
      >
        {data?.map?.((plan, index) => (
          <GridCol key={index} span={{ base: 12, xs: 12, sm: 4, md: 3 }}>
            <CountryCard
              //@ts-ignore
              img={getCDNUrl(`/assets/destination/${countryNameException(plan.originalName || plan.country)}.webp`)}
              price={plan.price + ""}
              //@ts-ignore
              url={`/region/${normalizeAndKebabCase(plan?.originalName || plan?.country?.name || plan?.country)}${normalizeAndKebabCase(plan?.originalName || plan?.country?.name || plan?.country) === "japan" ? "?scroll=plan" : ""}`}
              name={countryNameException(plan?.originalName || plan?.country?.name) as string}
              originalName={countryNameException(plan.originalName)}
              selectedCode={Currency.getSelectedCurrency()}
              convertedPrice={Currency.formatToSelectedNoCode({
                xe: plan.xe,
                price: +plan.price,
              })}
              dark
            />
          </GridCol>
        ))}
      </Grid>
      <Grid
        gutter={"1rem"}
        hiddenFrom="sm"
      >
        {data?.splice(0, 4).map?.((plan, index) => (
          <GridCol key={index} span={{ base: 12, xs: 12, sm: 4, md: 3 }}>
            <CountryCard
              //@ts-ignore
              img={getCDNUrl(`/assets/destination/${countryNameException(plan.originalName || plan.country)}.webp`)}
              price={plan.price + ""}
              //@ts-ignore
              url={`/region/${normalizeAndKebabCase(plan?.originalName || plan?.country?.name || plan?.country)}${normalizeAndKebabCase(plan?.originalName || plan?.country?.name || plan?.country) === "japan" ? "?scroll=plan" : ""}`}
              name={countryNameException(plan?.originalName || plan?.country?.name) as string}
              originalName={countryNameException(plan.originalName)}
              selectedCode={Currency.getSelectedCurrency()}
              convertedPrice={Currency.formatToSelectedNoCode({
                xe: plan.xe,
                price: +plan.price,
              })}
              dark
            />
          </GridCol>
        ))}
      </Grid>
      <SeeMoreButton />
    </Stack>
  );
}