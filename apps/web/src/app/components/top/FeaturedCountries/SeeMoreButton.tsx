'use client';

/*
 * import modules and libraries
 */
import Link from "next/link";
import { Button } from "@mantine/core";
import { IconPlaneDeparture } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

export default function SeeMoreButton() {
  const { t } = useTranslation();

  return (
    <Button
      variant="outline"
      size="md"
      className="max-w-sm w-full mx-auto rounded-md"
      component={Link}
      href="/destination"
    >
      <IconPlaneDeparture className="text-primary mr-2" />
      <span className="text-black">
        {t("home:all.country.button")}
      </span>
    </Button>
  )
}