"use client";

import { Grid, GridCol } from "@mantine/core";

import { useTranslation } from "react-i18next";

import FeaturesItem from "@repo/ui/src/FeaturesItem";

import CTAButton from "@/app/components/common/CTAButton";

import { getCDNUrl } from "@/utils";

export default function Page() {
  const { t } = useTranslation();

  const items = [
    ...Array.from(Array(6).keys()).map((key) => {
      return {
        id: `0${key + 1}`,
        title: t(`home:features.item${key + 1}.title`),
        content: t(`home:features.item${key + 1}.content`),
        url: `/assets/features/step${key + 1}.webp`,
      };
    }),
  ];

  return (
    <>
      <Grid className="mb-8">
        {items.map((feature) => (
          <GridCol key={feature.id} span={{ base: 12, xs: 12, md: 4 }}>
            <FeaturesItem
              step={feature.id}
              title={feature.title}
              content={feature.content}
              url={getCDNUrl(feature.url)}
            />
          </GridCol>
        ))}
      </Grid>
      <CTAButton
        customText={t("home:ctabutton.label")}
        customUrl="/destination"
      />
    </>
  );
}
