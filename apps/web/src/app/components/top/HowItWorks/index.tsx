"use client";

import dynamic from "next/dynamic";

import { Button, Image, Stack, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";

import { Trans, useTranslation } from "react-i18next";

import SetupGuide from "@repo/ui/src/SetupGuide";
import WorkGuide from "@repo/ui/src/WorkGuide";

import CTAButton from "@/app/components/common/CTAButton";

import { getCDNUrl } from "@/utils";

import { useOrderStore } from "@/store";

/*
 * import dynamic components
 */
const DynamicLGUCompatibility = dynamic(
  () => import("@repo/ui/src/modals/LGUCompatibility")
);

const HowItWorks = ({ isLGU }: { isLGU?: boolean }) => {
  const { t } = useTranslation();
  const [lguModal, setLguModal] = useDisclosure();
  const toggleCompatibilityModalOpen: () => void = useOrderStore(
    (state) => state.toggleCompatibilityModalOpen
  );

  return (
    <>
      <Stack className="gap-8">
        <WorkGuide
          count="1"
          iconURl={getCDNUrl("/assets/check-compatibility.webp")}
          header={t("home:work-step1.title")}
          content={
            <>
              <Text
                className="text-sm md:text-base"
                dangerouslySetInnerHTML={{
                  __html: t("home:work-step1.details"),
                }}
              />
              <Button
                size="lg"
                radius="md"
                variant="outline"
                color="app-pink.4"
                bg="white"
                onClick={
                  isLGU ? setLguModal.open : toggleCompatibilityModalOpen
                }
                classNames={{
                  root: "max-w-sm w-full",
                }}
              >
                <Image w={32} src={getCDNUrl("/assets/gm-device.svg")} />
                <Text className="font-bold text-black" ml={10}>
                  {t("common:compatibility.function")}
                </Text>
              </Button>
            </>
          }
        />
        <WorkGuide
          count="2"
          iconURl={getCDNUrl("/assets/get-your-esim.webp")}
          header={t("home:work-step2.title")}
          content={
            <Text
              className="text-sm md:text-base"
              dangerouslySetInnerHTML={{
                __html: t("home:work-step2.details"),
              }}
            />
          }
        />
        <WorkGuide
          count="3"
          iconURl={getCDNUrl("/assets/install-your-esim.webp")}
          header={t("home:work-step3.title")}
          content={
            <>
              <Text
                className="text-sm md:text-base"
                dangerouslySetInnerHTML={{
                  __html: t("home:work-step3.details"),
                }}
              />
              <Text className="self-center">
                <Trans
                  i18nKey="home:work-step3.details2"
                  components={{ a: <a /> }}
                />
              </Text>
              <SetupGuide
                CTAButton={
                  <CTAButton
                    customText={t("home:ctabutton.label")}
                    customUrl="/destination"
                  />
                }
                hideVideo
              />
            </>
          }
        />
      </Stack>
      {isLGU && (
        <DynamicLGUCompatibility
          size="lg"
          title="LGU⁺ eSIM対応機種について"
          opened={lguModal}
          onClose={setLguModal.close}
        ></DynamicLGUCompatibility>
      )}
    </>
  );
};
export default HowItWorks;
