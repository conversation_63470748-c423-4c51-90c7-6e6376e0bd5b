"use client";

/*
 * import modules and libraries
 */
import Link from "next/link";
import { useState, useMemo } from "react";
import Image from "next/image";
import {
  Text,
  Box,
  Flex,
  Stack,
  Group,
  Button,
  rem,
  Combobox,
  useCombobox,
  ComboboxTarget,
  ComboboxDropdown,
  ComboboxOptions,
  ComboboxOption,
  ComboboxEmpty,
  ComboboxChevron,
  TextInput,
  ScrollAreaAutosize,
} from "@mantine/core";
import {
  IconWorldPin,
  IconMoodHeart,
  IconSettingsBolt,
  IconMessageCircle,
  IconWifi,
  IconSearch,
  IconRouter,
  IconQrcode,
} from "@tabler/icons-react";
import { useTranslation, Trans } from "react-i18next";
import { useRouter } from "next/navigation";
/*
 * import constants and utils
 */
import { getCDNUrl } from "@/utils";
import { COUNTRIES } from "@repo/ui/src/constants/regions";
/*
 * import styles
 */
import styles from "./hero.module.css";

export default function Hero() {
  const { t } = useTranslation();
  const router = useRouter();
  const [searchCountry, setSearchCountry] = useState("");
  const [search, setSearch] = useState("");
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const shouldFilterOptions = !COUNTRIES.some(
    (item) =>
      item.value === searchCountry.toLowerCase() &&
      item.jp === search.toLowerCase()
  );
  const filteredOptions = shouldFilterOptions
    ? COUNTRIES.filter(
        (item) =>
          item.jp.includes(search.trim().replaceAll(" ", "").toLowerCase()) ||
          item.value.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          ) ||
          item.hiragana.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          ) ||
          item.katakana.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          )
      )
    : COUNTRIES;

  const options = useMemo(
    () =>
      filteredOptions.map((item) => (
        <ComboboxOption
          value={item.value}
          key={item.value}
          className="text-center md:text-left border-t py-4 md:border-t-0 last:border-b-0 md:py-2 last:mb-10 last:md:mb-0 flex items-center gap-2"
        >
          <Image
            src={getCDNUrl(`/assets/flags/svg/${item.code.toLowerCase()}.svg`)}
            width={24}
            height={24}
            alt={item.code}
            className="border border-gray-100"
          />
          {t(`countries:${item.value}`)}
        </ComboboxOption>
      )),
    [filteredOptions]
  );

  const onSubmit = () => {
    if (searchCountry) {
      router.push("/region/" + searchCountry);
    } else {
      alert("Please choose your travel destination.");
    }
  };

  return (
    <>
      <div className="invisible absolute md:-top-20" id="select-plan" />
      <Box className={`${styles["hero-bg"]} bg-primary`}>
        <Stack
          className="relative h-full items-center justify-center mx-auto md:px-4"
          maw={rem(1080)}
        >
          <Stack className="items-center justify-center p-4 md:py-8 gap-0 h-fit md:bg-black/70 w-full md:rounded-md md:shadow-md">
            <Text className="text-white font-black text-3xl md:text-4xl flex items-center">
              <span>
                <Trans
                  i18nKey="home:top.header"
                  components={{
                    small: (
                      <span className="text-2xl md:text-3xl"/>
                    ),
                  }}
                />
              </span>
              <IconWifi className="rotate-90 lg:h-8 lg:w-8" />
            </Text>
            <table className="table table-auto">
              <tbody>
                <tr>
                  <td>
                    <Image
                      src={getCDNUrl("/assets/hero/esim-icon.svg")}
                      width={48}
                      height={60}
                      alt="100 countries"
                      className="w-[44px] h-[56px] md:w-[70px] md:h-[130px] shrink"
                    />
                  </td>
                  <td>
                    <Image
                      src={getCDNUrl("/assets/hero/esim-logo-white.svg")}
                      width={305}
                      height={69}
                      alt="global mobile esim"
                      className="w-[305px] h-[69px] md:w-[455px] md:h-[104px] shrink-0"
                    />
                  </td>
                </tr>
              </tbody>
            </table>

            <Stack className="w-full items-center justify-center gap-4 flex-col-reverse md:flex-col">
              <Stack className="w-full items-center justify-center gap-4">
                <Combobox
                  store={combobox}
                  withinPortal={true}
                  position="bottom"
                  onOptionSubmit={(val) => {
                    setSearchCountry(val);
                    setSearch(
                      t(`countries:${
                      COUNTRIES.find(
                        (item) => item.value === val
                      )?.value || ""}`)
                    );
                    combobox.closeDropdown();
                  }}
                >
                  <ComboboxTarget>
                    <TextInput
                      size="lg"
                      label={t("home:search.module.title")}
                      placeholder={t("home:empty.field.search.module")}
                      className="flex-1"
                      leftSection={<IconSearch className="text-primary" />}
                      rightSection={
                        <ComboboxChevron
                          onClick={() => combobox.openDropdown()}
                        />
                      }
                      value={search}
                      onChange={(event: any) => {
                        combobox.openDropdown();
                        combobox.updateSelectedOptionIndex();
                        setSearch(event.currentTarget.value);
                      }}
                      classNames={{
                        root: "w-full max-w-[600px] mt-4",
                        label: "!text-white",
                      }}
                      styles={{
                        label: {
                          paddingBottom: "6px",
                          fontSize: "0.875rem",
                          lineHeight: "18px",
                          letterSpacing: "0px",
                          color: "#555555",
                        },
                        input: {
                          boxSizing: "border-box",
                          border: "1px solid #DDDDDD",
                          backgroundColor: "#FFFFFF",
                        },
                      }}
                      onClick={() => combobox.openDropdown()}
                      onFocus={() => combobox.openDropdown()}
                      onBlur={() => {
                        combobox.closeDropdown();
                        if (searchCountry) {
                          setSearch(
                            t(`countries:${
                            COUNTRIES.find(
                              (item) => item.value === searchCountry
                            )?.value || ""}`)
                          );
                        } else {
                          setSearch("");
                        }
                      }}
                    />
                  </ComboboxTarget>

                  <ComboboxDropdown className="shadow-lg z-10">
                    {COUNTRIES.length !== 0 && (
                      <ComboboxOptions>
                        <ScrollAreaAutosize mah={200} type="scroll">
                          {options.length > 0 ? (
                            options
                          ) : (
                            <ComboboxEmpty>
                              {t("home:empty.field.search.result")}
                            </ComboboxEmpty>
                          )}
                        </ScrollAreaAutosize>
                      </ComboboxOptions>
                    )}
                  </ComboboxDropdown>
                </Combobox>
                <Button
                  size="md"
                  color="app-action.4"
                  className="max-w-sm w-full mx-auto text-black"
                  onClick={onSubmit}
                >
                  {t("common:ctabutton")}
                </Button>
              </Stack>

              <Flex className="flex-col md:flex-row md:flex-wrap gap-2 md:gap-0 mt-4 md:max-w-[700px]">
                <Text className="text-white text-base md:text-xl font-bold flex items-center md:w-1/2 md:flex-wrap md:py-2">
                  <IconSettingsBolt
                    className="mb-1 mr-2 md:text-action"
                    size={28}
                  />
                  <Trans
                    i18nKey="home:key.point.1"
                    components={{
                      small: (
                        <span className="text-[#FFF507]" />
                      ),
                    }}
                  />
                </Text>
                <Text className="text-white text-base md:text-xl font-bold flex items-center md:w-1/2 md:flex-wrap md:py-2">
                  <IconMoodHeart
                    className="mb-1 mr-2 md:text-action"
                    size={28}
                  />
                  <Trans
                    i18nKey="home:key.point.2"
                    components={{
                      small: (
                        <span className="text-[#FFF507]" />
                      ),
                    }}
                  />
                </Text>
                <Text className="text-white text-base md:text-xl font-bold flex items-center md:w-1/2 md:flex-wrap md:py-2">
                  <IconMessageCircle
                    className="mb-1 mr-2 md:text-action"
                    size={28}
                  />
                  <Trans
                    i18nKey="home:key.point.3"
                    components={{
                      small: (
                        <span className="text-[#FFF507]" />
                      ),
                    }}
                  />
                </Text>
                <Text className="text-white text-base md:text-xl font-bold flex items-center md:w-1/2 md:flex-wrap md:py-2">
                  <IconWorldPin
                    className="mb-1 mr-2 md:text-action"
                    size={28}
                  />
                  <Trans
                    i18nKey="home:key.point.4"
                    components={{
                      small: (
                        <span className="text-[#FFF507]" />
                      ),
                    }}
                  />
                </Text>
              </Flex>
            </Stack>
          </Stack>
        </Stack>
      </Box>
    </>
  );
}
