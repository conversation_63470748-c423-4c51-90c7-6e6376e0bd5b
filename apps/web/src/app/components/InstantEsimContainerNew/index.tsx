import {
  <PERSON>ert,
  BackgroundImage,
  Badge,
  Box,
  Button,
  Center,
  Image,
  Loader,
  Overlay,
  Paper,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import { useQuery } from "react-query";
import { useCallback, useEffect, useMemo, useState } from "react";
// import WorriedSadSVG from "./worried-sad.svg";
import { AxiosError } from "axios";
import QRSamplePNG from "./qr-sample.webp";
import { IconHeadset, IconBook2, IconQrcode } from "@tabler/icons-react";
import Link from "next/link";
import { IProfile } from "@/interfaces/IProfile";
import Storage from "@/utils/storage";
import { IPlan } from "@/interfaces/IPlan";
import { Trans, useTranslation } from "react-i18next";
import { simpleStringDecode } from "@/utils";
import { ApiService } from "@/api";
import RingLoader from "../common/RingLoader";
import { useParams, useRouter, useSearchParams } from "next/navigation";

// qrCodeImgUrl
const InstantEsimContainerNew = (props: {
  profile?: IProfile;
  orderId: string;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const ABAgent = Storage.getInstance().getFromCookie("ri-values")?.utm_params;
  const [orders, setOrders] = useState<
    { orderId: string; qr: string; plan: IPlan }[] | undefined
  >(undefined);
  const [isEsimInstantFailed, setIsEsimInstantFailed] = useState(false);
  const { t } = useTranslation();
  const sessionSecret = useMemo(() => {
    try {
      return simpleStringDecode(searchParams?.get("uid") + "");
    } catch (err) {
      return "";
    }
  }, [searchParams?.get("uid")]);
  const payload = {
    secret: sessionSecret,
    transaction: searchParams?.get("transaction") + "",
    timestamp: Date.now() + "",
    orderId: params?.orderId + "",
  };
  const isAllEsimFetched = useMemo(() => {
    return !!orders?.every((item) => item.qr);
  }, [orders]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!isAllEsimFetched) {
        setIsEsimInstantFailed(true);
      }
    }, 60000 * 5);
    return () => clearTimeout(timeout);
  }, [isAllEsimFetched]);

  const { data, error } = useQuery(
    "instant-esim" + props.orderId,
    async () => {
      const response = await ApiService.instantEsimNew(payload);
      return response.data?.data;
    },
    {
      refetchInterval: 20000, // Poll every 10 seconds
      enabled: !isAllEsimFetched,
    }
  );

  useEffect(() => {
    setOrders(data?.orders);
  }, [data]);

  useEffect(() => {
    if (
      error instanceof AxiosError &&
      error.response &&
      error.response?.status > 399
    ) {
      alert("Invalid order information or Expired link");
      // router.push("/");
    }
  }, [error]);

  const InfoButtons = () => {
    const infoButtons = [
      {
        icon: <IconBook2 className="text-colorPrimary" />,
        label: "FAQ",
        component: Link,
        href: "/faq",
      },
      // {
      //   icon: <IconHeadset className="text-colorPrimary" />,
      //   label: <Trans i18nKey={"success:contact.us"} />,
      //   href: "/contact-us",
      //   component: Link,
      // },
    ];
    return (
      <Stack className="w-full">
        {infoButtons.map((item, index) => (
          <Button
            href={item.href as string}
            key={index}
            component={item.component}
            leftSection={item.icon}
            className="text-black bg-white w-full rounded-lg"
          >
            {item.label}
          </Button>
        ))}
      </Stack>
    );
  };

  const EsimFailed = () => (
    <Alert
      variant="light"
      color="red"
      title="eSIM 生成エラー"
      icon={<IconQrcode />}
    >
      It looks like we're having trouble creating your eSIM.{" "}
      <Link href="/support/contactus">Contact us</Link> for more information.
    </Alert>
  );
  const planDescription = useCallback((plan: IPlan, type: string, t: any) => {
    const isUnlimited = plan.name === "unlimited";
    const is5gNetwork = plan.network.networkGeneration === "5G";

    const planKey = isUnlimited
      ? type === "regular" && is5gNetwork
        ? "common:cart.menu.item.unlimited.5g"
        : type === "regular" && !is5gNetwork
        ? "cart.menu.item.unlimited.4g"
        : is5gNetwork
        ? "common:cart.menu.item.unlimitedinsured5g"
        : "common:cart.menu.item.unlimitedinsured4g"
      : type === "regular"
      ? "common:cart.menu.item.fixed"
      : "common:cart.menu.item.fixedinsured";

    return `${t(planKey, {
      dataVolume: plan.dataId,
      country: plan.country.name,
    })} ${isUnlimited ? "/" : "-"} ${t("common:siminfo-card.validity.unit", {
      count: plan.validityDays,
    })}`;
  }, []);

  return (
    <>
      {isEsimInstantFailed ? null : isAllEsimFetched ? (
        <Text className="text-center animate__animated animate__fadeIn max-w-xl">
          Your QR code has been successfully generated! We have sent your QR
          code & instructions to your email as well. Thank you for your purchase
          and wishing you safe travels!
        </Text>
      ) : (
        <Text className="text-center max-w-xl">
          Please wait a few minutes for the QR code to generate! We will also
          send your QR code & instructions to your email.
        </Text>
      )}
      <Stack className="items-center">
        {isEsimInstantFailed ? (
          <EsimFailed />
        ) : (
          <Stack>
            {orders?.map((order) => (
              <Stack className="gap-1 md:gap-4" key={order.orderId}>
                <Paper className="rounded-lg p-2 md:p-3 bg-colorSecondary">
                  <Center mb={"xs"}>
                    <Badge
                      color="#BC002D"
                      variant="transparent"
                      radius={"sm"}
                      size="sm"
                      p={"md"}
                      leftSection={
                        <Image
                          src={`/assets/flags/16x16/${order.plan.country.code?.toLowerCase()}.png`}
                        />
                      }
                      className=" animate__animated animate__fadeIn"
                    >
                      {planDescription(order.plan, "regular", t)} #
                      {order.orderId}
                    </Badge>
                  </Center>
                  <Box className="relative h-80 w-80 mx-auto mb-2">
                    {order.qr ? (
                      <Image
                        src={order.qr}
                        className="w-full h-full animate__animated animate__fadeIn"
                        alt="Image for QR code."
                      />
                    ) : (
                      <>
                        <BackgroundImage
                          src={QRSamplePNG.src}
                          radius="sm"
                          className="w-full h-full"
                        />
                        <Overlay
                          backgroundOpacity={0.6}
                          blur={5}
                          className="z-0"
                        />
                        <Stack className="z-[200] px-5 absolute inset-0 flex items-center justify-center gap-6">
                          <Loader
                            width={24}
                            height={24}
                            loaders={{
                              ...Loader.defaultLoaders,
                              ring: RingLoader,
                            }}
                            type="ring"
                          />
                          <Text className="text-white text-xl text-center">
                            {" "}
                            <Trans i18nKey="success:waiting.txt1">
                              We are generating QR, Please wait...
                            </Trans>
                          </Text>
                          <Text className="text-white text-sm text-center">
                            <Trans i18nKey="success:waiting.txt2">
                              This may take a moment.
                            </Trans>
                          </Text>
                        </Stack>
                      </>
                    )}
                  </Box>
                </Paper>
                <Button
                  className="bg-colorPrimary min-w-full md:min-w-[187px] self-center"
                  onClick={() => {
                    window.open(
                      ApiService.getPdfDownLoadUrl({
                        ...payload,
                        orderId: order.orderId,
                      }),
                      "_blank"
                    );
                  }}
                  disabled={isEsimInstantFailed || !order.qr}
                  loading={!order.qr}
                  loaderProps={{ type: "dots" }}
                >
                  Download PDF #{order.orderId}
                </Button>
              </Stack>
            ))}
          </Stack>
        )}

        <Link
          className="self-center mt-1 md:mt-5"
          href={`/${ABAgent ? `?${ABAgent}` : ""}`}
        >
          <Trans i18nKey={"success:return.home"}>Return home</Trans>
        </Link>
        <Paper className="bg-colorSecondary rounded-lg md:max-w-[520px] p-3 md:py-8 md:px-14">
          <Stack className="items-center">
            <Title className="text-xl md:text-2xl">
              *
              <Trans i18nKey={"success:information.title"}>
                Important Information
              </Trans>
              *
            </Title>
            <Text className="text-center">
              <Trans i18nKey={"success:information.txt"}>
                Please save your order I.D. It will be needed when contacting
                our customer support if you run into any issues.
              </Trans>
            </Text>
            <InfoButtons />
          </Stack>
        </Paper>
      </Stack>
    </>
  );
};
export default InstantEsimContainerNew;
