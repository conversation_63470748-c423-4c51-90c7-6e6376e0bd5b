import { Modal, ModalProps } from "@mantine/core";
import React from "react";
import styles from "./fullPageModal.module.css";

interface FullPageProps {
  top?: string;
  hideHeaderBorder?: boolean;
  showOverlay?: boolean;
  children: React.ReactNode;
}

export type FullPageModalProps = FullPageProps & ModalProps;

// TODO: remove this, already existing in packages/ui
const FullPageModal = (props: FullPageModalProps) => {
  return (
    <Modal
      {...props}
      overlayProps={{
        display: props.showOverlay ? "block" : "none",
        zIndex: 1000,
      }}
      className="overflow-hidden"
      classNames={{
        content: styles["content"],
        title: styles["title"],
        header: props.hideHeaderBorder
          ? styles["header-hidden"]
          : styles["header"],
        inner: styles["inner"],
        body: styles["body"],
      }}
      size="md"
      opened={props.opened}
      title={props.title || "Currency"}
      transitionProps={{ transition: "pop" }}
    >
      {props.children}
    </Modal>
  );
};
export default FullPageModal;
