.content {
    max-height: 100vh !important;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    font-weight: 700;
    padding: 0.5rem;
    font-size: 1rem;
}

.header {
    border-bottom: 1px solid #dfdfdf;
}

.header-hidden {
    border-bottom: 0px;
}

.inner {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    padding-right: 0rem !important;
    padding-left: 0rem !important;
    z-index: 1000;
}

.body {
     height: 100vh;
     background: white;
     padding-top: 0px !important;
     padding-bottom: 0px !important;
     padding-right: 0rem !important;
     padding-left: 0rem !important;
}