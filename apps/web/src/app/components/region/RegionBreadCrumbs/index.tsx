'use client';

/*
 * import modules and libraries
 */
import Link from "next/link";
import { useTranslation, Trans } from "react-i18next";
import { Box } from "@mantine/core";
/*
 * import components
 */
import AppBreadCrumb from "@/app/components/common/AppBreadCrumb";
import SectionContent from "@repo/ui/src/common/SectionContent";
/*
 * import constants and utils
 */
import { countryAlias, reverseKebabCase } from "@/utils";

const CountryHeader = ({
  country
}: {
  country: string;
}) => {
  const { t } = useTranslation();

  return (
    <SectionContent noHeader noFooter>
      <Box className="pt-6">
        <AppBreadCrumb>
          <Link href={"/"}>{t("common:navbar.home.label")}</Link>
          <Link href={"#"}>
            <Trans i18nKey={`countries:${country}`}>
              {countryAlias(reverseKebabCase(country))}
            </Trans>
          </Link>
        </AppBreadCrumb>
      </Box>
    </SectionContent>
  );
};

export default CountryHeader;
