"use client";

/*
 * import modules and libraries
 */
import { Image, ImageProps } from "@mantine/core";
/*
 * import helpers
 */
import { getCDNUrl, getCountryImage, countryNameException } from "@/utils";

export const CoverImage = ({
  isLGU,
  ...props
}: ImageProps & { isLGU?: boolean }) => {
  return (
    <Image
      maw={"auto"}
      mx="auto"
      {...props}
      src={getCDNUrl(
        isLGU
          ? "/assets/banner/korea-lgu-banner.webp"
          : getCountryImage(countryNameException(props.src?.toLowerCase()) || "")
      )}
    />
  );
};
export default CoverImage;
