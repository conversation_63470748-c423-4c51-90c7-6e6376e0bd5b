"use client";

/*
 * import modules and libraries
 */
import { Text, Stack, Overlay } from "@mantine/core";
/*
 * import components
 */
import SectionContent from "@repo/ui/src/common/SectionContent";
import CoverImage from "@/app/components/region/CoverImage";
import CountryHeader from "@/app/components/region/CountryHeader";
import PlanList from "@/app/components/region/PlanList";
import Actions from "@/app/components/region/Actions";
/*
/*
 * import constants and utils
 */
import { regionalOrCountry } from "@/utils";
/*
 * import interfaces
 */
import type { IPlan } from "@repo/ui/src/interfaces/IPlan";
import type { INetwork } from "@/interfaces/INetwork";
import { useShoppingCart } from "@/store";
import { useEffect } from "react";

export default function PlanContainer({
  isNeedRegistration,
  isLGU,
  country,
  regionData,
}: {
  isNeedRegistration?: boolean;
  isLGU?: boolean;
  country: string;
  regionData: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  } | null;
}) {
  const [items, addToCart, showCartMenu, toggleCartMenu, quotation] =
    useShoppingCart((s) => [
      s.items,
      s.addToCart,
      s.showCartMenu,
      s.toggleCartMenu,
      s.quotation,
    ]);
  useEffect(() => {
    if (quotation?.orders.length) {
      toggleCartMenu(true);
    }
  }, []);

  return (
    <>
      {showCartMenu && <Overlay style={{ zIndex: 9, position: "fixed" }} />}
      {regionData ? (
        <>
          <SectionContent noHeader noFooter small>
            <CoverImage
              maw={"auto"}
              h={isLGU ? undefined : "13rem"}
              mx="auto"
              src={country}
            />
            <CountryHeader
              isLGU={isLGU}
              isRegional={
                regionalOrCountry(regionData.countryProfile?.name) !== "Country"
              }
              country={country}
              subcountries={regionData.countryProfile?.subCountries || "[]"}
            />
          </SectionContent>
          <SectionContent noHeader small>
            <Stack className="gap-2">
              <Actions
                isLGU={isLGU}
                isRegional={
                  regionalOrCountry(regionData.countryProfile?.name) !==
                  "Country"
                }
                network={regionData.network}
                country={country}
                subcountries={regionData.countryProfile?.subCountries || "[]"}
              />
              <PlanList
                props={regionData}
                isNeedRegistration={isNeedRegistration}
                isLGU={isLGU}
                country={country}
              />
            </Stack>
          </SectionContent>
        </>
      ) : (
        <SectionContent small noHeader>
          <Text className="text-center text-base pt-8">No data plan found</Text>
        </SectionContent>
      )}
    </>
  );
}
