"use client";

/*
 * import modules and libraries
 */
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect } from "react";

import { em } from "@mantine/core";
import { useMediaQuery, useScrollIntoView } from "@mantine/hooks";

import { uniqueId } from "lodash";

import { IPlan } from "@gmesim/fe-interfaces/src";

import SharedPlanContainer from "@repo/ui/src/PlanContainer";

/*
 * import interfaces
 */
import type { INetwork } from "@/interfaces/INetwork";

import { useOrderStore, useShoppingCart } from "@/store";
import { useProfile } from "@/store/UserStore";

export default function PlanContainer({
  agent,
  isNeedRegistration,
  isLGU,
  isJapan,
  country,
  ...props
}: {
  agent?: string;
  isNeedRegistration?: boolean;
  isLGU?: boolean;
  isJapan?: boolean;
  country: string;
  regionData: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  } | null;
}) {
  const isMobileSize = useMediaQuery(`(max-width: ${em(768)})`, true, {
    getInitialValueInEffect: false,
  });
  const { scrollIntoView, targetRef } = useScrollIntoView<HTMLDivElement>({
    offset: isMobileSize ? 70 : 250,
    duration: 250,
  });

  const searchParams = useSearchParams();
  const via = searchParams?.get("via");
  const scroll = searchParams?.get("scroll");

  useEffect(() => {
    if (scroll) {
      scrollIntoView({
        alignment: "start",
      });
    }
  }, [country]);

  const setSelectedPlan = useOrderStore((state) => state.setSelectedPlan);
  const setSelectedPlanType = useOrderStore(
    (state) => state.setSelectedPlanType
  );
  const profile = useProfile((s) => s.profile);
  const [items, addToCart, showCartMenu, toggleCartMenu, quotation] =
    useShoppingCart((s) => [
      s.items,
      s.addToCart,
      s.showCartMenu,
      s.toggleCartMenu,
      s.quotation,
    ]);

  const handleAddToCart = useCallback((selectedPlan: IPlan) => {
    addToCart({
      id: Date.now() + "" + uniqueId(),
      plan: selectedPlan,
      quantity: 1,
    });
    toggleCartMenu(true);
  }, []);

  return (
    <SharedPlanContainer
      service="GM_EN"
      onAddToCartClick={handleAddToCart}
      //@ts-expect-error
      setSelectedPlan={setSelectedPlan}
      //@ts-expect-error
      setSelectedPlanType={setSelectedPlanType}
      agent={agent}
      isLGU={isLGU}
      isJapan={isJapan}
      isNeedRegistration={isNeedRegistration}
      profile={profile}
      country={country}
      {...props}
    />
  );
}
