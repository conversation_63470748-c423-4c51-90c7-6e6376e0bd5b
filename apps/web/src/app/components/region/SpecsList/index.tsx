"use client";

/*
 * import modules and libraries
 */
import { useMemo } from "react";

/*
 * import components
 */
import { Anchor, Flex, Image, List, Text, ThemeIcon } from "@mantine/core";

import { useTranslation } from "react-i18next";

/*
 * import data types
 */
import { INetwork } from "@repo/ui/src/interfaces/INetwork";

/*
 * import utils
 */
import {
  capitalizeFirstLetter,
  countryAlias,
  getCDNUrl,
  regionalOrCountry,
} from "@/utils";

const SpecsList = (props: {
  isModal?: boolean;
  isLGU?: boolean;
  country: string;
  network: INetwork[];
  onClick?: (option: { type: string; value: Array<string> }) => void;
}) => {
  const { t } = useTranslation();
  const specNetwork =
    props.country === "japan" ? [props.network[0]] : props.network;
  const specs = useMemo(
    () => [
      {
        name: t("region:coverage"),
        value:
          regionalOrCountry(props.country) === "Country" ? (
            t(
              `countries:${props.country}`,
              capitalizeFirstLetter(countryAlias(props.country || ""))
            )
          ) : (
            <Anchor
              td={"underline"}
              onClick={() =>
                props.onClick?.({
                  type: "specs",
                  value: [],
                })
              }
            >
              {t("region:availablecountries")}
            </Anchor>
          ),
        iconURL: "coverage.png",
        invert: true,
      },
      {
        name: t("region:network", "NETWORK"),
        value: `${specNetwork?.map?.((item) => `${item.name} ${item.networkGeneration}`?.replace?.(/^\-/, "")).join(" - ")}`,
        iconURL: "specs-network.png",
      },
      {
        name: t("region:plantype", "PLAN TYPE"),
        value: props.isLGU
          ? t("region:datawithcalls", "Data and can make and receive calls")
          : t("region:dataonlynocalls", "Data only (No Calls)"),
        iconURL: "specs-type.png",
      },
      {
        name: t("region:e-kyc", "eKYC (IDENTITY VERIFICATION)"),
        value: t("region:notrequired", "Not required"),
        iconURL: "specs-ekyc.png",
      },
      {
        name: t("region:activationperiod.title", "ACTIVATION PERIOD"),
        value: props.isLGU
          ? t(
              "region:activationperiod.desc",
              "Can be activated within 180 days of purchase."
            ).replaceAll("30", "90")
          : t(
              "region:activationperiod.desc",
              "Can be activated within 180 days of purchase."
            ),
        iconURL: "specs-activation.png",
      },
      {
        name: t("region:other", "OTHER"),
        value: t("region:nonrefundable", "Non-refundable"),
        iconURL: "specs-other.png",
      },
    ],
    []
  );

  return (
    <List
      size="sm"
      classNames={{
        root: "mb-4",
        item: `${props.isModal ? "px-4 py-3" : "p-0"} border-b`,
      }}
    >
      {specs.map((spec, index) => (
        <List.Item
          key={index}
          icon={
            <ThemeIcon
              color={spec.invert ? "dark" : "transparent"}
              size={24}
              radius="xl"
            >
              <Image src={getCDNUrl(`/assets/${spec.iconURL}`)} />
            </ThemeIcon>
          }
        >
          <Flex direction={"column"}>
            <Text className="text-neutral text-xs font-semibold">
              {spec.name}
            </Text>
            <Text className="text-base">{spec.value}</Text>
          </Flex>
        </List.Item>
      ))}
    </List>
  );
};
export default SpecsList;
