"use client";

/*
 * import modules and libraries
 */
import { Box, Button, Group, Text, Transition, Container } from "@mantine/core";
import { Trans, useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
/*
 * import constants and utils
 */
import { useOrderStore } from "@/store";
import { Currency } from "@/utils/currency";
import { countryAlias, reverseKebabCase } from "@/utils";
/*
 * import interfaces
 */
import type { IPlan } from "@/interfaces/IPlan";

const CheckoutFooter = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const selectedPlan: IPlan | undefined = useOrderStore(
    (state) => state.selectedPlan
  );

  return (
    <Container
      size={700}
      classNames={{
        root: `${selectedPlan ? "py-4" : "p-0"} sticky bottom-0 z-30 bg-white`,
      }}
    >
      <Transition
        mounted={Boolean(selectedPlan)}
        transition="slide-up"
        duration={800}
        timingFunction="ease"
      >
        {(transitionStyles) => (
          <Box style={transitionStyles} className="bg-white">
            {selectedPlan && (
              <>
                <Text className="text-sm font-bold">
                  <Trans i18nKey={`countries:${selectedPlan?.country.name}`}>
                    {countryAlias(reverseKebabCase(selectedPlan?.country.name))}
                  </Trans>
                </Text>
                <Group
                  align="center"
                  justify="space-between"
                  className="mb-4 m-0 w-[unset]"
                >
                  <Text className="text-sm">
                    {t("common:siminfo-card.validity.unit_one", {
                      count: selectedPlan?.validityDays,
                      defaultValue: "{{ count }} day",
                      defaultValue_other: "{{ count }} days",
                    })}{" "}
                    - {selectedPlan?.dataVolume}
                    {selectedPlan?.dataUnit}/
                    {t("common:siminfo-card.perday.unit")}
                  </Text>
                  <Text className="text-base font-bold">
                    {Currency.formatToSelected({
                      xe: selectedPlan?.xe ?? {},
                      price: selectedPlan?.price ?? 0,
                    })}
                  </Text>
                </Group>
              </>
            )}
            <Button
              fullWidth
              size="lg"
              color="app-pink.5"
              onClick={() => {
                router.push(`/checkout/plan/${selectedPlan?.id}`);
              }}
              disabled={!selectedPlan}
            >
              {t("region:proceedtocheckout", "PROCEED TO CHECKOUT")}
            </Button>
          </Box>
        )}
      </Transition>
    </Container>
  );
};

export default CheckoutFooter;
