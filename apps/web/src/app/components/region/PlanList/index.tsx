"use client";

/*
 * import modules and libraries
 */
import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";



import { Box, Group, SegmentedControl, Stack, Text, Title } from "@mantine/core";



import { IconClockHour10, IconSettings, IconWorld } from "@tabler/icons-react";
import { uniqueId } from "lodash";
import { Trans, useTranslation } from "react-i18next";



/*
 * import components
 */
import PlanForm from "@repo/ui/src/PlanForm";
import TrafficData from "@repo/ui/src/TrafficData";
import { IDiscount } from "@repo/ui/src/interfaces/ICoupon";
/*
 * import interfaces
 */
import type { IPlan, IPlans } from "@repo/ui/src/interfaces/IPlan";



import { countryAlias, reverseKebabCase } from "@/utils";
/*
 * import constants and utils
 */
import { getCDNUrl } from "@/utils";
import { Currency } from "@/utils/currency";



import { ApiService } from "@/api";



import type { INetwork } from "@/interfaces/INetwork";



import { useOrderStore, useShoppingCart } from "@/store";
import { useProfile } from "@/store/UserStore";





const DataType = {
  FIXED_DAY: "FIXED_DAY",
  PER_DAY: "PER_DAY",
};
async function getQuotationAPI(planIds: any[], coupon?: string) {
  try {
    const response = await ApiService.getQuotation({
      products: planIds,
      couponId: coupon,
    });

    return {
      data: response.data?.data,
    };
  } catch (err) {
    console.log(err);
    return {
      discountedPrice: null,
    };
  }
}
export default function PlanList({
  props,
  isNeedRegistration,
  isLGU,
  country,
}: {
  props: {
    plans: {
      [key: string]: IPlan[];
    };
    countryProfile: IPlan["country"];
    network: INetwork[];
  };
  isNeedRegistration?: boolean;
  isLGU?: boolean;
  country?: string;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const profile = useProfile((s) => s.profile);
  const [discount, setDiscount] = useState<IDiscount>();
  const { t } = useTranslation();
  const setSelectedPlan = useOrderStore((state) => state.setSelectedPlan);
  const setSelectedPlanType = useOrderStore(
    (state) => state.setSelectedPlanType
  );
  const [selectedData, setSelectedData] = useState<string>();
  const [activePlanList, setActivePlanList] = useState(
    props.plans?.[DataType.PER_DAY] ? DataType.PER_DAY : DataType.FIXED_DAY
  );
  const [items, addToCart, showCartMenu, toggleCartMenu, quotation] =
    useShoppingCart((s) => [
      s.items,
      s.addToCart,
      s.showCartMenu,
      s.toggleCartMenu,
      s.quotation,
    ]);

  const allow5GActivePlanList =
    country === "japan" && activePlanList === `${DataType.PER_DAY}_${"5G"}`;
  const separateDataPlans = (dataType: string) => {
    try {
      let organizedPlans: IPlans[] = [];
      const originalPlans = props.plans?.[dataType]?.filter((plan: any) => {
        if (
          plan?.country?.name === "japan" &&
          plan?.network?.type === "ROAMING"
        ) {
          return false;
        }
        return true;
      });

      for (let x = 0; x < originalPlans?.length || 0; x += 1) {
        const itemIndex = organizedPlans.findIndex(
          (item: IPlans) =>
            item.dataId === originalPlans[x].dataId ||
            (originalPlans[x].name === "unlimited" &&
              item.dataId === originalPlans[x].name)
        );

        if (itemIndex > -1) {
          organizedPlans[itemIndex].plans.push(originalPlans[x]);
        } else {
          organizedPlans.push({
            dataId:
              originalPlans[x].name === "unlimited"
                ? originalPlans[x].name
                : originalPlans[x].dataId,
            validityDays: originalPlans[x].validityDays,
            plans: [originalPlans[x]],
          });
        }
      }

      return organizedPlans;
    } catch (err) {
      console.log(err);
      return [];
    }
  };

  const fixedDayPlansMemo = useMemo(() => {
    return separateDataPlans(DataType.FIXED_DAY);
  }, [props.plans]);

  const perDayPlansMemo = useMemo(() => {
    return separateDataPlans(DataType.PER_DAY);
  }, [props.plans]);

  const countryName = (
    <Trans i18nKey={`countries:${country}`}>
      {countryAlias(reverseKebabCase(country))}
    </Trans>
  );

  const handleAddToCart = useCallback((selectedPlan: IPlan) => {
    addToCart({
      id: Date.now() + "" + uniqueId(),
      //@ts-expect-error
      plan: selectedPlan,
      quantity: 1,
    });
    toggleCartMenu(true);
  }, []);

  const planForm = useCallback(
    () => (
      <PlanForm
        onAddToCartClick={handleAddToCart}
        plans={
          activePlanList === DataType.PER_DAY
            ? perDayPlansMemo
            : fixedDayPlansMemo
        }
        setSelectedData={setSelectedData}
        selectPlanEvent={selectPlanEvent}
        activePlanList={activePlanList}
        formatPrice={Currency.formatToSelectedNoCode}
        selectedCode={Currency.getSelectedCurrency()}
        dark
        country={countryName}
      />
    ),
    [activePlanList, fixedDayPlansMemo, perDayPlansMemo]
  );

  const trafficeData = useCallback(
    () => (
      <Box className="mt-4 traffic-card-container">
        <TrafficData
          dataLimit={(selectedData as string).replaceAll(" ", "")}
          getCDNUrl={getCDNUrl}
        />
      </Box>
    ),
    [selectedData]
  );

  // const selectPlanEvent = (planId: string) => {
  //   const selectedPlan: IPlan | undefined = props.plans?.[
  //     activePlanList.toUpperCase()
  //   ]?.find((plan) => plan.planId === planId);

  //   if (selectedPlan) {
  //     setSelectedPlan(selectedPlan);
  //   }

  //   // router.push(`/checkout/plan/${selectedPlan?.id}`);
  // };

  const selectPlanEvent = async (planId: string, getQuotation?: boolean) => {
    let selectedPlan: IPlan | undefined = undefined;

    if (allow5GActivePlanList) {
      selectedPlan = props.plans?.[DataType.PER_DAY]?.find(
        (plan) => plan.planId === planId
      );
    } else {
      selectedPlan = props.plans?.[activePlanList.toUpperCase()]?.find(
        (plan) => plan.planId === planId
      );
    }

    if (getQuotation) {
      if (selectedPlan?.id) {
        const res = await getQuotationAPI([
          {
            optionId: `${selectedPlan?.id}`,
          },
        ]);
        setDiscount(res?.data);
      }
    } else {
      if (selectedPlan) {
        //@ts-expect-error
        setSelectedPlan(selectedPlan);
      }

      router.push(`/checkout/plan/${selectedPlan?.id}?country=${selectedPlan?.country?.name}`);
    }
  };

  useEffect(() => {
    setSelectedPlanType(activePlanList);
  }, [activePlanList]);

  return (
    <Box className="relative">
      {fixedDayPlansMemo?.length !== 0 && perDayPlansMemo?.length !== 0 && (
        <SegmentedControl
          w={"100%"}
          radius="lg"
          defaultValue={activePlanList}
          value={activePlanList}
          onChange={setActivePlanList}
          color="app-pink.4"
          classNames={{
            root: "p-0 mt-1 bg-white",
            control: "rounded-none border border-gray-200",
            indicator: "rounded-none",
            label: "text-primary data-[active=true]:text-white",
          }}
          data={[
            {
              value: DataType.PER_DAY,
              label: (
                <Title order={2} className="text-base">
                  {t("chooseplan:unlimited.plan")}
                </Title>
              ),
            },
            {
              value: DataType.FIXED_DAY,
              label: (
                <Title order={2} className="text-base">
                  {t("chooseplan:fixed.data")}
                </Title>
              ),
            },
          ]}
        />
      )}

      <Stack className="my-5">
        <Stack className="gap-2">
          <Group className="flex-nowrap">
            <IconClockHour10 className="text-primary shrink-0" />
            <Text className="text-sm">{t("chooseplan:point.1")}</Text>
          </Group>
          <Group className="flex-nowrap">
            <IconSettings className="text-primary shrink-0" />
            <Text className="text-sm">{t("chooseplan:point.2")}</Text>
          </Group>
          <Group className="flex-nowrap">
            <IconWorld className="text-primary shrink-0" />
            <Text className="text-sm">{t("chooseplan:point.3")}</Text>
          </Group>
        </Stack>

        {isNeedRegistration && (
          <Text className="text-sm">
            <span
              dangerouslySetInnerHTML={{
                __html: t("home:note.caution.hongkongtaiwancontent")
                  .replaceAll("li>", "span>")
                  .replaceAll("<br/>", "") as string,
              }}
            />{" "}
            <br />
          </Text>
        )}
        {isLGU && (
          <Text className="text-sm">
            {t("chooseplan:korea.lgu.explanation")}
          </Text>
        )}
      </Stack>

      {planForm()}
      {/* TODO: put it back after translation */}
      {/* {
        selectedData &&
        activePlanList === DataType.FIXED_DAY && 
        selectedData !== "unlimited" && (
          trafficeData()
        )
      } */}
    </Box>
  );
}