"use client";

/*
 * import modules and libraries
 */
import dynamic from "next/dynamic";

import {
  <PERSON><PERSON>,
  <PERSON>,
  Collapse,
  ScrollAreaAutosize,
  Stack,
  Text,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";

import { IconMinus, IconPlus } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

/*
 * import interfaces
 */
import type { INetwork } from "@/interfaces/INetwork";

/*
 * import dynamic components
 */
const DynamicRegionalCountries = dynamic(
  () => import("@repo/ui/src/modals/RegionalCountries")
);
const DynamicSpecsList = dynamic(
  () => import("@/app/components/region/SpecsList")
);

const Actions = ({
  isLGU,
  isRegional,
  country,
  network,
  subcountries,
}: {
  isLGU?: boolean;
  isRegional?: boolean;
  country: string;
  network: INetwork[];
  subcountries: string;
}) => {
  const { t } = useTranslation();
  const [specs, setSpecs] = useDisclosure(false);
  const [regionalCountriesModal, setRegionalCountriesModalModal] =
    useDisclosure();

  return (
    <>
      <Stack>
        <Button
          fullWidth
          size="xs"
          radius="md"
          variant="outline"
          color="app-pink.4"
          bg="white"
          className=""
          classNames={{
            root: "shadow hover:bg-white relative",
            section: "absolute right-2",
          }}
          rightSection={
            specs ? (
              <IconMinus
                size={20}
                className="bg-primary rounded-full text-white"
              />
            ) : (
              <IconPlus
                size={20}
                className="bg-primary rounded-full text-white"
              />
            )
          }
          onClick={setSpecs.toggle}
        >
          <Text className="text-sm font-bold text-black">
            {t("region:about-esim")}
          </Text>
        </Button>
      </Stack>
      <Collapse in={specs} className="mb-4">
        <Card className="rounded-lg p-4 shadow-lg">
          <ScrollAreaAutosize mah={384} type="scroll">
            <DynamicSpecsList
              onClick={(options) => {
                if (options.type === "specs") {
                  setRegionalCountriesModalModal.open();
                }
              }}
              isLGU={isLGU}
              country={country}
              network={network}
              isModal
            />
            <Text className="text-neural text-xs font-bold">
              {t("region:about.notes-title")}
            </Text>
            <Text
              className="text-xs"
              dangerouslySetInnerHTML={{
                __html: isLGU
                  ? (t("region:about.notes") as string).replaceAll("30", "90")
                  : (t("region:about.notes") as string),
              }}
            />
          </ScrollAreaAutosize>
        </Card>
      </Collapse>
      {isRegional && (
        <DynamicRegionalCountries
          title={t("region:coverage")}
          opened={regionalCountriesModal}
          onClose={setRegionalCountriesModalModal.close}
          subcountries={subcountries}
        />
      )}
    </>
  );
};
export default Actions;
