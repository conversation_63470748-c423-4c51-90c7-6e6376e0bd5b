'use client';

/*
 * import modules and libraries
 */
import dynamic from "next/dynamic";
import { useTranslation, Trans } from "react-i18next";
import {
  Box,
  Text,
  Title,
  Blockquote,
  Group,
  Flex,
  Button,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { IconInfoCircle } from "@tabler/icons-react";
/*
 * import constants and utils
 */
import { countryAlias, reverseKebabCase } from "@/utils";
/*
 * import dynamic components
 */
const DynamicRegionalCountries = dynamic(
  () => import("@repo/ui/src/modals/RegionalCountries"),
);

const CountryHeader = ({
  isLGU,
  isRegional,
  country,
  subcountries,
}: {
  isLGU?: boolean;
  isRegional?: boolean;
  country: string;
  subcountries: string;
}) => {
  const { t } = useTranslation();
  const [regionalCountriesModal, setRegionalCountriesModalModal] = useDisclosure();

  return (
    <>
      <Box>
        <Blockquote classNames={{
          root: "p-0 pl-4 mt-2 bg-transparent"
        }}>
          <Box>
            <Text className="text-sm">{t("region:internationtravelesim")}</Text>
            <Group className="items-center gap-0 md:gap-2">
              <Flex className="md:gap-1 flex-col md:flex-row items-start md:items-end">
                <Title
                  order={2}
                  className="text-3xl font-bold"
                >
                  <Trans i18nKey={`countries:${country}`}>
                    {countryAlias(reverseKebabCase(country))}
                  </Trans>{" "}
                  eSIM
                </Title>
              </Flex>
              {isRegional && (
                <Button
                  variant="subtle"
                  color="dark"
                  classNames={{
                    root: "px-0",
                    section: "mx-0",
                    label: "h-[unset]"
                  }}
                  leftSection={
                    <IconInfoCircle />
                  }
                  onClick={setRegionalCountriesModalModal.open}
                >
                  <Title
                    order={2}
                    className="font-semibold text-base"
                  >
                    {t("region:see-regions")}
                  </Title>
                </Button>
              )}
            </Group>
          </Box>
        </Blockquote>
      </Box>
      <DynamicRegionalCountries 
        title={t("region:coverage")}
        opened={regionalCountriesModal}
        onClose={setRegionalCountriesModalModal.close}
        subcountries={subcountries}
      />
    </>
  );
};

export default CountryHeader;
