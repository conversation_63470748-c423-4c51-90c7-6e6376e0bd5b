"use client";

/*
 * import modules and libraries
 */
import { useState, useMemo } from "react";
import { useTranslation, Trans } from "react-i18next";
import {
  Tabs,
  TabsList,
  TabsTab,
  TabsPanel,
  Title,
  Grid,
  GridCol,
  Skeleton,
  Text,
  Stack,
  Group,
  Button,
} from "@mantine/core";
import { useSearchParams } from "next/navigation";
import { IconX } from "@tabler/icons-react";
/*
 * import components
 */
import CountryCard from "@repo/ui/src/CountryCard";
import CountrySearchInput from "@/app/components/common/CountrySearchInput";
/*
 * import constants and utils
 */
import {
  getCDNUrl,
  normalizeAndKebabCase,
  countryNameException,
} from "@/utils";
import { Currency } from "@/utils/currency";
import { COUNTRIES, ICountriesSearch } from "@repo/ui/src/constants/regions";
/*
 * import interfaces
 */
import type { IPlan } from "@/interfaces/IPlan";

export default function Countries({
  data,
  allData,
}: {
  data: {
    name: string;
    code?: string;
    plans: IPlan[];
  }[];
  allData: IPlan[];
}) {
  const searchParams = useSearchParams();
  const { t } = useTranslation();
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string | null>(
    (searchParams?.get("tab") as string) || "AS"
  );

  const onSearch = (searchValue: string) => {
    setSearch(searchValue);
  };

  const getFilteredCountries = (
    list1: IPlan[],
    list2: ICountriesSearch[],
    isUnion: boolean = true
  ) =>
    list1.filter(
      (a) => isUnion === list2.some((b) => a.countryCode === b.code)
    );

  const searchResults = useMemo(() => {
    if (search) {
      setLoading(true);

      let searchResults = COUNTRIES.filter(
        (item) =>
          item.jp.includes(search.trim().replaceAll(" ", "").toLowerCase()) ||
          item.value.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          ) ||
          item.hiragana.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          ) ||
          item.katakana.includes(
            search.trim().replaceAll(" ", "").toLowerCase()
          )
      );
      const filteredCountries = getFilteredCountries(
        allData,
        searchResults,
        true
      );

      setLoading(false);
      return filteredCountries;
    } else {
      return [];
    }
  }, [search]);

  return (
    <>
      <Text
        component="p"
        className="font-bold text-sm md:text-base text-center"
      >
        {/* {/* 世界<span className="text-primary">149か国</span>所以上で利用可能な海外eSIMを検索できます。<br />
        <span className="text-primary">ヨーロッパ、アジア、世界周遊など、2か国以上をカバーする周遊プランや、<br className="hidden md:inline-block"/>
        韓国、台湾、ハワイ、イタリア、中国、アメリカ、</span><br className="hidden md:inline-block"/>
        などの人気観光地で無制限のデータ通信をご利用いただけます。<br />
        海外eSIMを利用して、もっと気軽に海外旅行を楽しみましょう。 */}
        <Trans
          i18nKey="destination:description"
          components={{
            color: <span className="text-primary" />,
          }}
        />
      </Text>
      <Stack className="gap-4">
        <CountrySearchInput onSearch={onSearch} />

        {!search ? (
          <Tabs
            id="select-plan"
            w={"100%"}
            value={activeTab}
            onChange={setActiveTab}
            color="app-pink.4"
            variant="pills"
            classNames={{
              list: "mb-2",
              tab: "rounded-md data-[active=true]:bg-primary bg-gray-100",
              root: "md:mt-14",
            }}
          >
            <TabsList>
              {data.map((item) => (
                <TabsTab
                  value={item.code || item.name}
                  key={item.code || item.name}
                  color="app-pink.4"
                >
                  <Title
                    order={3}
                    className="text-sm md:text-base data-[selected=true]:text-primary"
                  >
                    {t(item.name)}
                  </Title>
                </TabsTab>
              ))}
            </TabsList>
            {data.map((item) =>
              item.plans ? (
                <TabsPanel
                  key={item.code || item.name}
                  value={item.code || item.name}
                  pt="xs"
                >
                  <Grid gutter={"1rem"}>
                    {item.plans?.map?.((plan, index) => (
                      <GridCol
                        key={index}
                        span={{ base: 12, xs: 12, sm: 4, md: 3 }}
                      >
                        <CountryCard
                          img={getCDNUrl(
                            `/assets/destination/${countryNameException(
                              //@ts-ignore
                              plan.originalName || plan.country
                            )}.webp`
                          )}
                          price={plan.price + ""}
                          url={`/region/${normalizeAndKebabCase(
                            //@ts-ignore
                            plan?.originalName ||
                              plan?.country?.name ||
                              plan?.country
                          )}${
                            normalizeAndKebabCase(
                              //@ts-ignore
                              plan?.originalName ||
                                plan?.country?.name ||
                                plan?.country
                            ) === "japan"
                              ? "?scroll=plan"
                              : ""
                          }`}
                          name={
                            countryNameException(
                              plan?.originalName || plan?.country?.name
                            ) as string
                          }
                          originalName={countryNameException(plan.originalName)}
                          selectedCode={Currency.getSelectedCurrency()}
                          convertedPrice={Currency.formatToSelectedNoCode({
                            xe: plan.xe,
                            price: +plan.price,
                          })}
                          dark
                        />
                      </GridCol>
                    ))}
                  </Grid>
                </TabsPanel>
              ) : null
            )}
          </Tabs>
        ) : (
          <>
            {loading ? (
              <>
                <Skeleton
                  className="md:mt-14"
                  height={14}
                  w={124}
                  radius="md"
                />
                <Grid gutter={"1rem"}>
                  {...Array.from(Array(4).keys()).map((key) => (
                    <GridCol span={{ base: 12, xs: 12, sm: 4, md: 3 }}>
                      <Skeleton height={250} radius="md" />
                    </GridCol>
                  ))}
                </Grid>
              </>
            ) : (
              <>
                <Group className="justify-between items-end">
                  <Text className="text-gray-400 text-sm italic md:mt-14">
                    検索結果数：{searchResults.length}
                  </Text>
                  <Button variant="subtle" onClick={() => setSearch("")}>
                    検索をクリア
                    <IconX />
                  </Button>
                </Group>
                <Grid gutter={"1rem"}>
                  {searchResults?.map?.((plan, index) => (
                    <GridCol
                      key={index}
                      span={{ base: 12, xs: 12, sm: 4, md: 3 }}
                    >
                      <CountryCard
                        //@ts-ignore
                        img={getCDNUrl(
                          `/assets/destination/${countryNameException(
                            //@ts-ignore
                            plan.originalName || plan.country
                          )}.webp`
                        )}
                        price={plan.price + ""}
                        //@ts-ignore
                        url={`/region/${normalizeAndKebabCase(
                          //@ts-ignore
                          plan?.originalName ||
                            plan?.country?.name ||
                            plan?.country
                        )}${
                          normalizeAndKebabCase(
                            //@ts-ignore
                            plan?.originalName ||
                              plan?.country?.name ||
                              plan?.country
                          ) === "japan"
                            ? "?scroll=plan"
                            : ""
                        }`}
                        name={
                          countryNameException(
                            plan?.originalName || plan?.country?.name
                          ) as string
                        }
                        originalName={countryNameException(plan.originalName)}
                        selectedCode={Currency.getSelectedCurrency()}
                        convertedPrice={Currency.formatToSelectedNoCode({
                          xe: plan.xe,
                          price: +plan.price,
                        })}
                        dark
                      />
                    </GridCol>
                  ))}
                </Grid>
              </>
            )}
          </>
        )}

        {/* TODO: use this when revert to accordion view for SP */}
        {/* <Accordion
          defaultValue={activeTab}
          hiddenFrom="md"
          classNames={{
            content: "p-0",
            control: "active:bg-primary data-[active=true]:rounded-b-none",
            label: "py-3",
            item: "border-b-0",
          }}
        >
          {data.map((item) =>
            item.plans ? (
              <Accordion.Item
                value={item.code || item.name}
                key={item.code || item.name}
                className="pb-2"
              >
                <Accordion.Control className="relative text-white text-left rounded-t-lg py-0 bg-primary rounded-b-lg hover:bg-primary">
                  <div className="text-md font-bold py-0">{t(item.name)}</div>
                </Accordion.Control>
                <Accordion.Panel>
                  <Grid
                    gutter={0}
                    classNames={{
                      root: "bg-secondary border-solid border-2 border-primary rounded-b-lg",
                      inner: "p-1",
                    }}
                  >
                    {item.plans?.map?.((plan, index) => (
                      <GridCol
                        key={index}
                        className="p-1"
                        span={{ base: 6, xs: 6, sm: 4, md: 3 }}
                      >
                        <CountryCard
                          //@ts-ignore
                          img={getCDNUrl(`/assets/destination/${countryNameException(plan.originalName || plan.country)}.webp`)}
                          price={plan.price + ""}
                          //@ts-ignore
                          url={`/region/${normalizeAndKebabCase(plan?.originalName || plan?.country?.name || plan?.country)}${normalizeAndKebabCase(plan?.originalName || plan?.country?.name || plan?.country) === "japan" ? "?scroll=plan" : ""}`}
                          name={countryNameException(plan?.originalName || plan?.country?.name) as string}
                          originalName={countryNameException(plan.originalName)}
                          selectedCode={Currency.getSelectedCurrency()}
                          convertedPrice={Currency.formatToSelectedNoCode({
                            xe: plan.xe,
                            price: +plan.price,
                          })}
                        />
                      </GridCol>
                    ))}
                  </Grid>
                </Accordion.Panel>
              </Accordion.Item>
            ) : null
          )}
        </Accordion> */}
      </Stack>
    </>
  );
}
