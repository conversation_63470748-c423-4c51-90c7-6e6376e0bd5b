@layer tailwind {
  @tailwind base;
}
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

a {
  color: -webkit-link;
  text-decoration: underline;
}

ul {
  list-style-type: disc;
}

.text-hour {
  font-size: 9px;
  line-height: 14px;
}
.text-phone {
  line-height: 21px;
}

.grecaptcha-badge {
  /* bottom: 150px !important; */
  display: none !important;
}

.success {
  background-image: url("/assets/icon/circle.png");
  background-color:rgba(255,255,255,0.8);
  background-blend-mode:lighten;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.partial {
  background-image: url("/assets/icon/triangle.png");
  background-color:rgba(255,255,255,0.8);
  background-blend-mode:lighten;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.fail {
  background-image: url("/assets/icon/cross.png");
  background-color:rgba(255,255,255,0.8);
  background-blend-mode:lighten;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}