import { IPlan } from "@/interfaces/IPlan";
import Storage from "./storage";

export class Currency {
  static getSelectedCurrency() {
    const defaultCurrency = {
      code: "USD",
      name: "US Dollar",
      sign: "$"
    };
    return (
      Storage.getInstance().get("currency") ?? defaultCurrency
    );
  }
  static formatToSelected(plan: Pick<IPlan, "xe" | "price">) {
    const currency = Currency.getSelectedCurrency();
    let currencyCode = currency?.code;
    if (!currency) currencyCode = "JPY";
    return Currency.formatCurrency(+(plan.xe[currencyCode] || 0), currencyCode, {
      currencyStyle: true,
    });
  }
  static formatToSelectedNoCode(plan: Pick<IPlan, "xe" | "price">) {
    const currency = Currency.getSelectedCurrency();
    let currencyCode = currency?.code;
    if (!currency) currencyCode = "JPY";
    return Currency.formatCurrency(+(plan.xe[currencyCode] || 0), currencyCode, {
      currencyStyle: false,
    });
  }
  static formatCurrency = (
    amount: number,
    currency: string,
    options = {
      currencyStyle: false,
    }
  ) => {
    let locale = "en-US";
    if (currency === "JPY") {
      locale = "ja-JP";
    }
    if (currency === "EUR") {
      locale = "eu-EU";
    }
    if (currency === "AUD") {
      locale = "en-AU";
    }
    if (currency === "CAD") {
      locale = "en-CA";
    }
    if (currency === "TWD") {
      locale = "zh-Hant-TW";
    }
    if (currency === "GBP") {
      locale = "en-GB";
    }
    if (currency === "HKD") {
      locale = "en-HK";
    }
    if (currency === "CNY") {
      locale = "zh-CN";
    }
    const config = {
      currency,
    };
    if (options?.currencyStyle) {
      //@ts-ignore
      config.style = "currency";
    }
    return new Intl.NumberFormat(locale, config).format(amount);
  };
}
