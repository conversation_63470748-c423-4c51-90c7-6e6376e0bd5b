import { jwtDecode } from "jwt-decode";
import Storage from "@/utils/storage";
import { AUTH_TOKEN_NAME } from "@/app/constants";
import { IOauthResponse } from "@/interfaces/IOauthResponse";
import { format } from "date-fns";

/* eslint-disable turbo/no-undeclared-env-vars */

export const getCDNUrl = (file: string) => `${file}`;
// `${process.env.NEXT_PUBLIC_CDN_URL}global-esim${file}`;

export const countryAlias = (countryName: string) => {
  switch (countryName?.toLowerCase()) {
    case "usa":
      return "united states";
    case "uk":
      return "united kingdom";
    case "korea":
      return "south korea";
    case "saudi arabia":
      return "saudiarabia";
    default:
      return countryName;
  }
};

export function simpleStringDecode(string: string) {
  const decodedString = atob(string);
  return decodedString.split("").reverse().join("");
}
export function simpleStringEncode(string: string) {
  const strings = string?.split("").reverse().join("");
  return btoa(strings);
}

export function normalizeAndKebabCase(str: string) {
  // Normalize the string by converting it to lowercase, removing special characters, and replacing non-alphanumeric characters with a space
  const normalizedStr = str
    .toLowerCase()
    .replace(/[^\w\s]+/g, "")
    .replace(/[^a-z0-9]+/g, " ");

  // Convert the normalized string to kebab case
  const kebabCaseStr = normalizedStr.trim().replace(/\s+/g, "");

  return kebabCaseStr;
}

export function reverseKebabCase(str: any) {
  if (!str) return;
  // Split the kebab case string into an array of words
  const words = str.split("-");

  // Capitalize the first letter of each word
  const reversedWords = words.map(
    (word: any) => word.charAt(0).toUpperCase() + word.slice(1)
  );

  // Join the reversed words with spaces
  const reversedStr = reversedWords.join(" ");

  return reversedStr;
}

export const getCountryImage = (countryName: string) => {
  return `/assets/destination/${countryName}.webp`;
};

export function capitalizeFirstLetter(string: string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export const regionalOrCountry = (countryName: string) => {
  if (["worldwide", "asia", "europe"].includes(countryName?.toLowerCase()))
    return "Regional";
  return "Country";
};

export const countryNameException = (countryName: string | undefined) => {
  const countryNameException: { [key: string]: string } = {};

  const foundCountryNameException = Object.entries(countryNameException).find(
    ([key, value]) => value === countryName || key === countryName
  );

  if (foundCountryNameException) {
    const [key, value] = foundCountryNameException;
    if (countryName === value) return key;
    if (countryName === key) return value;
  }
  return countryName;
};

export const getDecodedCode = (): {
  [key: string]: string | boolean | number;
} => {
  const token = Storage.getInstance().get(AUTH_TOKEN_NAME) as IOauthResponse;
  return jwtDecode(token.accessToken);
};

export const withBasePath = (url: string) => {
  return process.env.NEXT_PUBLIC_BASE_PATH
    ? process.env.NEXT_PUBLIC_BASE_PATH + "/" + url
    : "/" + url;
};

export const formatDate = (date: string | Date) => {
  return format(new Date(date), "yyyy.MM.dd");
};

export const formatDateAndTime = (date: string | Date) => {
  return format(new Date(date), "yyyy.MM.dd HH:mm");
};

export const getBytes = (planId: string) => {
  {
    /* @ts-ignore */
  }
  if (planId.includes("GB")) return +planId.split("GB")[0] * Math.pow(1024, 3);
  {
    /* @ts-ignore */
  }
  if (planId.includes("kB")) return +planId.split("kB")[0] * 1024;
  {
    /* @ts-ignore */
  }
  if (planId.includes("Bytes")) return +planId.split("Bytes")[0];

  {
    /* @ts-ignore */
  }
  return +planId.split("MB")[0] * Math.pow(1024, 2);
};

export const isDev = () => process.env.NODE_ENV !== "production";
