{"validation": {"minlength1": {"firstname": "<PERSON> unang pangalan ay dapat na hindi bababa sa 1 character ang haba", "lastname": "Dapat na hindi bababa sa 1 character ang haba ng apelyido", "profileimage": "<PERSON> larawan sa profile ay dapat na hindi bababa sa 1 character ang haba"}, "minlength2": {"defaultpaymentmethod": "Dapat na hindi bababa sa 2 character ang haba ng default na paraan ng pagbabayad", "locale": "Dapat na hindi bababa sa 2 character ang haba ng default na lokal (wika).", "referral": "Dapat na hindi bababa sa 2 character ang haba ng default na referral", "website": "Dapat na hindi bababa sa 2 character ang haba ng default na website"}, "wrongusernamepassword": "Maling username o password", "failed": "Nabigo ang input data sa mga validation rules.", "error4009": {"label": "An account already exists with the same email address logged in using social login. Please use your social login to sign in."}}, "invalid": {"email": "Hindi wasto ang ibinigay na email address.", "password": "Hindi wastong password ang i<PERSON>k", "resetpasswordcode": "Di-wastong reset password code ang i<PERSON>sok", "verifyemailcode": "Di-wastong verify na email code ang ipinasok", "firstname": "<PERSON>-wastong pangalan ang i<PERSON>k", "lastname": "Di-wastong a<PERSON>do ang i<PERSON>", "username": {"0": "H", "1": "i", "2": "n", "3": "d", "4": "i", "5": " ", "6": "t", "7": "u", "8": "g", "9": "m", "10": "a", "11": " ", "12": "a", "13": "n", "14": "g", "15": " ", "16": "i", "17": "b", "18": "i", "19": "n", "20": "i", "21": "g", "22": "a", "23": "y", "24": " ", "25": "n", "26": "a", "27": " ", "28": "u", "29": "s", "30": "e", "31": "r", "32": "n", "33": "a", "34": "m", "35": "e", "36": " ", "37": "s", "38": "a", "39": " ", "40": "m", "41": "g", "42": "a", "43": " ", "44": "k", "45": "i", "46": "n", "47": "a", "48": "k", "49": "a", "50": "i", "51": "l", "52": "a", "53": "n", "54": "g", "55": "a", "56": "n", "57": ".", "password": "Hindi tugma ang kombinasyon ng username at password."}, "newpasswordsameoldpassword": "<PERSON> bagong password ay hindi maaaring pareho sa lumang password", "plan": "Di-wastong plano ang i<PERSON>sok", "order": {"dataoptionid": "Hindi wastong data ang inilagay na opsyon", "quantity": "Di-wastong dami ng order", "topupid": "Di-wastong top up <PERSON> ang <PERSON>", "iccid": "Di-wastong order na ipinasok ng ICCID"}, "usernotconfirmed": "Hindi nakumpirma ang user", "request": "Mali ang porma ng request o may kulang na kinakailangang mga parameter.", "json": "Hindi tamang JSON format ang body ng request.", "phone": {"found": "Umiiral na ang numero ng telepono."}, "token": "Hindi wasto o paso na ang ibinigay na authentication token.", "operation": "Hindi maisagawa ang hiniling na <PERSON>yon sa mapag<PERSON>kunan."}, "unable": {"resendverificationemail": "Hindi maipadalang muli ang email sa pagpapatunay", "forgotpassword": "Hindi maipadala ang nakalimutang password na email", "changepassword": "Hindi mapalitan ang password", "confirmpassword": "Hindi makumpirma ang password"}, "login": {"social": {"email": "Error sa pag-log in sa pamamagitan ng email account", "social": "Error sa pag-log in sa pamamagitan ng social login"}, "wrongpassword": "Maling password ang i<PERSON>k", "error": {"wrong": {"password": "Mali ang iyong password o email address."}, "not-authorized-exception": "Hindi wasto ang email o password. <PERSON>is<PERSON><PERSON><PERSON> muli."}}, "alreadyexists": {"email": "Nakarehistro na ang account na may email na ito", "phone": "Nakarehistro na ang numero ng telepono sa ibang user"}, "user": {"notfound": "Hindi nahanap ang user", "not": {"found": "Hindi umiiral ang tinukoy na user."}}, "esim": {"subscribe": "Error sa pag-subscribe/pagbili ng eSIM", "checkusage": "Error sa pagkuha ng katayuan ng paggamit ng biniling eSIM", "unauthorizedaccess": "Hindi awtorisadong pag-access sa mga biniling detalye ng eSIM", "getorders": "Error sa pag-log in sa pamamagitan ng email account", "notfound": "hindi nahanap ang eSIM"}, "plan": {"notfound": "Hindi nakita ang plano", "allplans": "Error sa pagkuha ng lahat ng plano"}, "request": {"missingparameters": "<PERSON> ka<PERSON>ingan ay mali o nawawala ang mga kinakailangang parameter", "invalidformat": "Ang nilalaman ng kahilingan ay wala sa wastong JSON na format", "duplicatecontent": "Umiiral na ang content", "missingtoken": "Nawawala ang token ng pagpapatotoo sa mga header ng kahilingan", "expiredtoken": "Ang ibinigay na token ng pagpapatotoo ay hindi wasto o nag-expire", "insufficientpermissions": "Walang sapat na pahintulot ang user para isagawa ang pagkilos", "accessdenied": "Hindi pinapayagan ang user na i-access ang hiniling na mapagkukunan", "userblocked": "Na-block o na-deactivate ang user account", "resourcenotfound": "Ang hiniling na mapagkukunan ay hindi umiiral", "resourceconflict": "Sumasalungat ang hiniling na pagkilos sa kasalukuyang estado ng mapagkukunan", "validationfailed": "Nabigo ang input ng data sa mga panuntunan sa pagpapatunay", "operationconflict": "<PERSON> hiniling na operasyon ay hindi maisagawa sa mapag<PERSON>kunan"}, "server": {"unexpectederror": "Naganap ang hindi inaa<PERSON>hang error sa server", "databaseoperationfailed": "Nabigo ang pagpapatakbo ng database", "externalserviceerror": "Nagkaroon ng error habang nakikipag-ugnayan sa isang panlabas na serbisyo"}, "code": {"4000": "Umiiral na ang email address."}, "email": {"not": {"verified": "Hindi pa na-verify ang email address."}}, "account": {"not": {"idp": {"manual": "Ang account ay ginawa gamit ang social login."}}}, "content": {"conflict": "May duplicate na content."}, "missing": {"token": "Wala ang authentication token sa request headers."}, "insufficient": {"permissions": "Walang sapat na pahintulot ang user para isagawa ang aksyon."}, "access": {"denied": "Hindi pinapayagan ang user na ma-access ang hiniling na mapagkukunan."}, "blocked": {"user": "Nakablock o nadedeactivate ang user account."}, "resource": {"not": {"found": "Hindi umiiral ang hiniling na mapag<PERSON>kunan."}}, "product": {"not": {"found": "Hindi umiiral ang tinukoy na produkto."}}, "duplicate": {"resource": "Umiiral na ang mapagkukunan na may kaparehong identifier."}, "conflicting": {"state": "Ang hiniling na aksyon ay sumasalungat sa kasalukuyang estado ng mapagkukunan."}, "internal": {"server": {"error": "May hindi in<PERSON><PERSON><PERSON> error na nangyari sa server."}}, "database": {"error": "Nabigo ang operasyon sa database."}, "external": {"service": {"error": "Nagkaroon ng error sa pakikipag-ugnayan sa external na serbisyo."}}, "coupons": {"countryspecificcoupon": "Ang kupon na ito ay hindi pinapayagan para sa rehiyong ito.", "couponactive": "<PERSON> kupon ay hindi aktibo.", "couponexpired": "<PERSON> kupon ay nag-expire na.", "coupononwrongplan": "Ang code ng kupon ay hindi magagamit para sa planong ito.", "couponperpersonusageexceeded": "Ang code ng kupon ay maaari lamang gamitin isang beses.", "couponusageexceeded": "Naabot na ang limitasyon ng paggamit ng kupon code.", "loyalcustomercoupon": "Nagamit mo na ang isang kupon para sa kampanyang ito.", "onlyoncecampaign": "Nagamit mo na ang isang kupon para sa kampanyang ito.", "selfonlycoupon": "Di-wastong kupon."}}