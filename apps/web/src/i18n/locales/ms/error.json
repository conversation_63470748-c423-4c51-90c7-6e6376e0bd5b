{"validation": {"minlength1": {"firstname": "Nama pertama mesti sekurang-kurangnya 1 aksara panjang.", "lastname": "Nama keluarga mesti sekurang-kurangnya 1 aksara panjang.", "profileimage": "Imej profil mesti sekurang-kurangnya 1 aksara panjang."}, "minlength2": {"defaultpaymentmethod": "<PERSON><PERSON><PERSON> p<PERSON>aran lalai mesti sekurang-kurangnya 2 aksara panjang.", "locale": "<PERSON><PERSON><PERSON> bahasa lalai mesti sekurang-kurangnya 2 aksara panjang.", "referral": "R<PERSON>ju<PERSON> lalai mesti sekurang-kurangnya 2 aksara panjang.", "website": "Laman web lalai mesti sekurang-kurangnya 2 aksara panjang."}, "wrongusernamepassword": "<PERSON>a pengguna atau kata laluan salah.", "failed": "Data input gagal memenuhi peraturan pengesahan.", "error4009": {"label": "An account already exists with the same email address logged in using social login. Please use your social login to sign in."}}, "invalid": {"email": "<PERSON><PERSON><PERSON> e-mel yang diberikan tidak sah.", "password": "<PERSON>a laluan yang dimasukkan tidak sah.", "resetpasswordcode": "Kod tetapan semula kata laluan yang dimasukkan tidak sah.", "verifyemailcode": "Kod pengesahan e-mel yang dimasukkan tidak sah.", "firstname": "<PERSON>a pertama yang dimasukkan tidak sah.", "lastname": "<PERSON>a keluarga yang dimasukkan tidak sah.", "username": {"0": "N", "1": "a", "2": "m", "3": "a", "4": " ", "5": "p", "6": "e", "7": "n", "8": "g", "9": "g", "10": "u", "11": "n", "12": "a", "13": " ", "14": "y", "15": "a", "16": "n", "17": "g", "18": " ", "19": "d", "20": "i", "21": "b", "22": "e", "23": "r", "24": "i", "25": "k", "26": "a", "27": "n", "28": " ", "29": "t", "30": "i", "31": "d", "32": "a", "33": "k", "34": " ", "35": "m", "36": "e", "37": "m", "38": "e", "39": "n", "40": "u", "41": "h", "42": "i", "43": " ", "44": "k", "45": "e", "46": "p", "47": "e", "48": "r", "49": "l", "50": "u", "51": "a", "52": "n", "53": ".", "password": "Kombinasi nama pengguna dan kata laluan yang diberikan tidak sepadan."}, "newpasswordsameoldpassword": "<PERSON>a laluan baharu tidak boleh sama seperti kata laluan lama.", "plan": "<PERSON><PERSON>n yang dimasukkan tidak sah.", "order": {"dataoptionid": "Pilihan data yang dimasukkan tidak sah.", "quantity": "<PERSON><PERSON><PERSON> pesanan tidak sah.", "topupid": "ID tambah nilai yang dimasukkan tidak sah.", "iccid": "Nombor ICCID pesanan yang dimasukkan tidak sah."}, "usernotconfirmed": "Pengguna belum disahkan.", "request": "<PERSON><PERSON><PERSON><PERSON> adalah salah atau parameter yang diperlukan hilang.", "json": "Kandungan permintaan tidak dalam format JSON yang sah.", "phone": {"found": "Nombor telefon sudah wujud."}, "token": "Token pengesahan yang diberikan tidak sah atau telah tamat tempoh.", "operation": "Operasi yang diminta tidak boleh dilakukan pada sumber."}, "unable": {"resendverificationemail": "Tidak dapat menghantar semula e-mel pengesahan.", "forgotpassword": "Tidak dapat menghantar e-mel lupa kata laluan.", "changepassword": "Tidak dapat menukar kata laluan.", "confirmpassword": "Tidak dapat mengesahkan kata laluan."}, "login": {"social": {"email": "<PERSON><PERSON> semasa log masuk melalui akaun e-mel.", "social": "<PERSON><PERSON> semasa log masuk melalui log masuk sosial."}, "wrongpassword": "<PERSON>a laluan yang dimasukkan salah.", "error": {"wrong": {"password": "<PERSON>a laluan atau alamat e-mel anda mungkin salah."}, "not-authorized-exception": "E-mel atau kata laluan tidak sah. Sila cuba lagi."}}, "alreadyexists": {"email": "<PERSON><PERSON><PERSON> dengan e-mel ini telah didaft<PERSON>an.", "phone": "Nombor telefon telah didaftarkan kepada pengguna lain."}, "user": {"notfound": "Pengguna tidak ditemui.", "not": {"found": "Pengguna yang ditentukan tidak wujud."}}, "esim": {"subscribe": "<PERSON><PERSON> semasa melanggan/membeli eSIM.", "checkusage": "<PERSON><PERSON> mendapatkan status penggunaan eSIM yang dibeli.", "unauthorizedaccess": "Akses tidak dibenarkan kepada butiran eSIM yang dibeli.", "getorders": "<PERSON><PERSON> semasa log masuk melalui akaun e-mel.", "notfound": "eSIM tidak ditemui."}, "plan": {"notfound": "<PERSON>elan tidak di<PERSON>.", "allplans": "<PERSON><PERSON> mendapa<PERSON> semua pelan."}, "request": {"missingparameters": "Permintaan salah atau tiada parameter yang diperlukan.", "invalidformat": "Kandungan permintaan bukan dalam format JSON yang sah.", "duplicatecontent": "Kandungan sudah wujud.", "missingtoken": "Token pengesahan tiada dalam header permintaan.", "expiredtoken": "Token pengesahan yang diberikan tidak sah atau telah tamat tempoh.", "insufficientpermissions": "Pengguna tidak mempunyai kebenaran yang mencukupi untuk melakukan tindakan ini.", "accessdenied": "Pengguna tidak dibenarkan untuk mengakses sumber yang diminta.", "userblocked": "<PERSON><PERSON><PERSON> pengguna telah disekat atau dinyahaktifkan.", "resourcenotfound": "Sumber yang diminta tidak wujud.", "resourceconflict": "<PERSON><PERSON>an yang diminta bercanggah dengan keadaan semasa sumber.", "validationfailed": "Data input gagal mematuhi peraturan pengesahan.", "operationconflict": "Operasi yang diminta tidak boleh dilakukan pada sumber."}, "server": {"unexpectederror": "<PERSON><PERSON> yang tidak dijangka berlaku pada pelayan.", "databaseoperationfailed": "Operasi pangkalan data gagal.", "externalserviceerror": "<PERSON><PERSON> berlaku semasa berk<PERSON>i dengan perkhid<PERSON>n luar."}, "code": {"4000": "<PERSON><PERSON><PERSON> e-mel sudah wujud."}, "email": {"not": {"verified": "<PERSON><PERSON><PERSON> e-mel belum disahkan."}}, "account": {"not": {"idp": {"manual": "Akaun dibuat menggunakan log masuk sosial."}}}, "content": {"conflict": "Kandungan duplikat."}, "missing": {"token": "Token pengesahan tidak terdapat dalam tajuk permintaan."}, "insufficient": {"permissions": "Pengguna tidak mempunyai kebenaran yang mencukupi untuk melaksanakan tindakan ini."}, "access": {"denied": "Pengguna tidak dibenarkan mengakses sumber yang diminta."}, "blocked": {"user": "<PERSON><PERSON><PERSON> pengguna telah disekat atau dinyahaktifkan."}, "resource": {"not": {"found": "Sumber yang diminta tidak wujud."}}, "product": {"not": {"found": "Produk yang ditentukan tidak wujud."}}, "duplicate": {"resource": "Sumber dengan pengecam yang sama sudah wujud."}, "conflicting": {"state": "<PERSON><PERSON>an yang diminta bercanggah dengan keadaan semasa sumber."}, "internal": {"server": {"error": "<PERSON><PERSON> tidak dijangka berlaku pada pelayan."}}, "database": {"error": "Operasi pangkalan data gagal."}, "external": {"service": {"error": "<PERSON><PERSON> berlaku semasa berk<PERSON>i dengan perkhid<PERSON>n luar."}}, "coupons": {"countryspecificcoupon": "Kupon tidak dibenarkan untuk kawasan ini.", "couponactive": "Kupon tidak aktif.", "couponexpired": "<PERSON><PERSON>n telah tamat tempoh.", "coupononwrongplan": "Kod kupon tidak tersedia untuk pelan ini.", "couponperpersonusageexceeded": "Kod kupon hanya boleh digunakan sekali.", "couponusageexceeded": "Had pengg<PERSON>an kod kupon telah melebihi.", "loyalcustomercoupon": "Anda sudah menggunakan kupon untuk kempen ini.", "onlyoncecampaign": "Anda sudah menggunakan kupon untuk kempen ini.", "selfonlycoupon": "Kupon tidak sah."}}