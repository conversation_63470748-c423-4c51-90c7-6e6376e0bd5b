{"validation": {"minlength1": {"firstname": "お名前は1文字以上で登録してください", "lastname": "お名前は1文字以上で登録してください", "profileimage": "プロフィール画像は少なくとも 1 文字の長さである必要があります"}, "minlength2": {"defaultpaymentmethod": "デフォルトの支払い方法は 2 文字以上である必要があります", "locale": "デフォルトのロケール (言語) は 2 文字以上である必要があります", "referral": "デフォルトの紹介は少なくとも 2 文字の長さである必要があります", "website": "デフォルトの Web サイトの長さは 2 文字以上である必要があります"}, "wrongusernamepassword": "ユーザー名またはパスワードが間違っています。再度ご確認のうえ、正しい情報を入力してください。", "failed": "入力データが検証ルールに適合しません。", "error4009": {"label": "同じメールアドレスで登録されたアカウントが既に存在します。ソーシャルログインでログインしてください。"}}, "invalid": {"email": "提供されたメールアドレスは無効です。", "password": "バスワードが無効です", "resetpasswordcode": "リセットコードが無効です", "verifyemailcode": "確認コードが無効です", "firstname": "無効な名が入力されました", "lastname": "無効な姓が入力されました", "username": {"0": "提", "1": "供", "2": "さ", "3": "れ", "4": "た", "5": "ユ", "6": "ー", "7": "ザ", "8": "ー", "9": "名", "10": "は", "11": "要", "12": "件", "13": "を", "14": "満", "15": "た", "16": "し", "17": "て", "18": "い", "19": "ま", "20": "せ", "21": "ん", "22": "。", "password": "提供されたユーザー名とパスワードの組み合わせが一致しません。"}, "newpasswordsameoldpassword": "新しいパスワードと古いパスワードと同じにすることはできません", "plan": "プラン入力が無効です", "order": {"dataoptionid": "データオプション入力が無効です", "quantity": "注文数量が無効です", "topupid": "トップアップID入力が無効です", "iccid": "注文 ICCID 入力が無効です"}, "usernotconfirmed": "ユーザーが確認できませんでした", "request": "リクエストが不正確または必要なパラメーターが欠けています。", "json": "リクエストの内容が正しいJSON形式ではありません。", "phone": {"found": "電話番号はすでに存在します。"}, "token": "提供された認証トークンは無効または期限切れです。", "operation": "リソースに対して要求された操作を実行できません。"}, "unable": {"resendverificationemail": "確認メールを再送信できません", "forgotpassword": "パスワードを忘れた場合のメールを送信できません", "changepassword": "パスワードを変更できません", "confirmpassword": "パスワードを確認できません"}, "login": {"social": {"email": "メールアカウントによるログインエラー", "social": "ソーシャルログインによるログインエラー"}, "wrongpassword": "パスワードに誤りがあります", "error": {"wrong": {"password": "パスワードまたはメールアドレスが間違っている可能性があります。"}, "not-authorized-exception": "無効なメールアドレスまたはパスワードです。もう一度お試しください。"}}, "alreadyexists": {"email": "このメールアドレスはすでにアカウント登録されています", "phone": "この電話番号はすでにアカウント登録されています"}, "user": {"notfound": "ユーザーが見つかりません", "not": {"found": "指定されたユーザーは存在しません。"}}, "esim": {"subscribe": "eSIMの購読/購入エラー", "checkusage": "購入したeSIMの使用状況取得エラー", "unauthorizedaccess": "購入した eSIM の詳細への不正アクセス", "getorders": "メールアカウントによるログインエラー", "notfound": "eSIMが見つかりません"}, "plan": {"notfound": "プランが見つかりません", "allplans": "すべてのプランを取得中にエラーが発生しました"}, "request": {"missingparameters": "リクエストの形式が不正であるか、必要なパラメータが欠落しています", "invalidformat": "リクエスト本文が有効な JSON 形式ではありません", "duplicatecontent": "コンテンツはすでに存在します", "missingtoken": "リクエストヘッダーに認証トークンがありません", "expiredtoken": "提供された認証トークンが無効か期限切れです", "insufficientpermissions": "ユーザーにはアクションを実行するための十分な権限がありません", "accessdenied": "ユーザーは要求されたリソースへのアクセスを許可されていません", "userblocked": "ユーザーアカウントがブロックまたは無効化されています", "resourcenotfound": "要求されたリソースは存在しません", "resourceconflict": "要求されたアクションはリソースの現在の状態と競合します", "validationfailed": "入力データが検証ルールに失敗しました", "operationconflict": "要求された操作はリソースに対して実行できません"}, "server": {"unexpectederror": "サーバーで予期しないエラーが発生しました", "databaseoperationfailed": "データベース操作に失敗しました", "externalserviceerror": "外部サービスとの通信中にエラーが発生しました"}, "code": {"4000": "メールアドレスはすでに存在します。"}, "email": {"not": {"verified": "メールアドレスが確認されていません。"}}, "account": {"not": {"idp": {"manual": "アカウントはソーシャルログインを使用して作成されました。"}}}, "content": {"conflict": "重複するコンテンツです。"}, "missing": {"token": "認証トークンがリクエストヘッダーにありません。"}, "insufficient": {"permissions": "この操作を実行する権限が不足しています。"}, "access": {"denied": "リクエストされたリソースへのアクセスは許可されていません。"}, "blocked": {"user": "ユーザーアカウントがブロックまたは無効化されています。"}, "resource": {"not": {"found": "リクエストされたリソースは存在しません。"}}, "product": {"not": {"found": "指定された製品は存在しません。"}}, "duplicate": {"resource": "同じ識別子を持つリソースがすでに存在します。"}, "conflicting": {"state": "リクエストされた操作はリソースの現在の状態と矛盾しています。"}, "internal": {"server": {"error": "サーバーで予期しないエラーが発生しました。"}}, "database": {"error": "データベース操作に失敗しました。"}, "external": {"service": {"error": "外部サービスとの通信中にエラーが発生しました。"}}, "coupons": {"countryspecificcoupon": "この地域ではクーポンは使用できません。", "couponactive": "クーポンは有効ではありません。", "couponexpired": "クーポンの有効期限が切れています。", "coupononwrongplan": "このプランではクーポンコードを使用できません。", "couponperpersonusageexceeded": "クーポンコードは一度しか使用できません。", "couponusageexceeded": "クーポンコードの使用回数上限を超えています。", "loyalcustomercoupon": "このキャンペーンではすでにクーポンを使用しています。", "onlyoncecampaign": "このキャンペーンではすでにクーポンを使用しています。", "selfonlycoupon": "無効なクーポンです。"}}