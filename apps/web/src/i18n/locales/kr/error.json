{"validation": {"minlength1": {"firstname": "이름은 최소 1자 이상이어야 합니다.", "lastname": "성은 최소 1자 이상이어야 합니다.", "profileimage": "프로필 이미지는 최소 1자 이상이어야 합니다."}, "minlength2": {"defaultpaymentmethod": "기본 결제 방법은 최소 2자 이상이어야 합니다.", "locale": "기본 언어 설정은 최소 2자 이상이어야 합니다.", "referral": "기본 추천 코드는 최소 2자 이상이어야 합니다.", "website": "기본 웹사이트는 최소 2자 이상이어야 합니다."}, "wrongusernamepassword": "사용자 이름 또는 비밀번호가 잘못되었습니다.", "failed": "입력된 데이터가 유효성 검사 규칙을 통과하지 못했습니다.", "error4009": {"label": "An account already exists with the same email address logged in using social login. Please use your social login to sign in."}}, "invalid": {"email": "제공된 이메일 주소가 유효하지 않습니다.", "password": "잘못된 비밀번호가 입력되었습니다.", "resetpasswordcode": "잘못된 비밀번호 재설정 코드가 입력되었습니다.", "verifyemailcode": "잘못된 이메일 확인 코드가 입력되었습니다.", "firstname": "잘못된 이름이 입력되었습니다.", "lastname": "잘못된 성이 입력되었습니다.", "username": {"0": "제", "1": "공", "2": "된", "3": " ", "4": "사", "5": "용", "6": "자", "7": " ", "8": "이", "9": "름", "10": "이", "11": " ", "12": "요", "13": "구", "14": " ", "15": "사", "16": "항", "17": "을", "18": " ", "19": "충", "20": "족", "21": "하", "22": "지", "23": " ", "24": "않", "25": "습", "26": "니", "27": "다", "28": ".", "password": "제공된 사용자 이름과 비밀번호 조합이 일치하지 않습니다."}, "newpasswordsameoldpassword": "새 비밀번호는 이전 비밀번호와 같을 수 없습니다.", "plan": "잘못된 요금제가 입력되었습니다.", "order": {"dataoptionid": "잘못된 데이터 옵션이 입력되었습니다.", "quantity": "잘못된 주문 수량입니다.", "topupid": "잘못된 충전 ID가 입력되었습니다.", "iccid": "잘못된 주문 ICCID가 입력되었습니다."}, "usernotconfirmed": "사용자가 확인되지 않았습니다.", "request": "요청이 잘못되었거나 필수 매개 변수가 누락되었습니다.", "json": "요청 본문이 유효한 JSON 형식이 아닙니다.", "phone": {"found": "전화번호가 이미 존재합니다."}, "token": "제공된 인증 토큰이 유효하지 않거나 만료되었습니다.", "operation": "요청된 작업을 리소스에 대해 수행할 수 없습니다."}, "unable": {"resendverificationemail": "인증 이메일을 다시 보낼 수 없습니다.", "forgotpassword": "비밀번호 재설정 이메일을 보낼 수 없습니다.", "changepassword": "비밀번호를 변경할 수 없습니다.", "confirmpassword": "비밀번호를 확인할 수 없습니다."}, "login": {"social": {"email": "이메일 계정으로 로그인하는 중 오류가 발생했습니다.", "social": "소셜 로그인 중 오류가 발생했습니다."}, "wrongpassword": "잘못된 비밀번호가 입력되었습니다.", "error": {"wrong": {"password": "비밀번호 또는 이메일 주소가 잘못되었을 수 있습니다."}, "not-authorized-exception": "이메일 또는 비밀번호가 올바르지 않습니다. 다시 시도해주세요."}}, "alreadyexists": {"email": "이 이메일로 등록된 계정이 이미 존재합니다.", "phone": "전화번호가 다른 사용자에게 이미 등록되어 있습니다."}, "user": {"notfound": "사용자를 찾을 수 없습니다.", "not": {"found": "지정된 사용자가 존재하지 않습니다."}}, "esim": {"subscribe": "eSIM 구독/구매 중 오류가 발생했습니다.", "checkusage": "구매한 eSIM의 사용 상태를 조회하는 중 오류가 발생했습니다.", "unauthorizedaccess": "구매한 eSIM의 세부 정보에 대한 무단 접근입니다.", "getorders": "이메일 계정으로 로그인하는 중 오류가 발생했습니다.", "notfound": "eSIM을 찾을 수 없습니다."}, "plan": {"notfound": "요금제를 찾을 수 없습니다.", "allplans": "모든 요금제를 조회하는 중 오류가 발생했습니다."}, "request": {"missingparameters": "요청이 잘못되었거나 필수 매개 변수가 누락되었습니다.", "invalidformat": "요청 본문이 올바른 JSON 형식이 아닙니다.", "duplicatecontent": "콘텐츠가 이미 존재합니다.", "missingtoken": "요청 헤더에 인증 토큰이 없습니다.", "expiredtoken": "제공된 인증 토큰이 유효하지 않거나 만료되었습니다.", "insufficientpermissions": "사용자가 이 작업을 수행할 권한이 부족합니다.", "accessdenied": "사용자가 요청한 리소스에 접근할 수 없습니다.", "userblocked": "사용자 계정이 차단되었거나 비활성화되었습니다.", "resourcenotfound": "요청한 리소스가 존재하지 않습니다.", "resourceconflict": "요청된 작업이 현재 리소스 상태와 충돌합니다.", "validationfailed": "입력된 데이터가 유효성 검증에 실패했습니다.", "operationconflict": "요청된 작업을 리소스에 대해 수행할 수 없습니다."}, "server": {"unexpectederror": "서버에서 예기치 않은 오류가 발생했습니다.", "databaseoperationfailed": "데이터베이스 작업이 실패했습니다.", "externalserviceerror": "외부 서비스와의 통신 중 오류가 발생했습니다."}, "code": {"4000": "이메일 주소가 이미 존재합니다."}, "email": {"not": {"verified": "이메일 주소가 확인되지 않았습니다."}}, "account": {"not": {"idp": {"manual": "계정이 소셜 로그인으로 생성되었습니다."}}}, "content": {"conflict": "중복된 콘텐츠입니다."}, "missing": {"token": "요청 헤더에 인증 토큰이 없습니다."}, "insufficient": {"permissions": "사용자가 이 작업을 수행할 권한이 없습니다."}, "access": {"denied": "사용자가 요청된 리소스에 액세스할 수 없습니다."}, "blocked": {"user": "사용자 계정이 차단되었거나 비활성화되었습니다."}, "resource": {"not": {"found": "요청된 리소스가 존재하지 않습니다."}}, "product": {"not": {"found": "지정된 제품이 존재하지 않습니다."}}, "duplicate": {"resource": "동일한 식별자가 있는 리소스가 이미 존재합니다."}, "conflicting": {"state": "요청된 작업이 리소스의 현재 상태와 충돌합니다."}, "internal": {"server": {"error": "서버에서 예상치 못한 오류가 발생했습니다."}}, "database": {"error": "데이터베이스 작업에 실패했습니다."}, "external": {"service": {"error": "외부 서비스와의 통신 중 오류가 발생했습니다."}}, "coupons": {"countryspecificcoupon": "이 지역에서는 쿠폰을 사용할 수 없습니다.", "couponactive": "쿠폰이 활성화되지 않았습니다.", "couponexpired": "쿠폰이 만료되었습니다.", "coupononwrongplan": "이 플랜에서는 쿠폰 코드를 사용할 수 없습니다.", "couponperpersonusageexceeded": "쿠폰 코드는 한 번만 사용할 수 있습니다.", "couponusageexceeded": "쿠폰 코드 사용 한도를 초과했습니다.", "loyalcustomercoupon": "이 캠페인에서 이미 쿠폰을 사용하셨습니다.", "onlyoncecampaign": "이 캠페인에서 이미 쿠폰을 사용하셨습니다。", "selfonlycoupon": "잘못된 쿠폰입니다。"}}