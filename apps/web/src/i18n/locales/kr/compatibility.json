{"iphone": "<0>✓ iPhone XR</0><1>✓ iPhone XS</1><2>✓ iPhone XS Max</2><3>✓ iPhone 11</3><4>✓ iPhone 11 Pro</4><5>✓ iPhone SE 2nd generation (2020)</5><6>✓ iPhone 12</6><7>✓ iPhone 12 Mini</7><8>✓ iPhone 12 Pro</8><9>✓ iPhone 12 Pro Max</9><10>✓ iPhone 13</10><11>✓ iPhone 13 Mini</11><12>✓ iPhone 13 Pro</12><13>✓ iPhone 13 Pro Max</13><14>✓ iPhone SE 3rd generation (2022)</14><15>✓ iPhone 14</15><16>✓ iPhone 14 Plus</16><17>✓ iPhone 14 Pro</17><18>✓ iPhone 14 Pro Max</18><19>✓iPhone 15 models</19><20>✓iPhone 16 models</20>", "iphonemainlandchina": "중국본토에서 아이폰은 대응하지 않습니다.", "iphonechinasar": "홍콩, 마카오에서의 아이폰(iPhone 13 Mini、iPhone 12 Mini、iPhone SE 제2세대 2020、iPhone X을제외)<br/>은 대응하지않습니디.", "ipadwithwificellular": "아이패드는 와이파이 셀룰러모델만 eSIM 사용가능 합니다.", "ipad": "✓ iPad Air (제1세대)<br/>✓ iPad Air (제4세대)<br/>✓ iPad Pro 11-inch (제1세대)<br/>✓ iPad Pro 11-inch (제2세대)<br/>✓ iPad Pro 11-inch (제3세대)<br/>✓ iPad Pro 12.9-inch (제3세대)<br/>✓ iPad Pro 12.9-inch (제4세대)<br/>✓ iPad Pro 12.9-inch (제5세대)<br/>✓ iPad (제7세대)<br/>✓ iPad (제8세대)<br/>✓ iPad (제9세대)<br/>✓ iPad (제10세대)<br/>✓ iPad Mini (제5세대)<br/>✓ iPad Mini (제6세대)", "android": "<0>✓ Samsung Galaxy S20</0><1>✓ Samsung Galaxy S20+</1><2>✓ Samsung Galaxy S20+ 5g</2><3>✓ Samsung Galaxy S20 Ultra</3><4>✓ Samsung Galaxy S21</4><5>✓ Samsung Galaxy S21+ 5G</5><6>✓ Samsung Galaxy S21+ Ultra 5G</6><7>✓ Samsung Galaxy S22</7><8>✓ Samsung Galaxy S22+</8><9>✓ Samsung Galaxy S22 Ultra</9><10>✓ Samsung Galaxy Note 20</10><11>✓ Samsung Galaxy Note 20 Ultra 5G</11><12>✓ Samsung Galaxy Fold</12><13>✓ Samsung Galaxy Z Fold2 5G</13><14>✓ Samsung Galaxy Z Fold3 5G</14><15>✓ Samsung Galaxy Z Fold4</15><16>✓ Samsung Galaxy Z Flip</16><17>✓ Samsung Galaxy Z Flip3 5G</17><18>✓ Samsung Galaxy Z Flip4</18><19>✓ Samsung Galaxy S23</19><20>✓ Samsung Galaxy S23+</20><21>✓ Samsung Galaxy S23 Ultra</21><22>✓ Google Pixel 2</22><23>✓ Google Pixel 2 XL</23><24>✓ Google Pixel 3</24><25>✓ Google Pixel 3 XL</25><26>✓ Google Pixel 3a</26><27>✓ Google Pixel 3a XL</27><28>✓ Google Pixel 4</28><29>✓ Google Pixel 4a</29><30>✓ Google Pixel 4 XL</30><31>✓ Google Pixel 5</31><32>✓ Google Pixel 5a</32><33>✓ Google Pixel 6</33><34>✓ Google pixel 6a</34><35>✓ Google Pixel 6 Pro</35><36>✓ Google Pixel 7</36><37>✓ Google Pixel 7 Pro</37><38>✓ Huawei P40</38><39>✓ Huawei P40 Pro</39><40>✓ Huawei Mate 40 Pro</40><41>✓ Oppo Find X3 Pro</41><42>✓ Oppo Reno 5A</42><43>✓ Oppo Find X5</43><44>✓ Oppo Find X5 Pro</44><45>✓ Motorola Razr 2019</45><46>✓ Motorola Razr 5G</46><47>✓ Gemini PDA</47><48>✓ Rakuten Mini</48><49>✓ Rakuten Big-S</49><50>✓ Rakuten Big</50><51>✓ Rakuten Hand</51><52>✓ Rakuten Hand 5G</52><53>✓ Sony Xperia 10 III Lite</53><54>✓ Sony Xperia 10 IV</54><55>✓ Xperia 1 IV</55><56>✓ Sony Xperia 5 IV</56><57>✓ Surface Pro X</57><58>✓ Honor Magic 4 Pro</58><59>✓ Fairphone 4</59><60>✓ Sharp Aquos Sense6s</60><61>✓ Sharp Aquos Wish</61><62>✓ Xiaomi 12T Pro</62><63>✓ DOOGEE V30</63><64>✓ Samsung Galaxy S24 models</64><65>✓ Samsung Galaxy S25 models</65><66>✓ Google Pixel 9 models</66>", "androidgooglepixel2": "Google Pixel 2 / 2XL  Google Fi network 대응가능", "androidgooglepixel3else": "구글픽셀3/3 XL은 호주,대만,일본에서 구입하신 단말 및 Spring Google FI\r<br/>이외의 미국 또는 캐나다의 통신사업자로부터 구입하신 단말은 eSIM 사용 불가입니다.", "androidgooglepixel3aelse": "구글픽셀3a/3a XL은 일본, 동남아시아에서 구입한 단말 및 Verizon 서비스를\r<br/>이용한 단말은 대응하지않습니다.", "huaweinotcompatible": "화웨이 P40 프로+ 는 eSIM 대응하지 않습니다.", "opponotcompatible": "Oppo Find X5 Lite는 eSIM 대응하지 않습니다.", "title": "eSIM 대응가능한 기기", "head": {"title": "eSIM 대응가능한 - Global Mobile eSIM", "description": "아이폰,아이패드,안드로이드등의 기종마다eSIM대응 일람입니다.<br/>eSIM신청전에, 반드시 대응기종, 단말을 확인 해 주세요."}, "forios12later": "iOS iPhone (iOS 12.1 이후)", "2ndgeneration": "제2세대", "3rdgeneration": "제3세대", "i-pad-air3rdgeneration": "iPad Air (제3세대)", "i-pad-air4thgeneration": "iPad Air (제4세대)", "i-pad-pro11inch1stgeneration": "iPad Pro 11-inch (제1세대)", "i-pad-pro11inch2ndgeneration": "iPad Pro 11-inch (제2세대)", "i-pad-pro11inch3rdgeneration": "iPad Pro 11-inch (제3세대)", "i-pad7thgeneration": "iPad (제7세대)", "i-pad8thgeneration": "iPad (제8세대", "i-pad9thgeneration": "iPad (제9세대)", "i-pad10thgeneration": "iPad (제10세대)", "i-pad-mini5thgeneration": "iPad Mini (제5세대)", "i-pad-mini6thgeneration": "iPad Mini (제6세대)", "i-pad-pro12": {"9inch3rdgeneration": "iPad Pro 12.9-inch (제3세대)", "9inch4thgeneration": "iPad Pro 12.9-inch (제4세대)", "9inch5thgeneration": "iPad Pro 12.9-inch (제5세대)"}, "compatibilitywarning": "주의: 귀하의 장치는 국가에 따라 eSIM을 지원하지 않을 수 있습니다. 호환 목록에 나와 있더라도 특정 지역 모델은 eSIM 제한이 있을 수 있습니다. 또한 귀하의 장치가 SIM 잠금이 해제되어 있지 않은지 확인해야 합니다.", "home": {}, "compatibilitysuccess": {}, "modal": {"apple": {}, "android": {}, "others": {}}, "headers": {}}