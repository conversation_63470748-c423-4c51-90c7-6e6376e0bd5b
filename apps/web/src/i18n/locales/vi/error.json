{"validation": {"minlength1": {"firstname": "<PERSON>ên ph<PERSON>i có ít nhất 1 ký tự", "lastname": "Họ phải có ít nhất 1 ký tự", "profileimage": "<PERSON><PERSON><PERSON><PERSON> hồ sơ phải có ít nhất 1 ký tự"}, "minlength2": {"defaultpaymentmethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán mặc định phải có ít nhất 2 ký tự", "locale": "<PERSON><PERSON>n ngữ mặc định phải có ít nhất 2 ký tự", "referral": "<PERSON>ham chiếu mặc định phải có ít nhất 2 ký tự", "website": "Trang web mặc định phải có ít nhất 2 ký tự"}, "wrongusernamepassword": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập hoặc mật khẩu sai", "failed": "<PERSON><PERSON> liệu đầu và<PERSON> không v<PERSON><PERSON><PERSON> qua quy tắc xác thực.", "error4009": {"label": "An account already exists with the same email address logged in using social login. Please use your social login to sign in."}}, "invalid": {"email": "Địa chỉ email đ<PERSON><PERSON><PERSON> cung cấp không hợp lệ.", "password": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> không hợp lệ", "resetpasswordcode": "<PERSON>ã đặt lại mật khẩu không hợp lệ", "verifyemailcode": "Mã x<PERSON>c minh email không hợp lệ", "firstname": "<PERSON><PERSON><PERSON> kh<PERSON>ng h<PERSON> lệ", "lastname": "<PERSON><PERSON> không hợp lệ", "username": {"0": "T", "1": "ê", "2": "n", "3": " ", "4": "n", "5": "g", "6": "ư", "7": "ờ", "8": "i", "9": " ", "10": "d", "11": "ù", "12": "n", "13": "g", "14": " ", "15": "đ", "16": "ư", "17": "ợ", "18": "c", "19": " ", "20": "c", "21": "u", "22": "n", "23": "g", "24": " ", "25": "c", "26": "ấ", "27": "p", "28": " ", "29": "k", "30": "h", "31": "ô", "32": "n", "33": "g", "34": " ", "35": "đ", "36": "á", "37": "p", "38": " ", "39": "ứ", "40": "n", "41": "g", "42": " ", "43": "y", "44": "ê", "45": "u", "46": " ", "47": "c", "48": "ầ", "49": "u", "50": ".", "password": "<PERSON>ê<PERSON> người dùng và mật khẩu không khớp."}, "newpasswordsameoldpassword": "<PERSON><PERSON>t khẩu mới không thể giống mật khẩu cũ", "plan": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>", "order": {"dataoptionid": "<PERSON><PERSON><PERSON> chọn dữ liệu không hợp lệ", "quantity": "Số lượng đơn hàng không hợp lệ", "topupid": "ID nạp tiền không hợp lệ", "iccid": "ICCID đơn hàng không hợp lệ"}, "usernotconfirmed": "<PERSON><PERSON><PERSON><PERSON> dùng chưa đ<PERSON><PERSON> x<PERSON>c <PERSON>n", "request": "<PERSON><PERSON><PERSON> cầu không hợp lệ hoặc thiếu tham số cần thiết.", "json": "<PERSON><PERSON><PERSON> dung yêu cầu không đúng định dạng JSON hợp lệ.", "phone": {"found": "<PERSON><PERSON> điện thoại đã tồn tại."}, "token": "<PERSON><PERSON> thông báo xác thực không hợp lệ hoặc đã hết hạn.", "operation": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện thao tác yêu cầu trên tài nguyên."}, "unable": {"resendverificationemail": "<PERSON><PERSON><PERSON><PERSON> thể gửi lại email x<PERSON>c minh", "forgotpassword": "<PERSON><PERSON><PERSON><PERSON> thể gửi email quên mật khẩu", "changepassword": "<PERSON><PERSON><PERSON><PERSON> thể đổi mật khẩu", "confirmpassword": "<PERSON><PERSON><PERSON><PERSON> thể xác nhận mật khẩu"}, "login": {"social": {"email": "Lỗi đăng nhập qua tài k<PERSON>n email", "social": "Lỗi đăng nhập qua mạng xã hội"}, "wrongpassword": "<PERSON><PERSON><PERSON> sai", "error": {"wrong": {"password": "<PERSON><PERSON><PERSON> khẩu hoặc địa chỉ email của bạn có thể sai."}, "not-authorized-exception": "Email hoặc mật kh<PERSON>u không hợp lệ. <PERSON><PERSON> lòng thử lại."}}, "alreadyexists": {"email": "<PERSON><PERSON><PERSON> k<PERSON>n với email này đã đư<PERSON><PERSON> đăng ký", "phone": "Số điện thoại đã đư<PERSON><PERSON> đăng ký cho người dùng khác"}, "user": {"notfound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng", "not": {"found": "Người dùng được chỉ định không tồn tại."}}, "esim": {"subscribe": "Lỗi đăng ký/mua eSIM", "checkusage": "Lỗi lấy trạng thái sử dụng của eSIM đã mua", "unauthorizedaccess": "<PERSON><PERSON><PERSON> cập tr<PERSON>i phép vào chi tiết eSIM đã mua", "getorders": "Lỗi đăng nhập qua tài k<PERSON>n email", "notfound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy eSIM"}, "plan": {"notfound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói", "allplans": "Lỗi l<PERSON>y tất cả các gói"}, "request": {"missingparameters": "<PERSON><PERSON><PERSON> c<PERSON>u không hợp lệ hoặc thiếu tham số bắt buộc", "invalidformat": "<PERSON><PERSON><PERSON> dung yêu cầu không đúng định dạng JSON hợp lệ", "duplicatecontent": "<PERSON>ội dung đã tồn tại", "missingtoken": "Thiếu mã xác thực trong tiêu đề yêu cầu", "expiredtoken": "<PERSON><PERSON> xác thực không hợp lệ hoặc đã hết hạn", "insufficientpermissions": "Ngư<PERSON>i dùng không có đủ quyền để thực hiện hành động này", "accessdenied": "<PERSON>ư<PERSON>i dùng không đ<PERSON><PERSON><PERSON> phép truy cập tài nguyên yêu cầu", "userblocked": "<PERSON><PERSON><PERSON> kho<PERSON>n người dùng bị khóa hoặc vô hiệu hóa", "resourcenotfound": "<PERSON><PERSON><PERSON> nguyên yêu cầu không tồn tại", "resourceconflict": "<PERSON><PERSON><PERSON> động yêu cầu không khớp với trạng thái hiện tại của tài nguyên", "validationfailed": "<PERSON><PERSON> liệu nhập không đáp ứng quy tắc xác thực", "operationconflict": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện yêu cầu trên tài nguyên"}, "server": {"unexpectederror": "Lỗi không mong muốn xảy ra trên máy chủ", "databaseoperationfailed": "<PERSON><PERSON><PERSON> động cơ sở dữ liệu thất bại", "externalserviceerror": "Lỗi x<PERSON>y ra khi kết nối với dịch vụ bên ngoài"}, "code": {"4000": "Địa chỉ email đã tồn tại."}, "email": {"not": {"verified": "Địa chỉ email ch<PERSON>a đ<PERSON><PERSON><PERSON> xác <PERSON>h."}}, "account": {"not": {"idp": {"manual": "<PERSON><PERSON><PERSON> k<PERSON>n được tạo bằng đăng nhập mạng xã hội."}}}, "content": {"conflict": "<PERSON><PERSON>i dung trùng lặp."}, "missing": {"token": "Thi<PERSON>u mã thông báo xác thực trong tiêu đề yêu cầu."}, "insufficient": {"permissions": "<PERSON>ư<PERSON>i dùng không có đủ quyền để thực hiện hành động này."}, "access": {"denied": "<PERSON>ư<PERSON>i dùng không đ<PERSON><PERSON><PERSON> phép truy cập tài nguyên yêu cầu."}, "blocked": {"user": "<PERSON><PERSON><PERSON> k<PERSON>n người dùng bị chặn hoặc vô hiệu hóa."}, "resource": {"not": {"found": "<PERSON><PERSON><PERSON> nguyên yêu cầu không tồn tại."}}, "product": {"not": {"found": "<PERSON><PERSON><PERSON> phẩm được chỉ định không tồn tại."}}, "duplicate": {"resource": "<PERSON>ã tồn tại tài nguyên với cùng một mã nhận dạng."}, "conflicting": {"state": "<PERSON><PERSON><PERSON> động yêu cầu mâu thuẫn với trạng thái hiện tại của tài nguyên."}, "internal": {"server": {"error": "<PERSON><PERSON> lỗi không mong muốn xảy ra trên máy chủ."}}, "database": {"error": "<PERSON><PERSON> t<PERSON>c cơ sở dữ liệu thất bại."}, "external": {"service": {"error": "<PERSON><PERSON> xảy ra lỗi khi liên lạc với dịch vụ bên ngoài."}}, "coupons": {"countryspecificcoupon": "<PERSON><PERSON><PERSON> giảm giá không đ<PERSON><PERSON><PERSON> phép sử dụng trong khu vực này.", "couponactive": "<PERSON><PERSON><PERSON> gi<PERSON>m giá chưa đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>.", "couponexpired": "<PERSON><PERSON><PERSON> giảm giá đã hết hạn.", "coupononwrongplan": "<PERSON><PERSON> phiếu giảm giá không áp dụng cho gói này.", "couponperpersonusageexceeded": "<PERSON><PERSON> phiếu giảm giá chỉ được sử dụng một lần.", "couponusageexceeded": "Giới hạn sử dụng mã phiếu giảm giá đã bị vượt quá.", "loyalcustomercoupon": "Bạn đã sử dụng phiếu giảm giá cho chiến dịch này.", "onlyoncecampaign": "Bạn đã sử dụng phiếu giảm giá cho chiến dịch này.", "selfonlycoupon": "<PERSON><PERSON><PERSON> gi<PERSON>m gi<PERSON> không hợp lệ."}}