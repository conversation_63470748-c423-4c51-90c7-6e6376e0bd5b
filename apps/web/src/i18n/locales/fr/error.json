{"validation": {"minlength1": {"firstname": "Le prénom doit comporter au moins 1 caractère", "lastname": "Le nom de famille doit comporter au moins 1 caractère", "profileimage": "L'image de profil doit comporter au moins 1 caractère"}, "minlength2": {"defaultpaymentmethod": "Le mode de paiement par défaut doit comporter au moins 2 caractères", "locale": "La langue par défaut doit comporter au moins 2 caractères", "referral": "La référence par défaut doit comporter au moins 2 caractères", "website": "Le site web par défaut doit comporter au moins 2 caractères"}, "wrongusernamepassword": "Nom d'utilisateur ou mot de passe incorrect", "failed": "Les données saisies n'ont pas réussi la validation.", "error4009": {"label": "An account already exists with the same email address logged in using social login. Please use your social login to sign in."}}, "invalid": {"email": "L'adresse e-mail fournie n'est pas valide.", "password": "Mot de passe invalide", "resetpasswordcode": "Code de réinitialisation du mot de passe invalide", "verifyemailcode": "Code de vérification de l'email invalide", "firstname": "Prénom invalide", "lastname": "Nom de famille invalide", "username": {"0": "L", "1": "e", "2": " ", "3": "n", "4": "o", "5": "m", "6": " ", "7": "d", "8": "'", "9": "u", "10": "t", "11": "i", "12": "l", "13": "i", "14": "s", "15": "a", "16": "t", "17": "e", "18": "u", "19": "r", "20": " ", "21": "f", "22": "o", "23": "u", "24": "r", "25": "n", "26": "i", "27": " ", "28": "n", "29": "e", "30": " ", "31": "r", "32": "é", "33": "p", "34": "o", "35": "n", "36": "d", "37": " ", "38": "p", "39": "a", "40": "s", "41": " ", "42": "a", "43": "u", "44": "x", "45": " ", "46": "e", "47": "x", "48": "i", "49": "g", "50": "e", "51": "n", "52": "c", "53": "e", "54": "s", "55": ".", "password": "La combinaison de nom d'utilisateur et de mot de passe ne correspond pas."}, "newpasswordsameoldpassword": "Le nouveau mot de passe ne peut pas être identique à l'ancien mot de passe", "plan": "Plan invalide", "order": {"dataoptionid": "Option de données invalide", "quantity": "Quantité de commande invalide", "topupid": "ID de recharge invalide", "iccid": "ICCID de commande invalide"}, "usernotconfirmed": "L'utilisateur n'est pas confirmé", "request": "La requête est mal formée ou manque de paramètres requis.", "json": "Le corps de la requête n'est pas au format JSON valide.", "phone": {"found": "Le numéro de téléphone existe déjà."}, "token": "Le jeton d'authentification fourni est invalide ou expiré.", "operation": "L'opération demandée ne peut pas être effectuée sur la ressource."}, "unable": {"resendverificationemail": "Impossible de renvoyer l'email de vérification", "forgotpassword": "Impossible d'envoyer l'email de mot de passe oublié", "changepassword": "Impossible de changer le mot de passe", "confirmpassword": "Impossible de confirmer le mot de passe"}, "login": {"social": {"email": "Erreur lors de la connexion par compte email", "social": "Erreur lors de la connexion par connexion sociale"}, "wrongpassword": "Mot de passe incorrect", "error": {"wrong": {"password": "Votre mot de passe ou adresse e-mail peut être incorrect."}, "not-authorized-exception": "Adresse e-mail ou mot de passe invalide. Veuillez réessayer."}}, "alreadyexists": {"email": "Un compte avec cet email est déjà enregistré", "phone": "Le numéro de téléphone est déjà enregistré par un autre utilisateur"}, "user": {"notfound": "Utilisateur non trouvé", "not": {"found": "L'utilisateur spécifié n'existe pas."}}, "esim": {"subscribe": "Erreur lors de l'abonnement/achat de l'eSIM", "checkusage": "Erreur lors de la récupération de l'état d'utilisation de l'eSIM achetée", "unauthorizedaccess": "Accès non autorisé aux détails de l'eSIM achetée", "getorders": "Erreur lors de la connexion par compte email", "notfound": "eSIM non trouvée"}, "plan": {"notfound": "Plan non trouvé", "allplans": "Erreur lors de la récupération de tous les plans"}, "request": {"missingparameters": "La demande est mal formée ou manque des paramètres requis", "invalidformat": "Le corps de la demande n'est pas au format JSON valide", "duplicatecontent": "Le contenu existe déjà", "missingtoken": "Le jeton d'authentification est manquant dans les en-têtes de la demande", "expiredtoken": "Le jeton d'authentification fourni est invalide ou expiré", "insufficientpermissions": "L'utilisateur n'a pas les permissions suffisantes pour effectuer l'action", "accessdenied": "L'utilisateur n'est pas autorisé à accéder à la ressource demandée", "userblocked": "Le compte utilisateur est bloqué ou désactivé", "resourcenotfound": "La ressource demandée n'existe pas", "resourceconflict": "L'action demandée est en conflit avec l'état actuel de la ressource", "validationfailed": "Les données saisies n'ont pas réussi les règles de validation", "operationconflict": "L'opération demandée ne peut pas être effectuée sur la ressource"}, "server": {"unexpectederror": "Une erreur inattendue s'est produite sur le serveur", "databaseoperationfailed": "L'opération de base de données a échoué", "externalserviceerror": "Une erreur s'est produite lors de la communication avec un service externe"}, "code": {"4000": "L'adresse e-mail existe déjà."}, "email": {"not": {"verified": "L'adresse e-mail n'est pas vérifiée."}}, "account": {"not": {"idp": {"manual": "Le compte a été créé avec une connexion sociale."}}}, "content": {"conflict": "<PERSON><PERSON><PERSON> dupli<PERSON>."}, "missing": {"token": "Le jeton d'authentification est absent des en-têtes de requête."}, "insufficient": {"permissions": "L'utilisateur n'a pas les permissions nécessaires pour effectuer cette action."}, "access": {"denied": "L'accès à la ressource demandée est refusé à l'utilisateur."}, "blocked": {"user": "Le compte utilisateur est bloqué ou désactivé."}, "resource": {"not": {"found": "La ressource demandée n'existe pas."}}, "product": {"not": {"found": "Le produit spécifié n'existe pas."}}, "duplicate": {"resource": "Une ressource avec le même identifiant existe déjà."}, "conflicting": {"state": "L'action demandée est en conflit avec l'état actuel de la ressource."}, "internal": {"server": {"error": "Une erreur inattendue s'est produite sur le serveur."}}, "database": {"error": "Échec de l'opération de base de données."}, "external": {"service": {"error": "Une erreur s'est produite lors de la communication avec un service externe."}}, "coupons": {"countryspecificcoupon": "Ce coupon n'est pas autorisé pour cette région.", "couponactive": "Le coupon n'est pas actif.", "couponexpired": "Le coupon a expiré.", "coupononwrongplan": "Le code promo n'est pas disponible pour ce plan.", "couponperpersonusageexceeded": "Le code promo n'est utilisable qu'une seule fois.", "couponusageexceeded": "La limite d'utilisation du code promo a été dépassée.", "loyalcustomercoupon": "Vous avez déjà utilisé un coupon pour cette campagne.", "onlyoncecampaign": "Vous avez déjà utilisé un coupon pour cette campagne.", "selfonlycoupon": "Coupon invalide."}}