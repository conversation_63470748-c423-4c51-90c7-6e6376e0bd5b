import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ReactNode, useCallback, useEffect, useMemo, useState } from "react";

import { notifications } from "@mantine/notifications";

import { IconMoodSad } from "@tabler/icons-react";
import { groupBy, startCase, sumBy, uniqueId } from "lodash";
import { Trans, useTranslation } from "react-i18next";
import { useQuery } from "react-query";
import { useDebounce, useDebouncedCallback } from "use-debounce";

import { Currency } from "@/utils/currency";

import { ICouponDiscounted } from "@/interfaces/ICouponDiscounted";
import { IQuotationPayload } from "@/interfaces/IQuotationPayload";

import { ApiService } from "../api";
import { IPlan } from "../interfaces/IPlan";
import { IQuotation, XeType } from "../interfaces/IQuotation";
import { ICart, useShoppingCart } from "../store";

interface IProps {
  discount?: ICouponDiscounted | null;
  quotation?: IQuotation;
  onRemoveItem?: (items: ICart[]) => void;
  source?: "NAVBAR" | "EMBEDDED";
}

function sendCartRemoveEvent(total: number, items: ICart[]) {}

export const useCartController = (
  props: IProps = { discount: undefined, quotation: undefined }
) => {
  const [
    items,
    removeBulk,
    addBulk,
    localQuotation,
    setQuotation,
    clearItems,
    showCartMenu,
    coupon,
  ] = useShoppingCart((s) => [
    s.items,
    s.removeBulk,
    s.addBulk,
    s.quotation,
    s.setQuotation,
    s.clearItems,
    s.showCartMenu,
    s.coupon,
  ]);
  const [loading, setLoading] = useState(false);
  const selectedCode = useMemo(() => Currency.getSelectedCurrency(), []);
  const router = useRouter();
  const pathname = usePathname();
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const via = searchParams?.get("via") || searchParams?.get("coupon");

  const [debouncedItems] = useDebounce(items, 500);

  useEffect(() => {
    setLoading(true);
    const timeout = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => {
      clearTimeout(timeout);
    };
  }, [showCartMenu]);

  const key = useMemo(() => {
    return (
      debouncedItems
        .map((item) => item.plan?.id + "" + (item?.insurance || "none"))
        .join("-") + (props?.discount?.code || coupon || "none")
    );
  }, [debouncedItems, props?.discount, coupon]);

  const [debouncedKey] = useDebounce(key, 100);
  const quotationQuery = useQuery(
    ["quotation ", debouncedKey],
    async () => {
      //@ts-expect-error
      const payload = {
        debouncedKey,
        couponId: coupon || via, //props?.discount?.code,
        products: items.map((item) => ({
          optionId: item.plan?.id + "",
          insurance: item.insurance ? "insured" : undefined,
        })),
      } as IQuotationPayload;
      const { data } = await ApiService.getOrderQuotation(payload, {
        requestOriginServiceName: via as string,
      });
      setLoading(false);
      return data;
    },
    {
      keepPreviousData: true,
      cacheTime: 3000,
      enabled: pathname?.includes("/checkout")
        ? props.source !== "NAVBAR"
          ? false
          : true
        : true,
    }
  );

  useEffect(() => {
    const { errors } = quotationQuery?.data?.data || {};
    if (!errors?.length) return;
    if (props.source === "NAVBAR") return;
    errors.forEach((element: any) => {
      if (!element) return;
      notifications.show({
        id: `notification-${element.code}`, // Unique ID
        icon: <IconMoodSad />,
        message: t(
          `error:coupons.${element.code}`,
          t(`error:coupons.selfonlycoupon`, "Unable to apply coupon.")
        ) as string,
      });
    });
  }, [quotationQuery?.data?.data?.errors]);

  useEffect(() => {
    setLoading(quotationQuery.isFetching);
  }, [quotationQuery.isFetching]);

  useEffect(() => {
    setQuotation(quotationQuery.data?.data);
  }, [quotationQuery.data]);

  const [errorDebounce] = useDebounce(quotationQuery.error, 5000);
  useEffect(() => {
    if (errorDebounce) console.log("Something went wrong, please try again!");
  }, [errorDebounce]);
  const planDescription = useCallback((plan: IPlan, type: string, t: any) => {
    const isUnlimited = plan.name === "unlimited";
    const is5gNetwork = plan.network.networkGeneration === "5G";

    const planKey = isUnlimited
      ? type === "regular" && is5gNetwork
        ? "common:cart.menu.item.unlimited.5g"
        : type === "regular" && !is5gNetwork
          ? "cart.menu.item.unlimited.4g"
          : is5gNetwork
            ? "common:cart.menu.item.unlimitedinsured5g"
            : "common:cart.menu.item.unlimitedinsured4g"
      : type === "regular"
        ? "common:cart.menu.item.fixed"
        : "common:cart.menu.item.fixedinsured";

    return `${t(planKey, {
      dataVolume: plan.dataId,
      country: startCase(plan.country.name),
    })} ${isUnlimited ? "/" : "-"} ${t("common:siminfo-card.validity.unit", {
      count: plan.validityDays,
    })}`;
  }, []);

  const cartItemBuilder = useCallback(
    ({
      plan,
      type,
      items,
    }: {
      type: "regular" | "insured";
      plan: IPlan;
      items: any[];
    }) => {
      return {
        plan,
        id: plan.id + ":" + type,
        perPrice: Currency.formatCurrency(
          items[0]?.xe[selectedCode.code],
          selectedCode.code,
          {
            currencyStyle: true,
          }
        ),
        label: planDescription(plan, type, t),
        link: `/region/${plan.country.name}`,
        price: Currency.formatCurrency(
          sumBy(items, `xe.${selectedCode.code}`),
          selectedCode.code,
          {
            currencyStyle: true,
          }
        ),
        quantity: items.length,
        hoverImage: `https://cdn.gmobile.biz/global-esim/assets/destination/${plan.country.name.toLocaleLowerCase()}.webp`,
        productImage: `https://cdn.gmobile.biz/global-esim/assets/flags/100x100/${plan.country.code.toLocaleLowerCase()}.png`,
        //   getImageUrlByCategory({
        //     networkGeneration: plan.networkGeneration,
        //     category: (plan?.packageType === PACKAGES.FIXED
        //       ? undefined
        //       : plan?.name) as PlanCategory,
        //     name: plan.name === "unlimited" ? plan.name : plan.dataId,
        //     planId: plan.id + "",
        //     validityDays: plan.validityDays + "",
        //   }) + ".png",
      };
    },
    []
  );
  const groupedItems = useMemo(() => {
    const groupByPlans = groupBy(props.quotation?.orders || [], "plan.id");
    const cartItems: any[] = [];
    Object.keys(groupByPlans).forEach((planId) => {
      const items = groupBy(groupByPlans[planId], (item) => {
        return item.insured ? "insured" : "regular";
      });
      const { regular, insured } = items;
      if (regular && regular?.length) {
        const plan = regular[0]?.plan;
        cartItems.push(
          cartItemBuilder({ plan: plan, items: regular, type: "regular" })
        );
      }
      if (insured && insured?.length) {
        const plan = insured[0]?.plan;
        cartItems.push(
          cartItemBuilder({ plan: plan, items: insured, type: "insured" })
        );
      }
    });
    return cartItems;
  }, [props.quotation?.orders, selectedCode]);

  const handleQuantityChange = useDebouncedCallback(
    ({
      value,
      intent,
      item,
    }: {
      value: number;
      intent: string;
      item: { id: string };
    }) => {
      const [planId, type] = item.id.split(":");
      const eligibleItems = items.filter((item) => item.plan.id === +planId);
      if (intent === "add") {
        const noOfItemToAdd = value - eligibleItems.length;
        if (noOfItemToAdd < 1) return;
        const plan = items.find((item) => item.plan?.id === +planId)?.plan;
        if (!plan) return;
        const nextItems = Array(noOfItemToAdd)
          .fill(true)
          .map(() => {
            return {
              id: Date.now() + "" + uniqueId(),
              plan: plan,
              insurance: type === "regular" ? "" : "insured",
              quantity: 1,
            };
          });
        // const items = Array
        setLoading(false);
        addBulk(nextItems);
        return;
      }

      const removableItems = eligibleItems.slice(value).map((item) => item.id);
      if (removableItems.length) {
        setLoading(false);
        props?.onRemoveItem?.(eligibleItems);
        removeBulk(removableItems);
        sendCartRemoveEvent(props.quotation?.total || 0, eligibleItems);
      }
    },
    500
  );

  const handleItemRemove = useCallback(
    (id: string) => {
      if (!confirm(t("common:cart.error.deleteitemwarning"))) {
        return false;
      }
      const [planId, type] = id.split(":");
      const eligibleItems = items.filter((item) => item.plan.id === +planId);
      const ids = eligibleItems.map((item) => item.id);
      if (ids.length) {
        removeBulk(ids);
        props?.onRemoveItem?.(eligibleItems);
        sendCartRemoveEvent(props.quotation?.total || 0, eligibleItems);
      }
    },
    [items]
  );
  const handleCheckout = useCallback(() => {
    if (!items.length) return;
    router.push(`/checkout`);
  }, [items]);

  const handleContinueShopping = useCallback(() => {
    router.push(`/choosePlan`);
  }, []);

  const total = useMemo(() => {
    return Currency.formatCurrency(
      //@ts-expect-error
      props.quotation?.xe?.[selectedCode.code] || 0,
      selectedCode.code,
      {
        currencyStyle: true,
      }
    );
  }, [props.quotation]);

  const formattedDiscount = useMemo(() => {
    return Currency.formatCurrency(
      //@ts-expect-error
      props.quotation?.discountXe?.[selectedCode.code] || 0,
      selectedCode.code,
      {
        currencyStyle: true,
      }
    );
  }, [props.quotation]);
  const formattedTotalWithoutDiscount = useMemo(() => {
    return Currency.formatCurrency(
      //@ts-expect-error
      props.quotation?.totalWithoutDiscountXe?.[selectedCode.code] || 0,
      selectedCode.code,
      {
        currencyStyle: true,
      }
    );
  }, [props.quotation]);

  return {
    loading,
    total,
    items: groupedItems,
    rawItems: items,
    handleCheckout,
    handleItemRemove,
    handleContinueShopping,
    handleQuantityChange,
    quotation: localQuotation,
    formattedTotalWithoutDiscount,
    formattedDiscount,
    clearItems,
  };
};
