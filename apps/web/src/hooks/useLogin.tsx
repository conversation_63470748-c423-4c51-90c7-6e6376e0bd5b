import React, { useCallback } from "react";
import { useAuth } from "@ri/fe-auth";
import { jwtDecode } from "jwt-decode";

import { ApiService } from "@/api";
import { useMessageStore } from "@/store/MessageStore";
import Link from "next/link";
import { Group, Text } from "@mantine/core";
import Storage from "@/utils/storage";
import { AUTH_TOKEN_NAME, ERR_PASSWORD_NOT_MATCHED } from "@/app/constants";
import { withBasePath } from "@/utils";

const useLogin = () => {
  const auth = useAuth();
  const [setGlobalMessage, setGlobalLoading, isLoading] = useMessageStore(
    (s) => [s.setMessage, s.toggleLoading, s.isLoading]
  );

  const logout = React.useCallback(() => {
    auth?.endSession();
    Storage.getInstance().set(AUTH_TOKEN_NAME, "");
    setTimeout(() => {
      //Buffer time for state clearance
      window.location.href = withBasePath("auth/signin");
    }, 100);
  }, []);

  const onToken = useCallback((data: { accessToken: string }) => {
    Storage.getInstance().set(AUTH_TOKEN_NAME, data);
    const decoded = jwtDecode(data.accessToken) as {
      email: string;
      user_id: string;
      name: string;
      companyId: string;
    };
    auth?.startSession({
      id: decoded.user_id,
      displayName: decoded.name,
      email: decoded.email,
      data: {
        companyId: "",
      },
    });
  }, []);

  const login = React.useCallback(
    (payload: { email: string; password: string }) => {
      setGlobalLoading(true);
      ApiService.login(payload)
        .then(({ data }) => {
          onToken(data.data);
        })
        .catch((err) => {
          let errorMessage =
            err?.response?.data?.message || ERR_PASSWORD_NOT_MATCHED;
          if (
            err?.response?.data?.message ===
              "Incorrect username or password." ||
            err?.response?.data?.message ===
              "The provided username and password combination does not match."
          ) {
            errorMessage = (
              <Text size={"sm"} c="app-dark">
                メールアドレスまたは、パスワードが違います。
                <br />
                ご確認お願い致します。
              </Text>
            );
          }
          // TODO: only check errorCode once all API error codes are set
          if (
            err?.response?.data?.message === "User is not confirmed." ||
            err?.response?.data?.errorCode === 4007
          ) {
            errorMessage = (
              <Group className="gap-0">
                <Text size={"sm"} c="app-dark">
                  ユーザーは確認されていません。
                </Text>
                <Link href={"/account/resend-email"}>
                  <Text size={"sm"}>メールを再送</Text>
                </Link>
              </Group>
            );
          }

          setGlobalMessage(<>{errorMessage}</>);
        })
        .finally(() => {
          setGlobalLoading(false);
        });
    },
    []
  );

  return {
    onToken,
    isLoading,
    login,
    logout,
  };
};
export default useLogin;
