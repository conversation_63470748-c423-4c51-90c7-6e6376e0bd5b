export interface IPlan {
  country: {
    code: string;
    name: string;
    subCountries?: string;
  };
  countryCode?: string;
  region?: string;
  createdAt: string;
  dataId: string;
  dataUnit: string;
  dataVolume: string;
  description: string;
  id: number;
  name: string;
  packageType: "PER_DAY" | "FIXED_DAY";
  planId: string;
  planProvider: string; //'LOCAL';
  price: number;
  updatedAt: string; //'2023-06-08T09:11:26.400Z';
  validityDays: number;
  xe: {
    [key: string]: string;
  };
  originalName?: string;
  order?: number;
  network: INetwork;
}

export interface INetwork {
  apn: string;
  code: string;
  countryId: number;
  enabled: boolean;
  id: number;
  name: string;
  networkGeneration: string;
  qos: string;
  type: string;
}
