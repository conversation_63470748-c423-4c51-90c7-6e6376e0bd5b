import { IPlan } from "./IPlan";

export interface IOrder {
  id: number;
  userId: number;
  response: any;
  orders: object;
  createdAt: Date;
  updatedAt: Date;
  pi_id: string;
  paymentStatus: string;
  txnId: string;
  type: "subscribe";
  planId: number;
  iccid: string | null;
  topupId: string | null;
  activateCode: string | null;
  qrCodeImgUrl: string | null;
  stripeLatestChargeId: string | null;
  jpyPrice: number | null;
  orderId: string | null;
  orderCreatedAt: Date | null;
  downloadLink: string | null;
  isActivated: boolean | null;
  activateDate: Date | null;
  expireTime: Date | null;
  provisionPrice: number | null;
  price: number | null;
  plan: IPlan;
  status?: string
}
