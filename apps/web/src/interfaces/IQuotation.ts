import { ICouponDiscounted } from "./ICouponDiscounted";
import { IPlan } from "./IPlan";

export interface IQuotationError {
  message: string;
  code: string;
}

export interface IQuotation {
  total: number;
  xe: Xe;
  orders: IOrder[];
  discount?: number;
  totalWithoutDiscount?: number;
  discountXe: Xe;
  coupon?: ICouponDiscounted;
  errors?: IQuotationError[];
}

export type XeType = keyof Xe;
export interface Xe {
  JPY: number;
  EUR: string;
  AUD: string;
  CAD: string;
  GBP: string;
  TWD: string;
  HKD: string;
  CNY: string;
  KRW: string;
  USD: number;
}

export interface IOrder {
  insured: boolean;
  price: number;
  plan: IPlan;
  xe: Xe;
}
