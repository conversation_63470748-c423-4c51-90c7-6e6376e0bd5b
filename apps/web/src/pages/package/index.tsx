import { GetServerSideProps } from "next";

export const getServerSideProps: GetServerSideProps<{}> = async (_) => {
  const destination =
    process.env.NODE_ENV === "production"
      ? "https://www.gmobile.biz/esim/package"
      : "https://www-dev.gmobile.biz/esim/package";
  return {
    redirect: {
      destination,
      permanent: true,
    },
  };
};

export default function PackagePage() {
  return <></>;
}
