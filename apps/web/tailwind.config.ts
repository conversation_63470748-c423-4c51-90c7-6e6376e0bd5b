import type { Config } from "tailwindcss";

const config: Config = {
  important: "#app",
  content: [
    "./src/app/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/[locale]/**/*.{js,ts,jsx,tsx,mdx}",
    "../../packages/ui/src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      xxs: "400px",
      xs: "480px",
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
    },
    extend: {
      colors: {
        primary: "#c72b4d",
        secondary: "#FEF4F6",
        neutral: "#777777",
        action: "#fedb18",
      },
      fontSize: {
        xxs: "0.6rem",
      },
    },
  },
};
export default config;
