{"emailsentverification": "<PERSON>úng tôi đã gửi email tới {{ email}}. Chỉ cần nhấp vào liên kết trong email đó để hoàn tất đăng ký của bạn.", "verify-fail": {"title": "<PERSON><PERSON><PERSON><PERSON> thể xác thực", "header": "<PERSON><PERSON><PERSON> thực email thất bại", "txt": "<PERSON><PERSON> xảy ra lỗi khi xác thực email của bạn. <PERSON><PERSON> lòng kiểm tra email và thử lại.", "btn": {"continue": "<PERSON><PERSON><PERSON><PERSON>"}}, "closebtn": {"image": "close btn"}, "verifyemail": {"title": "<PERSON>ui lòng xác thực email", "header": {"title": "<PERSON><PERSON><PERSON> thực tài k<PERSON> - Global Mobile eSIM"}}, "verificationemailsent": "<PERSON>ail xác thực đã đư<PERSON>c gửi thành công.", "verificationemailnotsent": "Rất tiếc! chúng tôi không thể gửi email xác thực.", "verifyemailimage": "<PERSON><PERSON><PERSON> thực email", "emailnotification": {"txt": "Bạn sắp hoàn tất! Chúng tôi đã gửi email tới <b>{{email}}</b> Chỉ cần nhấp vào liên kết trong email đó để hoàn tất đăng ký của bạn. Nếu bạn không tìm thấy email, vui lòng kiểm tra mục thư rác của bạn."}, "btn": {"resendemail": "G<PERSON>i lại email x<PERSON>c thực"}, "verify-success": {"title": "<PERSON><PERSON><PERSON> thực địa chỉ email thành công", "header": {"title": "<PERSON><PERSON><PERSON> thực thành công"}, "close": {"image": "close btn"}, "success": {"image": "<PERSON><PERSON><PERSON> thực email"}, "btn": {"continue": "<PERSON><PERSON><PERSON><PERSON>"}}}