{"validation": {"minlength1": {"firstname": "名字至少必須包含1個字元。", "lastname": "姓氏至少必須包含1個字元。", "profileimage": "個人檔案圖片至少必須包含1個字元。"}, "minlength2": {"defaultpaymentmethod": "預設付款方式至少必須包含2個字元。", "locale": "預設語言（語系）至少必須包含2個字元。", "referral": "預設推薦碼至少必須包含2個字元。", "website": "預設網站至少必須包含2個字元。"}, "wrongusernamepassword": "用戶名或密碼錯誤。", "failed": "輸入數據未通過驗證規則。", "error4009": {"label": "An account already exists with the same email address logged in using social login. Please use your social login to sign in."}}, "invalid": {"email": "提供的電子郵件地址無效。", "password": "輸入的密碼無效。", "resetpasswordcode": "輸入的重設密碼碼無效。", "verifyemailcode": "Código de verificación de correo electrónico inválido", "firstname": "輸入的名字無效。", "lastname": "輸入的姓氏無效。", "username": {"0": "提", "1": "供", "2": "的", "3": "用", "4": "戶", "5": "名", "6": "不", "7": "符", "8": "合", "9": "要", "10": "求", "11": "。", "password": "提供的用戶名和密碼組合不匹配。"}, "newpasswordsameoldpassword": "新密碼不能與舊密碼相同。", "plan": "輸入的方案無效。", "order": {"dataoptionid": "輸入的數據選項無效。", "quantity": "訂購數量無效。", "topupid": "輸入的充值ID無效。", "iccid": "輸入的訂單ICCID無效。"}, "usernotconfirmed": "用戶尚未確認。", "request": "請求格式錯誤或缺少必需的參數。", "json": "請求內容不是有效的JSON格式。", "phone": {"found": "電話號碼已存在。"}, "token": "提供的認證令牌無效或已過期。", "operation": "無法對該資源執行請求的操作。"}, "unable": {"resendverificationemail": "無法重新發送驗證電子郵件。", "forgotpassword": "無法發送重置密碼的電子郵件。", "changepassword": "無法更改密碼。", "confirmpassword": "無法確認密碼。"}, "login": {"social": {"email": "通過電子郵件登入時發生錯誤。", "social": "通過社交登入時發生錯誤。"}, "wrongpassword": "輸入的密碼錯誤。", "error": {"wrong": {"password": "您的密碼或電子郵件地址可能有誤。"}, "not-authorized-exception": "電子郵件或密碼無效。請再試一次。"}}, "alreadyexists": {"email": "使用該電子郵件的帳戶已註冊。", "phone": "電話號碼已註冊至其他用戶。"}, "user": {"notfound": "找不到用戶。", "not": {"found": "指定的用戶不存在。"}}, "esim": {"subscribe": "訂閱/購買eSIM時發生錯誤。", "checkusage": "無法檢索已購買eSIM的使用狀態。", "unauthorizedaccess": "未授權訪問已購買的eSIM詳細信息。", "getorders": "通過電子郵件登入時發生錯誤。", "notfound": "找不到eSIM。"}, "plan": {"notfound": "找不到方案。", "allplans": "無法檢索所有方案。"}, "request": {"missingparameters": "請求格式錯誤或缺少必要的參數。", "invalidformat": "請求正文不是有效的JSON格式。", "duplicatecontent": "內容已存在。", "missingtoken": "認證令牌缺失於請求標頭中。", "expiredtoken": "提供的認證令牌無效或已過期。", "insufficientpermissions": "用戶沒有足夠的權限執行此操作。", "accessdenied": "用戶不被允許訪問請求的資源。", "userblocked": "用戶帳戶被封鎖或已停用。", "resourcenotfound": "請求的資源不存在。", "resourceconflict": "請求的操作與當前資源狀態衝突。", "validationfailed": "輸入的數據未通過驗證規則。", "operationconflict": "無法對該資源執行請求的操作。"}, "server": {"unexpectederror": "伺服器發生了意外錯誤。", "databaseoperationfailed": "資料庫操作失敗。", "externalserviceerror": "與外部服務通信時發生錯誤。"}, "code": {"4000": "電子郵件地址已存在。"}, "email": {"not": {"verified": "電子郵件地址未驗證。"}}, "account": {"not": {"idp": {"manual": "帳戶是使用社交登入創建的。"}}}, "content": {"conflict": "重複的內容。"}, "missing": {"token": "認證令牌在請求標頭中丟失。"}, "insufficient": {"permissions": "用戶沒有足夠的權限執行此操作。"}, "access": {"denied": "不允許用戶訪問請求的資源。"}, "blocked": {"user": "用戶帳戶被封鎖或停用。"}, "resource": {"not": {"found": "請求的資源不存在。"}}, "product": {"not": {"found": "指定的產品不存在。"}}, "duplicate": {"resource": "已經存在具有相同標識符的資源。"}, "conflicting": {"state": "請求的操作與資源的當前狀態衝突。"}, "internal": {"server": {"error": "伺服器發生了意外錯誤。"}}, "database": {"error": "數據庫操作失敗。"}, "external": {"service": {"error": "與外部服務通信時發生錯誤。"}}, "coupons": {"countryspecificcoupon": "此區域不允許使用優惠券。", "couponactive": "優惠券未啟用。", "couponexpired": "優惠券已過期。", "coupononwrongplan": "此方案不可使用優惠券代碼。", "couponperpersonusageexceeded": "優惠券代碼僅能使用一次。", "couponusageexceeded": "優惠券代碼的使用次數已超過限制。", "loyalcustomercoupon": "您已在此活動中使用過優惠券。", "onlyoncecampaign": "您已在此活動中使用過優惠券。", "selfonlycoupon": "無效的優惠券。"}}