{"logout": "로그아웃", "accountinformation": "계정설정", "country-esim": "나라별 eSIM", "regional-esim": "지역적인 eSIM", "resendanemail": "메일 재전송", "features": "특징", "compatibility": {"0": "호", "1": "환", "2": "성", "function": "eSIM 호환성 확인"}, "specs": "스펙", "language": {"title": "语言 / Language", "or": "또는"}, "copyable-item": {"btn": {"copy": {"txt": "복사"}}}, "dataplan": {"fixeditem": {"no-price": "{{ dataPlan }} / 30-일", "with-price": "{{ dataPlan }} / 30일에  ${{ price }}"}, "item": "{{ dataPlan }}  ${{ price }}/날", "recommended": "추천", "bestseller": "베스트셀러", "capacity": "데이터 용량", "capacity-total": "총 데이터 용량", "usage-days": "사용 일수", "communication-line": "네트워크"}, "btn": {"login": "로그인", "getyouresim": "바로 주문하기"}, "navbar": {"profile": {"label": "프로필"}, "shop": {"label": "상품검색"}, "home": {"label": "홈"}, "myesim": {"label": "구입 내역"}, "faq": {"label": "자주 묻는 질문"}, "help": {"label": "도움말"}, "account": {"label": "계정"}, "destination": {"label": "목적지"}, "column": {"label": "열"}, "pocketwifi": {"label": "포켓 와이파이"}, "chargewifi": {}, "contactus": {"label": "문의하기"}}, "siminfo-card": {"remainingdata": "남은 데이터", "usageperiod": "이용기간", "plantype": "eSIM 플랜:", "reminded-message": "*데이터 용량을 {{dataType}}, 을 초과하신 경우에는 통신속도가 느려 질 수 있습니다..", "notinuse": {"txt": "미사용"}, "notavailable": "-", "tag": {"expired": "기한만료", "activated": "시작", "notactivated": "미사용"}, "orderdate": "주문일", "countryplaceholder": "country placeholder", "fixedday": {"type": "데이터 플랜"}, "perday": {"type": "데일리 플랜", "unit": "일"}, "order-id": "주문 아이디", "currentdatausage": "현재의 데이터 사용량", "usagedays": {"title": "이용일수", "usageplanunit_one": "{{ count }} 일", "usageplanunit_other": "{{ count }} 일", "usageplanunit_many": "siminfoCard.usage-days.usage-plan-unit_many"}, "resettimenotification": "데이터용량은 다음날(24시간마다) 리셋됩니다.", "dataplan": {"title": "데이터 용량"}, "validity": {"unit_one": "{{ count }} 일", "unit_other": "{{ count }} 일", "unit_many": "siminfoCard.validity.unit_many"}, "expiredate": {"title": "유효기한"}, "info": {"txt": "이용하시는 단말을 선택하신 후, eSIM 설치를 해 주세요"}, "provider": "Global Mobile eSIM"}, "page-header": {"btn": {"back": "back btn", "close": "close btn"}}, "social-login": {"fbicon": {"alt": "FB icon"}, "link": {"facebook": "페이스북으로 로그인", "google": "구글로 로그인"}, "googleicon": {"alt": "Google icon"}}, "social-signup": {"link": {"facebook": "페이스북으로 로그인", "google": "구글로 로그인"}}, "payment-method": {"creditcard": "신용카드"}, "regional-plan-amount": "<1>{{ 양 }}</1><0>〜</0>", "company-name": "Inbound Platform Corp.", "division-name": "Global Mobile Division", "company-address": "SW Shinbashi Building 4th floor, 6-14-5 Shinbashi, Minato-ku, Tokyo 105-0004", "ctabutton": "지금 구매하기", "cart": {"menu": {"total": "전체", "emptycart": "카트가 비었습니다", "youritems": "상품 내역", "continueshopping": "쇼핑 계속하기", "checkout": "결제", "discount": "할인", "details": "상세", "item": {"unlimited": {"4g": "일본 eSIM 4G 무제한", "5g": "일본 eSIM 5G 무제한"}, "unlimitedinsured5g": "일본 eSIM 5G 무제한 + 보험", "unlimitedinsured4g": "일본 eSIM 4G 무제한 + 보험", "fixed": "일본 eSIM {{dataVolume}}", "fixedinsured": "일본 eSIM {{dataVolume}} + 보험"}, "addtocart": "Add to cart", "ordersummary": "Order Summary"}, "error": {"emptycartwarning": "모든 상품을 삭제할까요?", "deleteitemwarning": "모든 상품이 삭제 됩니다. 다시한번 확인 해주세요!"}}, "topup": {"title": "Topup", "recharge": {"title": "Recharge"}, "importantinformation": {"title": "Important Information", "message": "Topping up to existing esim will remove your remaining data and replace it with new data, your new data will be {{dataVolume}} for {{dayCount}} day(s). ", "warning": "will be deleted. We recommend purchasing again after using up all your data."}, "postpurchaseinformation": {"title": "Topup Successful", "message": "Your eSIM has been successfully topped up. You do not need to reinstall the eSIM. The QR code below belongs to your topped-up eSIM. To access the new data, simply toggle Airplane Mode on and off on your mobile device. No further steps are required for scanning or installation."}, "input": {"placeholderguest": "Enter your order id", "label": "This will topup your existing eSIM {{orderId}}", "placeholderauth": "Select your eSIM"}, "history": {"title": "Topup History", "table": {"order-id": "Order ID", "datavolume": "Data Volume", "validitydays": "Validity Days", "purchasedon": "Purchased On"}, "nodata": "No Records Found"}, "dataplan": {"title": "Data plan to recharge"}}}