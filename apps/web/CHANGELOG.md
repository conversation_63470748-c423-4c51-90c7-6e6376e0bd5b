# Changelog

## [1.15.2](https://github.com/InboundPlatform/global-esim/compare/web-v1.15.1...web-v1.15.2) (2025-06-24)


### Bug Fixes

* update mobile app translations ([d0de903](https://github.com/InboundPlatform/global-esim/commit/d0de903f162f924029137a9dec135c943581928c))
* update mobile app translations ([c21b9fc](https://github.com/InboundPlatform/global-esim/commit/c21b9fc087c4a3adf2a842e11392da10162f35e9))
* update mobile tl ([3d0db67](https://github.com/InboundPlatform/global-esim/commit/3d0db6706735cd8973c63406e4743f15d38d61a5))

## [1.15.1](https://github.com/InboundPlatform/global-esim/compare/web-v1.15.0...web-v1.15.1) (2025-06-19)


### Bug Fixes

* update mobile app translations ([9cb96fd](https://github.com/InboundPlatform/global-esim/commit/9cb96fd18ab9b1bad1444f82decac9bf1938a481))
* update mobile app translations ([f3879df](https://github.com/InboundPlatform/global-esim/commit/f3879dfaebcfa567d35f1d80f1ba03b569a3e40a))

## [1.15.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.14.3...web-v1.15.0) (2025-06-18)


### Features

* **paypay:** add the error path with locale for the global esim multilang ([b101fa4](https://github.com/InboundPlatform/global-esim/commit/b101fa4c9f5942907a086aad1f48c2efb9903169))
* **paypay:** add translations for paypay airtrip and gm esim jp page ([6413f66](https://github.com/InboundPlatform/global-esim/commit/6413f66faebdefda53e908a4611f8ee5c49da71b))
* **paypay:** add translations for paypay error page ([eb0980b](https://github.com/InboundPlatform/global-esim/commit/eb0980b01e21b6a2e747fa171b95443ea224f2da))

## [1.14.3](https://github.com/InboundPlatform/global-esim/compare/web-v1.14.2...web-v1.14.3) (2025-06-09)


### Bug Fixes

* update auth token and project name for sentry ([b8e4372](https://github.com/InboundPlatform/global-esim/commit/b8e4372f875a6be1ebeaa0a5c54260e11c03d24a))

## [1.14.2](https://github.com/InboundPlatform/global-esim/compare/web-v1.14.1...web-v1.14.2) (2025-06-05)


### Bug Fixes

* remove error page for sentry verification ([9428e68](https://github.com/InboundPlatform/global-esim/commit/9428e6899c9462c5fff504cab9053e95e1d7e30d))

## [1.14.1](https://github.com/InboundPlatform/global-esim/compare/web-v1.14.0...web-v1.14.1) (2025-06-05)


### Bug Fixes

* coupon issue ([4cfe556](https://github.com/InboundPlatform/global-esim/commit/4cfe5567ac833672cc0978912f4cdd794df0412a))
* ireland image update ([79a5188](https://github.com/InboundPlatform/global-esim/commit/79a5188e4fd9aa1be79d8c30488e3f6e41fffb58))
* sentry env for webjp and web ([9a27005](https://github.com/InboundPlatform/global-esim/commit/9a27005587c9090488bd8adcb30a0808830d7f3d))

## [1.14.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.13.1...web-v1.14.0) (2025-05-29)


### Features

* **coupon-function:** add coupon function to gloable esim en ([521e784](https://github.com/InboundPlatform/global-esim/commit/521e78406faef3df20a6f8e0042e46f85f0ef5cc))
* **coupon-function:** fix empty cart button issue ([491d3b5](https://github.com/InboundPlatform/global-esim/commit/491d3b5147a6a99ed169c4666389dd2c52b85c90))
* **coupon-function:** fix issue with the discounted pproduct price and added columns for final price ([a4f6a85](https://github.com/InboundPlatform/global-esim/commit/a4f6a85f81a377002c853a6e971b66753b5f2d24))
* **coupon-function:** fix multiple rendering issue on coupon apply ([7dc13ff](https://github.com/InboundPlatform/global-esim/commit/7dc13ff946e83eb8afa28f4837ae47a4e1448b5e))
* **coupon-function:** fix ui issue with multiple order checkout page ([b7db392](https://github.com/InboundPlatform/global-esim/commit/b7db392568e9d8154ccdf57aa93876929d06c3cc))
* **store-affiliate:** get the affiliate from the referer ([f77f20a](https://github.com/InboundPlatform/global-esim/commit/f77f20a70438f41016ca794a087eb21efc9d48ca))
* **store-affiliate:** stop redirection and return modified response on saving cookie ([d72181e](https://github.com/InboundPlatform/global-esim/commit/d72181e29b42c7b1e757588310724f502e31bc73))


### Bug Fixes

* gmocp free esim fixes ([9164f61](https://github.com/InboundPlatform/global-esim/commit/9164f618ceeafe4bdc597a173840afd1a6e60bf4))
* **web:** update banner order and compatibility list for mobile app ([746c41b](https://github.com/InboundPlatform/global-esim/commit/746c41b121d5db58174caf61898eab097a6edd26))

## [1.13.1](https://github.com/InboundPlatform/global-esim/compare/web-v1.13.0...web-v1.13.1) (2025-05-16)


### Bug Fixes

* update translations ([dd66019](https://github.com/InboundPlatform/global-esim/commit/dd660196934f64eb435a5f5965361e990b8e4d0b))

## [1.13.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.12.1...web-v1.13.0) (2025-04-24)


### Features

* add country param gmesim multi-lang ([05eb9fe](https://github.com/InboundPlatform/global-esim/commit/05eb9fe2b914857fcf40de2c5f43d8dfc733a22b))
* add price to plan dropdown ([#1603](https://github.com/InboundPlatform/global-esim/issues/1603)) ([6a3a05a](https://github.com/InboundPlatform/global-esim/commit/6a3a05a70c291d07ae4602ca7d4ca2647ba3f399))
* add reset password with code and email for lost users ([6605a1e](https://github.com/InboundPlatform/global-esim/commit/6605a1eb4bb6b7a1812f1f639117aa6d8513ee01))
* add to specification table ([#1571](https://github.com/InboundPlatform/global-esim/issues/1571)) ([552deb9](https://github.com/InboundPlatform/global-esim/commit/552deb90f25aa34562c6ee9b83b7bfe9ea65e388))
* region page SEO improvements ([a8d77f7](https://github.com/InboundPlatform/global-esim/commit/a8d77f7493e2a7a698f29fd927612f4ad64b0e48))
* topup api and ui with flow ([a4945cd](https://github.com/InboundPlatform/global-esim/commit/a4945cd6001339fb275df6a50dde1263fe049ce6))


### Bug Fixes

* add coupon currency with default to USD ([d4a03da](https://github.com/InboundPlatform/global-esim/commit/d4a03dac96e9edec89c0e3b120b08b532030668e))
* add missing faq ([#1465](https://github.com/InboundPlatform/global-esim/issues/1465)) ([d56ce85](https://github.com/InboundPlatform/global-esim/commit/d56ce850bc91fdd417a29591c5f5d341e9f2b0a1))
* add multi lang translations ([#1478](https://github.com/InboundPlatform/global-esim/issues/1478)) ([c7be571](https://github.com/InboundPlatform/global-esim/commit/c7be571464d04ab5f2ddbf7c4b06db8bcf791c66))
* basket follow up issues fixes ([1969ecc](https://github.com/InboundPlatform/global-esim/commit/1969eccceb9a15e04815e37757b0031c9257c885))
* catch error on all routes ([b8c262c](https://github.com/InboundPlatform/global-esim/commit/b8c262c8a9558bb7977241b9dc5423bc5c43da5b))
* convert png to webp gmesim multi-lang ([a306681](https://github.com/InboundPlatform/global-esim/commit/a3066810c997703b4c661554d97be968350b3038))
* faq correct links ([#1546](https://github.com/InboundPlatform/global-esim/issues/1546)) ([6f0191e](https://github.com/InboundPlatform/global-esim/commit/6f0191e0a37c08d30791b3a1ac47bafb94786227))
* faq correct links ([#1546](https://github.com/InboundPlatform/global-esim/issues/1546)) ([2e3b19d](https://github.com/InboundPlatform/global-esim/commit/2e3b19dc53cdc143d2181cbabb8919abb3cf52f6))
* faq missing content ([#1549](https://github.com/InboundPlatform/global-esim/issues/1549)) ([f7a853d](https://github.com/InboundPlatform/global-esim/commit/f7a853df921a16481354e2bb2a70d2388be35060))
* faq missing content ([#1549](https://github.com/InboundPlatform/global-esim/issues/1549)) ([fa4577c](https://github.com/InboundPlatform/global-esim/commit/fa4577cea86daf074e2de6e5a4291715d31f3ade))
* ges 113 update faq ([#1453](https://github.com/InboundPlatform/global-esim/issues/1453)) ([6524072](https://github.com/InboundPlatform/global-esim/commit/6524072442cdd27bb53028a7f4060e9f76849327))
* hyperlinks and translation consumption ([4810d09](https://github.com/InboundPlatform/global-esim/commit/4810d09f05e314a9d55182039266a55038fd911c))
* kr translations ([#1589](https://github.com/InboundPlatform/global-esim/issues/1589)) ([7e460f2](https://github.com/InboundPlatform/global-esim/commit/7e460f2393c5dd5dadc94c9d5539e4b466c1c2e4))
* language update for airtrip, missing locale for qatar, turkey and republic of south africa ([165dd55](https://github.com/InboundPlatform/global-esim/commit/165dd550380927e55d013780dd468a7aa4ea48d2))
* language, mobile app locale addition for airtrip mobile app ([bb53ec1](https://github.com/InboundPlatform/global-esim/commit/bb53ec1c0068bd414ac839495d3ef140183e6143))
* misc fixes ([907830c](https://github.com/InboundPlatform/global-esim/commit/907830c29baa6425232f824cfda5778ac0b5b042))
* refactor top page to ssg gmesim multi-lang ([733f5b6](https://github.com/InboundPlatform/global-esim/commit/733f5b656d279ff58d6e0ce1eaa5ca528a819f97))
* set proper id at top of complete page ([#1572](https://github.com/InboundPlatform/global-esim/issues/1572)) ([9c88195](https://github.com/InboundPlatform/global-esim/commit/9c881955adfd2bf7b3274615a2b49afd1e26bf33))
* show qr on the esim details accordion ([624719d](https://github.com/InboundPlatform/global-esim/commit/624719d48bd81c6f0ec1561ae2900f8638636c40))
* social login ([c14edb7](https://github.com/InboundPlatform/global-esim/commit/c14edb7367d34869478f9651d2ce61fe7499c2a4))
* translation changes for airtrip mobile app ([0ece45a](https://github.com/InboundPlatform/global-esim/commit/0ece45ab2573cff1431e94ecffbb9da1923659b3))
* translation fixes for error sheet ([97c7a35](https://github.com/InboundPlatform/global-esim/commit/97c7a357199eced4de2122b017780be1e4a42e87))
* trigger release ([47b8a2d](https://github.com/InboundPlatform/global-esim/commit/47b8a2df46e5e181c6cb2254a02d4dae2517180e))
* trigger release ([1751f0b](https://github.com/InboundPlatform/global-esim/commit/1751f0baf5d45ab22b0dcfb931af8a69f353bf93))
* trigger release ([1adf426](https://github.com/InboundPlatform/global-esim/commit/1adf42601e6d303f52fda8f14e62bb78c939581f))
* trigger translation update ([#1568](https://github.com/InboundPlatform/global-esim/issues/1568)) ([ff1682d](https://github.com/InboundPlatform/global-esim/commit/ff1682dfddbc4382c4c4cdbc157934425f655eb1))
* trigger web deploy ([e7bcc12](https://github.com/InboundPlatform/global-esim/commit/e7bcc124f28e5eb74bfcd88ce0c6691a271a3c2b))
* update speclist network section ([#1464](https://github.com/InboundPlatform/global-esim/issues/1464)) ([fa36642](https://github.com/InboundPlatform/global-esim/commit/fa3664259581520ba0295ed320f106068acd9d00))
* update text, button text ([1a61c46](https://github.com/InboundPlatform/global-esim/commit/1a61c46d02e83f516be3742992e553e43aa34607))
* update translation for link fix ([#1479](https://github.com/InboundPlatform/global-esim/issues/1479)) ([568143c](https://github.com/InboundPlatform/global-esim/commit/568143c0e22ffa145bbeaa22cbb0ca0e66c5c4b4))
* update translations ([#1588](https://github.com/InboundPlatform/global-esim/issues/1588)) ([93c41f5](https://github.com/InboundPlatform/global-esim/commit/93c41f5a5fdbb3836f86439449a211f81b6aa39c))
* use translation ([c67fc76](https://github.com/InboundPlatform/global-esim/commit/c67fc7604a954a36bcc8acccc5e2bc4b24f708f7))
* **web:** import not found component dynamically with no ssr ([52c6459](https://github.com/InboundPlatform/global-esim/commit/52c6459549a676dac6a0dc4fe7153b9c3dfa152d))
* wip basket ([5c502cf](https://github.com/InboundPlatform/global-esim/commit/5c502cfc665d3a947f49fd751b911c9f40c61500))

## [1.12.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.11.1...web-v1.12.0) (2025-04-17)


### Features

* add price to plan dropdown ([#1603](https://github.com/InboundPlatform/global-esim/issues/1603)) ([6a3a05a](https://github.com/InboundPlatform/global-esim/commit/6a3a05a70c291d07ae4602ca7d4ca2647ba3f399))

## [1.11.1](https://github.com/InboundPlatform/global-esim/compare/web-v1.11.0...web-v1.11.1) (2025-04-14)


### Bug Fixes

* refactor top page to ssg gmesim multi-lang ([733f5b6](https://github.com/InboundPlatform/global-esim/commit/733f5b656d279ff58d6e0ce1eaa5ca528a819f97))

## [1.11.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.10.4...web-v1.11.0) (2025-04-10)


### Features

* add to specification table ([#1571](https://github.com/InboundPlatform/global-esim/issues/1571)) ([552deb9](https://github.com/InboundPlatform/global-esim/commit/552deb90f25aa34562c6ee9b83b7bfe9ea65e388))


### Bug Fixes

* catch error on all routes ([b8c262c](https://github.com/InboundPlatform/global-esim/commit/b8c262c8a9558bb7977241b9dc5423bc5c43da5b))
* kr translations ([#1589](https://github.com/InboundPlatform/global-esim/issues/1589)) ([7e460f2](https://github.com/InboundPlatform/global-esim/commit/7e460f2393c5dd5dadc94c9d5539e4b466c1c2e4))
* set proper id at top of complete page ([#1572](https://github.com/InboundPlatform/global-esim/issues/1572)) ([9c88195](https://github.com/InboundPlatform/global-esim/commit/9c881955adfd2bf7b3274615a2b49afd1e26bf33))
* update translations ([#1588](https://github.com/InboundPlatform/global-esim/issues/1588)) ([93c41f5](https://github.com/InboundPlatform/global-esim/commit/93c41f5a5fdbb3836f86439449a211f81b6aa39c))
* **web:** import not found component dynamically with no ssr ([52c6459](https://github.com/InboundPlatform/global-esim/commit/52c6459549a676dac6a0dc4fe7153b9c3dfa152d))

## [1.10.4](https://github.com/InboundPlatform/global-esim/compare/web-v1.10.3...web-v1.10.4) (2025-04-03)


### Bug Fixes

* trigger translation update ([#1568](https://github.com/InboundPlatform/global-esim/issues/1568)) ([ff1682d](https://github.com/InboundPlatform/global-esim/commit/ff1682dfddbc4382c4c4cdbc157934425f655eb1))

## [1.10.3](https://github.com/InboundPlatform/global-esim/compare/web-v1.10.2...web-v1.10.3) (2025-03-28)


### Bug Fixes

* faq correct links ([#1546](https://github.com/InboundPlatform/global-esim/issues/1546)) ([6f0191e](https://github.com/InboundPlatform/global-esim/commit/6f0191e0a37c08d30791b3a1ac47bafb94786227))
* faq missing content ([#1549](https://github.com/InboundPlatform/global-esim/issues/1549)) ([f7a853d](https://github.com/InboundPlatform/global-esim/commit/f7a853df921a16481354e2bb2a70d2388be35060))

## [1.10.2](https://github.com/InboundPlatform/global-esim/compare/web-v1.10.1...web-v1.10.2) (2025-03-27)


### Bug Fixes

* basket follow up issues fixes ([1969ecc](https://github.com/InboundPlatform/global-esim/commit/1969eccceb9a15e04815e37757b0031c9257c885))
* language update for airtrip, missing locale for qatar, turkey and republic of south africa ([165dd55](https://github.com/InboundPlatform/global-esim/commit/165dd550380927e55d013780dd468a7aa4ea48d2))
* language, mobile app locale addition for airtrip mobile app ([bb53ec1](https://github.com/InboundPlatform/global-esim/commit/bb53ec1c0068bd414ac839495d3ef140183e6143))
* translation changes for airtrip mobile app ([0ece45a](https://github.com/InboundPlatform/global-esim/commit/0ece45ab2573cff1431e94ecffbb9da1923659b3))
* trigger release ([47b8a2d](https://github.com/InboundPlatform/global-esim/commit/47b8a2df46e5e181c6cb2254a02d4dae2517180e))

## [1.10.1](https://github.com/InboundPlatform/global-esim/compare/web-v1.10.0...web-v1.10.1) (2025-03-19)


### Bug Fixes

* trigger web deploy ([e7bcc12](https://github.com/InboundPlatform/global-esim/commit/e7bcc124f28e5eb74bfcd88ce0c6691a271a3c2b))

## [1.10.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.9.0...web-v1.10.0) (2025-03-19)


### Features

* add country param gmesim multi-lang ([05eb9fe](https://github.com/InboundPlatform/global-esim/commit/05eb9fe2b914857fcf40de2c5f43d8dfc733a22b))


### Bug Fixes

* convert png to webp gmesim multi-lang ([a306681](https://github.com/InboundPlatform/global-esim/commit/a3066810c997703b4c661554d97be968350b3038))
* translation fixes for error sheet ([97c7a35](https://github.com/InboundPlatform/global-esim/commit/97c7a357199eced4de2122b017780be1e4a42e87))

## [1.9.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.8.3...web-v1.9.0) (2025-03-14)


### Features

* **api:** campaign prices issues fixes and standardize ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))
* **api:** improve basket feature ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))
* enhanced authentication flow ([ca7a64c](https://github.com/InboundPlatform/global-esim/commit/ca7a64c2c712241ab2c12ffffb0b3e496e2b2c2a))


### Bug Fixes

* activation setup page fix ([20512f5](https://github.com/InboundPlatform/global-esim/commit/20512f5995aa05c81a26e0bdcb49f1eff7cb5583))
* add missing faq ([#1465](https://github.com/InboundPlatform/global-esim/issues/1465)) ([d56ce85](https://github.com/InboundPlatform/global-esim/commit/d56ce850bc91fdd417a29591c5f5d341e9f2b0a1))
* add multi lang translations ([#1478](https://github.com/InboundPlatform/global-esim/issues/1478)) ([c7be571](https://github.com/InboundPlatform/global-esim/commit/c7be571464d04ab5f2ddbf7c4b06db8bcf791c66))
* add overlay ([73fdb65](https://github.com/InboundPlatform/global-esim/commit/73fdb65f20d1a3316438d83c210ec28f2548d5f4))
* **api:** enhanced api caching for plans ([63719be](https://github.com/InboundPlatform/global-esim/commit/63719becbc50c7d9adecff8af959dbc76a53549d))
* basket api ([d3ce9cf](https://github.com/InboundPlatform/global-esim/commit/d3ce9cf4bc66cb36206a2f3b0ce72001ce0cad60))
* broken cover image for some countries ([a99c630](https://github.com/InboundPlatform/global-esim/commit/a99c63033eea0a0e47f1596026001785270c0d54))
* dev deploy ([57390a1](https://github.com/InboundPlatform/global-esim/commit/57390a1008e0b70f25d43b3dc851fb37db490752))
* env issues for dockerfile ([de79151](https://github.com/InboundPlatform/global-esim/commit/de79151d23de9626c8ac909911345c854b0d9215))
* feedback from PM ([2347444](https://github.com/InboundPlatform/global-esim/commit/2347444543bfdfeccf6bef37063a51dc30d0aa41))
* ges 113 update faq ([#1453](https://github.com/InboundPlatform/global-esim/issues/1453)) ([6524072](https://github.com/InboundPlatform/global-esim/commit/6524072442cdd27bb53028a7f4060e9f76849327))
* gm app redirect urls ([be6e389](https://github.com/InboundPlatform/global-esim/commit/be6e38959f5575ae64df77bd2205a477526cf605))
* hyperlinks and translation consumption ([4810d09](https://github.com/InboundPlatform/global-esim/commit/4810d09f05e314a9d55182039266a55038fd911c))
* korea campaign fixes ([975bc66](https://github.com/InboundPlatform/global-esim/commit/975bc667dc8d69e53de81b3364b442542a5aa72b))
* language select url and environment ([52e26ac](https://github.com/InboundPlatform/global-esim/commit/52e26acc16abc19dd91b147d3ab4e16bddeb685e))
* language select url error ([2d070df](https://github.com/InboundPlatform/global-esim/commit/2d070dff07a5d91a13311af3ac4662eda0bcccd2))
* merge branch develop-esim to develop, which was used to serve assets and documents to mobile app ([5d62e15](https://github.com/InboundPlatform/global-esim/commit/5d62e1594fc618450b91257dcc3b8fd191dd9fe7))
* missing user source for social login users ([4581a7d](https://github.com/InboundPlatform/global-esim/commit/4581a7d04609db3a7520383cbb8e458b014085c3))
* payment button fix, content fix ([701646d](https://github.com/InboundPlatform/global-esim/commit/701646d91306c0fa3f55a113eb6523aef43b2045))
* refactor aside menu and update menu links ([#1416](https://github.com/InboundPlatform/global-esim/issues/1416)) ([8b48385](https://github.com/InboundPlatform/global-esim/commit/8b48385c7712ac4811ffb6ba58779d6443a2a001))
* remove app breadcrumb from multi language site ([cdd0803](https://github.com/InboundPlatform/global-esim/commit/cdd08035480ec20ece479ada54a4c653cbcef886))
* remove cta button from side menu ([9ef0ebd](https://github.com/InboundPlatform/global-esim/commit/9ef0ebd6e18bba4478cf97606c1de9b448fd9993))
* set language select url for dev or prod ([4d162ef](https://github.com/InboundPlatform/global-esim/commit/4d162ef8961f6e2bb3bc46eb63df7295a01b3bf7))
* show qr on the esim details accordion ([624719d](https://github.com/InboundPlatform/global-esim/commit/624719d48bd81c6f0ec1561ae2900f8638636c40))
* stepper zindex issue on checkout page when korea plan is selected ([827e202](https://github.com/InboundPlatform/global-esim/commit/827e202146f3a13080d5a1bd380f842f80860164))
* top page clipping ([#1418](https://github.com/InboundPlatform/global-esim/issues/1418)) ([0b912aa](https://github.com/InboundPlatform/global-esim/commit/0b912aaba48022080b520d20a3ed7ddbd3ecc9df))
* translation at the my orders - guide page ([fb01f39](https://github.com/InboundPlatform/global-esim/commit/fb01f398cd9daf3ce9a82389d09a8ccadfa5aeb9))
* translation update web 2025-01-30 ([4acce4c](https://github.com/InboundPlatform/global-esim/commit/4acce4c6ce2ddb044997e94d1bdc03a48f5aa5bb))
* trigger deploys for airtrip and web ([9de8b8b](https://github.com/InboundPlatform/global-esim/commit/9de8b8bf7c016f6047a107857a2cecec565cf86a))
* trigger release ([1adf426](https://github.com/InboundPlatform/global-esim/commit/1adf42601e6d303f52fda8f14e62bb78c939581f))
* update banner and transtion ([fac3d47](https://github.com/InboundPlatform/global-esim/commit/fac3d476ec61fa3b82cb360db621736b69d32042))
* update language selector for gm multi lang ([d17f068](https://github.com/InboundPlatform/global-esim/commit/d17f068bc82e5dcb0a8d099e0389fcb730c0c8a7))
* update speclist network section ([#1464](https://github.com/InboundPlatform/global-esim/issues/1464)) ([fa36642](https://github.com/InboundPlatform/global-esim/commit/fa3664259581520ba0295ed320f106068acd9d00))
* update text, button text ([1a61c46](https://github.com/InboundPlatform/global-esim/commit/1a61c46d02e83f516be3742992e553e43aa34607))
* update translation for link fix ([#1479](https://github.com/InboundPlatform/global-esim/issues/1479)) ([568143c](https://github.com/InboundPlatform/global-esim/commit/568143c0e22ffa145bbeaa22cbb0ca0e66c5c4b4))
* update translations ([2bbc884](https://github.com/InboundPlatform/global-esim/commit/2bbc8842905475f00924ebb61773439ebcaee996))
* use translation ([c67fc76](https://github.com/InboundPlatform/global-esim/commit/c67fc7604a954a36bcc8acccc5e2bc4b24f708f7))
* **web:** add progress bar while page loads ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))
* webjp payment button fix, content fix ([255e9d6](https://github.com/InboundPlatform/global-esim/commit/255e9d6a85fcd4992a2a05fb76c44c8972172255))
* webjp payment button fix, content fix ([075c303](https://github.com/InboundPlatform/global-esim/commit/075c30330e2ac9d05b46fc7df184f1c19e656f7d))
* **web:** reload after currency is changed ([63719be](https://github.com/InboundPlatform/global-esim/commit/63719becbc50c7d9adecff8af959dbc76a53549d))
* **web:** remove japanese text ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))

## [1.8.3](https://github.com/InboundPlatform/global-esim/compare/web-v1.8.2...web-v1.8.3) (2025-03-14)


### Bug Fixes

* add missing faq ([#1465](https://github.com/InboundPlatform/global-esim/issues/1465)) ([d56ce85](https://github.com/InboundPlatform/global-esim/commit/d56ce850bc91fdd417a29591c5f5d341e9f2b0a1))
* add multi lang translations ([#1478](https://github.com/InboundPlatform/global-esim/issues/1478)) ([c7be571](https://github.com/InboundPlatform/global-esim/commit/c7be571464d04ab5f2ddbf7c4b06db8bcf791c66))
* ges 113 update faq ([#1453](https://github.com/InboundPlatform/global-esim/issues/1453)) ([6524072](https://github.com/InboundPlatform/global-esim/commit/6524072442cdd27bb53028a7f4060e9f76849327))
* hyperlinks and translation consumption ([4810d09](https://github.com/InboundPlatform/global-esim/commit/4810d09f05e314a9d55182039266a55038fd911c))
* show qr on the esim details accordion ([624719d](https://github.com/InboundPlatform/global-esim/commit/624719d48bd81c6f0ec1561ae2900f8638636c40))
* update speclist network section ([#1464](https://github.com/InboundPlatform/global-esim/issues/1464)) ([fa36642](https://github.com/InboundPlatform/global-esim/commit/fa3664259581520ba0295ed320f106068acd9d00))
* update text, button text ([1a61c46](https://github.com/InboundPlatform/global-esim/commit/1a61c46d02e83f516be3742992e553e43aa34607))
* update translation for link fix ([#1479](https://github.com/InboundPlatform/global-esim/issues/1479)) ([568143c](https://github.com/InboundPlatform/global-esim/commit/568143c0e22ffa145bbeaa22cbb0ca0e66c5c4b4))
* use translation ([c67fc76](https://github.com/InboundPlatform/global-esim/commit/c67fc7604a954a36bcc8acccc5e2bc4b24f708f7))

## [1.8.2](https://github.com/InboundPlatform/global-esim/compare/web-v1.8.1...web-v1.8.2) (2025-02-27)


### Bug Fixes

* trigger deploys for airtrip and web ([9de8b8b](https://github.com/InboundPlatform/global-esim/commit/9de8b8bf7c016f6047a107857a2cecec565cf86a))

## [1.8.1](https://github.com/InboundPlatform/global-esim/compare/web-v1.8.0...web-v1.8.1) (2025-02-27)


### Bug Fixes

* korea campaign fixes ([975bc66](https://github.com/InboundPlatform/global-esim/commit/975bc667dc8d69e53de81b3364b442542a5aa72b))

## [1.8.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.7.0...web-v1.8.0) (2025-02-26)


### Features

* **api:** campaign prices issues fixes and standardize ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))
* **api:** improve basket feature ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))


### Bug Fixes

* add overlay ([73fdb65](https://github.com/InboundPlatform/global-esim/commit/73fdb65f20d1a3316438d83c210ec28f2548d5f4))
* **api:** enhanced api caching for plans ([63719be](https://github.com/InboundPlatform/global-esim/commit/63719becbc50c7d9adecff8af959dbc76a53549d))
* basket api ([d3ce9cf](https://github.com/InboundPlatform/global-esim/commit/d3ce9cf4bc66cb36206a2f3b0ce72001ce0cad60))
* feedback from PM ([2347444](https://github.com/InboundPlatform/global-esim/commit/2347444543bfdfeccf6bef37063a51dc30d0aa41))
* gm app redirect urls ([be6e389](https://github.com/InboundPlatform/global-esim/commit/be6e38959f5575ae64df77bd2205a477526cf605))
* missing user source for social login users ([4581a7d](https://github.com/InboundPlatform/global-esim/commit/4581a7d04609db3a7520383cbb8e458b014085c3))
* stepper zindex issue on checkout page when korea plan is selected ([827e202](https://github.com/InboundPlatform/global-esim/commit/827e202146f3a13080d5a1bd380f842f80860164))
* **web:** add progress bar while page loads ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))
* webjp payment button fix, content fix ([255e9d6](https://github.com/InboundPlatform/global-esim/commit/255e9d6a85fcd4992a2a05fb76c44c8972172255))
* **web:** reload after currency is changed ([63719be](https://github.com/InboundPlatform/global-esim/commit/63719becbc50c7d9adecff8af959dbc76a53549d))
* **web:** remove japanese text ([2d77439](https://github.com/InboundPlatform/global-esim/commit/2d77439bfeab3c31dfec2600ed6acc66db6e9143))

## [1.7.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.6.1...web-v1.7.0) (2025-02-03)


### Features

* enhanced authentication flow ([ca7a64c](https://github.com/InboundPlatform/global-esim/commit/ca7a64c2c712241ab2c12ffffb0b3e496e2b2c2a))


### Bug Fixes

* merge branch develop-esim to develop, which was used to serve assets and documents to mobile app ([5d62e15](https://github.com/InboundPlatform/global-esim/commit/5d62e1594fc618450b91257dcc3b8fd191dd9fe7))
* payment button fix, content fix ([701646d](https://github.com/InboundPlatform/global-esim/commit/701646d91306c0fa3f55a113eb6523aef43b2045))
* translation update web 2025-01-30 ([4acce4c](https://github.com/InboundPlatform/global-esim/commit/4acce4c6ce2ddb044997e94d1bdc03a48f5aa5bb))
* update banner and transtion ([fac3d47](https://github.com/InboundPlatform/global-esim/commit/fac3d476ec61fa3b82cb360db621736b69d32042))

## [1.6.1](https://github.com/InboundPlatform/global-esim/compare/web-v1.6.0...web-v1.6.1) (2025-01-27)


### Bug Fixes

* activation setup page fix ([20512f5](https://github.com/InboundPlatform/global-esim/commit/20512f5995aa05c81a26e0bdcb49f1eff7cb5583))
* broken cover image for some countries ([a99c630](https://github.com/InboundPlatform/global-esim/commit/a99c63033eea0a0e47f1596026001785270c0d54))
* build issue, server components issue ([098cc9c](https://github.com/InboundPlatform/global-esim/commit/098cc9cebc2d625add742c96a9dfeb0928adc722))
* build issues ([48fccd2](https://github.com/InboundPlatform/global-esim/commit/48fccd2e95908074a7f99a101d79999461f589b7))
* dev deploy ([57390a1](https://github.com/InboundPlatform/global-esim/commit/57390a1008e0b70f25d43b3dc851fb37db490752))
* env issues for dockerfile ([de79151](https://github.com/InboundPlatform/global-esim/commit/de79151d23de9626c8ac909911345c854b0d9215))
* password length ([#1251](https://github.com/InboundPlatform/global-esim/issues/1251)) ([7c8e8c3](https://github.com/InboundPlatform/global-esim/commit/7c8e8c36a9338c5f6f352288e3320b0cde99a423))
* remove nikkei top campaign banner ([8773369](https://github.com/InboundPlatform/global-esim/commit/87733695a1591e38d11be329fd77ea8e292606d0))
* translation at the my orders - guide page ([fb01f39](https://github.com/InboundPlatform/global-esim/commit/fb01f398cd9daf3ce9a82389d09a8ccadfa5aeb9))
* translation data update ([f3628e7](https://github.com/InboundPlatform/global-esim/commit/f3628e70ebbfb59ba83a0f49b9cbd87cd02c8cfb))
* trigger deploy ([27b9f02](https://github.com/InboundPlatform/global-esim/commit/27b9f025a51fe53f88e5037ac66df613704f9566))
* ui content fixes ([e057933](https://github.com/InboundPlatform/global-esim/commit/e0579334f17992290e00459e19cff9f83cc41104))

## [1.6.0](https://github.com/InboundPlatform/global-esim/compare/web-v1.5.0...web-v1.6.0) (2025-01-07)


### Features

* add sitemap for esim multi lang ([#1111](https://github.com/InboundPlatform/global-esim/issues/1111)) ([4814869](https://github.com/InboundPlatform/global-esim/commit/48148692f9e507cc6b6e111229c4c198a63e8f4a))
* multilang brushup - home page en metadata ([bbfc859](https://github.com/InboundPlatform/global-esim/commit/bbfc85998cd9fdb00a65773d9799bdcce627fc66))


### Bug Fixes

* add name registration guide ([#1138](https://github.com/InboundPlatform/global-esim/issues/1138)) ([dd006be](https://github.com/InboundPlatform/global-esim/commit/dd006be069b3d49b9b8064e25e64f13af59affa3))
* add name registration guides for all languages ([#1139](https://github.com/InboundPlatform/global-esim/issues/1139)) ([4dd74a8](https://github.com/InboundPlatform/global-esim/commit/4dd74a8088d99436db3abe7944d75092de804944))
* add new plans ([f6f8fd4](https://github.com/InboundPlatform/global-esim/commit/f6f8fd40719d77891891350d75632ae5ba3a2ed7))
* auto redirect to checkout page when plan is selected ([3d22308](https://github.com/InboundPlatform/global-esim/commit/3d22308fb8085ac6386b30b43c0b6f1be70ca0f4))
* en: added icon, added current year at the footer, search box fix ([330339e](https://github.com/InboundPlatform/global-esim/commit/330339efe93ca375aec3edb4ddaf52789774676a))
* esim counter korea image change ([b5c95ac](https://github.com/InboundPlatform/global-esim/commit/b5c95ac47971b747e484122a2de87962b9df2184))
* fixes for ui ([4076490](https://github.com/InboundPlatform/global-esim/commit/407649080278a0c1581fbcca4e624abd31455904))
* frontend global esim stripe seperation work ([624edd3](https://github.com/InboundPlatform/global-esim/commit/624edd3312966c80d12fb33057ef0fb4817cfe2d))
* lang updates ([d9fe4df](https://github.com/InboundPlatform/global-esim/commit/d9fe4dfdabad99215897670fe9ed1d222e109eb2))
* multilang brushup - home page en metadata ([17eb3f9](https://github.com/InboundPlatform/global-esim/commit/17eb3f9d6fd739aca004f58f7ea3fed93d245f93))
* SEO content update ([33ce216](https://github.com/InboundPlatform/global-esim/commit/33ce216a06e7c92417500e1f8db6492ac86bfd54))
* update compatibility ([1f6067f](https://github.com/InboundPlatform/global-esim/commit/1f6067f22206f60d970dec2544d312e20cf5e5ba))
* update country flag ([4be8db8](https://github.com/InboundPlatform/global-esim/commit/4be8db8861fb5b7ae8d322cb803cf6fa29aeae9d))
* update new country plans ([c5ef09c](https://github.com/InboundPlatform/global-esim/commit/c5ef09c0fb2cd0ce4b4c8dbfb08cc4a5c586f13b))
* update new plan images ([655d92c](https://github.com/InboundPlatform/global-esim/commit/655d92c0103bc8dc675f7731c5f52deb08ed0e4f))

## [2.17.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.16.2...webjp-v2.17.0) (2024-09-27)


### Features

* change country page to static ([d625a5b](https://github.com/InboundPlatform/global-esim/commit/d625a5b4d47039d303414184e8cd0c06f290bd36))


### Bug Fixes

* show payment error message ([5650b76](https://github.com/InboundPlatform/global-esim/commit/5650b76d1476de143545fc6b881269dbb47ffc80))

## [2.16.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.16.1...webjp-v2.16.2) (2024-09-13)


### Bug Fixes

* column page 500 error ([7250463](https://github.com/InboundPlatform/global-esim/commit/725046348425a1b2839a0ffa50f41a6bd537c476))
* column page 500 error ([089917b](https://github.com/InboundPlatform/global-esim/commit/089917b726d2758eaf53b6450ba4b08e30afc59f))

## [2.16.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.16.0...webjp-v2.16.1) (2024-09-12)


### Bug Fixes

* login user purchase bug fix ([c39d5c7](https://github.com/InboundPlatform/global-esim/commit/c39d5c77536575a6d77abfe9e27a77671666558d))

## [2.16.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.15.2...webjp-v2.16.0) (2024-09-12)


### Features

* get iccid on complete page for guestc ([5e35c70](https://github.com/InboundPlatform/global-esim/commit/5e35c702c3bf0c9c3e50865ec35781ee85866e6a))
* remove iccid on thank you page ([456c6e3](https://github.com/InboundPlatform/global-esim/commit/456c6e327c0f6471c7a715d8df8cffe1363eee20))
* reset guest user password ([1d66447](https://github.com/InboundPlatform/global-esim/commit/1d66447ac94056f6bee167c1bf094d42d8ac9e72))
* text updates and resend email link ([c10ed9d](https://github.com/InboundPlatform/global-esim/commit/c10ed9df8b1289bd4489e2b4440dc55a3d005ad2))


### Bug Fixes

* dynamic rendering on column pages ([0b9b317](https://github.com/InboundPlatform/global-esim/commit/0b9b31700561a254d92b109e3de12da943ae34f8))
* login error translation fix ([ca9b890](https://github.com/InboundPlatform/global-esim/commit/ca9b89003d39e7d13048853f9787306d59135759))
* move complete page to no auth ([a888eb0](https://github.com/InboundPlatform/global-esim/commit/a888eb08dec18a6ba4b992f4d37569dcb3210df5))
* reset email logic fixes ([6199079](https://github.com/InboundPlatform/global-esim/commit/619907971b14fe50fa1c125afe26b71f5354dee9))

## [2.15.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.15.1...webjp-v2.15.2) (2024-09-11)


### Bug Fixes

* column remove no index ([c618e2f](https://github.com/InboundPlatform/global-esim/commit/c618e2f06f2020fc97eaf3133a1dd82d9b569ddc))

## [2.15.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.15.0...webjp-v2.15.1) (2024-09-05)


### Bug Fixes

* add 404 redirects to non-existent countries ([7767e3c](https://github.com/InboundPlatform/global-esim/commit/7767e3c7a361607993b2b156f7dc876a7eac0f94))

## [2.15.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.14.0...webjp-v2.15.0) (2024-08-29)


### Features

* 50860 column page esim ([bca7bcf](https://github.com/InboundPlatform/global-esim/commit/bca7bcf799bdb5990a147beefe7b3875ac997bc7))
* 50860 column page fixes ([f0f30ea](https://github.com/InboundPlatform/global-esim/commit/f0f30ea2be418b3495a7b4d6cd443012e567a489))
* 50860 column page popular columns ([4f2e6ac](https://github.com/InboundPlatform/global-esim/commit/4f2e6ac3c2808f00f83a791ff50fe8a7aeafefed))
* 51182 gm global navigation ([ad34d61](https://github.com/InboundPlatform/global-esim/commit/ad34d61f48a3a7356b00fca2aee108ff136cff13))

## [2.14.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.13.1...webjp-v2.14.0) (2024-08-23)


### Features

* add recaptcha to signup ([3d896e2](https://github.com/InboundPlatform/global-esim/commit/3d896e2f87d2692c36324ddc0e59f639e20957c3))


### Bug Fixes

* add recaptcha to callback ([fbd0ebb](https://github.com/InboundPlatform/global-esim/commit/fbd0ebb9c25d75a7ff6d2d6ba2d4b6ff141d3930))
* signup error message fix ([a54a96d](https://github.com/InboundPlatform/global-esim/commit/a54a96d498e27c366804e4b86934a24bbceb1639))
* translation fixes ([872db33](https://github.com/InboundPlatform/global-esim/commit/872db3302a86ce19d03c0dd6e0485f38da502954))

## [2.13.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.13.0...webjp-v2.13.1) (2024-08-16)


### Bug Fixes

* build issues webjp ([43f7986](https://github.com/InboundPlatform/global-esim/commit/43f7986c1847a072aa1695a6ad6dd0d0ef5fb684))

## [2.13.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.12.0...webjp-v2.13.0) (2024-08-16)


### Features

* add source parameter to gm esim signup ([637bde5](https://github.com/InboundPlatform/global-esim/commit/637bde54d99f9dfe06c95c907edcaede8fcfacd9))

## [2.12.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.11.0...webjp-v2.12.0) (2024-08-15)


### Features

* 50839 feedback updates 8/14 ([001c2b1](https://github.com/InboundPlatform/global-esim/commit/001c2b11954a40fde3209db6bb827e06789e3b3f))
* 51080 setup page ([e78ae66](https://github.com/InboundPlatform/global-esim/commit/e78ae6636c5b5a083ece984011c975b5adc55889))
* 51116 data usage note ([8d296f9](https://github.com/InboundPlatform/global-esim/commit/8d296f9f1ff21fd3ab7a315e7416b83a7d7316d2))


### Bug Fixes

* 51080 reset carrousel on tab change ([e667add](https://github.com/InboundPlatform/global-esim/commit/e667add9474154d7c797e64b4026cffd9fd53be1))

## [2.11.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.10.2...webjp-v2.11.0) (2024-08-08)


### Features

* 08/08 feedbacks ([d8423d4](https://github.com/InboundPlatform/global-esim/commit/d8423d489274ea7b97d097485ba6236cea70b5cf))

## [2.10.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.10.1...webjp-v2.10.2) (2024-08-07)


### Bug Fixes

* webjp social login issues ([39f1aec](https://github.com/InboundPlatform/global-esim/commit/39f1aec10c83e1ec1edd97d64f96ebecd4d3fa56))

## [2.10.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.10.0...webjp-v2.10.1) (2024-08-07)


### Bug Fixes

* deploy fix webjp ([c9bc136](https://github.com/InboundPlatform/global-esim/commit/c9bc1367216ac3b59341988c22dec220d8842833))

## [2.10.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.9.0...webjp-v2.10.0) (2024-08-07)


### Features

* 50839 feedback fixes ([a733ee4](https://github.com/InboundPlatform/global-esim/commit/a733ee45a757239b6726bd51946a34c95bfc4fce))
* 50839 feedback fixes ([8e44a17](https://github.com/InboundPlatform/global-esim/commit/8e44a17922ab78b58bd7e34ccf702e2465bf747c))
* 50839 feedback updates ([e518ec1](https://github.com/InboundPlatform/global-esim/commit/e518ec13f11d5423e3cc240c2fe667e31f7c7ee6))
* 51010 destination page ([36343dc](https://github.com/InboundPlatform/global-esim/commit/36343dce393a7bb984135053f624f85d06066fa1))
* add iccid to gm esim complete page ([56ab054](https://github.com/InboundPlatform/global-esim/commit/56ab0542200ca093fea87a7ff9e86480dd83cb5e))


### Bug Fixes

* 51010 uk search fix ([426641a](https://github.com/InboundPlatform/global-esim/commit/426641aa61f26bf5b2910e232ed38567332c55e5))
* image fixes, cherry pick fixes ([aa6567a](https://github.com/InboundPlatform/global-esim/commit/aa6567a74951b7379ccbca520b126890dbec3810))

## [2.9.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.8.1...webjp-v2.9.0) (2024-08-01)


### Features

* 50839 feedback fixes ([72ba8eb](https://github.com/InboundPlatform/global-esim/commit/72ba8eb1f1a5d68961c1182836c0a88a4d9a8a4c))
* 50964 add compatible function ([2be2fef](https://github.com/InboundPlatform/global-esim/commit/2be2fefd36f318de4780bb8e9dea3e11aad34621))
* gm esim search module ([d7fa8ae](https://github.com/InboundPlatform/global-esim/commit/d7fa8ae36d1c9bc8119c0f9ab327fffd5560a096))
* search by upper case ([0fa3fe5](https://github.com/InboundPlatform/global-esim/commit/0fa3fe52f0d2df531b612c5607e9914c90d80aae))


### Bug Fixes

* 50948 text updates ([5588f76](https://github.com/InboundPlatform/global-esim/commit/5588f7634d7131c97ea0c34120e7cdd604ca8274))
* 50964 translation fixes, image fixes ([eb6cc31](https://github.com/InboundPlatform/global-esim/commit/eb6cc313c6d613f62d5818864b27f5ad89711a91))
* image fixes, cherry pick fixes ([ba4626c](https://github.com/InboundPlatform/global-esim/commit/ba4626cff4137683e8d903d602f380e3b7ec6c44))
* search module missing image fix ([fa031c4](https://github.com/InboundPlatform/global-esim/commit/fa031c4c041da815c13dc32a97efa69ad52981d7))

## [2.8.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.8.0...webjp-v2.8.1) (2024-07-29)


### Bug Fixes

* remove spaces in country names ([8c73ab1](https://github.com/InboundPlatform/global-esim/commit/8c73ab197496cd3f9c076e09ebd806b07c61ed2e))
* top page sort fix ([637bfe6](https://github.com/InboundPlatform/global-esim/commit/637bfe6edc86b6f72acb3172dba82aaffce84d06))

## [2.8.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.7.2...webjp-v2.8.0) (2024-07-26)


### Features

* added recaptcha for airtrip fe ([48855de](https://github.com/InboundPlatform/global-esim/commit/48855deb4b273b92156f1ad75d428bf780b561d1))
* added recaptcha for fe ([520d2c0](https://github.com/InboundPlatform/global-esim/commit/520d2c00bcd603d5f20a39eb540821df991e5ff2))


### Bug Fixes

* add loading indicator for signin signup buttons ([9a58ad6](https://github.com/InboundPlatform/global-esim/commit/9a58ad613f10b7b0645674f5f54bedf2224ec44c))
* login error translations ([7720fb3](https://github.com/InboundPlatform/global-esim/commit/7720fb34769856bb82befa8f1b5123e9568fb957))

## [2.7.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.7.1...webjp-v2.7.2) (2024-07-26)


### Bug Fixes

* image fix ([b39d3b8](https://github.com/InboundPlatform/global-esim/commit/b39d3b89e6dc45c9d4793572f077d9f3b603892c))

## [2.7.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.7.0...webjp-v2.7.1) (2024-07-25)


### Bug Fixes

* missing images and api parameter fix ([f358950](https://github.com/InboundPlatform/global-esim/commit/f358950aa677c240267de70f3aea3992ebceaa8e))

## [2.7.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.6.2...webjp-v2.7.0) (2024-07-25)


### Features

* 50973 add new countries ([b830ee7](https://github.com/InboundPlatform/global-esim/commit/b830ee79cb9d04eb167b2cfd4346ab5cbdc674e8))
* 50973 updated countries price, country images ([b6b6d7d](https://github.com/InboundPlatform/global-esim/commit/b6b6d7dea667e4f074d6633c7a951bbfd324c004))
* countries translation updates, mobile ui updates ([ff483dd](https://github.com/InboundPlatform/global-esim/commit/ff483dd16abdc6cf2232ca9e32d47a349481641e))

## [2.6.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.6.1...webjp-v2.6.2) (2024-07-23)


### Bug Fixes

* region page currency symbol fix ([0e00ead](https://github.com/InboundPlatform/global-esim/commit/0e00ead32b28eb42a55d28a106d26531ffb4b3cb))

## [2.6.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.6.0...webjp-v2.6.1) (2024-07-22)


### Bug Fixes

* lgu feedback fixes ([#605](https://github.com/InboundPlatform/global-esim/issues/605)) ([624aebf](https://github.com/InboundPlatform/global-esim/commit/624aebf7dc8a0ebe69c6efd2158d7d12b336a4c0))
* regional data fix webjp ([2c99fd1](https://github.com/InboundPlatform/global-esim/commit/2c99fd122ef583ff94f0fecf3b76d6e6e458d33e))
* regional data fix webjp ([2c99fd1](https://github.com/InboundPlatform/global-esim/commit/2c99fd122ef583ff94f0fecf3b76d6e6e458d33e))
* regional data fix webjp ([f4f38a3](https://github.com/InboundPlatform/global-esim/commit/f4f38a3d782101d6403b2ca488e273586837afa4))
* regional data fix webjp ([5d97a6c](https://github.com/InboundPlatform/global-esim/commit/5d97a6c7678b245cc8cf5c186cc8a47cbeffae8b))

## [2.6.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.5.0...webjp-v2.6.0) (2024-07-18)


### Features

* 50839 lgu updates ([545b72d](https://github.com/InboundPlatform/global-esim/commit/545b72d93fd050c5638c38f2963bf78e1855f4d0))
* 50839 show LGU and ui updates ([46e1caa](https://github.com/InboundPlatform/global-esim/commit/46e1caad56b0716d254e5a95241ff2319d1b0a85))
* added featured plans to public and cdn ([1a16f73](https://github.com/InboundPlatform/global-esim/commit/1a16f733531d63345b673c30dfaa60b3f581e801))
* airtrip ui/ux updates, passport form updates ([56bb006](https://github.com/InboundPlatform/global-esim/commit/56bb0067b4137eb079ec3dd2a5d346d7ce944e66))


### Bug Fixes

* add auth signin/signup back to webjp ([b0590d6](https://github.com/InboundPlatform/global-esim/commit/b0590d67279f58bd7bff9096a69a96f097dfc3be))
* add auth signin/signup back to webjp ([f2f574f](https://github.com/InboundPlatform/global-esim/commit/f2f574f2f7fc8a70a4c1753790e239506a7e8d2a))
* build error fixes ([a149743](https://github.com/InboundPlatform/global-esim/commit/a14974312c57bc5561b4487492445c9a6b01dbc0))
* cdn image fix ([f9a4fbe](https://github.com/InboundPlatform/global-esim/commit/f9a4fbe41cfca9946c3b1bf13468143d7069613d))
* disable sso for now ([89926a9](https://github.com/InboundPlatform/global-esim/commit/89926a92e912dc3b2472970992291302a6a6d8eb))
* get plans api parameter fix ([16388f1](https://github.com/InboundPlatform/global-esim/commit/16388f161116480a23672b420ff1e4e3d491066a))
* gm esim jp signin page translation fix ([bab5a91](https://github.com/InboundPlatform/global-esim/commit/bab5a91037ba6f6667be04eb3b8339d4869e3f74))
* guide link fix ([2c21f28](https://github.com/InboundPlatform/global-esim/commit/2c21f2830a931a02e6b22d3accfb9f42cbae434b))
* lgu banner link fix ([9324de5](https://github.com/InboundPlatform/global-esim/commit/9324de5e29e48df7e2dfc0f94bc30e7e6a328754))
* lgu fixes ([8f89d23](https://github.com/InboundPlatform/global-esim/commit/8f89d23bf918a4d801debea213fa224e88c74db7))
* link fixes ([291569d](https://github.com/InboundPlatform/global-esim/commit/291569d132f0f09b8a0628e537fb73f532713f88))
* qr code src fix ([5f56cd3](https://github.com/InboundPlatform/global-esim/commit/5f56cd378e69b9addbd425dd5013515da01f6131))
* resend verification email link ([dcefa1a](https://github.com/InboundPlatform/global-esim/commit/dcefa1a5c01a1c2a5e173669658d65834f815a99))
* text and redirection fixes ([ed52b02](https://github.com/InboundPlatform/global-esim/commit/ed52b028e89ac4ab5e488e8051659b6bb380562b))
* text and translation fixes ([edf22fa](https://github.com/InboundPlatform/global-esim/commit/edf22fa2a38a3e6b8c8871cf68c8d6086134096f))
* wip ([095d7c3](https://github.com/InboundPlatform/global-esim/commit/095d7c35e51ce02c5fee2bad706dd86edb28943c))

## [2.5.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.4.1...webjp-v2.5.0) (2024-07-11)


### Features

* 50839 50932 ui/ux updates ([2ac3050](https://github.com/InboundPlatform/global-esim/commit/2ac30506725de33bff032df27e2cdf26434d6e1b))
* 50881 gm passport form ui/ux updates ([d8bf771](https://github.com/InboundPlatform/global-esim/commit/d8bf7719668e491a761bdbeec9fe37597e8a05b5))
* 50931 added traffic data ui ([ee1a61e](https://github.com/InboundPlatform/global-esim/commit/ee1a61e49f7990f73ced42819653daa78b506180))
* added new countries tawain hongkong ([42ef3e9](https://github.com/InboundPlatform/global-esim/commit/42ef3e9a91214cacc522c4fc996012a1bbf09285))
* airtrip hongkong taiwan, moved plan api call to client ([0cf53b8](https://github.com/InboundPlatform/global-esim/commit/0cf53b85c9425375a1497b497be33d708da77686))
* bg updates, scroll to traffic data ([058dfdb](https://github.com/InboundPlatform/global-esim/commit/058dfdb56aa033badb1590ad741a74179436f9c7))
* gm taiwan hongkong ([1410eb0](https://github.com/InboundPlatform/global-esim/commit/1410eb08e6da831f95893ad422c59d288f93dbae))
* order form appeal, text fixes ([3a0e9c1](https://github.com/InboundPlatform/global-esim/commit/3a0e9c157d28bef5eb14aa7532aa677efe29c8d2))
* top page country sort and price updates ([1b8edc0](https://github.com/InboundPlatform/global-esim/commit/1b8edc02ee4f3b6f4fa30bfd8fc103b95296ed18))
* update loading ui ([bbca723](https://github.com/InboundPlatform/global-esim/commit/bbca723dcf6ef4b46df25f9d9eaa8d23061d595d))


### Bug Fixes

* complete page message fix ([4272689](https://github.com/InboundPlatform/global-esim/commit/4272689225fb4d4780e1aa9323d6c44e773430f1))
* feedback fixes ([f0fb6c7](https://github.com/InboundPlatform/global-esim/commit/f0fb6c7aa52eee78fb5b6cd209a0600f1ab28ed3))
* feedback fixes 07/11 ([c816c1b](https://github.com/InboundPlatform/global-esim/commit/c816c1bc907b40e338b1748b3a150ad941f70aa5))
* gm complete redirect fix ([92a2b40](https://github.com/InboundPlatform/global-esim/commit/92a2b40094ef2aebbe04f08283a56e4d0bb47ca4))
* gm specs scroll area ([e274990](https://github.com/InboundPlatform/global-esim/commit/e274990188c56eae065ce872764b353c67d0eefe))
* gm specs scroll area ([cf73f91](https://github.com/InboundPlatform/global-esim/commit/cf73f91e941ae44512efdcda7701c1e290df287a))
* need registration session fix ([16d550e](https://github.com/InboundPlatform/global-esim/commit/16d550e2bf19ba3db761bfa0e7a49965aab6beb8))
* text fixes ([f863083](https://github.com/InboundPlatform/global-esim/commit/f863083f8d53182cc66f01737b373a71aeb82479))
* translation fix ([2aec554](https://github.com/InboundPlatform/global-esim/commit/2aec5543f08fd6abc962f475978316e79620d29e))

## [2.4.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.4.0...webjp-v2.4.1) (2024-07-08)


### Bug Fixes

* redeploy static page ([122737b](https://github.com/InboundPlatform/global-esim/commit/122737b7a7234196572ef4f614d7313ddb0e4cff))

## [2.4.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.3.1...webjp-v2.4.0) (2024-07-05)


### Features

* add line button, re-add guam ([d38edfd](https://github.com/InboundPlatform/global-esim/commit/d38edfd16322d3c2c9b18c622d60d045ee95fe01))

## [2.3.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.3.0...webjp-v2.3.1) (2024-07-04)


### Bug Fixes

* remove guam temporarily ([07a48f4](https://github.com/InboundPlatform/global-esim/commit/07a48f48be4a8cc91bcb54b00e6807064ec8cae7))

## [2.3.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.2.0...webjp-v2.3.0) (2024-07-04)


### Features

* 50449 android guide images ([46b4473](https://github.com/InboundPlatform/global-esim/commit/46b44735294ab261477af689d54c40087ce9a853))
* 50449 gm esim activation page updates ([5590470](https://github.com/InboundPlatform/global-esim/commit/5590470b638a9208152e8c09f33f2fa6e407df56))
* 50567 compatible page redesign ([0909b82](https://github.com/InboundPlatform/global-esim/commit/0909b820d578e219bf9b70eb252689f1956ad7ae))
* 50567 removed modal for future redesign ([5db9f8c](https://github.com/InboundPlatform/global-esim/commit/5db9f8c9139d97fe6568ce975fd494c0dbe42927))
* 50759 faq page updates for airtrip and gm ([3f66850](https://github.com/InboundPlatform/global-esim/commit/3f668504a34fec94066f8f2d70dd4c1db6b9d627))
* 50845 order form redesign ([1bb6c6c](https://github.com/InboundPlatform/global-esim/commit/1bb6c6c4587a53459a9e88a41560e94cbcc0a798))
* added new countries to gm esim ([20e88e6](https://github.com/InboundPlatform/global-esim/commit/20e88e601d6e323f9052df7947f7393dc6e44ff8))
* handle unlimited plans ([266ba44](https://github.com/InboundPlatform/global-esim/commit/266ba44209f824dbac697f126b44bbaa7d2fba54))
* help page redirection ([d6ff8d1](https://github.com/InboundPlatform/global-esim/commit/d6ff8d15ebbdaf68e4d01a67eb9e23a4fc147ecb))
* new faq page ([f41af7f](https://github.com/InboundPlatform/global-esim/commit/f41af7f13c5b03dbf47e9d1b9d938b04fb1d11f1))


### Bug Fixes

* 50839 gm scroll fix, ui updates ([ec9deb9](https://github.com/InboundPlatform/global-esim/commit/ec9deb98a13b139aedcfbd84c6abdfc276ae12ec))
* 50839 setup guide fixes ([b2bf6b9](https://github.com/InboundPlatform/global-esim/commit/b2bf6b954378266fff2bbde24ab81a73cb675f4e))
* 50839 text updates, validation updates ([edb2262](https://github.com/InboundPlatform/global-esim/commit/edb226231370178fb7e970af67161a63c1f136eb))
* button size fixes, text size fixes, link fixes ([34d64d3](https://github.com/InboundPlatform/global-esim/commit/34d64d3a4662744bd2549fd2d2420374f0916f9b))
* icons, style fixes ([b2a68e2](https://github.com/InboundPlatform/global-esim/commit/b2a68e29ee3f433a09a34cbf53d07088db4ded61))
* login error message update ([626e5d2](https://github.com/InboundPlatform/global-esim/commit/626e5d22dcb0e3ac9de61afbac1eac0a0389e549))
* missing gm esim activation texts ([829a4ef](https://github.com/InboundPlatform/global-esim/commit/829a4ef49b740ac18c9cc8dd96d8f4bbde55942f))
* missing hawaii translation ([826e6e0](https://github.com/InboundPlatform/global-esim/commit/826e6e091c1175b06cbf6bff160fb25eeaf1c8da))
* redirect update ([0fbdfd9](https://github.com/InboundPlatform/global-esim/commit/0fbdfd95c32b95958967b89161bc10338a29b9eb))
* style update ([aa7e5bf](https://github.com/InboundPlatform/global-esim/commit/aa7e5bf24b22849a96eee73246b83d919d62ec49))
* url update ([d567e6f](https://github.com/InboundPlatform/global-esim/commit/d567e6f1b361ac82fa0a4889dc01060c77f0dcdc))

## [2.2.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.1.0...webjp-v2.2.0) (2024-07-01)


### Features

* added line button, removed lgu contents ([4fd7f11](https://github.com/InboundPlatform/global-esim/commit/4fd7f11c642e89f1699e7d3245bd7f368970b736))
* added reset password, resend email pages ([157c1fd](https://github.com/InboundPlatform/global-esim/commit/157c1fd167bf48d862ab144b2280ca1664847971))
* init airtrip kv ([37ce23b](https://github.com/InboundPlatform/global-esim/commit/37ce23bbba2d33c467bb376184715e8749c9b049))
* sp kv updates ([f045019](https://github.com/InboundPlatform/global-esim/commit/f0450197569b798198bc6a59abfbd721ade2da2c))
* update airtrip register api ([2322082](https://github.com/InboundPlatform/global-esim/commit/2322082357fdae2ff24851f1873f130f2cc14761))
* update korea banners to lgu ([b9e1cee](https://github.com/InboundPlatform/global-esim/commit/b9e1cee9fecae313717194b52fb3b9db22b7a8bc))


### Bug Fixes

* 50839 added no user error message ([7f8623e](https://github.com/InboundPlatform/global-esim/commit/7f8623ede3ace3ca8343c3afea848137e9efa594))
* added terms and conditions page ([ee7d68c](https://github.com/InboundPlatform/global-esim/commit/ee7d68cd48fdf794012affb915f9f10b55b131f4))
* airtrip link fixes, korea counter details, lgu compatiblity ([6ac7ec0](https://github.com/InboundPlatform/global-esim/commit/6ac7ec0d280eb365b1b81195edda1cf235d3c5ef))
* changed name char limit to 1, update error messages to jp ([2aa461c](https://github.com/InboundPlatform/global-esim/commit/2aa461cb704eb468dc8f993a8ba3003043337e02))
* feedback fixes ([2ff26a6](https://github.com/InboundPlatform/global-esim/commit/2ff26a672afd17be780ff0c5b389889c729b824f))
* spacing fixes ([00c33b7](https://github.com/InboundPlatform/global-esim/commit/00c33b7d99c00e26b3c403a1307cc9e3e132ea9e))
* spacing fixes ([0ac9d8d](https://github.com/InboundPlatform/global-esim/commit/0ac9d8d07fa38196eb59c46807e1368e1788c013))

## [2.1.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.0.2...webjp-v2.1.0) (2024-06-19)


### Features

* contact page and help page updates ([7c7f858](https://github.com/InboundPlatform/global-esim/commit/7c7f8588219528bc5a7ea2edafacca08e66880b0))


### Bug Fixes

* feedback fixes 06/19 ([79d7dc6](https://github.com/InboundPlatform/global-esim/commit/79d7dc6fbd9fa17cb14514bb37a54b68606c08a9))
* footer links ([df58ab3](https://github.com/InboundPlatform/global-esim/commit/df58ab3aa1012e4353690b5375317be70f63747b))
* lang and currency modal fixes ([3de2717](https://github.com/InboundPlatform/global-esim/commit/3de2717b81292bbfeff45d49a208b0e289f7c3f9))
* **page:** missing locale for ToM page ([#494](https://github.com/InboundPlatform/global-esim/issues/494)) ([167ed1f](https://github.com/InboundPlatform/global-esim/commit/167ed1f80d935b1651e032e766948ad1c8d9d244))
* redirect profile page to top page# ([ab922c0](https://github.com/InboundPlatform/global-esim/commit/ab922c0aa36d91caf192b7b03ebdd399cdc6c66f))

## [2.0.2](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.0.1...webjp-v2.0.2) (2024-06-14)


### Bug Fixes

* manual url fix ([dce3a22](https://github.com/InboundPlatform/global-esim/commit/dce3a22365e06af2f9a9f3fa0c6b6a71578cead8))

## [2.0.1](https://github.com/InboundPlatform/global-esim/compare/webjp-v2.0.0...webjp-v2.0.1) (2024-06-14)


### Bug Fixes

* **webjp:** hotfixes for webjp release ([a827704](https://github.com/InboundPlatform/global-esim/commit/a8277049fefbbd484c76fbd5a912a8b36663b3a6))

## [2.0.0](https://github.com/InboundPlatform/global-esim/compare/webjp-v1.5.0...webjp-v2.0.0) (2024-06-14)


### ⚠ BREAKING CHANGES

* **web-jp:** new release

### Features

* **web-jp:** add japanese pc version ([95995bd](https://github.com/InboundPlatform/global-esim/commit/95995bdb9f45f263e3c81355c1446db372515bb3))


### Bug Fixes

* added dependency packages ([aa7b3c2](https://github.com/InboundPlatform/global-esim/commit/aa7b3c2facf37b5b6fe9ad01133c379478277d79))
* added eslint shared config ([b99cb7f](https://github.com/InboundPlatform/global-esim/commit/b99cb7fbe5124c07ee0a578799138d61b417d3b6))
* added eslint shared config ([46f47a4](https://github.com/InboundPlatform/global-esim/commit/46f47a4759d814005475d256ebd7525f5228b082))
* added eslint shared config ([24a63de](https://github.com/InboundPlatform/global-esim/commit/24a63de5a8ed02d9076948bc209185b33e2e4e2b))
* react dom version fix ([0505ad7](https://github.com/InboundPlatform/global-esim/commit/0505ad765baa7c839fe15bc752b335b7ebf1f404))
* translation error on packages ([c96da3e](https://github.com/InboundPlatform/global-esim/commit/c96da3e7c2ec03c79e040a481376afb595877011))
* update webjp node version ([3c6d029](https://github.com/InboundPlatform/global-esim/commit/3c6d029994149dc3a03a865fdcc1cf8cc10b7001))
* web jp only build fixes ([7827116](https://github.com/InboundPlatform/global-esim/commit/78271167678421c5ad349b7939c2e9fc832bc74f))
