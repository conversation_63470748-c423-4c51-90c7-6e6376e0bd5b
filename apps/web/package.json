{"name": "web", "version": "1.15.2", "private": true, "scripts": {"analyze": "ANALYZE=true next build", "build": "next build", "commit": "git-cz", "dev": "next dev -p 3014", "dev:web": "turbo run dev  --no-cache --filter webjp", "lint": "next lint --quiet", "prod": "next start -p 3000", "start": "next start -p 3000", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "dependencies": {"@gmesim/fe-apis": "*", "@gmesim/fe-utils": "*", "@mantine/carousel": "7.9.0", "@mantine/core": "7.9.0", "@mantine/dates": "7.9.0", "@mantine/form": "7.9.0", "@mantine/hooks": "7.9.0", "@mantine/notifications": "7.9.0", "@repo/ui": "*", "@ri/fe-auth": "*", "@sentry/nextjs": "^7.105.0", "@stripe/react-stripe-js": "^2.7.1", "@stripe/stripe-js": "^3.4.1", "@tabler/icons-react": "^3.3.0", "@types/js-cookie": "^3.0.6", "accept-language": "^3.0.18", "axios": "^1.6.8", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "dot-prop-immutable": "^2.1.1", "embla-carousel-react": "^8.1.5", "filesize": "^10.1.2", "i18next": "^23.11.3", "i18next-browser-languagedetector": "^8.0.0", "i18next-resources-to-backend": "^1.2.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "microcms-js-sdk": "^2.7.0", "next": "14.2.3", "next-i18n-router": "^5.4.3", "next-seo": "^6.5.0", "next-session": "^4.0.5", "nextjs-progressbar": "0.0.16", "react": "18.2.0", "react-dom": "18.2.0", "react-google-recaptcha-v3": "^1.10.1", "react-i18next": "^14.1.1", "react-infinite-scroll-component": "^6.1.0", "react-query": "^3.39.3", "use-debounce": "10.0.4", "zod": "^3.23.8", "zod-i18n-map": "^2.27.0", "zustand": "^4.5.2"}, "devDependencies": {"@next/eslint-plugin-next": "^14.1.1", "@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/eslint": "^8.56.5", "@types/lodash": "^4.17.4", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^10.0.1", "postcss": "^8", "postcss-preset-mantine": "^1.15.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}, "engines": {"node": "^20.18.1 || 20.2.0 || ^22.x || ^20.x"}}