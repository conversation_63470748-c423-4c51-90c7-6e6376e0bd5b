import { set } from "dot-prop-immutable";
import * as fs from "fs";
import { GoogleSpreadsheet } from "google-spreadsheet";
import * as path from "path";
import { fileURLToPath } from "url";

const header = [
  "Key",
  "English",
  "Japanese",
  "French",
  "Spanish",
  "Korean",
  "Malay",
  "Vietnamese",
  "Filipino",
  "Chinese Traditional",
  "Chinese Simplified",
];

const fileNameMap = {
  English: "en",
  Japanese: "jp",
  French: "fr",
  Spanish: "es",
  Korean: "kr",
  Malay: "ms",
  Vietnamese: "vi",
  Filipino: "ph",
  "Chinese Traditional": "zt",
  "Chinese Simplified": "zh",
};
function truncateString(str, maxLength) {
  if (str.length <= maxLength) {
    return str;
  }
  return str.substring(0, maxLength - 3) + "...";
}

function kebabCase(str = "", maxLength = 30) {
  const removeSpecialChars = str.replace(/[^A-Za-z0-9.|_]/g, ""); // Remove special characters except .
  let kebabCased = removeSpecialChars
    .replace(/([a-z])([A-Z])/g, "$1-$2") // Convert camelCase to kebab-case
    .replace(/\s+/g, "-") // Replace spaces with dashes
    // .replace(/_/g, '-') // Replace underscores with dashes
    .toLowerCase(); // Convert to lowercase

  return kebabCased?.trim?.();
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SHEET_ID = "1Hz_FNe1MqbNzyCc5q6O90IZZ_G6nmn7gfmQKMCpCa58";

const token = JSON.parse(
  fs.readFileSync("./apps/translation-sheet/token.json", "utf-8")
);
const doc = new GoogleSpreadsheet(SHEET_ID);
// Initialize Auth - see https://theoephraim.github.io/node-google-spreadsheet/#/getting-started/authentication
await doc.useServiceAccountAuth({
  // env var values are copied from service account credentials generated by google
  // see "Authentication" section in docs for more info
  client_email: token.client_email,
  private_key: token.private_key,
});
await doc.loadInfo();

const createNewTranslationFormat = async (title) => {
  const newSheet = await doc.addSheet({
    title,
  });
  newSheet.setHeaderRow(langs);
};

const saveSheetAsTranslatedJSON = async (index) => {
  const sheet =
    typeof index === "string"
      ? doc.sheetsByTitle[index]
      : doc.sheetsByIndex[index];
  const rows = await sheet.getRows();
  let langObj = {};
  const langs = header.slice(1, header.length);
  langs.forEach((lang) => {
    rows.forEach((row) => {
      const key = !row.Key
        ? row.Key
        : row.Key.startsWith(sheet.title + ".")
          ? row.Key
          : sheet.title + "." + row.Key;
      langObj = set(
        langObj,
        `${lang}.` + kebabCase(key),
        row[lang]?.replace?.(/\\n|\n/g, "<br/>")
      );
    });
  });

  return {
    name: sheet.title,
    langObj,
  };
};

// console.log(saveSheetAsTranslatedJSON(1));

async function* saveAsFile(saveOnlyThis) {
  try {
    const sheetcount = doc.sheetCount;
    const count = saveOnlyThis?.length || sheetcount;

    for (let i = 0; i < count; i++) {
      const langObj = await saveSheetAsTranslatedJSON(saveOnlyThis?.[i] || i);

      const langs = Object.keys(langObj.langObj);
      langs.forEach((lang) => {
        const folderName = fileNameMap[lang];
        if (!fs.existsSync(path.join(__dirname, "/i18n/" + folderName))) {
          fs.mkdirSync(path.join(__dirname, "/i18n/" + folderName));
        }
        let content = langObj.langObj[lang];
        content = content[langObj.name] ?? content;
        fs.writeFileSync(
          path.join(
            __dirname,
            "/i18n/" + folderName,
            langObj.name?.toLowerCase() + ".json"
          ),
          JSON.stringify(content,null,2),
          "utf-8"
        );
      });
      yield {
        sheet: i,
      };
    }
  } catch (err) {
    console.log(err);
  }
}

function delay() {
  return new Promise((res, rej) => setTimeout(res, 2000));
}

async function save(arr) {
  for await (const file of saveAsFile(arr)) {
    console.log("saved file", arr?.[file] || file);
    await delay();
  }
}
save().catch((err) => {
  console.log(err);
});
